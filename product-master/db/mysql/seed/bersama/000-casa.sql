-- Product Master DB Insert Queries For CASA

-- TABLE_NAME :  product_template
-- Queries :
INSERT INTO product_template (public_id, code, name, description, status, created_by, updated_by)
VALUES ('bf8658c8-9187-415b-af9c-79c8449d305a', 'SAVINGS_DEPOSIT', 'Savings Account', 'The savings account master product', 'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product_template_parameter
-- Queries :
INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
VALUES
    ('04df6c85-7f4e-11ec-93b7-0ac04b8a5758', 1, 'business', 'account_applicable_features', 'NULL', 'STRING', 'AT_PRODUCT_VARIANT', NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
    ('97ce86f2-9521-45fb-90ca-ae6c01e726c0', 1, 'business', 'tm_product_id', 'NULL', 'STRING', 'AT_PRODUCT_VARIANT', NULL,'The features that are applicable to an account','MANUAL','MANUAL');
    ('f3e65965-bfd1-11ec-b1b7-0aed065bc0fc', 1, 'risk',     'allowed_hold_codes','WHOLE_BALANCE_HOLD','ENUM','NO_OVERRIDE','NO_OVERRIDE','Is the list of allowed hold codes for this product','MANUAL','MANUAL');

-- TABLE_NAME :  pro    duct
-- Queries :
INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
VALUES
    ('1425f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, 'RETAIL_SAVINGS', 'Retail savings deposits', ' The retail savings deposits product', 'ACTIVE', 'MANUAL', 'MANUAL');
--  product_parameter

-- TABLE_NAME :  product_variant
-- Queries :
INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to, created_by, updated_by)
VALUES
    ('1525f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, 'casa_account_default', '1', 'Defaults retail savings deposits', 'The defaults retail savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'MANUAL', 'MANUAL') ,
    ('4a432945-eff1-48c1-8c51-33c74338d5f2', 1, 'casa_pocket_default', '2', 'Defaults retail savings deposits', 'The defaults retail savings deposits product variant','ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product_variant_parameter
-- Queries :
INSERT INTO product_variant_parameter (public_id, product_variant_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
VALUES
    ('1211e4ee-7f4f-11ec-93b7-a2f7a4e8ba3d', 1, 'business', 'account_applicable_features', '{"MAIN_ACCOUNT": true}','STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account', 'MANUAL','MANUAL') ,
    ('c7bbdc57-5592-471c-85da-030487ffa9e9', 2, 'business', 'account_applicable_features', '{"POCKET": true}', 'STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account','MANUAL', 'MANUAL') ,
    ('6eee7dac-1225-4200-b2d1-5e6e1805d479', 1, 'business', 'tm_product_id','deposit_product', 'STRING', 'NO_OVERRIDE',NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL') ,
    ('784cbc30-4e6b-4f1b-af41-6929422ea1d1', 2, 'business','tm_product_id', 'deposit_product', 'STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL');



-- TABLE_NAME :  transaction_catalogue
-- Queries :
INSERT INTO transaction_catalogue (public_id,`domain`,is_financial_txn,txn_type,txn_sub_type,display_name,description,status,created_by,updated_by) VALUES
('8da75539-929f-45ed-b21f-8096fe484e5e','deposits',1,'receive_money','rtol_alto','','Incoming RTOL funds transfer to Customer''s CASA Account - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('1c9217e3-53b1-497c-8c65-a4830f85724e','deposits',1,'receive_money_reversal','rtol_alto','','Reversal of incoming RTOL funds transfer to Customer''s CASA Account - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('47b8cdf5-d318-4b1d-a0a2-ae12d66e72ea','deposits',1,'send_money','rtol_alto','','AUTH - Outgoing RTOL funds transfer initiated by the BERSAMA Customer - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('ca8074fd-947a-4f72-98bb-f2f00bdf0e32','deposits',1,'send_money','rtol_alto','','SETTLE - Outgoing RTOL funds transfer initiated by the BERSAMA Customer - ALTO - 1', 'ACTIVE', 'MANUAL', 'MANUAL'),
('ae991da1-602f-4281-948e-2bc9c108c33c','deposits',1,'send_money','rtol_alto','','SETTLE - Outgoing RTOL funds transfer initiated by the BERSAMA Customer - ALTO - 2', 'ACTIVE', 'MANUAL', 'MANUAL'),
('ae7efc6c-5e71-4c66-989a-bc6da49d2f95','deposits',1,'send_money','rtol_alto','','RELEASE - Reversal of outgoing RTOL funds transfer initiated by the BERSAMA Customer - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('66795dc4-a6de-4a09-83d4-43854d097472','deposits',1,'send_money_reversal','rtol_alto','','REVERSAL - Reversal of outgoing RTOL funds transfer initiated by the BERSAMA Customer - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('ba0834b2-967b-416f-b652-85fec4d8fd00','deposits',1,'send_money_fee','rtol_alto','','AUTH - Fee to be collected for send money - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('99fa159f-abfc-408c-8666-1408f450654c','deposits',1,'send_money_fee','rtol_alto','','SETTLE - Fee to be collected for send money - ALTO - 1', 'ACTIVE', 'MANUAL', 'MANUAL'),
('4d4a5ce4-c421-444e-84db-fdc6527dc3aa','deposits',1,'send_money_fee','rtol_alto','','SETTLE - Fee to be collected for send money - ALTO - 2', 'ACTIVE', 'MANUAL', 'MANUAL'),
('806d4a53-16ab-4b6c-aec5-1c0248945397','deposits',1,'send_money_fee','rtol_alto','','RELEASE - Fee to be collected for send money - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('c07829b4-6509-4fe0-9cb1-cce2f24066b3','deposits',1,'send_money_fee_reversal','rtol_alto','','REVERSAL - Reversal of Fee to be collected for send money - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('c4eeed81-0b7e-40f3-ab53-312b4cac7625','deposits',1,'settlement','rtol_alto','','Periodic settlement to RTOL Network - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('042007e7-1c77-4379-8161-a3ca41cc5bfe','deposits',1,'settlement_reversal','rtol_alto','','Reversal of periodic settlement to RTOL Network - ALTO', 'ACTIVE', 'MANUAL', 'MANUAL'),
('c7340452-62c5-49b4-ac4b-03893cf48e0d','deposits',1,'receive_money','rtol_aj','','Incoming RTOL funds transfer to Customer''s CASA Account - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('561c49c6-80d6-4c25-b8c6-87441dead112','deposits',1,'receive_money_reversal','rtol_aj','','Reversal of incoming RTOL funds transfer to Customer''s CASA Account - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('953245d6-1017-4d0a-91bf-24df34be4b57','deposits',1,'send_money','rtol_aj','','AUTH - Outgoing RTOL funds transfer initiated by the BERSAMA Customer - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('5b31e65c-ec6d-447c-866c-5a1a269fc87a','deposits',1,'send_money','rtol_aj','','SETTLE - Outgoing RTOL funds transfer initiated by the BERSAMA Customer - AJ - 1', 'ACTIVE', 'MANUAL', 'MANUAL'),
('3f823cfa-7ae6-43c4-b443-d7f36aeace08','deposits',1,'send_money','rtol_aj','','SETTLE - Outgoing RTOL funds transfer initiated by the BERSAMA Customer - AJ - 2', 'ACTIVE', 'MANUAL', 'MANUAL'),
('6516780c-0ed3-43c8-a57b-871db85cdf11','deposits',1,'send_money','rtol_aj','','RELEASE - Reversal of outgoing RTOL funds transfer initiated by the BERSAMA Customer - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('********-3539-4417-8adc-ebb3b9c15da7','deposits',1,'send_money_reversal','rtol_aj','','REVERSAL - Reversal of outgoing RTOL funds transfer initiated by the BERSAMA Customer - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('623387a7-1abb-4ff3-a340-0c77ed599475','deposits',1,'send_money_fee','rtol_aj','','AUTH - Fee to be collected for send money - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('e92ae421-7611-45a7-b6b4-199897542f4c','deposits',1,'send_money_fee','rtol_aj','','SETTLE - Fee to be collected for send money - AJ - 1', 'ACTIVE', 'MANUAL', 'MANUAL'),
('3767d075-66fd-42b6-bd17-d80c4af0f935','deposits',1,'send_money_fee','rtol_aj','','SETTLE - Fee to be collected for send money - AJ - 2', 'ACTIVE', 'MANUAL', 'MANUAL'),
('5075f84a-5639-4730-836e-25c418269005','deposits',1,'send_money_fee','rtol_aj','','RELEASE - Fee to be collected for send money - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('f7658ace-23a4-4392-9bc6-10939ee66b36','deposits',1,'send_money_fee_reversal','rtol_aj','','REVERSAL - Reversal of Fee to be collected for send money - AJ', 'ACTIVE', 'MANUAL', 'MANUAL'),
('********-83ce-4c56-bab4-651b9ed8d7f4','deposits',1,'settlement','rtol_aj','','Periodic settlement to RTOL Network', 'ACTIVE', 'MANUAL', 'MANUAL'),
('5a0dbc0b-2dd1-4349-b627-296771ea9b77','deposits',1,'settlement_reversal','rtol_aj','','Reversal of periodic settlement to RTOL Network', 'ACTIVE', 'MANUAL', 'MANUAL'),
('6df112ad-8b77-4c91-a353-1ab5156d5ab3','deposits',1,'transfer_money','intrabank','','Intrabank funds transfer between CASA Accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
('46a287f9-3222-4d3f-9f15-ee222d2d41fc','deposits',1,'transfer_money_reversal','intrabank','','Reversal of intrabank funds transfer between CASA Accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
('93fc58d0-28a4-4c4f-ab88-94f621d5c1fc','deposits',1,'interest_accrual','savings','','Interest accrual for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
('8f897339-3ea3-4772-ad78-3bf979c8e50b','deposits',1,'interest_accrual_reversal','savings','','Reversal of interest accrual for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
('927548be-13b1-478e-9e45-8f89abdf6877','deposits',1,'interest_payout','savings','','Interest payout for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
('08d7a61d-6b67-45f8-b0d6-313ed00751b5','deposits',1,'interest_payout_reversal','savings','','Reversal of interest payout for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
('03e71475-0ed4-4ac0-8df8-81719753094e','deposits',1,'tax_accrual','savings','','Tax on accrued interest for savings acount', 'ACTIVE', 'MANUAL', 'MANUAL'),
('329cd5b0-07f3-4852-8dfd-f87f2478ba3a','deposits',1,'tax_accrual_reversal','savings','','Reversal of tax on accrued interest for savings account', 'ACTIVE', 'MANUAL', 'MANUAL'),
('6bdbabb7-8f39-4634-b128-0e5f3b16206c','deposits',1,'tax_payout','savings','','Tax posting of the accroued tax for the savings account', 'ACTIVE', 'MANUAL', 'MANUAL'),
('a5fdb895-c6ca-4bc5-8cf6-f4f6e134fb52','deposits',1,'tax_payout_reversal','savings','','Reversal of Tax accrual entries', 'ACTIVE', 'MANUAL', 'MANUAL'),
('ef65431c-cb96-43aa-b8c4-1bc6ab9473d4','finance',1,'adjustment','bank_initiated','','<Purpose captured in the remarks>', 'ACTIVE', 'MANUAL', 'MANUAL'),
('7c20e1ce-08ae-4b88-a171-b229e5ad9531','deposits',1,'transfer_money','from_main_to_pocket','','For funds transfer between main CASA account & Pocket (Savings, Spend)', 'ACTIVE', 'MANUAL', 'MANUAL'),
('ba3d876f-3c92-44a2-a3bf-7ee5ce0f574e','deposits',1,'transfer_money_reversal','from_main_to_pocket','','Reversal for funds transfer between main CASA account & Pocket (Savings, Spend)', 'ACTIVE', 'MANUAL', 'MANUAL'),
('899fe8cc-c480-422e-b466-4c4a0c2e0d1e','deposits',1,'transfer_money','from_pocket_to_main','','For funds transfer between Pocket (Savings, Spend) & main CASA account', 'ACTIVE', 'MANUAL', 'MANUAL'),
('a00b2027-41c1-4132-9ef5-9fd2cca88ff1','deposits',1,'transfer_money_reversal','from_pocket_to_main','','Reversal for funds transfer between Pocket (Savings, Spend) & main CASA account', 'ACTIVE', 'MANUAL', 'MANUAL'),
('060e00f5-418f-42fe-9616-26b67af70d60','deposits',1,'interest_accrual_transfer','savings','','Transfer of accrued interest in case of account closure', 'ACTIVE', 'MANUAL', 'MANUAL'),
('3a900a66-00fd-464e-8cee-a6ab71cae401','deposits',1,'tax_accrual_transfer','savings','','Transfer of accrued tax in case of account closure', 'ACTIVE', 'MANUAL', 'MANUAL'),
('704b4d5f-3a9d-4a84-a94f-3e250e39c8cc','deposits',1,'account_close_transfer','savings','','To park the fund in the event of account closure', 'ACTIVE', 'MANUAL', 'MANUAL')



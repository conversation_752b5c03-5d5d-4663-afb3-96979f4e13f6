-- Product Master DB Insert Queries For Lending
-- LastUpdatedAt: 13/03/2023

----------------------------------- TABLE_NAME :  product_template -----------------------------------
----------------------------------- Queries :

INSERT INTO `product_template` (id, public_id, code, name, description, status, created_by, updated_by)
VALUES (3, uuid(), "UNSECURED_LINE_OF_CREDIT", "Unsecured Line of Credit", "The unsecured line of credit product",
        "ACTIVE", "MANUAL", "MANUAL"),
       (4, uuid(), "UNSECURED_TERM_LOAN", "Unsecured Term Loan", "The unsecured term loan product", "ACTIVE", "MANUAL",
        "MANUAL");


----------------------------------- TABLE_NAME :  product_template_parameter -----------------------------------
----------------------------------- Queries :

INSERT INTO `product_template_parameter` (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                          data_type, override_level, description, created_by, updated_by)
VALUES (uuid(), 3, "finance", "general_ledger_mapping_for_normal_account", "902311-002", "INT", "AT_PRODUCT_VARIANT",
        "The general ledger at ERP normal accounts of this product variant will be reported under", "MANUAL", "MANUAL"),
       (uuid(), 4, "finance", "general_ledger_mapping_for_normal_account", "106112-001", "INT", "AT_PRODUCT_VARIANT",
        "The general ledger at ERP normal accounts of this product variant will be reported under", "MANUAL", "MANUAL"),
       (uuid(), 4, "finance", "general_ledger_mapping_for_interest_accrual", "110112-001", "INT", "AT_PRODUCT_VARIANT",
        "The general ledger at ERP normal accounts of this product variant will be reported under", "MANUAL", "MANUAL"),
       (uuid(), 4, "finance", "general_ledger_mapping_for_penal_interest", "109112", "INT", "AT_PRODUCT_VARIANT",
        "The general ledger at ERP normal accounts of this product variant will be reported under", "MANUAL", "MANUAL"),

       (uuid(), 3, "business", "account_number_structure", "UU-PP-RRRRRR-C", "STRING", "NO_OVERRIDE",
        "Is the account number format for this product variant", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "account_number_structure", "UU-PP-RRRRRRRRR-C", "STRING", "NO_OVERRIDE",
        "Is the account number format for this product variant", "MANUAL", "MANUAL"),

       (uuid(), 3, "business", "account_number_product_code", "01", "string", "NO_OVERRIDE",
        "Product code used for account number generation", "MANUAL", "MANUAL"),
       (uuid(), 3, "business", "account_number_unit_code", "80", "string", "NO_OVERRIDE",
        "Unit code used for account number generation", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "account_number_product_code", "10", "string", "NO_OVERRIDE",
        "Product code used for TermLoan account number generation", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "account_number_unit_code", "80", "string", "NO_OVERRIDE",
        "Unit code used for TermLoan account number generation", "MANUAL", "MANUAL"),

       (uuid(), 3, "business", "allowed_home_currency", "MYR", "CURRENCY", "NO_OVERRIDE",
        "Allowed currencies for this product variant", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "allowed_home_currency", "MYR", "CURRENCY", "NO_OVERRIDE",
        "Allowed currencies for this product variant", "MANUAL", "MANUAL"),

       (uuid(), 3, "business", "allowed_additional_currencies", "", "CURRENCY", "NO_OVERRIDE",
        "Allowed additional currencies for this product variant", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "allowed_additional_currencies", "", "CURRENCY", "NO_OVERRIDE",
        "Allowed additional currencies for this product variant", "MANUAL", "MANUAL"),

       (uuid(), 3, "business", "max_active_loc_accounts_per_customer", "1", "INT", "NO_OVERRIDE",
        "Limit to how many accounts of this product variant a customer is allowed to have", "MANUAL", "MANUAL"),
       (uuid(), 3, "business", "max_active_loan_accounts_per_loc_account", "10", "INT", "NO_OVERRIDE",
        "Limit to how many active loan accounts of this product variant a customer is allowed to have", "MANUAL",
        "MANUAL"),
       (uuid(), 3, "business", "max_account_holders", "1", "INT", "NO_OVERRIDE",
        "Limit to how many account holders are allowed for this product variant", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "max_account_holders", "1", "INT", "NO_OVERRIDE",
        "Limit to how many account holders are allowed for this product variant", "MANUAL", "MANUAL"),

       (uuid(), 3, "business", "allowed_signing_condition", "SINGLE", "ENUM", "NO_OVERRIDE",
        "This dictates the operating structure of the account i.e. Single, Joint etc. If it is not Single, the max_account_holders must be > 1",
        "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "allowed_signing_condition", "SINGLE", "ENUM", "NO_OVERRIDE",
        "This dictates the operating structure of the account i.e. Single, Joint etc.", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "min_days_between_drawdown_and_first_repayment", "28", "INT", "NO_OVERRIDE",
        "Is the minimum number of days between drawdown & first repayment", "MANUAL", "MANUAL"),
       (uuid(), 3, "business", "free_look_period_in_days", "3", "INT", "NO_OVERRIDE",
        "Are the number of days from drawdown during which prepayment results in zero interest to the customer",
        "MANUAL", "MANUAL"),
       (uuid(), 3, "business", "max_free_look_facility_usage_lifetime_limit", "2", "INT", "NO_OVERRIDE",
        "Maximum free look facility usage lifetime limit", "MANUAL",
        "MANUAL"),
       (uuid(), 3, "business", "repayment_order", "INSTALLMENT_FIRST_THEN_LOAN", "ENUM", "NO_OVERRIDE", "Can be:
- INSTALLMENT_FIRST_THEN_LOAN : L1.I1 > L2.I1 > L3.I1 > L1.I2 > L2.I2 > L3.I2
- LOAN_FIRST_THEN_INSTALLMENT : L1.I1 > L1. I2 > L2.I1 > L2.I2 > L3.I1 > L3.I2", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "default_repayment_hierarchy_within_installment",
        "PENAL_INTEREST, INTEREST, FEES, PRINCIPAL", "STRING", "NO_OVERRIDE",
        "Within an installment, this specifies the liquidation order across components of the installment for normal loans",
        "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "delinquent_repayment_hierarchy_within_installment",
        "OVERDUE PRINCIPAL, OVERDUE INTEREST, OVERDUE FEES, OVERDUE PENAL INTEREST", "STRING", "NO_OVERRIDE",
        "Within an installment, this specifies the liquidation order across components of the installment for delinquent loans",
        "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "min_drawdown_amount", "200", "DOUBLE", "NO_OVERRIDE",
        "Is the the minimum loan drawdown amount", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "overpayment_allowed", "Y", "BOOLEAN", "NO_OVERRIDE",
        "Signifies if overpayment (excess payment) is allowed", "MANUAL", "MANUAL"),
       (uuid(), 4, "business", "prepayment_allowed", "Y", "BOOLEAN", "NO_OVERRIDE",
        "Signifies if prepayment is allowed", "MANUAL", "MANUAL"),
       (uuid(), 3, "business", "statement_cycle_frequency", "MONTHLY", "ENUM", "AT_ACCOUNT",
        "This denotes the frequency of account statement generation", "MANUAL", "MANUAL"),
       (uuid(), 3, "credit risk", "min_customer_age_for_activation", "21", "INT", "NO_OVERRIDE",
        "Is the min age of the customer", "MANUAL", "MANUAL"),
       (uuid(), 3, "credit risk", "max_customer_age_for_activation", "60", "INT", "NO_OVERRIDE",
        "Is the max age of the customer", "MANUAL", "MANUAL"),
       (uuid(), 4, "credit risk", "min_customer_age_for_withdrawal", "21", "INT", "NO_OVERRIDE",
        "Is the min age of the customer", "MANUAL", "MANUAL"),
       (uuid(), 4, "credit risk", "max_customer_age_for_withdrawal", "65", "INT", "NO_OVERRIDE",
        "Is the max age of the customer", "MANUAL", "MANUAL"),
       (uuid(), 3, "credit risk", "min_credit_limit_amount", "1000", "DOUBLE", "AT_ACCOUNT",
        "Is the maximum balance limit beyond which only system-initiated credits are allowed and all other credits are rejected. Cannot be lesser than the value in min_drawdown_amount",
        "MANUAL", "MANUAL"),
       (uuid(), 3, "credit risk", "max_credit_limit_amount", "100000", "DOUBLE", "AT_ACCOUNT",
        "Is the maximum balance limit beyond which any credits are rejected", "MANUAL", "MANUAL"),
       (uuid(), 3, "credit risk", "min_loan_tenor_in_months", "2", "INT", "AT_ACCOUNT",
        "Is the minimum tenor of the loan in months", "MANUAL", "MANUAL"),
       (uuid(), 4, "credit risk", "min_loan_tenor_in_months", "2", "INT", "AT_ACCOUNT",
        "Is the minimum tenor of the loan in months", "MANUAL", "MANUAL"),
       (uuid(), 3, "credit risk", "max_loan_tenor_in_months", "60", "INT", "AT_ACCOUNT",
        "Is the maximum tenor of the loan in months", "MANUAL", "MANUAL"),
       (uuid(), 4, "credit risk", "max_loan_tenor_in_months", "60", "INT", "AT_ACCOUNT",
        "Is the maximum tenor of the loan in months", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "normal_interest_calc_type", "DEFAULT", "ENUM", "NO_OVERRIDE",
        "Default interest calculation method (Annual Percentage Rate (APR) * Principal Outstanding * Number of calendar days) / Days in a year",
        "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "normal_interest_days_in_a_year", "365", "ENUM", "NO_OVERRIDE",
        "Can be 360, 365 or ACTUAL", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "normal_interest_days_in_a_month", "ACTUAL", "ENUM", "NO_OVERRIDE", "Can be 30 or ACTUAL",
        "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "repayment_grace_period_in_days", "3", "INT", "NO_OVERRIDE",
        "Is the number of days from the due date during which loan is not considered overdue", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "penal_interest_calc_type", "DEFAULT", "ENUM", "NO_OVERRIDE",
        "Default interest calculation method (Penal Interest Rate * Overdue Principal * Actual number of calendar days overdue) / Days in a year",
        "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "penal_interest_days_in_a_year", "365", "ENUM", "NO_OVERRIDE",
        "Can be 360, 365 or ACTUAL", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "penal_interest_days_in_a_month", "ACTUAL", "ENUM", "NO_OVERRIDE", "Can be 30 or ACTUAL",
        "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "interest_accrual_frequency", "DAYS", "ENUM", "NO_OVERRIDE",
        "Denotes the frequency of accrual in DAYS, WEEKS, MONTHS", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "interest_accrual_frequency_units", "1", "INT", "NO_OVERRIDE",
        "Denotes the number of accrual frequency units for accrual schedule", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "interest_accrual_roundoff_precision_digits", "8", "INT", "NO_OVERRIDE",
        "Is the precision beyond which interest accrued is truncated", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "interest_display_roundoff_precision_digits", "2", "INT", "NO_OVERRIDE",
        "Is the precision beyond which interest displayed is truncated", "MANUAL", "MANUAL"),
       (uuid(), 4, "pricing", "interest_accrual_rounding_type", "HALF_ROUND_UP", "ENUM", "NO_OVERRIDE",
        "Specifies the rounding type for interest accrual", "MANUAL", "MANUAL"),
       (uuid(), 3, "risk", "allowed_hold_codes", "BLOCK_DRAWDOWN,BLOCK_REPAYMENT", "ENUM", "NO_OVERRIDE",
        "Is the list of allowed hold codes for this product", "MANUAL", "MANUAL"),
       (uuid(), 3, "business", "limit_reduction_denomination", "100", "INT", "NO_OVERRIDE",
        "specifices the denomination value for the limit reduction", "MANUAL", "MANUAL"),
       (uuid(), 3, "business", "min_available_credit_limit_for_drawdown", "200", "INT", "NO_OVERRIDE",
        "from functionality point, this value will be same min_drawdown_amount", "MANUAL",
        "MANUAL");

----------------------------------- TABLE_NAME :  product -----------------------------------
----------------------------------- Queries :

INSERT INTO `product` (id, public_id, product_template_id, code, numeric_code, name, description, status, created_by, updated_by)
values (4, uuid(), 3, "FLEXI_LOAN_LINE_OF_CREDIT", "02", "Flexi Loan Line of Credit", "Flexi Loan Line of Credit", "ACTIVE",
        "MANUAL", "MANUAL"),
       (5, uuid(), 4, "FLEXI_LOAN_TERM_LOAN", "03", "Flexi Loan Term Loan", "Flexi Loan Term Loan", "ACTIVE", "MANUAL",
        "MANUAL");


----------------------------------- TABLE_NAME :  product_variant -----------------------------------
----------------------------------- Queries :

INSERT INTO `product_variant` (id, public_id, product_id, code, version, name, description, status, valid_from, valid_to,
                               created_by, updated_by)
VALUES (5, uuid(), 4, 'DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT', '1', 'Default Flexi Loan Line of Credit',
        'Default Flexi Loan Line of Credit - v1.0 - product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        'MANUAL', 'MANUAL'),
       (6, uuid(), 5, 'DEFAULT_FLEXI_LOAN_TERM_LOAN', '1', 'Default Flexi Loan Term Loan',
        'Default Flexi Loan Term Loan  - v1.0 - product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MANUAL',
        'MANUAL');


----------------------------------- TABLE_NAME :  product_variant_question -----------------------------------
----------------------------------- Queries :

INSERT INTO product_variant_question (public_id, product_variant_id, question_text, locale, status, created_by,
                                      updated_by)
VALUES (uuid(), 6, 'What are you borrowing for?', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 5, 'What''s the reason for declining?', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 5, 'We''re sorry to see you go . What''s the reason ?', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME :  lending_product_answer_suggestion -----------------------------------
----------------------------------- Queries :

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale,
                                               status, created_by, updated_by)
VALUES (uuid(), 1, 'Travel', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 'Renovation', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 'Emergency', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 2, 'High interest rate', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 2, 'Low credit limit', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 2, 'I''ve got better offer', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 2, 'Changed my mind', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 2, 'Unsure about the product', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 3, 'High interest rate', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 3, 'Low credit limit', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 3, 'Better offer elsewhere', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 3, 'Not needed', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 3, 'Bad user experience', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL');


----------------------------------- TABLE_NAME :  loan_allowed_amount_tenor_slab -----------------------------------
----------------------------------- Queries :

INSERT INTO loan_allowed_amount_tenor_slab (product_variant_id, currency, from_amount, to_amount, tenor_unit, min_tenor,
                                            max_tenor, created_by, updated_by)
VALUES (6, 'SGD', '200.00', '500.00', 'MONTH', '2', '6', 'MANUAL', 'MANUAL'),
       (6, 'SGD', '500.01', '1000.00', 'MONTH', '2', '12', 'MANUAL', 'MANUAL'),
       (6, 'SGD', '1000.01', '2000.00', 'MONTH', '2', '24', 'MANUAL', 'MANUAL'),
       (6, 'SGD', '2000.01', '5000.00', 'MONTH', '2', '36', 'MANUAL', 'MANUAL'),
       (6, 'SGD', '5000.01', '************', 'MONTH', '2', '60', 'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME : loan_interest -----------------------------------
-- Queries :

INSERT INTO loan_interest (id, public_id, product_variant_id, is_linked_to_base_rate, base_interest_id, code, name,
                           description, currency, round_off_type, interest_type, interest_slab_unit_type,
                           interest_slab_structure, created_by, updated_by)
VALUES (1, uuid(), 6, 0, NULL, 'DEFAULT_FLEXI_LOAN_TERM_LOAN_INTEREST_NORMAL',
        'Default Flexi Loan - Term Loan Interest - Normal', 'Default Flexi Loan - Term Loan Interest - Normal',
        'SGD', 'HALF_ROUND_UP', 'NORMAL', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL'),
       (2, uuid(), 6, 0, NULL, 'DEFAULT_FLEXI_LOAN_TERM_LOAN_INTEREST_PENAL',
        'Default Flexi Loan - Term Loan Interest - Penal', 'Default Flexi Loan - Term Loan Interest - Penal',
        'SGD', 'HALF_ROUND_UP', 'PENAL', 'DAY', 'INCREMENTAL', 'MANUAL', 'MANUAL'),
       (3, uuid(), 6, 0, NULL, 'DEFAULT_FLEXI_LOAN_TERM_LOAN_INTEREST_DISCOVERY',
        'Default Flexi Loan - Term Loan Interest - Discovery', 'Default Flexi Loan - Term Loan Interest - Discovery',
        'SGD', 'HALF_ROUND_UP', 'DISCOVERY', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL');


----------------------------------- TABLE_NAME : loan_interest_version -----------------------------------
----------------------------------- Queries :

INSERT INTO loan_interest_version (id, public_id, loan_interest_id, version, effective_date, description, created_by,
                                   updated_by)
VALUES (1, uuid(), 1, '1', CURRENT_TIMESTAMP, 'Default Flexi Loan - Line of Credit Interest v1.0 - Normal', 'MANUAL',
        'MANUAL'),
       (2, uuid(), 2, '1', CURRENT_TIMESTAMP, 'Default Flexi Loan - Line of Credit Interest v1.0 - Penal', 'MANUAL',
        'MANUAL'),
       (3, uuid(), 3, '1', CURRENT_TIMESTAMP, 'Default Flexi Loan - Line of Credit Interest v1.0 - Discovery', 'MANUAL',
        'MANUAL');


----------------------------------- TABLE_NAME : loan_interest_slab_rate -----------------------------------
----------------------------------- Queries :

INSERT INTO loan_interest_slab_rate (id, public_id, loan_interest_version_id, slab_type, from_unit, to_unit,
                                     base_rate_interest_spread_percentage,
                                     absolute_interest_rate_percentage, created_by, updated_by)
VALUES (1, uuid(), 1, 'AMOUNT', '0', '1000000000', '0', '3', 'MANUAL', 'MANUAL'),
       (2, uuid(), 2, 'DAY', '1', '9999', '0', '0.1', 'MANUAL', 'MANUAL'),
       (4, uuid(), 3, 'AMOUNT', '0', '1000000000', '0', '3', 'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME : loan_past_due_version -----------------------------------
----------------------------------- Queries :

INSERT INTO loan_past_due_version (public_id, product_id, version, effective_date, description, created_by, updated_by)
VALUES (uuid(), 5, '1', CURRENT_TIMESTAMP, 'Default Flexi Loan - Term Loan Past Due v1.0', 'MANUAL', 'MANUAL');


----------------------------------- TABLE_NAME : loan_past_due_slab -----------------------------------
----------------------------------- Queries :

INSERT INTO loan_past_due_slab (public_id, loan_past_due_version_id, from_unit, to_unit, slab_type,
                                bucket_name, created_by, updated_by)
VALUES (uuid(), 1, 1, 29, 'DAY', 'Bucket 1', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 30, 59, 'DAY', 'Bucket 2', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 60, 89, 'DAY', 'Bucket 3', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 90, 119, 'DAY', 'Bucket 4', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 120, 149, 'DAY', 'Bucket 5', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 150, 179, 'DAY', 'Bucket 6', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 180, 2555, 'DAY', 'Bucket 7', 'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME : transaction_catalogue -----------------------------------
----------------------------------- Queries :

INSERT INTO transaction_catalogue (id, public_id, `domain`, is_financial_txn, txn_type, txn_sub_type, display_name,
                                   description, status, created_by, updated_by)
VALUES (13, uuid(), 'lending', 1, 'line_of_credit', 'account_opening',
        'Line Of Credit Account Opening', 'LOC account opening transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (14, uuid(), 'lending', 1, 'line_of_credit', 'increase_limit',
        'Line of Credit Increase Limit', 'LOC increase limit transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (15, uuid(), 'lending', 1, 'line_of_credit', 'decrease_limit',
        'Line of Credit Decrease Limit', 'LOC decrease limit transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (16, uuid(), 'lending', 1, 'line_of_credit', 'account_closure',
        'Line of Credit Account Closure', 'LOC account closure transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (17, uuid(), 'lending', 1, 'line_of_credit', 'utilise_limit',
        'Line of Credit Utilise Limit', 'LOC utilise limit transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (18, uuid(), 'lending', 1, 'drawdown', 'intrabank',
        'Term Loan Intrabank Drawdown', 'TL intrabank drawdown transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (19, uuid(), 'lending', 1, 'drawdown', 'fast_network',
        'Term Loan FAST Drawdown', 'TL FAST drawdown transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (20, uuid(), 'lending', 1, 'term_loan', 'account_opening',
        'Term Loan Account Opening', 'TL account opening transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (21, uuid(), 'lending', 1, 'line_of_credit', 'utilise_limit_reversal',
        'Line of Credit Utilise Limit Reversal', 'LOC utilise limit reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (22, uuid(), 'lending', 1, 'drawdown_reversal', 'intrabank',
        'Term Loan Intrabank Drawdown Reversal', 'TL intrabank drawdown reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (23, uuid(), 'lending', 1, 'drawdown_reversal', 'fast_network',
        'Term Loan FAST Drawdown Reversal', 'TL FAST drawdown reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (24, uuid(), 'lending', 1, 'term_loan', 'account_opening_reversal',
        'Term Loan Account Opening Reversal', 'TL account opening reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (25, uuid(), 'lending', 1, 'interest_accrual', 'normal_interest',
        'Term Loan Normal Interest Accrual', 'TL normal interest accrual transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (26, uuid(), 'lending', 1, 'interest_application', 'penal_interest',
        'Term Loan Penal Interest Application', 'TL penal interest application transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (27, uuid(), 'lending', 1, 'interest_accrual_reversal', 'normal_interest',
        'Term Loan Normal Interest Accrual Reversal', 'TL loan normal interest accrual reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (28, uuid(), 'lending', 1, 'interest_application_reversal', 'penal_interest',
        'Term Loan Penal Interest Application Reversal', 'TL penal interest application reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (29, uuid(), 'lending', 1, 'billing', 'on_due_date',
        'Term Loan On Due Date Billing', 'TL on due date billing transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (30, uuid(), 'lending', 1, 'billing', 'after_due_date',
        'Term Loan After Due Date Billing', 'TL on after due date billing transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (31, uuid(), 'lending', 1, 'billing_reversal', 'on_due_date',
        'Term Loan On Due Date Billing Reversal', 'TL on due date billing reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (32, uuid(), 'lending', 1, 'billing_reversal', 'after_due_date',
        'Term Loan After Due Date Billing Reversal', 'TL after due date billing reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (33, uuid(), 'lending', 1, 'repayment', 'intrabank',
        'Term Loan Intrabank Repayment ', 'TL intrabank repayment transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (34, uuid(), 'lending', 1, 'repayment', 'fast_network',
        'Term Loan FAST Repayment', 'TL fast repayment transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (35, uuid(), 'lending', 1, 'repayment_reversal', 'intrabank',
        'Term Loan Intrabank Repayment Reversal', 'TL intrabank repayment reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (36, uuid(), 'lending', 1, 'repayment_reversal', 'fast_network',
        'Term Loan FAST Repayment Reversal', 'TL FAST repayment reversal transaction', 'ACTIVE',
        'MANUAL', 'MANUAL');

-- TABLE_NAME : product_variant_transaction_catalogue_mapping --
-- Queries :
INSERT INTO product_variant_transaction_catalogue_mapping (id, public_id, product_variant_id, transaction_catalogue_id,
                                                           status, created_by, updated_by)
VALUES (11, uuid(), 5, 13, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (12, uuid(), 5, 14, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (13, uuid(), 5, 15, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (14, uuid(), 5, 16, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (15, uuid(), 5, 17, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (16, uuid(), 5, 21, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (17, uuid(), 5, 33, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (18, uuid(), 5, 34, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (19, uuid(), 5, 35, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (20, uuid(), 5, 36, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (21, uuid(), 6, 18, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (22, uuid(), 6, 19, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (23, uuid(), 6, 20, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (24, uuid(), 6, 22, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (25, uuid(), 6, 23, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (26, uuid(), 6, 24, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (27, uuid(), 6, 25, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (28, uuid(), 6, 26, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (29, uuid(), 6, 27, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (30, uuid(), 6, 28, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (31, uuid(), 6, 29, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (32, uuid(), 6, 30, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (33, uuid(), 6, 31, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (34, uuid(), 6, 32, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (35, uuid(), 6, 33, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (36, uuid(), 6, 34, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (37, uuid(), 6, 35, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (38, uuid(), 6, 36, 'ACTIVE', 'MANUAL', 'MANUAL');


----------------------------------- TABLE_NAME : general_ledger -----------------------------------
----------------------------------- Queries :

INSERT INTO general_ledger (id, public_id, code, name, description, currency, status, created_by, updated_by)
VALUES (52, uuid(), '902111', 'Loan undrawn commitment', 'Loan undrawn commitment', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (53, uuid(), '902311', 'Loan undrawn commitment - contra', 'Loan undrawn commitment - contra', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (54, uuid(), '401113', 'Interest Income (Normal/Penal)', 'Interest Income (Normal/Penal)', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (39, uuid(), '209412', 'Clearing - FAST', 'Clearing - FAST', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (40, uuid(), '209413', 'Clearing - GIRO', 'Clearing - GIRO', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (55, uuid(), '601911', 'Bad Debts Written Off', 'Bad Debts Written Off', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (56, uuid(), '501723', 'Generic Operational Losses', 'Generic Operational Losses', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (57, uuid(), '109114', 'Interest in suspense', 'Interest in suspense', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (47, uuid(), '104112', 'SGD Oracle Cash Mgt Bank', 'SGD Oracle Cash Mgt Bank', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (58, uuid(), '403415', 'Bad debt recovered', 'Bad debt recovered', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (59, uuid(), '105112', 'Term Loan Disbursement Contra', 'Term Loan Disbursement Contra', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL'),
       (60, uuid(), '203114', 'Excess Rapid Liability', 'Excess Rapid Liability', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME : internal_account -----------------------------------
----------------------------------- Queries :

INSERT INTO internal_account (id, public_id, general_ledger_id, code, name, description, currency, status, created_by,
                              updated_by)
VALUES (51, uuid(), 52, '*********', 'Loan undrawn commitment', 'Loan undrawn commitment', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (52, uuid(), 53, '*********', 'Loan undrawn commitment - contra', 'Loan undrawn commitment - contra', 'SGD',
        'ACTIVE', 'MANUAL', 'MANUAL'),
       (53, uuid(), 54, '*********', 'Interest Income (Normal)', 'Interest Income (Normal)', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (54, uuid(), 54, '*********', 'Interest Income (Penal)', 'Interest Income (Penal)', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (37, uuid(), 39, '*********', 'Clearing - FAST', 'Clearing - FAST', 'SGD', 'ACTIVE'	, 'MANUAL',
        'MANUAL'),
       (38, uuid(), 40, '*********', 'Clearing - GIRO', 'Clearing - GIRO', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (55, uuid(), 55, '*********', 'Bad Debts Written Off', 'Bad Debts Written Off', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (56, uuid(), 56, '*********', 'Generic Operational Losses', 'Generic Operational Losses', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (57, uuid(), 57, '*********', 'Interest in suspense', 'Interest in suspense', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (45, uuid(), 47, '*********', 'SGD Oracle Cash Mgt Bank', 'SGD Oracle Cash Mgt Bank', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (58, uuid(), 58, '*********', 'Bad debt recovered', 'Bad debt recovered', 'SGD', 'ACTIVE', 'MANUAL',
        'MANUAL'),
       (59, uuid(), 59, '*********', 'Term Loan Disbursement Contra', 'Term Loan Disbursement Contra', 'SGD', 'ACTIVE',
        'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME : product_variant_transaction_catalogue_internal_account_mapping -----------------------------------
----------------------------------- Queries :

INSERT INTO product_variant_transaction_catalogue_internal_account_mapping (public_id, id,
                                                                            product_variant_transaction_catalogue_mapping_id,
                                                                            internal_account_id, identifier_key, status,
                                                                            created_by, updated_by)
VALUES (uuid(), 7, 11, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 8, 11, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 9, 12, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 10, 12, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 11, 13, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 12, 13, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 13, 14, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 14, 14, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 15, 15, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 16, 15, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 17, 16, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 18, 16, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 19, 17, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 20, 17, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 21, 17, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 22, 18, 37, 'fast_network_clearing_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 23, 18, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 24, 18, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 25, 18, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 26, 19, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 27, 19, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 28, 19, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 29, 20, 37, 'fast_network_clearing_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 30, 20, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 31, 20, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 32, 20, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 33, 21, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 34, 22, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 35, 22, 37, 'fast_network_clearing_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 36, 23, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 37, 24, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 38, 25, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 39, 25, 37, 'fast_network_clearing_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 40, 26, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 41, 27, 53, 'normal_interest_income_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 42, 28, 54, 'penal_interest_income_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 43, 29, 53, 'normal_interest_income_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 44, 30, 54, 'penal_interest_income_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 45, 31, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 46, 31, 53, 'normal_interest_income_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 47, 33, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 48, 33, 53, 'normal_interest_income_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 49, 34, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 50, 35, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 51, 35, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 52, 35, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 53, 36, 37, 'fast_network_clearing_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 54, 36, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 55, 36, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 56, 36, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 57, 37, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 58, 37, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 59, 37, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 60, 38, 37, 'fast_network_clearing_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 61, 38, 51, 'loan_undrawn_commitment_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 62, 38, 52, 'loan_undrawn_commitment_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 63, 38, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 64, 32, 59, 'term_loan_disbursement_contra_internal_account', 'ACTIVE', 'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME : product_variant_account_general_ledger_mapping -----------------------------------
----------------------------------- Queries :

INSERT INTO product_variant_account_general_ledger_mapping (public_id, product_variant_id, account_key, account_address,
                                                            general_ledger_id, status, created_by, updated_by)
VALUES (uuid(), 5, 'Customer''s Line of Credit (LOC) Account', 'DEFAULT', 53, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 5, 'Customer''s Line of Credit (LOC) Account', 'REPAYMENT_MADE', 60, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 5, 'Customer''s Line of Credit (LOC) Account', 'EXCESS_AMOUNT', 60, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'PRINCIPAL', 59, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'DUE_PRINCIPAL', 59, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'OVERDUE_PRINCIPAL', 59, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'WRITTEN_OFF_PRINCIPAL', 55, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'ACCRUED_NORMAL_INTEREST', 10, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'DUE_NORMAL_INTEREST', 10, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'OVERDUE_NORMAL_INTEREST', 10, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'WRITTEN_OFF_NORMAL_INTEREST', 55, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'PENAL_INTEREST', 10, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 6, 'Customer''s Term Loan Account', 'WRITTEN_OFF_PENAL_INTEREST', 55, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 1, 'Customer''s CASA Account ', 'DEFAULT', 1, 'ACTIVE', 'MANUAL', 'MANUAL'),
       (uuid(), 5, 'Customer''s Line of Credit (LOC) Account', 'DISBURSEMENT_CONTRA', 59, 'ACTIVE', 'MANUAL', 'MANUAL');

----------------------------------- TABLE_NAME :  product_instruction -----------------------------------
----------------------------------- Queries :

INSERT INTO product_master.loan_instruction (public_id, loan_instruction_version_id, code, name, created_at, created_by,
                                             updated_at, updated_by)
VALUES(uuid(), 1, 'DECEASED', 'Deceased', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'PHYSICALLY_INCAPACITATED', 'Physically Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'MENTALLY_INCAPACITATED', 'Mentally Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'BANKRUPTCY', 'Bankruptcy', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'AML_IMMEDIATE_CLOSURE', 'AML Immediate Closure', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'AML_COLLECTION_POSSIBLE', 'AML Collection Possible', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'FRAUD_IDENTITY_THEFT', 'Fraud Identity Theft', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'FRAUD_VICTIM', 'Fraud Victim', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'FRAUD_VICTIM_LIABLE', 'Fraud VictIm Liable', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'FRAUD_BAD_ACTOR', 'Fraud Bad Actor', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 1, 'FRAUD_OTHER', 'Fraud Other', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'DECEASED', 'Deceased', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'PHYSICALLY_INCAPACITATED', 'Physically Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'MENTALLY_INCAPACITATED', 'Mentally Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'BANKRUPTCY', 'Bankruptcy', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'AML_IMMEDIATE_CLOSURE', 'AML Immediate Closure', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'AML_COLLECTION_POSSIBLE', 'AML Collection Possible', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'FRAUD_IDENTITY_THEFT', 'Fraud Identity Theft', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'FRAUD_VICTIM', 'Fraud Victim', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'FRAUD_VICTIM_LIABLE', 'Fraud VictIm Liable', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'FRAUD_BAD_ACTOR', 'Fraud Bad Actor', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 2, 'FRAUD_OTHER', 'Fraud Other', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'DECEASED', 'Deceased', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'PHYSICALLY_INCAPACITATED', 'Physically Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'MENTALLY_INCAPACITATED', 'Mentally Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'BANKRUPTCY', 'Bankruptcy', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'AML_IMMEDIATE_CLOSURE', 'AML Immediate Closure', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'AML_COLLECTION_POSSIBLE', 'AML Collection Possible', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'FRAUD_IDENTITY_THEFT', 'Fraud Identity Theft', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'FRAUD_VICTIM', 'Fraud Victim', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'FRAUD_VICTIM_LIABLE', 'Fraud VictIm Liable', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'FRAUD_BAD_ACTOR', 'Fraud Bad Actor', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 3, 'FRAUD_OTHER', 'Fraud Other', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'DECEASED', 'Deceased', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'PHYSICALLY_INCAPACITATED', 'Physically Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'MENTALLY_INCAPACITATED', 'Mentally Incapacitated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'BANKRUPTCY', 'Bankruptcy', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'AML_IMMEDIATE_CLOSURE', 'AML Immediate Closure', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'AML_COLLECTION_POSSIBLE', 'AML Collection Possible', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'FRAUD_IDENTITY_THEFT', 'Fraud Identity Theft', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'FRAUD_VICTIM', 'Fraud Victim', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'FRAUD_VICTIM_LIABLE', 'Fraud VictIm Liable', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'FRAUD_BAD_ACTOR', 'Fraud Bad Actor', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 4, 'FRAUD_OTHER', 'Fraud Other', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL');
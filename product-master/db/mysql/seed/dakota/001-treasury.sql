-- Product Master DB Insert Queries For Treasury

-- TABLE_NAME :  product_template
-- Queries :
INSERT INTO product_template
(public_id, code, name, description, status, created_by, updated_by)
VALUES(uuid(), 'TREASURY', 'Treasury', 'The Treasury master product template', 'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product_template_parameter
-- Queries :
INSERT INTO product_template_parameter
(public_id, product_template_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
VALUES(uuid(), 2, 'finance', 'allowed_currency', 'SGD', 'CURRENCY', 'NO_OVERRIDE', NULL, 'Allowed currencies for this product variant', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product
-- Queries :
INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
VALUES(uuid(), 2, 'TREASURY_BOND', 'Treasury Bonds', 'The product for Treasury Bonds security type', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), 2, 'TREASURY_TBILL', 'Treasury Bills', 'The product for Treasury Bills security type', 'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product_variant
-- Queries :
INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to, created_by, updated_by)
VALUES(uuid(), 2, 'DEFAULT_TREASURY_BOND', '1', 'Default Treasury Bonds', 'The default product variant for Treasury Bonds', 'ACTIVE', CURRENT_TIMESTAMP, '2122-02-09 09:44:28', 'MANUAL', 'MANUAL'),
      (uuid(), 3, 'DEFAULT_TREASURY_TBILL', '1', 'Default Treasury Bills', 'The default product variant for Treasury Bills', 'ACTIVE', CURRENT_TIMESTAMP, '2122-02-09 09:44:28', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  general_ledger
-- Queries :
INSERT INTO general_ledger (public_id, code, name, description, currency, status, created_by, updated_by)
VALUES(uuid(), '209518','Securities Settlement Payables','Securities Settlement Payables','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '109518','Securities Settlement Receivables','Securities Settlement Receivables','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '101111','Non-restricted Balances With Central Bank / Nostro / Clearing','Non-restricted Balances With Balances With Central Bank / Nostro / Clearing','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '109112','Accrued Interest Receivables','Accrued Interest Receivables','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102211','SG Govt Bond At FVOCI (par)','SG Govt Bond At FVOCI (par)','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102213','Premium SG Govt Bond At FVOCI','Premium SG Govt Bond At FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102214','Discount SG Govt Bond At FVOCI','Discount SG Govt Bond At FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102111','SG Govt Bond At Amortized Cost (gross)','SG Govt Bond At Amortized Cost (gross)','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102112','Premium SG Govt Bond At Amortised Cost','Premium SG Govt Bond At Amortised Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102113','Discount SG Govt Bond At Amortised Cost','Discount SG Govt Bond At Amortised Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '401131','Int Inc SG Govt Bond','Int Inc SG Govt Bond','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102411','SG Treasury Bills At FVOCI (par)','SG Treasury Bills At FVOCI (par)','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102414','Discount SG Treasury Bills At FVOCI','Discount SG Treasury Bills At FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102311','SG Treasury Bills At Amortized Cost','SG Treasury Bills At Amortized Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102313','Discount SG Treasury Bills At Amortized Cost','Discount SG Treasury Bills At Amortized Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '401134','Int Inc SG Treasury Bills Prem/Disc','Int Inc SG Treasury Bills Prem/Disc','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102212','SG Govt Bond at FVOCI - Fair value changes','SG Govt Bond at FVOCI - Fair value changes','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '303111','SG Govt Bond at FVOCI - Fair Value Changes','SG Govt Bond at FVOCI - Fair Value Changes','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '401132','Int inc SG Govt Bond prem/disc','Int inc SG Govt Bond prem/disc','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404101','Realised FV gains for SG government bonds at FVOCI','Realised FV gains for SG government bonds at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404102','Realised FV losses for SG government bonds at FVOCI','Realised FV losses for SG government bonds at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404105','Realised FV gains for SG government bonds at amortised cost','Realised FV gains for SG government bonds at amortised cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404106','Realised FV losses for SG government bonds at amortised cost','Realised FV losses for SG government bonds at amortised cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '102412','SG Treasury Bills at FVOCI - Fair value changes','SG Treasury Bills at FVOCI - Fair value changes','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '303112','SG Treasury Bills at FVOCI','SG Treasury Bills at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404103','Realised FV gains for SG Treasury bills at FVOCI','Realised FV gains for SG Treasury bills at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404104','Realised FV losses for SG Treasury bills at FVOCI','Realised FV losses for SG Treasury bills at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404107','Realised FV gains for SG Treasury bills at amortised cost','Realised FV gains for SG Treasury bills at amortised cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), '404108','Realised FV losses for SG Treasury bills at amortised cost','Realised FV losses for SG Treasury bills at amortised cost','SGD','ACTIVE','MANUAL','MANUAL');
      (uuid(), '501696', 'Bank Charges', 'Bank Charges', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '501722', 'CASA Operational losses', 'CASA Operational losses', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209414', 'Clearing - Credit card', 'Clearing - Credit card', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209412', 'Clearing - FAST', 'Clearing - FAST', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209413', 'Clearing - GIRO', 'Clearing - GIRO', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209411', 'Clearing - MEPS', 'Clearing - MEPS', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209422', 'Grab Ecosystem (Funds In) - Clearing', 'Grab Ecosystem (Funds In) - Clearing', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209421', 'Grab Ecosystem (Pay Out) - Clearing', 'Grab Ecosystem (Pay Out) - Clearing', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '401211', 'Int exp current accounts', 'Int exp current accounts', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '401213', 'Int exp term deposit', 'Int exp term deposit', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '403114', 'Service charge for cash transfer', 'Service charge for cash transfer', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '104112', 'SGD Oracle Cash Mgt Bank', 'SGD Oracle Cash Mgt Bank', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209424', 'Singtel Ecosystem (Funds In) - Clearing', 'Singtel Ecosystem (Funds In) - Clearing', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '209423', 'Singtel Ecosystem (Pay Out) - Clearing', 'Singtel Ecosystem (Pay Out) - Clearing', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
      (uuid(), '101113', 'Statutory Deposits with central Bank', 'Statutory Deposits with central Bank', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  internal_account
-- Queries :
INSERT INTO internal_account
(public_id, general_ledger_id, code, name, description, currency, status, created_by, updated_by)
VALUES(uuid(), 7,'*********','Securities Settlement Payables','Securities Settlement Payables','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 8,'*********','Securities Settlement Receivables','Securities Settlement Receivables','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 9,'*********','Non-restricted Balances With Balances With Central Bank / Nostro / Clearing','Non-restricted Balances With Balances With Central Bank / Nostro / Clearing','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 10,'*********','Accrued Interest Receivables','Accrued Interest Receivables','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 11,'*********','SG Govt Bond At FVOCI (par)','SG Govt Bond At FVOCI (par)','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 12,'*********','Premium SG Govt Bond At FVOCI','Premium SG Govt Bond At FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 13,'*********','Discount SG Govt Bond At FVOCI','Discount SG Govt Bond At FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 14,'*********','SG Govt Bond At Amortized Cost (gross)','SG Govt Bond At Amortized Cost (gross)','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 15,'*********','Premium SG Govt Bond At Amortised Cost','Premium SG Govt Bond At Amortised Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 16,'*********','Discount SG Govt Bond At Amortised Cost','Discount SG Govt Bond At Amortised Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 17,'401131001','Int Inc SG Govt Bond','Int Inc SG Govt Bond','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 18,'102411001','SG Treasury Bills At FVOCI (par)','SG Treasury Bills At FVOCI (par)','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 19,'102414001','Discount SG Treasury Bills At FVOCI','Discount SG Treasury Bills At FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 20,'102311001','SG Treasury Bills At Amortized Cost','SG Treasury Bills At Amortized Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 21,'102313001','Discount SG Treasury Bills At Amortized Cost','Discount SG Treasury Bills At Amortized Cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 22,'401134001','Int Inc SG Treasury Bills Prem/Disc','Int Inc SG Treasury Bills Prem/Disc','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 23,'102212001','SG Govt Bond at FVOCI - Fair value changes','SG Govt Bond at FVOCI - Fair value changes','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 24,'303111001','SG Govt Bond at FVOCI - Fair Value Changes','SG Govt Bond at FVOCI - Fair Value Changes','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 25,'401132001','Int inc SG Govt Bond prem/disc','Int inc SG Govt Bond prem/disc','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 26,'404101001','Realised FV gains for SG government bonds at FVOCI','Realised FV gains for SG government bonds at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 27,'404102001','Realised FV losses for SG government bonds at FVOCI','Realised FV losses for SG government bonds at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 28,'404105001','Realised FV gains for SG government bonds at amortised cost','Realised FV gains for SG government bonds at amortised cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 29,'404106001','Realised FV losses for SG government bonds at amortised cost','Realised FV losses for SG government bonds at amortised cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 30,'102412001','SG Treasury Bills at FVOCI - Fair value changes','SG Treasury Bills at FVOCI - Fair value changes','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 31,'303112001','SG Treasury Bills at FVOCI','SG Treasury Bills at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 32,'404103001','Realised FV gains for SG Treasury bills at FVOCI','Realised FV gains for SG Treasury bills at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 33,'404104001','Realised FV losses for SG Treasury bills at FVOCI','Realised FV losses for SG Treasury bills at FVOCI','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 34,'*********','Realised FV gains for SG Treasury bills at amortised cost','Realised FV gains for SG Treasury bills at amortised cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 35,'*********','Realised FV losses for SG Treasury bills at amortised cost','Realised FV losses for SG Treasury bills at amortised cost','SGD','ACTIVE','MANUAL','MANUAL'),
      (uuid(), 36,'*********','Bank Charges,','Bank Charges', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 37,'*********','CASA Operational losses','CASA Operational losses', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 38,'*********','Clearing - Credit card','Clearing - Credit card', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 39,'*********','Clearing - FAST','Clearing - FAST', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 40,'*********','Clearing - GIRO','Clearing - GIRO', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 41,'*********','Clearing - MEPS','Clearing - MEPS', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 42,'*********','Grab Ecosystem (Funds In) - Clearing','Grab Ecosystem (Funds In) - Clearing', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 43,'*********','Grab Ecosystem (Pay Out) - Clearing','Grab Ecosystem (Pay Out) - Clearing', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 44,'*********','Int exp current accounts','Int exp current accounts', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 45,'*********','Int exp term deposit','Int exp term deposit', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 46,'*********','Service charge for cash transfer','Service charge for cash transfer', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 47,'*********','SGD Oracle Cash Mgt Bank','SGD Oracle Cash Mgt Bank', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 48,'*********','Singtel Ecosystem (Funds In) - Clearing','Singtel Ecosystem (Funds In) - Clearing', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 49,'*********','Singtel Ecosystem (Pay Out) - Clearing','Singtel Ecosystem (Pay Out) - Clearing', 'SGD', 'ACTIVE', 'MANUAL','MANUAL'),
      (uuid(), 50,'*********','Statutory Deposits with central Bank','Statutory Deposits with central Bank', 'SGD', 'ACTIVE', 'MANUAL','MANUAL');

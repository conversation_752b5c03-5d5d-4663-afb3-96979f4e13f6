-- Product Master DB Insert Queries For Lending
-- LastUpdatedAt: 19/07/2022

-- TABLE_NAME :  product_template

-- Queries :

INSERT INTO `product_template` (id, public_id, code, name, description, status, created_by, updated_by)
VALUES (3, uuid(), "UNSECURED_LINE_OF_CREDIT", "Unsecured Line of Credit", "The unsecured line of credit product",
        "ACTIVE", "MANUAL", "MANUAL");

INSERT INTO `product_template` (id, public_id, code, name, description, status, created_by, updated_by)
VALUES (4, uuid(), "UNSECURED_TERM_LOAN", "Unsecured Term Loan", "The unsecured term loan product", "ACTIVE", "MANUAL",
        "MANUAL");


-- TABLE_NAME :  product_template_parameter

-- Queries :

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "finance", "general_ledger_mapping_for_normal_account", "?", "INT", "AT_PRODUCT_VARIANT",
        "The general ledger at ERP normal accounts of this product variant will be reported under", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "account_number_structure", "UU-PP-RRRRRC", "STRING", "NO_OVERRIDE",
        "Is the account number format for this product variant", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "account_number_structure", "UU-PP-RRRRRC", "STRING", "NO_OVERRIDE",
        "Is the account number format for this product variant", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "allowed_home_currency", "SGD", "CURRENCY", "NO_OVERRIDE",
        "Allowed currencies for this product variant", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "allowed_home_currency", "SGD", "CURRENCY", "NO_OVERRIDE",
        "Allowed currencies for this product variant", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type,
                                        override_level, created_by, updated_by)
values (uuid(), 3, "business", "allowed_additional_currencies", "NULL", "CURRENCY", "NO_OVERRIDE", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type,
                                        override_level, created_by, updated_by)
values (uuid(), 4, "business", "allowed_additional_currencies", "NULL", "CURRENCY", "NO_OVERRIDE", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "max_active_loc_accounts_per_customer", "1", "INT", "NO_OVERRIDE",
        "Limit to how many accounts of this product variant a customer is allowed to have", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "max_active_loan_accounts_per_loc_account", "10", "INT", "NO_OVERRIDE",
        "Limit to how many accounts of this product variant a customer is allowed to have", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "max_account_holders", 1, "INT", "NO_OVERRIDE",
        "Limit to how many account holders are allowed for this product variant", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "max_account_holders", 1, "INT", "NO_OVERRIDE",
        "Limit to how many account holders are allowed for this product variant", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "allowed_signing_condition", "SINGLE", "ENUM", "NO_OVERRIDE",
        "This dictates the operating structure of the account i.e. Single, Joint etc. If it is not Single, the max_account_holders must be > 1",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "allowed_signing_condition", "SINGLE", "ENUM", "NO_OVERRIDE",
        "This dictates the operating structure of the account i.e. Single, Joint etc.", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "min_days_between_drawdown_and_first_repayment", "30", "INT", "NO_OVERRIDE",
        "Is the minimum number of days between drawdown & first repayment", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "free_look_period_in_days", "2", "INT", "NO_OVERRIDE",
        "Are the number of days from drawdown during which prepayment results in zero interest to the customer",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "max_free_look_facility_usage_lifetime_limit", "2", "INT", "NO_OVERRIDE", "?", "MANUAL",
        "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "repayment_order", "INSTALLMENT_FIRST_THEN_LOAN", "ENUM", "NO_OVERRIDE", "Can be:
- INSTALLMENT_FIRST_THEN_LOAN : L1.I1 > L2.I1 > L3.I1 > L1.I2 > L2.I2 > L3.I2
- LOAN_FIRST_THEN_INSTALLMENT : L1.I1 > L1. I2 > L2.I1 > L2.I2 > L3.I1 > L3.I2", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "default_repayment_hierarchy_within_installment",
        "PENAL_INTEREST, INTEREST, FEES, PRINCIPAL", "STRING", "NO_OVERRIDE",
        "Within an installment, this specifies the liquidation order across components of the installment for normal loans",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "delinquent_repayment_hierarchy_within_installment",
        "OVERDUE PRINCIPAL, OVERDUE INTEREST, OVERDUE FEES, OVERDUE PENAL INTEREST", "STRING", "NO_OVERRIDE",
        "Within an installment, this specifies the liquidation order across components of the installment for delinquent loans",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "min_drawdown_amount", "100", "DOUBLE", "NO_OVERRIDE", "?", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "overpayment_allowed", "Y", "BOOLEAN", "NO_OVERRIDE",
        "Signifies if overpayment (excess payment) is allowed", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "business", "prepayment_allowed", "Y", "BOOLEAN", "NO_OVERRIDE",
        "Signifies if prepayment is allowed", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "statement_cycle_frequency", "MONTHLY", "ENUM", "AT_ACCOUNT",
        "This denotes the frequency of account statement generation", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "limit_product_code", "01", "STRING", "?",
        "This denotes the product_code of limit product ", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "business", "limit_unit_code", "80", "STRING", "?", "This denotes the unit_code of limit product ",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "credit risk", "min_customer_age_for_activation", "21", "INT", "NO_OVERRIDE",
        "Is the min age of the customer", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "credit risk", "max_customer_age_for_activation", "55", "INT", "NO_OVERRIDE",
        "Is the max age of the customer", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "credit risk", "min_customer_age_for_withdrawal", "21", "INT", "NO_OVERRIDE",
        "Is the min age of the customer", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "credit risk", "max_customer_age_for_withdrawal", "62", "INT", "NO_OVERRIDE",
        "Is the max age of the customer", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "credit risk", "min_credit_limit_amount", "100", "DOUBLE", "AT_ACCOUNT",
        "Is the maximum balance limit beyond which only system-initiated credits are allowed and all other credits are rejected. Cannot be lesser than the value in min_drawdown_amount",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "credit risk", "max_credit_limit_amount", "50000", "DOUBLE", "AT_ACCOUNT",
        "Is the maximum balance limit beyond which any credits are rejected", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "credit risk", "min_loan_tenor_in_months", "2", "INT", "AT_ACCOUNT",
        "Is the minimum tenor of the loan in months", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "credit risk", "max_loan_tenor_in_months", "60", "INT", "AT_ACCOUNT",
        "Is the maximum tenor of the loan in months", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "credit risk", "min_loan_tenor_in_months", "2", "INT", "AT_ACCOUNT",
        "Is the minimum tenor of the loan in months", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "credit risk", "max_loan_tenor_in_months", "60", "INT", "AT_ACCOUNT",
        "Is the maximum tenor of the loan in months", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "normal_interest_calc_type", "DEFAULT", "ENUM", "NO_OVERRIDE",
        "Default interest calculation method (Annual Percentage Rate (APR) * Principal Outstanding * Number of calendar days) / Days in a year",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "normal_interest_days_in_a_year", "365", "ENUM", "NO_OVERRIDE",
        "Can be 360, 365 or ACTUAL", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "normal_interest_days_in_a_month", "ACTUAL", "ENUM", "NO_OVERRIDE", "Can be 30 or ACTUAL",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "repayment_grace_period_in_days", "0", "INT", "NO_OVERRIDE",
        "Is the number of days from the due date during which loan is not considered overdue", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "penal_interest_calc_type", "DEFAULT", "ENUM", "NO_OVERRIDE",
        "Default interest calculation method (Penal Interest Rate * Overdue Principal * Actual number of calendar days overdue) / Days in a year",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "penal_interest_days_in_a_year", "365", "ENUM", "NO_OVERRIDE",
        "Can be 360, 365 or ACTUAL", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "penal_interest_days_in_a_month", "ACTUAL", "ENUM", "NO_OVERRIDE", "Can be 30 or ACTUAL",
        "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "interest_accrual_frequency", "DAYS", "ENUM", "NO_OVERRIDE",
        "Denotes the frequency of accrual in DAYS, WEEKS, MONTHS", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "interest_accrual_frequency_units", "1", "INT", "NO_OVERRIDE",
        "Denotes the number of accrual frequency units for accrual schedule", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "interest_accrual_roundoff_precision_digits", "8", "INT", "NO_OVERRIDE",
        "Is the precision beyond which interest accrued is truncated", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "interest_display_roundoff_precision_digits", "2", "INT", "NO_OVERRIDE",
        "Is the precision beyond which interest displayed is truncated", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 4, "pricing", "interest_accrual_rounding_type", "HALF_ROUND_UP", "ENUM", "NO_OVERRIDE",
        "Specifies the rounding type for interest accrual", "MANUAL", "MANUAL");

INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, description, created_by, updated_by)
values (uuid(), 3, "risk", "allowed_hold_codes",
        "WHOLE_BALANCE_HOLD, COURT_ORDER, POLICE_ORDER, SPECIAL_INSTRUCTIONS, NO_DEBIT, NO_CREDIT", "ENUM",
        "NO_OVERRIDE", "Is the list of allowed hold codes for this product", "MANUAL", "MANUAL");


-- TABLE_NAME :  product

-- Queries :

INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
values (uuid(), 3, "FLEXI_LOAN_LINE_OF_CREDIT", "Flexi Loan Line of Credit", "Flexi Loan Line of Credit", "ACTIVE",
        "MANUAL", "MANUAL");

INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
values (uuid(), 4, "FLEXI_LOAN_TERM_LOAN", "Flexi Loan Term Loan", "Flexi Loan Term Loan", "ACTIVE", "MANUAL",
        "MANUAL");


-- TABLE_NAME :  product_variant

-- Queries :

INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to,
                             created_by, updated_by)
VALUES (uuid(), 4, 'DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT', '1', 'Default Flexi Loan Line of Credit',
        'The default Flexi Loan Line of Credit product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
        'MANUAL', 'MANUAL');

INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to,
                             created_by, updated_by)
VALUES (uuid(), 5, 'DEFAULT_FLEXI_LOAN_TERM_LOAN', '1', 'Default Flexi Loan Term Loan',
        'The default Flexi Loan Term Loan product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MANUAL',
        'MANUAL');


-- TABLE_NAME :  product_variant_question

-- Queries :

INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
VALUES (uuid(), 5, 'drawdown_name', 'What are you borrowing for?', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
VALUES (uuid(), 4, 'closure_reason', 'Why you''re leaving us?', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  lending_product_answer_suggestion

-- Queries :

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 1, 'Travel', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 1, 'Renovation', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 1, 'Emergency', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'Deceased', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'Lost Interest', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'Others', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
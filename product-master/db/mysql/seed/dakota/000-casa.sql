-- Product Master DB Insert Queries For CASA

-- TABLE_NAME :  product_variant
-- Queries :
INSERT INTO product_template (public_id, code, name, description, status, created_by, updated_by) VALUES
('bf8658c8-9187-415b-af9c-79c8449d305a', 'SAVINGS_DEPOSIT', 'Savings Account', 'The savings account master product', 'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product_template_parameter
-- Queries :
INSERT INTO product_template_parameter (public_id,product_template_id,namespace,parameter_key,parameter_value,data_type,override_level,exception_level,description,created_by,updated_by) VALUES
('04df6c85-7f4e-11ec-93b7-0ac04b8a5758',1,'business','max_account_holders','1','INT','NO_OVERRIDE',NULL,'Limit to how many account holders are allowed for this product variant','MANUAL','MANUAL'),
('1211e4ee-7f4f-11ec-93b7-0ac04b8a5758',1,'pricing','interest_accrual_frequency_units','1','INT','NO_OVERRIDE',NULL,'Denotes the number of accrual frequency units for accrual schedule','MANUAL','MANUAL'),
('172d2b02-7f4e-11ec-93b7-0ac04b8a5758',1,'business','allowed_signing_condition','SINGLE','ENUM','NO_OVERRIDE',NULL,'This dictates the operating structure of the account i.e. Single - use for single name accounts; Either one to sign (Not for R1) - use for joint accounts where either account holder can operate the account singly; Both to sign (Not for R1) - use for joint accounts where both account holder must provide instructions together to operate the account; Parent to sign (Not for R1) - use for kids accounts where the parent is the primary operator','MANUAL','MANUAL'),
('26323c1c-7f4f-11ec-93b7-0ac04b8a5758',1,'pricing','interest_posting_frequency_units','1','INT','NO_OVERRIDE',NULL,'Denotes the number of accrual frequency units for payout schedule','MANUAL','MANUAL'),
('3173f7f8-7f4e-11ec-93b7-0ac04b8a5758',1,'risk & business','min_customer_age','16','INT','NO_OVERRIDE',NULL,'Is the min age of the customer','MANUAL','MANUAL'),
('346d226d-7f4f-11ec-93b7-0ac04b8a5758',1,'pricing','interest_accrual_roundoff_precision_digits','5','INT','NO_OVERRIDE',NULL,'Is the precision beyond which interest accrued is truncated','MANUAL','MANUAL'),
('3f670ec4-7f4e-11ec-93b7-0ac04b8a5758',1,'risk & business','max_customer_age','999','INT','NO_OVERRIDE',NULL,'Is the max age of the customer','MANUAL','MANUAL'),
('561e06d6-7f4e-11ec-93b7-0ac04b8a5758',1,'business','soft_deposit_cap_limit','20000','DOUBLE','AT_ACCOUNT',NULL,'Is the maximum balance limit beyond which only system-initiated credits are allowed and all other credits are rejected','MANUAL','MANUAL'),
('695f3f90-7f4e-11ec-93b7-0ac04b8a5758',1,'business','hard_deposit_cap_limit','75000','DOUBLE','AT_ACCOUNT',NULL,'Is the maximum balance limit beyond which any credits are rejected','MANUAL','MANUAL'),
('6a3bfa8d-7f4d-11ec-93b7-0ac04b8a5758',1,'finance','general_ledger_mapping_for_normal_account','203112','INT','AT_PRODUCT_VARIANT',NULL,'The general ledger at ERP normal accounts of this product variant will be reported under','MANUAL','MANUAL'),
('796b3149-7f4d-11ec-93b7-0ac04b8a5758',1,'finance','general_ledger_mapping_for_dormant_account','203119','INT','AT_PRODUCT_VARIANT',NULL,'The general ledger at ERP dormant accounts of this product variant will be reported under','MANUAL','MANUAL'),
('7c398005-7f4e-11ec-93b7-0ac04b8a5758',1,'business','days_to_dormancy','365','INT','NO_OVERRIDE',NULL,'Is the number of days in which an account will turn dormant','MANUAL','MANUAL'),
('87fc5db4-7f4d-11ec-93b7-0ac04b8a5758',1,'finance','general_ledger_mapping_for_interest_accrual','209112','INT','AT_PRODUCT_VARIANT',NULL,'The general ledger at ERP where accrued interest will be reported under','MANUAL','MANUAL'),
('8ecc86b7-7f4e-11ec-93b7-0ac04b8a5758',1,'business','days_to_auto_closure','183','INT','NO_OVERRIDE',NULL,'Is the number of days an account will be auto-closed if there is zero balance and inactivity in the account','MANUAL','MANUAL'),
('9fb73342-7f4d-11ec-93b7-0ac04b8a5758',1,'finance','allowed_currency','SGD','CURRENCY','NO_OVERRIDE',NULL,'Allowed currencies for this product variant','MANUAL','MANUAL'),
('a3ac7742-7f4e-11ec-93b7-0ac04b8a5758',1,'business','statement_cycle_frequency','MONTHLY','ENUM','AT_ACCOUNT',NULL,'This denotes the frequency of account statement generation','MANUAL','MANUAL'),
('bae30aee-7f4e-11ec-93b7-0ac04b8a5758',1,'pricing','interest_calculation_type','DEFAULT','ENUM','NO_OVERRIDE',NULL,'Default interest calculation method Interest rate * spot balance * actual number of calendar days/days in a year','MANUAL','MANUAL'),
('bb622cbe-7f4d-11ec-93b7-0ac04b8a5758',1,'business','account_number_structure','BBB-NNNNNN-C','STRING','NO_OVERRIDE',NULL,'Is the account number format for this product variant. First 3 digits - branch code; Middle string of 6 digits - customer randomly assigned or picked account number; Last 1 digit - Checksum. e.g. 123-456789-1','MANUAL','MANUAL'),
('daf697bc-7f4e-11ec-93b7-0ac04b8a5758',1,'pricing','days_in_a_year','360','ENUM','NO_OVERRIDE',NULL,'Can be 360, 365 or ACTUAL','MANUAL','MANUAL'),
('e719e833-7f4e-11ec-93b7-0ac04b8a5758',1,'pricing','days_in_a_month','30','ENUM','NO_OVERRIDE',NULL,'Can be 30 or ACTUAL','MANUAL','MANUAL'),
('f70e372e-7f4d-11ec-93b7-0ac04b8a5758',1,'business','max_accounts_per_customer','1','INT','NO_OVERRIDE',NULL,'Limit to how many accounts of this product variant a customer is allowed to have','MANUAL','MANUAL'),
('fe212109-7f4e-11ec-93b7-0ac04b8a5758',1,'pricing','interest_accrual_frequency','DAYS','ENUM','NO_OVERRIDE',NULL,'Denotes the frequency of accrual in DAYS, WEEKS, MONTHS','MANUAL','MANUAL'),
('1325f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d',1,'business', 'interest_posting_day', '1', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'The day of the month on which interest is applied.', 'MANUAL', 'MANUAL'),
('1225f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d',1,'pricing', 'interest_posting_frequency', 'DAYS', 'STRING', 'NO_OVERRIDE', NULL, 'Denotes the frequency of payout in DAYS, WEEKS, MONTHS', 'MANUAL', 'MANUAL'),
('1125f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d',1,'business', 'interest_posting_account', 'MAIN_ACCOUNT', 'STRING', 'AT_PRODUCT_VARIANT', NULL, 'The account to which accrued interest can be posted to / applied to.', 'MANUAL', 'MANUAL'),
('f3e65965-bfd1-11ec-b1b7-0aed065bc0fc',1,'risk','allowed_hold_codes','WHOLE_BALANCE_HOLD, ON_LIEN, COURT_ORDER, POLICE_ORDER, SPECIAL_INSTRUCTIONS, NO_DEBIT, NO_CREDIT','ENUM','NO_OVERRIDE','NO_OVERRIDE','Is the list of allowed hold codes for this product','MANUAL','MANUAL');

-- TABLE_NAME :  product
-- Queries :
INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by) VALUES
('1425f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, 'RETAIL_SAVINGS', 'Retail savings deposits',' The retail savings deposits product', 'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to, created_by, updated_by) VALUES
('1525f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, 'deposit_account', '1', 'Defaults retail savings deposits', 'The defaults retail savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'MANUAL','MANUAL');

-- TABLE_NAME :  deposit_interest
-- Queries :
INSERT INTO deposit_interest (public_id, product_variant_id, is_linked_to_base_rate, base_interest_id, code, name, description, currency, round_off_type, interest_slab_type, interest_slab_structure, created_by, updated_by) VALUES
('1625f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, 0, NULL, 'DEFAULT_SAVINGS_INTEREST', 'Default Savings Interest', 'Default Savings Interest', 'SGD', 'HALF_ROUND_UP', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  deposit_interest_version
-- Queries :
INSERT INTO deposit_interest_version (public_id,deposit_interest_id, version, effective_date, description, created_by, updated_by) VALUES
('1725f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, '1', CURRENT_TIMESTAMP, 'Default Savings Interest v1.0', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  deposit_interest_amount_slab_rate
-- Queries :
INSERT INTO deposit_interest_amount_slab_rate (public_id,deposit_interest_version_id, from_amount, to_amount, base_rate_interest_spread_percentage, absolute_interest_rate_percentage, created_by, updated_by) VALUES
('1825f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, '0', '75000', '0', '0.08', 'MANUAL', 'MANUAL'),
('1925f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, '75000', '*********', '0', '0.08', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  general_ledger
-- Queries :
INSERT INTO general_ledger (public_id, code, name, description, currency, status, created_by, updated_by) VALUES
('1e69052d-7a9d-11ec-93b7-0ac04b8a5758', '203112', 'Saving accounts',' Saving accounts', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('2d688098-7a9d-11ec-93b7-0ac04b8a5758', '203119', 'Dormant customer accounts', 'Dormant customer accounts',' SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('39453c5f-7a9d-11ec-93b7-0ac04b8a5758', '209419', 'Other clearing payables', 'Other clearing payables', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('********-7a9d-11ec-93b7-0ac04b8a5758', '104118', 'SGD Nostro Bank - Clearing', 'SGD Nostro Bank - Clearing', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('515aedc0-7a9d-11ec-93b7-0ac04b8a5758', '401212', 'Int exp savings accounts', 'Int exp savings accounts', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('6942746f-7a9d-11ec-93b7-0ac04b8a5758', '209112', 'Accrued interest payables', 'Accrued interest payables', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  internal_account
-- Queries :
INSERT INTO internal_account (public_id, general_ledger_id, code, name, description, currency, status, created_by, updated_by) VALUES
('cb7d3e6f-7a9d-11ec-93b7-0ac04b8a5758', 3, '*********', 'Payments Clearing Account (FAST)', 'Payments Clearing Account (FAST)', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('cb7d3e6f-7a9d-11ec-93b7-0ac04b8a5758', 3, '*********', 'Payments Clearing Account (Cards)', 'Payments Clearing Account (Cards)', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('e981ca10-7a9d-11ec-93b7-0ac04b8a5758', 4, '*********', 'Payments Clearing Nostro  (FAST)', 'Payments Clearing Nostro  (FAST)', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL'),
('f9f61f92-7a9d-11ec-93b7-0ac04b8a5758', 5, '*********', 'Savings Interest Expense Account', 'Savings Interest Expense Account', 'SGD', 'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  base_interest
-- Queries :
INSERT INTO base_interest (public_id, code, name, description, currency, created_by, updated_by) VALUES
('079df075-7a9f-11ec-93b7-0ac04b8a5758', 'SIBOR', 'Singapore Interbank Offered Rate', 'Singapore Interbank Offered Rate', 'SGD', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  base_interest_version
-- Queries :
INSERT INTO base_interest_version (public_id, base_interest_id, version, effective_date, description, created_by, updated_by) VALUES
('655c5bc8-7a9f-11ec-93b7-0ac04b8a5758', 1, '1', CURRENT_TIMESTAMP, 'SIBOR SGD v1', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  base_interest_time_slab_rate
-- Queries :
INSERT INTO base_interest_time_slab_rate (public_id, base_interest_version_id, term_unit, term_value, base_rate_percentage, created_by, updated_by) VALUES
('b5be8c5c-7a9f-11ec-93b7-0ac04b8a5758', 1, 'MONTH', 1, '0.75', 'MANUAL', 'MANUAL'),
('be3d370a-7a9f-11ec-93b7-0ac04b8a5758', 1, 'MONTH', 3, '0.80', 'MANUAL', 'MANUAL'),
('c54b116b-7a9f-11ec-93b7-0ac04b8a5758', 1, 'MONTH', 6, '0.85', 'MANUAL', 'MANUAL'),
('cc0aa628-7a9f-11ec-93b7-0ac04b8a5758', 1, 'MONTH', 12, '0.90', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  transaction_catalogue
-- Queries :
INSERT INTO transaction_catalogue (public_id,`domain`,is_financial_txn,txn_type,txn_sub_type,display_name,description,status,created_by,updated_by) VALUES
('dac5810c-7a9a-11ec-93b7-0ac04b8a5758','deposits',1,'receive_money','fast_network','Receive money from FAST Network','Incoming FAST/ PayNow funds transfer to Customer''s CASA Account','ACTIVE','MANUAL','MANUAL'),
('ff16ef93-7e7a-11ec-93b7-0ac04b8a5758','deposits',1,'receive_money_reversal','fast_network','Reversal of receive money from FAST Network','Reversal of incoming FAST/ PayNow funds transfer to Customer''s CASA Account','ACTIVE','MANUAL','MANUAL');

-- TABLE_NAME :  transaction_catalogue
-- Queries :
INSERT INTO transaction_catalogue (public_id,`domain`,is_financial_txn,txn_type,txn_sub_type,display_name,description,status,created_by,updated_by) VALUES
('dac5810c-7a9a-11ec-93b7-0ac04b8a5758', 'deposits', 1, 'receive_money', 'fast_network', 'Receive money from FAST Network', 'Incoming FAST/ PayNow funds transfer to Customer''s CASA Account', 'ACTIVE', 'MANUAL', 'MANUAL'),
('ff16ef93-7e7a-11ec-93b7-0ac04b8a5758', 'deposits', 1, 'receive_money_reversal', 'fast_network', 'Reversal of receive money from FAST Network', 'Reversal of incoming FAST/ PayNow funds transfer to Customer''s CASA Account', 'ACTIVE', 'MANUAL', 'MANUAL'),                                                                                                                                                       ('0df251fc-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'transfer_money','intrabank','Funds transfer to GXS Bank Account','Intrabank funds transfer between CASA Accounts','ACTIVE','MANUAL','MANUAL'),
('1b300f2b-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'transfer_money_reversal','intrabank','Reversal of funds transfer to GXS Bank Account','Reversal of intrabank funds transfer between CASA Accounts','ACTIVE','MANUAL','MANUAL'),
('2e8df6ed-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'send_money','fast_network','Send money through FAST Network','Outgoing FAST/ PayNow funds transfer initiated by the GXS Customer - auth','ACTIVE','MANUAL','MANUAL'),
('39affe2c-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'send_money_reversal','fast_network','Reversal of send money through FAST Network','Reversal of outgoing FAST/ PayNow funds transfer initiated by the GXS Customer - auth','ACTIVE','MANUAL','MANUAL'),
('4741f4e3-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'settlement','fast_network','Periodic settlement to FAST Network','Periodic settlement to FAST Network','ACTIVE','MANUAL','MANUAL'),
('515a6b09-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'settlement_reversal','fast_network','Reversal of periodic settlement to FAST Network','Reversal of periodic settlement to FAST Network','ACTIVE','MANUAL','MANUAL'),
('613d1eaa-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'interest_accrual','savings','Interest accrual for savings accounts','Interest accrual for savings accounts','ACTIVE','MANUAL','MANUAL'),
('6b2d6d77-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'interest_accrual_reversal','savings','Reversal of interest accrual for savings accounts','Reversal of interest accrual for savings accounts','ACTIVE','MANUAL','MANUAL'),
('75727d96-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'interest_payout','savings','Interest payout for savings accounts','Interest payout for savings accounts','ACTIVE','MANUAL','MANUAL'),
('7f16e003-7e7b-11ec-93b7-0ac04b8a5758','deposits',1,'interest_payout_reversal','savings','Reversal of interest payout for savings accounts','Reversal of interest payout for savings accounts','ACTIVE','MANUAL','MANUAL');

-- TABLE_NAME :  product_variant_transaction_catalogue_mapping
-- Queries :
INSERT INTO product_variant_transaction_catalogue_mapping (public_id,product_variant_id,transaction_catalogue_id,status,created_by,updated_by) VALUES
('b7c1145d-7e7c-11ec-93b7-0ac04b8a5758',1,1,'ACTIVE','MANUAL','MANUAL'),
('d19c74c5-7e7c-11ec-93b7-0ac04b8a5758',1,2,'ACTIVE','MANUAL','MANUAL'),
('d597a50b-7e7c-11ec-93b7-0ac04b8a5758',1,3,'ACTIVE','MANUAL','MANUAL'),
('d9beceb3-7e7c-11ec-93b7-0ac04b8a5758',1,4,'ACTIVE','MANUAL','MANUAL'),
('df46142f-7e7c-11ec-93b7-0ac04b8a5758',1,5,'ACTIVE','MANUAL','MANUAL'),
('e40aa993-7e7c-11ec-93b7-0ac04b8a5758',1,6,'ACTIVE','MANUAL','MANUAL'),
('e944d770-7e7c-11ec-93b7-0ac04b8a5758',1,9,'ACTIVE','MANUAL','MANUAL'),
('ed704791-7e7c-11ec-93b7-0ac04b8a5758',1,10,'ACTIVE','MANUAL','MANUAL'),
('f18df103-7e7c-11ec-93b7-0ac04b8a5758',1,11,'ACTIVE','MANUAL','MANUAL'),
('fa2f2d7d-7e7c-11ec-93b7-0ac04b8a5758',1,12,'ACTIVE','MANUAL','MANUAL');

-- TABLE_NAME :  product_variant_transaction_catalogue_internal_account_mapping
-- Queries :
INSERT INTO product_variant_transaction_catalogue_internal_account_mapping (public_id,product_variant_transaction_catalogue_mapping_id,internal_account_id,identifier_key,status,created_by,updated_by) VALUES
('ac199dd7-7e7e-11ec-97c8-06c9a9431426',1,1,'fast_network_internal_account','ACTIVE','MANUAL','MANUAL'),
('ade3e229-7e7e-11ec-97c8-06c9a9431426',2,1,'fast_network_internal_account','ACTIVE','MANUAL','MANUAL'),
('af6e8623-7e7e-11ec-97c8-06c9a9431426',5,1,'fast_network_internal_account','ACTIVE','MANUAL','MANUAL'),
('b1291a45-7e7e-11ec-97c8-06c9a9431426',6,1,'fast_network_internal_account','ACTIVE','MANUAL','MANUAL'),
('b1291a45-7e7e-11ec-97c8-06c9a9431426',7,4,'savings_interest_expense_account','ACTIVE','MANUAL','MANUAL'),
('b1291a45-7e7e-11ec-97c8-06c9a9431426',8,4,'savings_interest_expense_account','ACTIVE','MANUAL','MANUAL');

-- Product Master DB Insert Queries For CASA

-- TABLE_NAME :  product_template
-- Queries :
INSERT INTO product_template (public_id, code, name, description, status, created_by, updated_by)
VALUES
    ('bf8658c8-9187-415b-af9c-79c8449d305a', 'SAVINGS_DEPOSIT', 'Savings Account', 'The savings account master product', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_template_parameter
-- Queries :
INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
VALUES
    ('04df6c85-7f4e-11ec-93b7-0ac04b8a5758', 1, 'business', 'account_applicable_features', 'NULL', 'STRING', 'AT_PRODUCT_VARIANT', NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
    ('97ce86f2-9521-45fb-90ca-ae6c01e726c0', 1, 'business', 'tm_product_id', 'NULL', 'STRING', 'AT_PRODUCT_VARIANT', NULL,'The features that are applicable to an account','MANUAL','MANUAL'),
    ('a18f2f66-1ff7-4d7b-b20b-e37d22102771', 1, 'risk', 'allowed_hold_codes', 'ADVERSE_NEWS, AML/SANCTION_HITS, BANKRUPT, DECEASED, FRAUD_ACTOR, FRAUD_VICTIM, MENTAL_INCAPACITY, MISSING_DEVICE, MULE_ACCOUNT, POLICE/COURT/SEIZURE/LEA_ORDER, KILL_SWITCH, GARNISHEE/CLAWBACK, BANK_RESTRICTED', 'ENUM', 'NO_OVERRIDE', 'no_override', 'Is the list of allowed hold codes for this product', 'MANUAL', 'MANUAL'),
    ('967f5a6e-367c-4226-a236-5eeb558e8cc1', 1, 'pricing', 'interest_posting_frequency', 'DAYS', 'ENUM', 'AT_PRODUCT_VARIANT', 'no_override', 'Denotes the frequency of payout in DAYS, WEEKS, MONTHS, END_OF_GOAL', 'MANUAL', 'MANUAL'),
    ('9c987a72-43ac-43c7-983d-fbcc54b7941b', 1, 'business', 'interest_posting_account', 'SELF_ACCOUNT', 'ENUM', 'NO_OVERRIDE', NULL, 'The account to which accrued interest can be posted to / applied to.', 'MANUAL', 'MANUAL'),
    ('0be8381f-24e9-4b79-9a72-db9b6057e452', 1, 'business', 'interest_posting_day', '1', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'The day of the month on which interest is applied', 'MANUAL', 'MANUAL'),
    ('e840fb04-84ea-11ed-a1eb-0242ac120002', 1, 'business', 'earmark_reason_codes', 'CLAWBACK, CUSTOMER_DISPUTE/ERRONEOUS_CREDIT, DEBIT_CARD_DISPUTE, GARNISHEE_ORDER, POLICE/COURT/SEIZURE/LEA_ORDER', 'ENUM', 'NO_OVERRIDE', 'NO_OVERRIDE', 'Is the list of reason codes that are applicable for earmarking an account', 'MANUAL', 'MANUAL');

--     ('2cfb85d0-2126-46ce-8476-7f8ec6dca192', 1, 'finance', 'general_ledger_mapping_for_normal_account', '203112', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'The general ledger at ERP normal accounts of this product variant will be reported under', 'MANUAL', 'MANUAL'),
--     ('fb29f4ba-8e0e-44cd-a9ef-b2df2f2c882c', 1, 'finance', 'general_ledger_mapping_for_dormant_account', '203119', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'The general ledger at ERP dormant accounts of this product variant will be reported under', 'MANUAL', 'MANUAL'),
--     ('bbbb0b1b-11f7-45c6-ad29-54ef0d328a9b', 1, 'finance', 'general_ledger_mapping_for_interest_accrual', '209112', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'The general ledger at ERP where accrued interest will be reported under', 'MANUAL', 'MANUAL'),
--     ('102eb580-20da-4011-a6ae-c97c948c95f7', 1, 'finance', 'allowed_currency', 'MYR', 'CURRENCY', 'NO_OVERRIDE', NULL, 'Allowed currencies for this product variant', 'MANUAL', 'MANUAL'),
--     ('0634f48c-37ab-400e-8c97-99698b12bb05', 1, 'business', 'account_number_structure', 'BLAA-NNNNNNNN-C[PPP]', 'STRING', 'NO_OVERRIDE', NULL, 'Is the account number format for this product variant. First 3 digits - branch code; Middle string of 6 digits - customer randomly assigned or picked account number; Last 1 digit - Checksum. e.g. 123-456789-1', 'MANUAL', 'MANUAL'),
--     ('3c3e418b-5f56-4095-b34c-e551f147b881', 1, 'business', 'max_accounts_per_customer', '1', 'INT', 'NO_OVERRIDE', NULL, 'Limit to how many accounts of this product variant a customer is allowed to have', 'MANUAL', 'MANUAL'),
--     ('35b7b9b9-9bc5-409f-8cad-e15d87c302d3', 1, 'business', 'max_account_holders', '1', 'INT', 'NO_OVERRIDE', NULL, 'Limit to how many account holders are allowed for this product variant', 'MANUAL', 'MANUAL'),
--     ('********-cbd1-4459-ba59-9e1f80286d93', 1, 'business', 'allowed_signing_condition', 'SINGLE', 'ENUM', 'NO_OVERRIDE', NULL, 'This dictates the operating structure of the account i.e. Single - use for single name accounts; Either one to sign (Not for R1) - use for joint accounts where either account holder can operate the account singly; Both to sign (Not for R1) - use for joint accounts where both account holder must provide instructions together to operate the account; Parent to sign (Not for R1) - use for kids accounts where the parent is the primary operator', 'MANUAL', 'MANUAL'),
--     ('ea1521e3-29af-48f4-aa12-ea127fa11332', 1, 'risk & business', 'min_customer_age', '18', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'Is the min age of the customer', 'MANUAL', 'MANUAL'),
--     ('27dbdd9a-f7ca-4ec4-9195-a61e262031fc', 1, 'risk & business', 'max_customer_age', '999', 'INT', 'NO_OVERRIDE', NULL, 'Is the max age of the customer', 'MANUAL', 'MANUAL'),
--     ('8d3f6fc1-8254-43e6-bb38-dca2058b24cd', 1, 'business', 'soft_deposit_cap_limit', '18446700000000000000', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the maximum balance limit beyond which only system-initiated credits are allowed and all other credits are rejected', 'MANUAL', 'MANUAL'),
--     ('06eb6318-3997-45aa-a1be-8d11f228273b', 1, 'business', 'hard_deposit_cap_limit', '18446700000000000000', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the maximum balance limit beyond which any credits are rejected', 'MANUAL', 'MANUAL'),
--     ('8ddf30cd-57f7-4606-8662-3a5966776877', 1, 'business', 'days_to_dormancy', '365', 'INT', 'NO_OVERRIDE', NULL, 'Is the number of days in which an account will turn dormant (excluding last transaction day and account dormancy day)', 'MANUAL', 'MANUAL'),
--     ('5d2cdd85-6ff2-45e0-9777-4e2853157923', 1, 'business', 'min_account_balance', '0.00', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the minimum balance to be maintained at an account', 'MANUAL', 'MANUAL'),
--     ('e0c78475-6568-425a-a344-b5128e2334a9', 1, 'business', 'max_account_pending_activation_aging_period.nad_ewallet', '90', 'INT', 'NO_OVERRIDE', NULL, 'Is the number of days in which an account will be auto closed if account is pending and customer is registered with NAD', 'MANUAL', 'MANUAL'),
--     ('6167d4aa-a108-401e-b532-0beb5b5a875a', 1, 'business', 'max_account_pending_activation_aging_period.no_nad', '90', 'INT', 'NO_OVERRIDE', NULL, 'Is the number of days in which an account will be auto closed if account is pending and customer is NOT registered with NAD', 'MANUAL', 'MANUAL'),
--     ('c89fd0a6-1048-4d7e-891c-a55b6f77913b', 1, 'business', 'max_dormancy_period', '2190', 'INT', 'NO_OVERRIDE', NULL, 'Is the number of days an account will be auto-closed if the account is dormant for that amount of day', 'MANUAL', 'MANUAL'),
--     ('7a3d337d-e5dd-4f24-a2ab-3534a9d83aa4', 1, 'business', 'auto_close_triggers', 'SYSTEM_DEBIT_INSUFFICIENT_BALANCE, OPENED_STATUS_AGING, DORMANCY_AGING', 'ENUM', 'AT_PRODUCT_VARIANT', NULL, 'Are the events that will triggered account auto close', 'MANUAL', 'MANUAL'),
--     ('4a459706-8858-47f9-b3ea-51e559699b63', 1, 'business', 'statement_cycle_frequency', 'MONTHLY', 'ENUM', 'AT_ACCOUNT', 'no_override', 'This denotes the frequency of account statement generation', 'MANUAL', 'MANUAL'),
--     ('01b5e723-e1c1-4c79-821b-bc568b822f27', 1, 'pricing', 'interest_calculation_type', 'DEFAULT', 'ENUM', 'NO_OVERRIDE', 'no_override', 'Default interest calculation method Interest rate * spot balance * actual number of calendar days/days in a year', 'MANUAL', 'MANUAL'),
--     ('e19dbd20-0e94-4e8d-b13b-e8a4e876d9f4', 1, 'pricing', 'days_in_a_year', '360, 365, ACTUAL', 'ENUM', 'NO_OVERRIDE', 'no_override', 'Can be 360, 365 or ACTUAL', 'MANUAL', 'MANUAL'),
--     ('dac87c41-5e16-4ff9-9fc0-571f82e45873', 1, 'pricing', 'days_in_a_month', '30, ACTUAL', 'ENUM', 'NO_OVERRIDE', 'no_override', 'Can be 30 or ACTUAL', 'MANUAL', 'MANUAL'),
--     ('c51eb3ad-7d75-4251-9c5a-ab1ec2960fd0', 1, 'pricing', 'interest_accrual_frequency', 'DAYS', 'ENUM', 'NO_OVERRIDE', 'no_override', 'Denotes the frequency of accrual in DAYS, WEEKS, MONTHS', 'MANUAL', 'MANUAL'),
--     ('506d2781-b5ef-4a3d-81f6-4ff89b23739b', 1, 'pricing', 'interest_accrual_frequency_units', '1', 'INT', 'NO_OVERRIDE', 'no_override', 'Denotes the number of accrual frequency units for accrual schedule', 'MANUAL', 'MANUAL'),
--     ('0be7e079-57d6-425a-aed9-cf07f4ccf451', 1, 'pricing', 'interest_posting_frequency_units', '1', 'INT', 'AT_PRODUCT_VARIANT', 'no_override', 'Denotes the number of accrual frequency units for payout schedule', 'MANUAL', 'MANUAL'),
--     ('c1c330c7-e68f-47e5-a2d1-0e5573795138', 1, 'pricing', 'interest_accrual_roundoff_precision_digits', '4', 'INT', 'AT_PRODUCT_VARIANT', 'no_override', 'Is the precision beyond which interest accrued is truncated', 'MANUAL', 'MANUAL'),
--     ('84d9612d-b352-4103-a302-63357e344b8f', 1, 'business', 'max_asset_amount', 'NULL', 'DOUBLE', 'NO_OVERRIDE', 'no_override', 'Is the maximum asset capability requirement from BNM foundation ', 'MANUAL', 'MANUAL'),


-- TABLE_NAME :  product
-- Queries :
INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
VALUES
    ('1425f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, 'RETAIL_SAVINGS', 'Retail savings deposits', ' The retail savings deposits product', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_variant
-- Queries :
INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to, created_by, updated_by)
VALUES
    ('91857c7a-a5b5-465b-ba27-cc4f506260bd', 1, 'DEPOSITS_ACCOUNT', '1', 'Defaults retail savings deposits', 'The defaults retail savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MANUAL', 'MANUAL'),
    ('7ed658f8-764e-4cea-8695-26c1e108762d', 1, 'SAVINGS_POCKET', '2', 'Defaults retail savings deposits', 'The defaults retail savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_variant_parameter
-- Queries :
INSERT INTO product_variant_parameter (public_id, product_variant_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
VALUES
    ('1211e4ee-7f4f-11ec-93b7-a2f7a4e8ba3d', 1, 'business', 'account_applicable_features', '{"MAIN_ACCOUNT": true}','STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account', 'MANUAL','MANUAL'),
    ('c7bbdc57-5592-471c-85da-030487ffa9e9', 2, 'business', 'account_applicable_features', '{"POCKET": true}', 'STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account','MANUAL', 'MANUAL'),
    ('6eee7dac-1225-4200-b2d1-5e6e1805d479', 1, 'business', 'tm_product_id','deposit_product', 'STRING', 'NO_OVERRIDE',NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
    ('784cbc30-4e6b-4f1b-af41-6929422ea1d1', 2, 'business','tm_product_id', 'deposit_product', 'STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL');
--     ('0a56ad10-cadf-4633-8881-6092a589b3dc', 2, 'business', 'max_pockets_per_customer', '10', 'INT', 'NO_OVERRIDE', 'NULL', 'Limit to how many pockets a customer is allowed to have', 'MANUAL', 'MANUAL'),
--     ('2d6b7dfd-4f57-4ca5-86c7-febfe41f5c00', 2, 'business', 'min_pocket_balance', '0', 'DOUBLE', 'NO_OVERRIDE', 'NULL', 'Is the minimum balance that a pocket is allowed to have', 'MANUAL', 'MANUAL'),
--     ('5cbbcd30-90a2-4484-9c72-6a9cc9d59344', 2, 'business', 'max_pocket_balance', '18446700000000000000', 'DOUBLE', 'NO_OVERRIDE', 'NULL', 'Is the maximum balance that a pocket is allowed to have', 'MANUAL', 'MANUAL'),
--     ('8227771b-5f56-4052-b1be-90d53f94bf71', 2, 'business', 'cash_in_account', 'DEFAULT', 'ENUM', 'NO_OVERRIDE', 'NULL', "Is the fund in account of a customer. DEFAULT will be customer's main savings account", 'MANUAL', 'MANUAL'),
--     ('649eadae-3795-4d49-a6b5-a4618e338787', 2, 'business', 'cash_out_account', 'DEFAULT', 'ENUM', 'NO_OVERRIDE', 'NULL', "Is the cash out account of a customer. DEFAULT will be customer's main savings account", 'MANUAL', 'MANUAL'),
--     ('b9f9eec8-067a-4155-a67d-729506f137ad', 2, 'business', 'pocket_name', 'DEFAULT', 'STRING', 'AT_ACCOUNT', 'NO_OVERRIDE', 'Is the pocket name', 'MANUAL', 'MANUAL'),
--     ('9de389e4-40b0-4c41-a0b2-474c983c9ead', 2, 'business', 'pocket_type', 'DEFAULT', 'ENUM', 'AT_ACCOUNT', 'NO_OVERRIDE', 'Is the type of pocket, which can be DEFAULT, LOCKED_POTS', 'MANUAL', 'MANUAL'),
--     ('4d54b7b0-87bd-46fe-913c-cff235252aaa', 2, 'business', 'pocket_goal', 'DEFAULT', 'DOUBLE', 'AT_ACCOUNT', 'NO_OVERRIDE', 'Is the goal amount for a pocket', 'MANUAL', 'MANUAL'),
--     ('cf469053-306e-4f14-bbe0-7402abf81243', 2, 'business', 'pocket_goal_target_date', 'DEFAULT', 'ENUM', 'AT_ACCOUNT', 'NO_OVERRIDE', 'Is the pocket goal target date', 'MANUAL', 'MANUAL'),
--     ('eddc3a9f-0765-4d87-983f-5f5c6f890e26', 2, 'pricing', 'interest_posting_frequency', 'END_OF_GOAL', 'ENUM', 'NO_OVERRIDE', 'NO_OVERRIDE', 'Denotes the frequency of payout in DAYS, WEEKS, MONTHS, END_OF_GOAL', 'MANUAL', 'MANUAL'),
--     ('4d41a2f2-0f6d-4992-a8fc-9f4915b14338', 2, 'pricing', 'interest_posting_frequency_units', '1', 'INT', 'NO_OVERRIDE', 'NO_OVERRIDE', 'Denotes the number of accrual frequency units for payout schedule', 'MANUAL', 'MANUAL'),
--     ('8232f4a5-a416-4c3a-acee-d818ce9e44c6', 2, 'pricing', 'interest_accrual_roundoff_precision_digits', '5', 'INT', 'NO_OVERRIDE', 'NO_OVERRIDE', 'Is the precision beyond which interest accrued is truncated', 'MANUAL', 'MANUAL'),
--     ('5dec558e-5035-41b0-b265-6d93fb35647c', 2, 'business', 'interest_posting_day', 'DEFAULT', 'ENUM', 'NO_OVERRIDE', 'NULL', 'The day of the month on which interest is applied. DEFAULT will be the pocket close day.', 'MANUAL', 'MANUAL'),
--     ('74fbb8a2-16b2-454f-96fb-f7675bfa5aac', 2, 'risk & business', 'min_customer_age', '18', 'INT', 'AT_PRODUCT_VARIANT', 'NULL', 'Is the min age of the customer', 'MANUAL', 'MANUAL'),
--     ('4cdcac6f-53f6-4b82-a287-154369aa180b', 2, 'business', 'auto_close_triggers', 'SYSTEM_DEBIT_INSUFFICIENT_BALANCE, OPENED_STATUS_AGING, DORMANCY_AGING', 'ENUM', 'AT_PRODUCT_VARIANT', 'NULL', 'Are the events that will triggered account auto close', 'MANUAL', 'MANUAL'),


-- TABLE_NAME :  transaction_catalogue
-- Queries :
INSERT INTO transaction_catalogue (public_id,`domain`,is_financial_txn,txn_type,txn_sub_type,display_name,description,status,created_by,updated_by)
VALUES
    ('e5b9b416-6c4c-422b-bade-35e169bf5278', 'deposits', 1, 'receive_money', 'rpp_network', 'Receive money from Paynet Network', "Incoming Paynet/ PayNow funds transfer to Customer's CASA Account", 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('8f01309e-04e2-43c1-b6eb-7516bd276f47', 'deposits', 1, 'receive_money_reversal', 'rpp_network', 'Reversal of receive money from Paynet Network', "Reversal of incoming Paynet/ PayNow funds transfer to Customer's CASA Account", 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('49d58928-319f-444e-966c-d8d8cebbb960', 'deposits', 1, 'transfer_money', 'intrabank', 'Funds transfer to DBMY Bank Account', 'Intrabank funds transfer between CASA Accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('07fd8780-4997-4d2f-bef1-256a62099dd5', 'deposits', 1, 'transfer_money_reversal', 'intrabank', 'Reversal of funds transfer to DBMY Bank Account', 'Reversal of intrabank funds transfer between CASA Accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('02ee0ed0-2391-4bbe-9809-c53f32b365f5', 'deposits', 1, 'send_money', 'rpp_network', 'Send money through Paynet Network', 'Outgoing Paynet/ PayNow funds transfer initiated by the DBMY Customer - auth', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('ec139f63-cfb5-441f-951d-4ed5c5de454a', 'deposits', 1, 'send_money_reversal', 'rpp_network', 'Reversal of send money through Paynet Network', 'Reversal of outgoing Paynet/ PayNow funds transfer initiated by the DBMY Customer - auth', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('cbf74f70-cc3b-4080-bf2d-e613a1722a31', 'deposits', 1, 'settlement', 'rpp_network', 'Periodic settlement to Paynet Network', 'Periodic settlement to Paynet Network', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('8bc03288-85e6-4e13-bde9-ce4a57194fbc', 'deposits', 1, 'settlement_reversal', 'rpp_network', 'Reversal of periodic settlement to Paynet Network', 'Reversal of periodic settlement to Paynet Network', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('9bce2517-c0b8-4fbd-a5cc-fa85ebca1281', 'deposits', 1, 'interest_accrual', 'savings', 'Interest accrual for savings accounts', 'Interest accrual for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('3af1dcd7-c2c1-405c-9094-215d12d99bf1', 'deposits', 1, 'interest_accrual_reversal', 'savings', 'Reversal of interest accrual for savings accounts', 'Reversal of interest accrual for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('584cd9a9-5f23-43c9-a1db-6b8c0d855848', 'deposits', 1, 'interest_payout', 'savings', 'Interest payout for savings accounts', 'Interest payout for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('9007e422-9ec0-48f3-acae-ae6fcdca1723', 'deposits', 1, 'interest_payout_reversal', 'savings', 'Reversal of interest payout for savings accounts', 'Reversal of interest payout for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_variant_transaction_catalogue_mapping
-- Queries :
INSERT INTO product_variant_transaction_catalogue_mapping (public_id,product_variant_id,transaction_catalogue_id,status,created_by,updated_by)
VALUES
    ('4bc3943b-6451-47e3-8a51-634c304dcf0c', 1, 1, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('********-18ce-4e0b-b46c-16b1cf1053ab', 1, 2, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('612d5947-7e14-453a-b88d-53b7bba56490', 1, 3, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('5e71ff7b-b7d4-4da6-975f-679891d61b78', 1, 4, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('ccb69576-0ec6-446d-9a18-cc23277b7643', 1, 5, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('8ade3a53-ed5c-4a1f-9091-6ce5b75ce7c4', 1, 6, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('1cf48003-8959-4b63-a9a6-7f6b94810996', 1, 9, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('a5895089-226e-4d19-a23a-3533c61e93f7', 1, 10, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4d86b345-fe11-470d-8c1d-27ef08ec17c4', 1, 11, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('fedbda61-c68a-484c-bfd0-ef812cfb1a51', 1, 12, 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  general_ledger
-- Queries :
INSERT INTO general_ledger (public_id, code, name, description, currency, status, created_by, updated_by)
VALUES
    ('b764685e-4cb4-4a4f-94cd-20983c73da19', '203112', 'Saving accounts', 'Saving accounts', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('50e8302f-7096-45e1-9045-19af99e276f3', '203119', 'Dormant customer accounts', 'Dormant customer accounts', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('33f18996-6fcb-4c0c-a91b-f8a87a3a4533', '401212', 'Int exp savings accounts', 'Int exp savings accounts', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('d8c7134d-3413-4a2a-8b53-1afc08965299', '209112', 'Accrued interest payables', 'Accrued interest payables', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('fada6e4a-18b3-4704-a860-9d1b07d53d29', '209412', 'Clearing - Paynet', 'Clearing - Paynet', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('f82d677e-b0ca-48cb-b859-f6c72f90ceab', '209413', 'Clearing - GIRO', 'Clearing - GIRO', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('d1e08d7d-856b-4e7a-923f-ab1455eaccc4', '101113', 'Statutory deposits with central bank', 'Statutory deposits with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  internal_account
-- Queries :
INSERT INTO internal_account (public_id, general_ledger_id, code, name, description, currency, status, created_by, updated_by)
VALUES
    ('a32c5ac3-8232-4810-a88a-c8303ce4f360', 3, '*********', 'Savings Interest Expense Account', 'Savings Interest Expense Account', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('e9e088ad-609a-49d8-abd4-f7d8fcb3bff8', 5, '*********', 'Clearing - Paynet', 'Clearing - Paynet', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('f4f42abe-1492-4495-976e-ac019b597891', 6, '*********', 'Clearing - GIRO', 'Clearing - GIRO', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('92b8aed4-95a6-4cad-b0dd-1018845847c2', 7, '*********', 'Statutory deposits with central bank', 'Statutory deposits with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  deposit_interest
-- Queries :
INSERT INTO deposit_interest (public_id, product_variant_id, is_linked_to_base_rate, base_interest_id, code, name, description, currency, round_off_type, interest_slab_type, interest_slab_structure, created_by, updated_by)
VALUES
    ('1045c523-38c4-4c85-80c0-28172ea103b0', 1, 0, NULL, 'DEFAULT_SAVINGS_INTEREST', 'Default Savings Interest', 'Default Savings Interest', 'MYR', 'HALF_ROUND_UP', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL'),
    ('5407b327-f122-4f71-befb-6d3318847c16', 2, 0, NULL, 'DEFAULT_SAVINGS_POCKET_INTEREST', 'Default Savings Pocket Interest', 'Default Savings Pocket Interest', 'MYR', 'HALF_ROUND_UP', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  deposit_interest_version
-- Queries :
INSERT INTO deposit_interest_version (public_id,deposit_interest_id, version, effective_date, description, created_by, updated_by)
VALUES
    ('3bfaaf73-7296-4856-8e22-ed41802e97f1', 1, '1', CURRENT_TIMESTAMP, 'Default Savings Interest v1.0', 'MANUAL', 'MANUAL'),
    ('15de0517-f2c8-4ce7-b3a3-a26ed8e66bab', 2, '1', CURRENT_TIMESTAMP, 'Default Pockets Interest v1.0', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  deposit_interest_amount_slab_rate
-- Queries :
INSERT INTO deposit_interest_amount_slab_rate (public_id,deposit_interest_version_id, from_amount, to_amount, base_rate_interest_spread_percentage, absolute_interest_rate_percentage, created_by, updated_by)
VALUES
    ('4ad1e5dc-d4d6-4961-b01e-1f440bcbe6f5', 1, '0', '999999999', '0', '1.00', 'MANUAL', 'MANUAL'),
    ('382ece3c-9d9f-459c-b3c5-82dd63122ab0', 2, '0', '999999999', '0', '1.00', 'MANUAL', 'MANUAL');

-- Product Master DB Insert Queries For CASA

-- TABLE_NAME :  product_template
-- Queries :
INSERT INTO product_template (public_id, code, name, description, status, created_by, updated_by)
VALUES
    ('bf8658c8-9187-415b-af9c-79c8449d305a', 'SAVINGS_DEPOSIT', 'Savings Account', 'The savings account master product', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_template_parameter
-- Queries :
INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
VALUES
    ('04df6c85-7f4e-11ec-93b7-0ac04b8a5758', 1, 'business', 'account_applicable_features', 'NULL', 'STRING', 'AT_PRODUCT_VARIANT', NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
    ('97ce86f2-9521-45fb-90ca-ae6c01e726c0', 1, 'business', 'tm_product_id', 'deposit_product', 'STRING', 'AT_PRODUCT_VARIANT', NULL,'The features that are applicable to an account','MANUAL','MANUAL'),
    ('a18f2f66-1ff7-4d7b-b20b-e37d22102771', 1, 'risk', 'allowed_hold_codes', 'ADVERSE_NEWS, AML/SANCTION_HITS, BANKRUPT, DECEASED, FRAUD_ACTOR, FRAUD_VICTIM, MENTAL_INCAPACITY, MISSING_DEVICE, MULE_ACCOUNT, POLICE/COURT/SEIZURE/LEA_ORDER, KILL_SWITCH, GARNISHEE/CLAWBACK, BANK_RESTRICTED', 'ENUM', 'NO_OVERRIDE', 'no_override', 'Is the list of allowed hold codes for this product', 'MANUAL', 'MANUAL'),
    ('967f5a6e-367c-4226-a236-5eeb558e8cc1', 1, 'pricing', 'interest_posting_frequency', 'DAYS', 'ENUM', 'AT_PRODUCT_VARIANT', 'no_override', 'Denotes the frequency of payout in DAYS, WEEKS, MONTHS, END_OF_GOAL', 'MANUAL', 'MANUAL'),
    ('9c987a72-43ac-43c7-983d-fbcc54b7941b', 1, 'business', 'interest_posting_account', 'SELF_ACCOUNT', 'ENUM', 'NO_OVERRIDE', NULL, 'The account to which accrued interest can be posted to / applied to.', 'MANUAL', 'MANUAL'),
    ('0be8381f-24e9-4b79-9a72-db9b6057e452', 1, 'business', 'interest_posting_day', '1', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'The day of the month on which interest is applied. Will be ignored if interest_posting_frequency is DAILY or lesser', 'MANUAL', 'MANUAL');
    ('e840fb04-84ea-11ed-a1eb-0242ac120002', 1, 'business', 'earmark_reason_codes', 'CLAWBACK, CUSTOMER_DISPUTE/ERRONEOUS_CREDIT, DEBIT_CARD_DISPUTE, GARNISHEE_ORDER, POLICE/COURT/SEIZURE/LEA_ORDER', 'ENUM', 'NO_OVERRIDE', 'NO_OVERRIDE', 'Is the list of reason codes that are applicable for earmarking an account', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product
-- Queries :
INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
VALUES
    ('1425f3fc-75dd-45ae-bcc4-a2f7a4e8ba3d', 1, 'RETAIL_SAVINGS', 'Retail savings deposits', ' The retail savings deposits product', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_variant
-- Queries :
INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to, created_by, updated_by)
VALUES
    ('91857c7a-a5b5-465b-ba27-cc4f506260bd', 1, 'DEPOSITS_ACCOUNT', '1', 'Defaults retail savings deposits', 'The defaults retail savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MANUAL', 'MANUAL'),
    ('7ed658f8-764e-4cea-8695-26c1e108762d', 1, 'SAVINGS_POCKET', '2', 'Defaults retail savings deposits', 'The defaults retail savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_variant_parameter
-- Queries :
INSERT INTO product_variant_parameter (public_id, product_variant_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
VALUES
    ('1211e4ee-7f4f-11ec-93b7-a2f7a4e8ba3d', 1, 'business', 'account_applicable_features', '{"MAIN_ACCOUNT": true}','STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account', 'MANUAL','MANUAL'),
    ('c7bbdc57-5592-471c-85da-030487ffa9e9', 2, 'business', 'account_applicable_features', '{"POCKET": true}', 'STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account','MANUAL', 'MANUAL'),
    ('6eee7dac-1225-4200-b2d1-5e6e1805d479', 1, 'business', 'tm_product_id','deposit_product', 'STRING', 'NO_OVERRIDE',NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
    ('784cbc30-4e6b-4f1b-af41-6929422ea1d1', 2, 'business','tm_product_id', 'deposit_product', 'STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  transaction_catalogue
-- Queries :
INSERT INTO transaction_catalogue (public_id,`domain`,is_financial_txn,txn_type,txn_sub_type,display_name,description,status,created_by,updated_by)
VALUES
    ('e5b9b416-6c4c-422b-bade-35e169bf5278', 'deposits', 1, 'receive_money', 'rpp_network', 'Receive money from Paynet Network', "Incoming Paynet funds transfer to Customer's CASA Account", 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('8f01309e-04e2-43c1-b6eb-7516bd276f47', 'deposits', 1, 'receive_money_reversal', 'rpp_network', 'Reversal of receive money from Paynet Network', "Reversal of incoming Paynet funds transfer to Customer's CASA Account", 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('49d58928-319f-444e-966c-d8d8cebbb960', 'deposits', 1, 'transfer_money', 'intrabank', 'Funds transfer to DBMY Bank Account', 'Intrabank funds transfer between CASA Accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('07fd8780-4997-4d2f-bef1-256a62099dd5', 'deposits', 1, 'transfer_money_reversal', 'intrabank', 'Reversal of funds transfer to DBMY Bank Account', 'Reversal of intrabank funds transfer between CASA Accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('02ee0ed0-2391-4bbe-9809-c53f32b365f5', 'deposits', 1, 'send_money', 'rpp_network', 'Send money through Paynet Network', 'Outgoing Paynet funds transfer initiated by the DBMY Customer - auth', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('ec139f63-cfb5-441f-951d-4ed5c5de454a', 'deposits', 1, 'send_money_reversal', 'rpp_network', 'Reversal of send money through Paynet Network', 'Reversal of outgoing Paynet funds transfer initiated by the DBMY Customer - auth', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('cbf74f70-cc3b-4080-bf2d-e613a1722a31', 'deposits', 1, 'settlement', 'rpp_network', 'Periodic settlement to Paynet Network', 'Periodic settlement to Paynet Network', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('6f3dff4e-fe72-4232-82b9-b6e690c5672f', 'deposits', 1, 'fund_in', 'rpp_network', 'Receive money from Duitnow Online Banking', '', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('37f2f90a-d181-4bcb-8caa-94411c01d9d6', 'deposits', 1, 'fund_in_reversal', 'rpp_network', 'Reversal of receive money from Duitnow Online Banking', '', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('bbdea39d-a0c9-4da4-9e0d-866fb34a8c17', 'deposits', 1, 'transfer_money', 'from_main_to_pocket', 'Funds transfer to DBMY Bank Account', 'For funds transfer between main CASA account & Pocket (Savings, Spend)', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('33315b96-3328-45ce-938c-985a00705a1f', 'deposits', 1, 'transfer_money', 'from_pocket_to_main', 'Funds transfer to DBMY Bank Account', 'For funds transfer between Pocket (Savings, Spend) & main CASA account', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('584cd9a9-5f23-43c9-a1db-6b8c0d855848', 'deposits', 1, 'interest_payout', 'savings', 'Interest payout for savings accounts', 'Interest payout for savings accounts', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('********-f651-4ac0-85ec-34e141f150be', 'deposits', 1, 'adjustment', 'bank_initiated', 'Adjustment done by Ops team', 'Adjustment done by Ops team', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_variant_transaction_catalogue_mapping
-- Queries :
INSERT INTO product_variant_transaction_catalogue_mapping (public_id,product_variant_id,transaction_catalogue_id,status,created_by,updated_by)
VALUES
    ('4bc3943b-6451-47e3-8a51-634c304dcf0c', 1, 1, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('********-18ce-4e0b-b46c-16b1cf1053ab', 1, 2, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('612d5947-7e14-453a-b88d-53b7bba56490', 1, 3, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('5e71ff7b-b7d4-4da6-975f-679891d61b78', 1, 4, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('ccb69576-0ec6-446d-9a18-cc23277b7643', 1, 5, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('8ade3a53-ed5c-4a1f-9091-6ce5b75ce7c4', 1, 6, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('1cf48003-8959-4b63-a9a6-7f6b94810996', 1, 8, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('017d10b4-bd84-43d4-a6a8-d48999ae60a2', 1, 9, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('01ee40be-e6ba-4d4c-a018-b64286d3f650', 1, 10, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('fedbda61-c68a-484c-bfd0-ef812cfb1a51', 1, 11, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('5cc59245-68c0-479c-8f06-30770eb1a831', 1, 12, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('a5895089-226e-4d19-a23a-3533c61e93f7', 1, 13, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4d86b345-fe11-470d-8c1d-27ef08ec17c4', 2, 10, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('d590ffb6-a905-4bd2-adc4-4921f9bd2af6', 2, 11, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('586c61e2-7e8d-4025-b4af-a10c07aedd0a', 2, 12, 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('a2f9bff8-3e29-4f5e-b7ef-82dad5238ac3', 2, 13, 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  general_ledger
-- Queries :
INSERT INTO general_ledger (public_id, code, name, description, currency, status, created_by, updated_by)
VALUES
    ('b764685e-4cb4-4a4f-94cd-20983c73da19', '101111', 'Non-restricted balances with central bank', 'Non-restricted balances with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4d36921f-dedb-4479-8e34-9c95fc306359', '101112', 'Restricted balances with central bank', 'Restricted balances with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('f4e2f97d-5627-45f2-bd5e-c47ffd796268', '104121', 'Cash at bank (MYR) - MBB', 'Cash at bank (MYR) - MBB', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('7b2840d8-55e8-459c-bbee-47526e20d6b9', '104124', 'Cash at bank (MYR) - RHB', 'Cash at bank (MYR) - RHB', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('98b52f78-dd9a-4d80-b46c-72679254ac71', '209425', 'Clearing - Paynet', 'PayNet clearing account', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('e0ab625d-897d-465f-ac81-20dd157fd1b2', '401212', 'Interest Paid Account', 'Savings Interest Expense Account', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('668f0eee-ce3e-44f4-a1ca-1bb5c0eb628e', '501312', 'Customer rewards', 'Customer rewards',  'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('06b9411f-7b33-47c5-8bb4-87601c7cb419', '501516', 'PayNet Transaction Fees', 'PayNet Transaction Fees', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('b57ed47e-dccb-4786-a658-ffa01193503c', '501696', 'Bank Charges', 'Bank Charges', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('bbdbdd05-204b-4349-b894-25e86ca44860', '501722', 'CASA Operational Losses', 'CASA Operational Losses', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ea2b3a8-18d3-4861-b9fd-4af23cf393e4', '209421', 'Grab Ecosystem (Pay Out) - Clearing', 'Grab Ecosystem (Pay Out) - Clearing', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('38aa1562-8166-47c4-819b-6a0f31e7fede', '503134', 'Business Permits And Licenses (Initial)', 'Business Permits And Licenses (Initial)', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('5a252d30-315f-48cf-8204-0bbdf7459b7d', '503135', 'Business Permits And Licenses (Renewal/annual)', 'Business Permits And Licenses (Renewal/annual)', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('8a8caca2-9de6-4cc5-9ba9-b6fb4c745bf0', '501517', 'Clearing Fees', 'Clearing Fees', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('839e8e6e-5dea-4836-b444-c3ddbb52b4a7', '501511', 'Svc charge Bank/Agency', 'Svc charge Bank/Agency', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('7078d89e-a0e9-4fa1-943a-0aedd4b51c97', '501711', 'Fines and Penalty', 'Fines and Penalty', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('718fcc89-3060-43c4-bed4-6cebcbf0fa2c', '501671', 'Business Continuity Plan', 'Business Continuity Plan', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('650f3db9-4e7f-4a9e-afe8-74f6f6514e74', '501111', 'Backend Infrastructure & Cloud Hosting Services', 'Backend Infrastructure & Cloud Hosting Services', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4e096df1-ef7d-43e5-91f2-925c877d90c8', '503128', 'Professional/Consultancy Fees - Revenue', 'Professional/Consultancy Fees - Revenue', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('d0b87f99-d8a1-4918-9e2e-b4f5ec5eae0f', '503129', 'Professional/Consultancy Fees - Capital', 'Professional/Consultancy Fees - Capital', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('a57467e3-3808-43d4-af3c-982018c20f95', '101113', 'Custody account with central bank', 'Custody account with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  internal_account
-- Queries :
INSERT INTO internal_account (public_id, general_ledger_id, code, name, description, currency, status, created_by, updated_by)
VALUES
    ('cf4bfb90-fef6-4121-9786-d54297b29352', 1, '*********', 'Non-restricted balances with central bank', 'Non-restricted balances with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('ddd9991f-7204-413f-a71b-48694d9bd99a', 2, '*********', 'Restricted balances with central bank', 'Restricted balances with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('b5d32bf1-0e38-4b41-aa66-90ee1a0ffcf4', 3, '*********', 'Cash at bank (MYR) - MBB', 'Cash at bank (MYR) - MBB', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('c823be7c-aeff-4055-a8a9-7d7283e09891', 4, '*********', 'Cash at bank (MYR) - RHB', 'Cash at bank (MYR) - RHB', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('13b4501b-8cc6-47e0-866b-5fe10fdd59d5', 5, '*********', 'Clearing - Paynet', 'Clearing - Paynet', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('55399cc2-0618-4855-b36d-a761d0cee3ae', 6, '*********', 'Interest Paid Account', 'Savings Interest Expense Account', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('a6aadb4d-45ac-4831-a4ab-ce79c1648848', 7, '*********', 'Customer rewards (Grab unlimited)', 'Rewards for Grab unlimited', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('5c9d9a13-d7c8-4d9f-9610-629af2c633d6', 7, '*********', 'Customer rewards (NAD)', 'Rewards for NAD', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('f8f7084b-b984-461a-9615-8b573e091188', 7, '*********', 'Customer rewards (Funding)', 'Rewards for funding', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('91b2dfa9-f00c-4785-8c83-9d77f06507e0', 8, '*********', 'PayNet Transaction Fees', 'PayNet Transaction Fees', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('88f6ad1d-c20c-46bd-82f7-478d03901f47', 9, '*********', 'Bank Charges', 'Bank Charges', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('e9e088ad-609a-49d8-abd4-f7d8fcb3bff8', 10, '*********', 'CASA Operational Losses', 'CASA Operational Losses Account', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('57a51ce9-c752-4b52-88d6-a39039a992ff', 11, '*********', 'Grab Ecosystem (Pay Out) - Clearing', 'Grab Ecosystem (Pay Out) - Clearing', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('7b24b1ae-2223-4969-968b-6941382bb37f', 12, '*********', 'Business Permits And Licenses (Initial)', 'Business Permits And Licenses (Initial)', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4c38d5c3-8fb3-4fb8-ac6a-cc3a135a4fe5', 13, '*********', 'Business Permits And Licenses (Renewal/annual)', 'Business Permits And Licenses (Renewal/annual)', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('98d8a3e0-906a-49b5-ab4f-c891d02cc356', 14, '*********', 'Clearing Fees', 'Clearing Fees', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('598aef77-af52-47e4-890f-7dcc0c2567ed', 15, '*********', 'Svc charge Bank/Agency', 'Svc charge Bank/Agency', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('3f10eed9-7d5e-47aa-bff2-f0311a911f7a', 16, '*********', 'Fines and Penalty', 'Fines and Penalty', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('3ea627e7-0a4e-47fa-8c99-e85c0ba5aab5', 17, '*********', 'Business Continuity Plan', 'Business Continuity Plan', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('f226662d-c817-47d3-a1c4-3e647a52e06c', 18, '*********', 'Backend Infrastructure & Cloud Hosting Services', 'Backend Infrastructure & Cloud Hosting Services', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('78b7a96a-48d9-4863-9cff-42673f07130a', 19, '*********', 'Professional/Consultancy Fees - Revenue', 'Professional/Consultancy Fees - Revenue', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('175eaed3-e591-4031-b623-57179dcdeeba', 20, '*********', 'Professional/Consultancy Fees - Capital', 'Professional/Consultancy Fees - Capital', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('f6df6d61-2119-4d12-b0de-1683d85cf3dd', 21, '*********', 'Custody account with central bank', 'Custody account with central bank', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  deposit_interest
-- Queries :
INSERT INTO deposit_interest (public_id, product_variant_id, is_linked_to_base_rate, base_interest_id, code, name, description, currency, round_off_type, interest_slab_type, interest_slab_structure, created_by, updated_by)
VALUES
    ('1045c523-38c4-4c85-80c0-28172ea103b0', 1, 0, NULL, 'DEFAULT_SAVINGS_INTEREST', 'Default Savings Interest', 'Default Savings Interest', 'MYR', 'HALF_ROUND_UP', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL'),
    ('5407b327-f122-4f71-befb-6d3318847c16', 2, 0, NULL, 'DEFAULT_SAVINGS_POCKET_INTEREST', 'Default Savings Pocket Interest', 'Default Savings Pocket Interest', 'MYR', 'HALF_ROUND_UP', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  deposit_interest_version
-- Queries :
INSERT INTO deposit_interest_version (public_id,deposit_interest_id, version, effective_date, description, created_by, updated_by)
VALUES
    ('3bfaaf73-7296-4856-8e22-ed41802e97f1', 1, '1', CURRENT_TIMESTAMP, 'Default Savings Interest v1.0', 'MANUAL', 'MANUAL'),
    ('15de0517-f2c8-4ce7-b3a3-a26ed8e66bab', 2, '1', CURRENT_TIMESTAMP, 'Default Pockets Interest v1.0', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  deposit_interest_amount_slab_rate
-- Queries :
INSERT INTO deposit_interest_amount_slab_rate (public_id,deposit_interest_version_id, from_amount, to_amount, base_rate_interest_spread_percentage, absolute_interest_rate_percentage, created_by, updated_by)
VALUES
    ('4ad1e5dc-d4d6-4961-b01e-1f440bcbe6f5', 1, '0', '999999999', '0', '3.00', 'MANUAL', 'MANUAL'),
    ('382ece3c-9d9f-459c-b3c5-82dd63122ab0', 2, '0', '999999999', '0', '3.00', 'MANUAL', 'MANUAL');

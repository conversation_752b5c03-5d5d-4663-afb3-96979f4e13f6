-- Product Master DB Insert Queries For Pockets

-- TABLE_NAME :  pocket_template
-- Queries :
INSERT INTO pocket_template (public_id, type, name, status, created_by, updated_by)
VALUES
    ("350cf31e-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Emergency Savings",  "ACTIVE", "MANUAL", "MANUAL"),
    ("350cf7e8-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Holiday", "ACTIVE", "MANUAL", "MANUAL"),
    ("350cf9ac-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Investment", "ACTIVE", "MANUAL", "MANUAL"),
    ("350cfa99-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Home", "ACTIVE", "MANUAL", "MANUAL"),
    ("350cfb75-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Vehicle", "ACTIVE", "MANUAL", "MANUAL"),
    ("350cfc75-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Health & Fitness", "ACTIVE", "MANUAL", "MANUAL"),
    ("350cfd44-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Education", "ACTIVE", "MANUAL", "MANUAL"),
    ("350cfe2a-e63a-11ec-9dc2-0aed065bc0fc", "SAVINGS", "Custom", "ACTIVE", "MANUAL", "MANUAL");

-- TABLE_NAME :  pocket_template_question
-- Queries :
INSERT INTO pocket_template_question (public_id, pocket_template_id, question_text, locale, status, created_by, updated_by)
VALUES
    ("39f0ec27-e63a-11ec-9dc2-0aed065bc0fc", 1, "What is the purpose of this emergency saving?", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("39f0f056-e63a-11ec-9dc2-0aed065bc0fc", 2, "Bestnya holiday, where are you going?", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("39f0fb2d-e63a-11ec-9dc2-0aed065bc0fc", 3, "Let's keep your money rolling, where to start?", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("39f10110-e63a-11ec-9dc2-0aed065bc0fc", 4, "Home sweet home, what's the goal?", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("39f10232-e63a-11ec-9dc2-0aed065bc0fc", 5, "Vrooom vrooom! What are you saving for?", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("39f102f1-e63a-11ec-9dc2-0aed065bc0fc", 6, "Let's get fit! What's your plan?", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("39f103a0-e63a-11ec-9dc2-0aed065bc0fc", 7, "The future's looking bright! Jom start?", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("39f1045d-e63a-11ec-9dc2-0aed065bc0fc", 8, "What are you up to?", "EN", "ACTIVE", "MANUAL", "MANUAL");

-- TABLE_NAME :  pocket_template_answer_suggestion
-- Queries :
INSERT INTO pocket_template_answer_suggestion (public_id, pocket_template_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES
    ("3e78c24f-e63a-11ec-9dc2-0aed065bc0fc", 1, "Rainy days", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78c856-e63a-11ec-9dc2-0aed065bc0fc", 1, "Sudden expenses", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78c9fc-e63a-11ec-9dc2-0aed065bc0fc", 1, "Sickness", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78cdb7-e63a-11ec-9dc2-0aed065bc0fc", 2, "Overseas", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78cef7-e63a-11ec-9dc2-0aed065bc0fc", 2, "Beach resort", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d005-e63a-11ec-9dc2-0aed065bc0fc", 2, "Theme park", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d293-e63a-11ec-9dc2-0aed065bc0fc", 3, "Retirement plan", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d395-e63a-11ec-9dc2-0aed065bc0fc", 3, "Property", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d4b6-e63a-11ec-9dc2-0aed065bc0fc", 3, "Gold", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d5c5-e63a-11ec-9dc2-0aed065bc0fc", 4, "Downpayment", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d6ab-e63a-11ec-9dc2-0aed065bc0fc", 4, "Renovation", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d7a6-e63a-11ec-9dc2-0aed065bc0fc", 4, "Furniture", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d8ad-e63a-11ec-9dc2-0aed065bc0fc", 5, "Car", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78d99a-e63a-11ec-9dc2-0aed065bc0fc", 5, "Bike", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78dff6-e63a-11ec-9dc2-0aed065bc0fc", 5, "Service", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78e6c6-e63a-11ec-9dc2-0aed065bc0fc", 6, "Fitness Equipment", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78e80f-e63a-11ec-9dc2-0aed065bc0fc", 6, "Medical Check-up", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78e93d-e63a-11ec-9dc2-0aed065bc0fc", 6, "Supplement", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78ea43-e63a-11ec-9dc2-0aed065bc0fc", 7, "Kids education", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78eaf2-e63a-11ec-9dc2-0aed065bc0fc", 7, "Certifications", "EN", "ACTIVE", "MANUAL", "MANUAL"),
    ("3e78ebc7-e63a-11ec-9dc2-0aed065bc0fc", 7, "Tuition Fee", "EN", "ACTIVE", "MANUAL", "MANUAL");

-- TABLE_NAME :  pocket_template_image_suggestion
-- Queries :
INSERT INTO pocket_template_image_suggestion (public_id, pocket_template_id, image_id, status, created_by, updated_by)
VALUES
    ('a610fb5a-36ba-4877-b308-4cfe327b7844', 1, 'bbcade0a-2284-4ca3-bdf7-3a93768b8a64', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('aad5a6e7-5ef4-4326-a3a8-b9222b3fcb84', 1, '75bc0750-e440-4d92-8e96-d6957f97db98', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('493262a6-852d-4639-bbc7-4855add865e0', 1, 'abaddd3e-eca4-4fd0-b3d5-384f79be9627', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba8f902-fda1-11ec-b9d1-b21c76fd90da', 2, '9e36f763-1556-4bfa-95ae-590e97ef798a', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba942a4-fda1-11ec-b9d1-b21c76fd90da', 2, 'f623b0ae-9667-4019-94c8-4b3cb136f96b', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba94542-fda1-11ec-b9d1-b21c76fd90da', 2, '403ccaf6-0cd2-42be-bed5-0d027d9b1574', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba94b3c-fda1-11ec-b9d1-b21c76fd90da', 3, 'c90e9983-5320-44c6-abe8-6f92a46a478d', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba94c7c-fda1-11ec-b9d1-b21c76fd90da', 3, 'ff5a3f71-4869-40bc-a8bf-031537a2d818', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba94d6c-fda1-11ec-b9d1-b21c76fd90da', 3, '328a4aa0-d831-46ee-a684-c39b7f0f2a0c', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba946dc-fda1-11ec-b9d1-b21c76fd90da', 4, 'e651c711-b4f7-4089-b15b-ab6b1e8bed60', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba9486c-fda1-11ec-b9d1-b21c76fd90da', 4, '2c88236a-4787-41dc-b85e-43f2be5cda7a', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba949b6-fda1-11ec-b9d1-b21c76fd90da', 4, 'be2a86a3-84b6-4d84-8f6a-fce0958502a0', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba94e5c-fda1-11ec-b9d1-b21c76fd90da', 5, '881ef5d9-5a75-494c-a107-a5cc4d35f030', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba94f06-fda1-11ec-b9d1-b21c76fd90da', 5, '189bc03d-6b02-4485-8d9f-a8594ff66436', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba94fa6-fda1-11ec-b9d1-b21c76fd90da', 5, '8fcf6dae-3adc-4621-88a2-21e782ae14e2', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba9503c-fda1-11ec-b9d1-b21c76fd90da', 6, '994339a8-9192-4aac-b20d-f9bbb9995cb0', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba950d2-fda1-11ec-b9d1-b21c76fd90da', 6, '618eb8bb-1203-4da5-9789-8e009030dabf', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba95172-fda1-11ec-b9d1-b21c76fd90da', 6, '67894fb2-fe01-42c7-a530-779874238014', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba952d0-fda1-11ec-b9d1-b21c76fd90da', 7, 'c99f0b37-3c01-46b8-9c63-b9c1b05d0799', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba95424-fda1-11ec-b9d1-b21c76fd90da', 7, '7ed1d2b7-5964-413d-a7cb-77d1f9ff826b', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba955a0-fda1-11ec-b9d1-b21c76fd90da', 7, 'f79541d5-4da2-4a0e-a21d-d4ca84e7f496', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba95654-fda1-11ec-b9d1-b21c76fd90da', 8, '57fc801e-476e-4c84-8af0-1af74adaf3f6', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba956f4-fda1-11ec-b9d1-b21c76fd90da', 8, '00367dae-511c-4a50-a9e0-8b25a2391ca0', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('4ba95820-fda1-11ec-b9d1-b21c76fd90da', 8, '3e6c8689-7946-4746-9f53-14646da3369a', 'ACTIVE', 'MANUAL', 'MANUAL');
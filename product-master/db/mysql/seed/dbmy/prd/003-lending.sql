-- -- Product Master DB Insert Queries For Lending
-- -- LastUpdatedAt: 19/07/2022
--
-- -- TABLE_NAME :  product_variant_question
--
-- -- Queries :
--
-- INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 5, 'drawdown_name', 'What are you borrowing for?', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
--
-- INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 4, 'loc_closure_reason', 'Why you''re leaving us?', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
--
--
-- -- TABLE_NAME :  product_variant_answer_suggestion
--
-- -- Queries :
--
-- INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 1, 'Travel', 'EN',  'ACTIVE', 'MANUAL', 'MANU<PERSON>');
--
-- INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 1, 'Renovation', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
--
-- INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 1, 'Emergency', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
--
-- INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 2, 'Deceased', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
--
-- INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 2, 'Lost Interest', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
--
-- INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by)
-- VALUES (uuid(), 2, 'Others', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');
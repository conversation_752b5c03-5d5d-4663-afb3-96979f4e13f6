-- -- TODO Task
-- -- Product Master DB Insert Queries For BIZ
-- -- TABLE_NAME :  product_template
-- -- Queries :
-- INSERT INTO product_template (public_id, code, name, description, status, created_by, updated_by)
-- VALUES
--     ('c3420019-feb9-4bfc-a409-976bfa84620a', 'BIZ_SAVINGS_DEPOSIT', 'BIZ Savings Account', 'The biz savings account master product', 'ACTIVE', 'MANUAL', 'MANUAL');
--
-- -- TABLE_NAME :  product_template_parameter
-- -- Queries :
-- INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
-- VALUES
--     ('cea3bf6d-c42d-46a6-b4ee-dba1278a5514', 5, 'pricing', 'days_in_a_year', '365', 'ENUM', 'NO_OVERRIDE', NULL, 'Can be 360, 365 or ACTUAL', 'MANUAL', 'MANUAL'),
--     ('66b2f22b-f7d7-49b7-b91d-62420c223ca4', 5, 'business', 'max_account_per_business', '1', 'INT', 'AT_PRODUCT_VARIANT', NULL,'Limit to how many accounts of this product variant a customer is allowed to have','MANUAL','MANUAL'),
--     ('0fd814e3-7f0f-42aa-aad7-ce584fe3361e', 5, 'business', 'max_account_per_product_variant', '1', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'maximum interest rate offered', 'MANUAL', 'MANUAL'),
--     ('09cb7b7a-f073-4a9b-b0df-a0043fc3ba06', 5, 'business', 'max_account_holders_per_product_variant', '1', 'ENUM', 'AT_PRODUCT_VARIANT', NULL, 'Is the maximum balance limit beyond which only system-initiated credits are allowed and all other credits are rejected', 'MANUAL', 'MANUAL'),
--     ('a10205af-5887-4497-99b1-299f513407b1', 5, 'finance', 'allowed_currency', 'MYR', 'CURRENCY', 'NO_OVERRIDE', NULL, 'Allowed currencies for this product variant', 'MANUAL', 'MANUAL');
--
-- -- TABLE_NAME :  product
-- -- Queries :
-- INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
-- VALUES
--     ('c5d40641-e601-4c3d-ae11-4c29d4d3738e', 5, 'BIZ_SAVINGS', 'BIZ savings deposits', ' The BIZ savings deposits product', 'ACTIVE', 'MANUAL', 'MANUAL');
--
--
-- -- TABLE_NAME :  product_variant
-- -- Queries :
-- INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to, created_by, updated_by)
-- VALUES
--     ('4d44a6eb-d3f0-4ba7-b8cd-9b3fbe0b63f5', 6, 'BIZ_DEPOSIT_ACCOUNT', '7', 'BIZ savings deposits', 'The BIZ savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, '2100-12-31 09:44:28', 'MANUAL', 'MANUAL');
--
-- -- TABLE_NAME :  product_variant_parameter
-- -- Queries :
-- INSERT INTO product_variant_parameter (public_id, product_variant_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_by, updated_by)
-- VALUES
--     ('807ab5e8-325b-479f-afbd-403df0317337', 7, 'business', 'max_accounts_per_customer', '1','INT', 'AT_PRODUCT_VARIANT', NULL, 'Limit to how many accounts of this product variant a business is allowed to have', 'MANUAL','MANUAL'),
--     ('c976717f-8240-4b02-a4d6-2e062b4019ca', 7, 'business', 'maximum_interest_rate_offered', '0.15', 'FLOAT', 'AT_PRODUCT_VARIANT', NULL, 'maximum interest rate offered','MANUAL', 'MANUAL'),
--     ('3739e39d-53ff-408a-8e93-2382aef8ca2b', 7, 'business', 'soft_deposit_cap_limit','0', 'DOUBLE', 'AT_ACCOUNT',NULL, 'Is the maximum balance limit beyond which only system-initiated credits are allowed and all other credits are rejected', 'MANUAL', 'MANUAL'),
--     ('7d60003b-c875-4172-b3a8-d57d24873607', 7, 'business', 'hard_deposit_cap_limit', '0', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the maximum balance limit beyond which any credits are rejected', 'MANUAL', 'MANUAL'),
--     ('6e655f9a-782a-4067-908d-1bc78fe8e6f5', 7, 'business', 'tm_product_id','biz_deposit_product', 'STRING', 'NO_OVERRIDE',NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL');

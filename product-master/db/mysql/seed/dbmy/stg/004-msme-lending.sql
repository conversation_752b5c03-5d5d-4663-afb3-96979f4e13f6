-- Product Master DB Insert Queries For BIZ Lending
-- LastUpdatedAt: 01/09/2024

-- TABLE_NAME :  product
-- Queries :
INSERT INTO product (
    id, public_id, product_template_id, code, name, description, status, created_by, updated_by
) VALUES
      (
          7, uuid(), 3, 'BIZ_FLEXI_CREDIT_LINE_OF_CREDIT', 'BIZ Flexi Credit Line Of Credit', 'BIZ Flexi Credit Line Of Credit', 'ACTIVE', 'MANUAL', 'MANUAL'
      ),
      (
          8, uuid(), 4, 'BIZ_FLEXI_CREDIT_TERM_LOAN', 'BIZ Flexi Credit Term Loan', 'BIZ Flexi Credit Term Loan', 'ACTIVE', 'MANUAL', 'MANUAL'
      );

-- TABLE_NAME :  product_variant
-- Queries :
INSERT INTO product_variant (
    id, public_id, product_id, code, name, version, description, status, valid_from, valid_to, created_by, updated_by
) VALUES
      (
          8, uuid(), 7, 'DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT', 'Default BIZ Flexi Credit Line of Credit', 8, 'The default BIZ Flexi Credit Line of Credit product variant', 'ACTIVE', CURRENT_TIMESTAMP, '9999-12-31T15:59:59Z', 'MANUAL', 'MANUAL'
      ),
      (
          9, uuid(), 8, 'DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN', 'Default BIZ Flexi Credit Term Loan', 9, 'The default BIZ Flexi Credit Term Loan product variant', 'ACTIVE', CURRENT_TIMESTAMP, '9999-12-31T15:59:59Z', 'MANUAL', 'MANUAL'
      );

-- TABLE_NAME :  product_variant_question
-- Queries :
INSERT INTO product_variant_question (
    id, public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by
) VALUES
    (
        8, uuid(), 9, 'FLEXI_CREDIT_BORROW_REASON', 'What are you borrowing for?', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'
    );

-- TABLE_NAME :  product_variant_answer_suggestion
-- Queries :
INSERT INTO product_variant_answer_suggestion (
    public_id, product_variant_question_id, answer_suggestion_text, locale, status, created_by, updated_by
) VALUES
      (
          uuid(), 8, 'Rent and Utilities', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'
      ),
      (
          uuid(), 8, 'Equipment', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'
      ),
      (
          uuid(), 8, 'Renovation', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'
      ),
      (
          uuid(), 8, 'Supplies', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'
      ),
      (
          uuid(), 8, 'Payroll', 'EN', 'ACTIVE', 'MANUAL', 'MANUAL'
      );

-- TABLE_NAME :  loan_allowed_amount_tenor_slab
-- Queries :
INSERT INTO loan_allowed_amount_tenor_slab (
    product_variant_id, currency, from_amount, to_amount, tenor_unit, min_tenor, max_tenor, created_by, updated_by, created_at, updated_at
) VALUES
      (
          9, 'MYR', 1000, 1199, 'MONTH', 2, 20, 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ),
      (
          9, 'MYR', 1200, 1499, 'MONTH', 2, 24, 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ),
      (
          9, 'MYR', 1500, 1799, 'MONTH', 2, 30, 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ),
      (
          9, 'MYR', 1800, ************, 'MONTH', 2, 36, 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      );

-- TABLE_NAME :  loan_instruction_version
-- Queries :
INSERT loan_instruction_version(id, public_id, product_variant_id, version, instruction_type, effective_date,
                                     description, created_at, created_by, updated_at, updated_by)
VALUES (5, uuid(), 9, '1', 'ACCELERATE', CURRENT_TIMESTAMP, 'Default Biz Flexi Credit - Term Loan Accelerate Instruction v1.0 ', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
       (6, uuid(), 9, '1', 'WRITE_OFF', CURRENT_TIMESTAMP, 'Default Biz Flexi Credit - Term Loan WriteOff Instruction v1.0 ', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
       (7, uuid(), 9, '1', 'WAIVE_OFF', CURRENT_TIMESTAMP, 'Default Biz Flexi Credit - Term Loan WaiveOff Instruction v1.0 ', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
       (8, uuid(), 8, '1', 'DEACTIVATION', CURRENT_TIMESTAMP,'Default Biz Flexi Credit - Line of Credit Deactivation Instruction v1.0', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP,  'MANUAL'),
       (9, uuid(), 8, '1', 'BLOCK_UNBLOCK', CURRENT_TIMESTAMP, 'Default Biz Flexi Credit - Line of Credit Block/Unblock Instruction v1.0', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL');

-- TABLE_NAME :  loan_instruction
-- Queries :
INSERT loan_instruction (public_id, loan_instruction_version_id, code, name, restrictions ,created_at, created_by,
                              updated_at, updated_by)
values(uuid(), 5, 'DECEASED_NPL', 'Deceased NPL','{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 5, 'DECEASED_NON_NPL', 'Deceased Non NPL', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 5, 'BANKRUPT_NPL', 'Bankrupt NPL', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 5, 'BANKRUPT_NON_NPL', 'Bankrupt Non NPL', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 5, 'FRAUD_LOSS', 'Fraud Loss', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 5, 'DELINQUENT_SETTLEMENT', 'Delinquent Settlement', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 6, 'DECEASED_NPL', 'Deceased NPL', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 6, 'DECEASED_NON_NPL', 'Deceased Non NPL', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 6, 'BANKRUPT_NPL', 'Bankrupt NPL', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 6, 'BANKRUPT_NON_NPL', 'Bankrupt Non NPL', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 6, 'FRAUD_LOSS', 'Fraud Loss', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 6, 'DELINQUENT_SETTLEMENT', 'Delinquent Settlement', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 7, 'DELINQUENT_SETTLEMENT', 'Delinquent Settlement', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'DELINQUENT_SETTLEMENT', 'Delinquent Settlement', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'BANK_INITIATED_NON_VOLUNTARY', 'Bank Initiated Non Voluntary', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'CUSTOMER_INITIATED_VOLUNTARY', 'Customer Initiated Voluntary', '{}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'ADVERSE_NEWS', 'Adverse News', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'AML/SANCTION_HITS', 'AML Sanction Hits', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'FRAUD_ACTOR', 'Fraud Actor', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": true}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'FRAUD_VICTIM', 'Fraud Victim', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'MULE_ACCOUNT', 'Mule Account', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'POLICE/COURT/SEIZURE/LEA_ORDER', 'Police Court Seizure Lea-Order', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'DECEASED', 'Deceased', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'BANKRUPT', 'Bankrupt', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'MENTAL_INCAPACITY', 'Mental Incapacity', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'MISSING_DEVICE', 'Missing Device', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'KILL_SWITCH', 'Kill Switch', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'GARNISHEE/CLAWBACK', 'Garnishee Clawback', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'FRAUD_DOCUMENT', 'Fraud Document', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'CUSTOMER_RUNAWAY', 'Customer Runaway', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'CREDIT_LINE_DECREASED', 'Credit Line Decreased', '{"DRAWDOWN_BLOCKED": false,  "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'INVOLUNTARY_CLOSED_ACCOUNT', 'Involuntary Closed Account', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": true}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'EXTERNAL_R&R', 'External R and R', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'MORATORIUM', 'MORATORIUM', '{"DRAWDOWN_BLOCKED": false,  "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'DEBT_SALES', 'Debt Sales', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": true}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'INTEREST_WAIVER', 'Interest Waiver', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'SETTLEMENT_WAIVER', 'Settlement Waiver', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'NON_PERFORMING_LOAN', 'Non Performing Loan', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'WRITE_OFF', 'Write Off', '{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false}', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL');

-- TABLE_NAME :  loan_interest
-- Queries :
INSERT INTO loan_interest (
    id, public_id, product_variant_id, is_linked_to_base_rate, base_interest_id, code, name, description, currency, round_off_type, interest_slab_unit_type, interest_slab_structure, created_by, updated_by, interest_type, created_at, updated_at
) VALUES
      (
          4, uuid(), 9, false, NULL, 'DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN_INTEREST_NORMAL', 'Default Biz Flexi Credit -  Term Loan Interest - Normal', 'Default Biz Flexi Credit -  Term Loan Interest - Normal', 'MYR', 'HALF_ROUND_UP', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL', 'NORMAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ),
      (
          5, uuid(), 9, false, NULL, 'DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN_INTEREST_PENAL', 'Default Biz Flexi Credit -  Term Loan Interest - Penal', 'Default Biz Flexi Credit -  Term Loan Interest - Penal', 'MYR', 'HALF_ROUND_UP', 'DAY', 'INCREMENTAL', 'MANUAL', 'MANUAL', 'PENAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      );

-- TABLE_NAME :  loan_interest_version
-- Queries :
INSERT INTO loan_interest_version (
    id, public_id, loan_interest_id, version, effective_date, description, created_by, updated_by, created_at, updated_at
) VALUES
      (
          4, uuid(), 4, 1, CURRENT_TIMESTAMP, 'Default Biz Flexi Credit - Term Loan Interest v1.0 - Normal', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ),
      (
          5, uuid(), 5, 1, CURRENT_TIMESTAMP, 'Default Biz Flexi Credit - Term Loan Interest v1.0 - Penal', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      );

-- TABLE_NAME :  loan_interest_slab_rate
-- Queries :
INSERT INTO loan_interest_slab_rate (
    id, public_id, loan_interest_version_id, slab_type, from_unit, to_unit, base_rate_interest_spread_percentage, absolute_interest_rate_percentage, created_by, updated_by, created_at, updated_at
) VALUES
      (
          4, uuid(), 4, 'AMOUNT', 0, **********, 0, 9, 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ),
      (
          5, uuid(), 5, 'DAY', 1, 9999, 0, 0, 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      );

-- TABLE_NAME :  loan_past_due_version
-- Queries :
INSERT INTO loan_past_due_version (public_id, product_id, version, purpose, effective_date, description, created_by,updated_by, created_at, updated_at)
VALUES (uuid(), 8, 1, 'ACCOUNTING', CURRENT_TIMESTAMP, 'Default Biz Flexi Credit Term Loan - Past Due v1.0 For Accounting', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- TABLE_NAME :  loan_past_due_version
-- Queries :
INSERT INTO loan_past_due_slab (public_id, loan_past_due_version_id, from_unit, to_unit, slab_type, bucket_name,created_by,updated_by, created_at, updated_at)
VALUES
    (uuid(), 3, 1, 29, 'DAY', 'Bucket 1', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid(), 3, 30, 59, 'DAY', 'Bucket 2', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid(), 3, 60, 89, 'DAY', 'Bucket 3', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid(), 3, 90, 119, 'DAY', 'Bucket 4', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid(), 3, 120, 149, 'DAY', 'Bucket 5', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid(), 3, 150, 179, 'DAY', 'Bucket 6', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    (uuid(), 3, 180, 2555, 'DAY', 'Bucket 7', 'MANUAL', 'MANUAL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


-- TABLE_NAME :  product_variant_parameter
-- Queries :
INSERT INTO product_variant_parameter (
    public_id, product_variant_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_at, created_by, updated_at, updated_by
) VALUES
      (uuid(), 8, 'credit risk', 'min_customer_age_for_activation', '18', 'INT', 'NO_OVERRIDE', NULL, 'Is the min age of the customer', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'max_customer_age_for_activation', '60', 'INT', 'NO_OVERRIDE', NULL, 'Is the max age of the customer', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'max_credit_limit_amount', '1000000', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the maximum balance limit beyond which any credits are rejected', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'available_loan_tenor_in_months_for_fc', '2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,***********,30,31,32,33,34,35,36', 'STRING', 'AT_ACCOUNT', NULL, 'Available loan tenors option (in months) for user tagged as FC', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'available_loan_tenor_in_months_for_mat', '2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,***********,30,31,32,33,34,35,36', 'STRING', 'AT_ACCOUNT', NULL, 'Available loan tenors option (in months) for user tagged as MAT', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'business', 'min_available_credit_limit_for_drawdown', '1000', 'INT', 'NO_OVERRIDE', NULL, 'from functionality point, this value will be same min_drawdown_amount', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'business', 'dpd_count_to_disable_loan_drawdown', '15', 'INT', 'NO_OVERRIDE', NULL, 'Days past due count for which the loan drawdown is disabled due to deliquency', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'business', 'dpd_count_to_indefinately_block_loan_drawdown', '60', 'INT', 'NO_OVERRIDE', NULL, 'Days past due count for which if the customer ever reached for any of loans, all further drawdowns are blocked even after repayment', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'min_loan_tenor_in_months', '2', 'INT', 'AT_ACCOUNT', NULL, 'Is the minimum tenor of the loan in months', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'max_loan_tenor_in_months', '36', 'INT', 'AT_ACCOUNT', NULL, 'Is the maximum tenor of the loan in months', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'min_emi_amount', '50', 'INT', 'AT_ACCOUNT', NULL, 'Is the minimum EMI amount in IDR', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'credit risk', 'min_auto_repayment_amount', '1', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'Minimum saving account balance for auto repayment', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'business', 'max_drawdown_amount_when_available_limit_more_than_1000', '1000000', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the maximum drawdown amount of a loan when available credit limit is more than 1000', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'business', 'min_drawdown_amount_when_available_limit_less_than_1000', '1000', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the minimum drawdown amount of a loan when available credit limit is less than 1000', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'business', 'max_drawdown_amount_when_available_limit_less_than_1000', '1000', 'DOUBLE', 'AT_ACCOUNT', NULL, 'Is the maximum drawdown amount of a loan when available credit limit is less than 1000', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'pricing', 'interest_accrual_roundoff_precision_digits', '8', 'INT', 'NO_OVERRIDE', NULL, 'Is the precision beyond which interest accrued is truncated', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'credit risk', 'min_loan_tenor_in_months', '2', 'INT', 'AT_ACCOUNT', NULL, 'Is the minimum tenor of the loan in months', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'credit risk', 'max_loan_tenor_in_months', '36', 'INT', 'AT_ACCOUNT', NULL, 'Is the maximum tenor of the loan in months', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'credit risk', 'supported_loan_tenor_in_months', '2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,***********,30,31,32,33,34,35,36', 'ARRAY', 'AT_PRODUCT_VARIANT', NULL, 'Supported loan tenors in month', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'credit risk', 'min_drawdown_amount_config', '[{"fromAvailableLOCAmt": 1000000, "toAvailableLOCAmt": *********, "minAmt": 1000}, {"fromAvailableLOCAmt": 0, "toAvailableLOCAmt": 100000, "minAmt": 1000}]', 'ARRAY', 'AT_PRODUCT_VARIANT', NULL, 'Min drawdown amount configurations', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'credit risk', 'min_drawdown_amount', '1000', 'INT', 'AT_ACCOUNT', NULL, 'Minimun drawdown amount of the loan', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'business', 'account_number_product_code', '33', 'string', 'NO_OVERRIDE', NULL, 'Product code used for account number generation', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'business', 'account_number_unit_code', '81', 'string', 'NO_OVERRIDE', NULL, 'Unit code used for account number generation', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 8, 'business', 'max_active_loan_accounts_per_loc_account', '10', 'INT', 'NO_OVERRIDE', NULL, 'Limit to how many active loan accounts of this product variant a customer is allowed to have', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL'),
      (uuid(), 9, 'business', 'repayment_allocation_strategy',
       '{"Bucket 1":["penalty","interest","principal"],"Bucket 2":["penalty","interest","principal"],"Bucket 3":["penalty","interest","principal"],
       "Bucket 4":["penalty","interest","principal"],"Bucket 5":["penalty","interest","principal"],"Bucket 6":["penalty","interest","principal"],
       "Bucket 7":["penalty","interest","principal"]}', 'STRING', 'NO_OVERRIDE', NULL, 'strategy to decide how installment components will be paid', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL');

-- TABLE_NAME :  product_template_parameter
-- Queries :
INSERT INTO product_template_parameter (
    public_id, product_template_id, namespace, parameter_key, parameter_value, data_type, override_level, exception_level, description, created_at, created_by, updated_at, updated_by
) VALUES
    (uuid(), 4, 'pricing', 'repayment_grace_period_in_days', '3', 'INT', 'NO_OVERRIDE', NULL, 'Is the number of days from the due date during which loan is not considered overdue', CURRENT_TIMESTAMP, 'MANUAL', CURRENT_TIMESTAMP, 'MANUAL');
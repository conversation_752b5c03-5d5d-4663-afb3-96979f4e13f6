-- TODO Task
-- Product Master DB Insert Queries For
-- TABLE_NAME :  product_template
-- Queries :
INSERT INTO product_template (public_id, code, name, description, status, created_by, updated_by)
VALUES
    ('c3420019-feb9-4bfc-a409-976bfa84620a', 'BIZ_SAVINGS_DEPOSIT', 'BIZ Savings Account', 'The biz savings account master product', 'ACTIVE', 'MANUAL', 'MANUAL');
--
-- -- TABLE_NAME :  product_template_parameter
-- -- Queries :
INSERT INTO product_template_parameter (public_id, product_template_id, namespace, parameter_key, parameter_value,
                                        data_type, override_level, exception_level, description, created_by, updated_by)
VALUES ('cea3bf6d-c42d-46a6-b4ee-dba1278a5514', 5, 'pricing', 'days_in_a_year', '365', 'ENUM', 'NO_OVERRIDE', NULL,
        'Can be 360, 365 or ACTUAL', 'MANUAL', 'MANUAL'),
       ('66b2f22b-f7d7-49b7-b91d-62420c223ca4', 5, 'business', 'max_account_per_business', '1', 'INT',
        'AT_PRODUCT_VARIANT', NULL, 'Limit to how many accounts this business is allowed to have',
        'MANUAL', 'MANUAL'),
       ('0fd814e3-7f0f-42aa-aad7-ce584fe3361e', 5, 'business', 'max_account_per_product_variant', '1', 'INT',
        'AT_PRODUCT_VARIANT', NULL, 'Limit to how many accounts of this product variant a business is allowed to have', 'MANUAL', 'MANUAL'),
       ('09cb7b7a-f073-4a9b-b0df-a0043fc3ba06', 5, 'business', 'max_account_holders_per_product_variant', '1', 'ENUM',
        'AT_PRODUCT_VARIANT', NULL,
        'Limit to how many account holders of this product variant a business is allowed to have', 'MANUAL', 'MANUAL'),
       ('a10205af-5887-4497-99b1-299f513407b1', 5, 'finance', 'allowed_currency', 'MYR', 'CURRENCY', 'NO_OVERRIDE',
        NULL, 'Allowed currencies for this product variant', 'MANUAL', 'MANUAL'),
       ('********-5d19-4917-9a1d-459a0ac2f5e8', 5, 'risk', 'allowed_hold_codes',
        'ADVERSE_NEWS, AML/SANCTION_HITS, BANKRUPT, DECEASED, FRAUD_ACTOR, FRAUD_VICTIM, MENTAL_INCAPACITY, MISSING_DEVICE, MULE_ACCOUNT, POLICE/COURT/SEIZURE/LEA_ORDER, KILL_SWITCH, GARNISHEE/CLAWBACK',
        'ENUM', 'NO_OVERRIDE', NULL, 'Is the list of allowed hold codes for this product', 'MANUAL', 'MANUAL'),
       ('f183c529-bc46-4b68-bd4d-602b80556f2d', 5, 'business', 'earmark_reason_codes',
        'CLAWBACK, CUSTOMER_DISPUTE/ERRONEOUS_CREDIT, DEBIT_CARD_DISPUTE, GARNISHEE_ORDER, POLICE/COURT/SEIZURE/LEA_ORDER',
        'ENUM', 'NO_OVERRIDE', NULL, 'Is the list of reason codes that are applicable for earmarking an account', 'MANUAL', 'MANUAL'),
       ('eb6c21b2-c552-46bf-8341-0a25c0d92abc', 5, 'business', 'tm_product_id','biz_deposit_product', 'STRING', 'NO_OVERRIDE',NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
       ('e39ca1ab-8ff8-4a2a-9b54-eced07fe22c2', 5, 'business', 'account_applicable_features', 'NULL', 'STRING', 'AT_PRODUCT_VARIANT', NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
       ('1db4dd85-50a3-4713-ba25-5ae229934410', 5, 'pricing', 'interest_posting_frequency', 'DAYS', 'ENUM', 'AT_PRODUCT_VARIANT', 'no_override', 'Denotes the frequency of payout in DAYS, WEEKS, MONTHS, END_OF_GOAL', 'MANUAL', 'MANUAL'),
       ('98c56f06-7079-4f79-b9e6-48f4aba7e4eb', 5, 'business', 'interest_posting_account', 'SELF_ACCOUNT', 'ENUM', 'NO_OVERRIDE', NULL, 'The account to which accrued interest can be posted to / applied to.', 'MANUAL', 'MANUAL'),
       ('d516967b-fbef-40b0-a828-1f79860b15e8', 5, 'business', 'interest_posting_day', '1', 'INT', 'AT_PRODUCT_VARIANT', NULL, 'The day of the month on which interest is applied', 'MANUAL', 'MANUAL');
--
-- -- TABLE_NAME :  product
-- -- Queries :
INSERT INTO product (public_id, product_template_id, code, name, description, status, created_by, updated_by)
VALUES
    ('c5d40641-e601-4c3d-ae11-4c29d4d3738e', 5, 'BIZ_SAVINGS', 'BIZ savings deposits', ' The BIZ savings deposits product', 'ACTIVE', 'MANUAL', 'MANUAL');
--
--
-- -- TABLE_NAME :  product_variant
-- -- Queries :
INSERT INTO product_variant (public_id, product_id, code, version, name, description, status, valid_from, valid_to, created_by, updated_by)
VALUES
    ('4d44a6eb-d3f0-4ba7-b8cd-9b3fbe0b63f5', 6, 'BIZ_DEPOSIT_ACCOUNT', '7', 'BIZ savings deposits', 'The BIZ savings deposits product variant', 'ACTIVE', CURRENT_TIMESTAMP, '9999-12-31T15:59:59Z', 'MANUAL', 'MANUAL');
--
-- -- TABLE_NAME :  product_variant_parameter
-- -- Queries :
INSERT INTO product_variant_parameter (public_id, product_variant_id, namespace, parameter_key, parameter_value,
                                       data_type, override_level, exception_level, description, created_by, updated_by)
VALUES ('807ab5e8-325b-479f-afbd-403df0317337', 7, 'business', 'max_account_per_business', '1', 'INT',
        'AT_PRODUCT_VARIANT', NULL, 'Limit to how many accounts this business is allowed to have',
        'MANUAL', 'MANUAL'),
       ('4e5a5f41-2461-4c18-884f-c1a6a27a5bc0', 7, 'business', 'max_account_per_product_variant', '1', 'INT',
        'AT_PRODUCT_VARIANT', NULL, 'Limit to how many accounts of this product variant a business is allowed to have',
        'MANUAL', 'MANUAL'),
       ('233fe714-7654-45f9-ac6b-2e61931c21d1', 7, 'business', 'max_account_holders_per_product_variant', '1', 'INT',
        'AT_PRODUCT_VARIANT', NULL,
        'Limit to how many account holders of this product variant a business is allowed to have', 'MANUAL', 'MANUAL'),
       ('6e655f9a-782a-4067-908d-1bc78fe8e6f5', 7, 'business', 'tm_product_id','biz_deposit_product', 'STRING', 'NO_OVERRIDE',NULL, 'The features that are applicable to an account', 'MANUAL', 'MANUAL'),
       ('bb4b0921-9cc5-4440-bb19-affb70a51565', 7, 'business', 'account_applicable_features', '{"MAIN_ACCOUNT": true}','STRING', 'NO_OVERRIDE', NULL, 'The features that are applicable to an account', 'MANUAL','MANUAL');


-- TABLE_NAME :  deposit_interest
-- Queries :
INSERT INTO deposit_interest (public_id, product_variant_id, is_linked_to_base_rate, base_interest_id, code, name, description, currency, round_off_type, interest_slab_type, interest_slab_structure, created_by, updated_by)
VALUES
    ('e5fe24bd-ed1e-4907-b977-6f1fb41e7d17', 7, 0, NULL, 'BIZ_SAVINGS_INTEREST', 'BIZ Savings Interest', 'BIZ Savings Interest', 'MYR', 'HALF_ROUND_UP', 'AMOUNT', 'INCREMENTAL', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  deposit_interest_version
-- Queries :
INSERT INTO deposit_interest_version (public_id,deposit_interest_id, version, effective_date, description, created_by, updated_by)
VALUES
    ('11c837bd-d351-491b-8575-0c13079c98a4', 3, '1', CURRENT_TIMESTAMP, 'BIZ Savings Interest v1.0', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  deposit_interest_amount_slab_rate
-- Queries :
INSERT INTO deposit_interest_amount_slab_rate (public_id,deposit_interest_version_id, from_amount, to_amount, base_rate_interest_spread_percentage, absolute_interest_rate_percentage, created_by, updated_by)
VALUES
    ('62baaf17-a129-455b-99c5-0a5b273a8b36', 5, '0', '*********', '0', '1.00', 'MANUAL', 'MANUAL');


-- TABLE_NAME :  product_template_parameter
-- Queries :
update product_template_parameter set parameter_value = 'ADVERSE_NEWS, AML/SANCTION_HITS, BANKRUPT, DECEASED, FRAUD_ACTOR, FRAUD_VICTIM, MENTAL_INCAPACITY, MISSING_DEVICE, MULE_ACCOUNT, POLICE/COURT/SEIZURE/LEA_ORDER, KILL_SWITCH, GARNISHEE/CLAWBACK, BANK_RESTRICTED'
where product_template_id = 5 and parameter_key = 'allowed_hold_codes' and public_id  = '********-5d19-4917-9a1d-459a0ac2f5e8';


-- TABLE_NAME :  internal_account
-- Queries :
INSERT INTO internal_account (public_id, general_ledger_id, code, name, description, currency, status, created_by, updated_by)
VALUES
    ('f7d0b11d-500f-402f-a556-36782f47caf3', 8, '*********', 'Customer acquisition cost - MSME Promo Code', 'Customer acquisition cost - MSME Promo Code', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL');
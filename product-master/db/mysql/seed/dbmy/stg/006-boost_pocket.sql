-- Product Master DB Insert Queries For BP

-- TABLE_NAME :  internal_account
-- Queries :
INSERT INTO internal_account (public_id, general_ledger_id, code, name, description, currency, status, created_by, updated_by)
VALUES
    ('b55509fe-fbc4-4853-8f77-db830ba715c4', 5, '*********', 'Int exp savings accounts - Bonus pocket bonus interest', 'Int exp savings accounts - Bonus pocket bonus interest', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL'),
    ('2defdaee-3637-4cf9-8979-83b7020593c8', 8, '*********', 'Customer acquisition cost -  Bonus pocket', 'Customer acquisition cost -  Bonus pocket', 'MYR', 'ACTIVE', 'MANUAL', 'MANUAL');
-- Product Master DB Insert Queries For Lending
-- LastUpdatedAt: 19/07/2022

-- TABLE_NAME :  product_variant_question

-- Queries :

INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
VALUES (uuid(), 5, 'drawdown_name', 'Name your drawdown', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
VALUES (uuid(), 4, 'loc_closure_reason', 'Tell us why you''re leaving', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
VALUES (uuid(), 4, 'purpose_of_loan', 'Purpose of your FlexiCredit application', 'EN',  'ACTIVE', 'MANU<PERSON>', 'MANU<PERSON>');

INSERT INTO product_variant_question (public_id, product_variant_id, code, question_text, locale, status, created_by, updated_by)
VALUES (uuid(), 4, 'purpose_of_loan', 'Tujuan permohonan FlexiCredit', 'MS',  'ACTIVE', 'MANUAL', 'MANUAL');

-- TABLE_NAME :  product_variant_answer_suggestion

-- Queries :
-- product_variant_question_id = 1 (drawdown_name)(EN)
INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 1, 'expenses', 'Expenses', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 1, 'renovation', 'Renovation', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 1, 'down_payment', 'Down payment', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

-- product_variant_question_id = 2 (loc_closure_reason)(EN)
INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'dont_need_now', 'I don''t need it now', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'high_interest_rate', 'High interest rate', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'better_offer_elsewhere', 'Better offer elsewhere', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'low_credit_limit', 'Low credit limit', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 2, 'bad_ux', 'Unsatisfactory user experience', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

-- product_variant_question_id = 3 (purpose_of_loan)(EN)
INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 3, 'household_expenditure', 'Household expenditure', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 3, 'renovation', 'Renovation', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 3, 'others', 'Other personal use', 'EN',  'ACTIVE', 'MANUAL', 'MANUAL');

-- product_variant_question_id = 3 (purpose_of_loan)(MS)
INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 3, 'household_expenditure', 'Perbelanjaan isi rumah', 'MS',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 3, 'renovation', 'Ubahsuai', 'MS',  'ACTIVE', 'MANUAL', 'MANUAL');

INSERT INTO product_variant_answer_suggestion (public_id, product_variant_question_id, code, answer_suggestion_text, locale, status, created_by, updated_by)
VALUES (uuid(), 3, 'others', 'Lain-lain kegunaan peribadi', 'MS',  'ACTIVE', 'MANUAL', 'MANUAL');
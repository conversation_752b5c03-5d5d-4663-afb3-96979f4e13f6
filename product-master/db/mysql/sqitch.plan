%syntax-version=1.0.0
%project=product-master
%uri=https://gitlab.com/gx-regional/dbmy/core-banking/product-master

product_template 2021-08-06T08:27:14Z ‘Abhishek <‘<EMAIL>’> # Create table for entity product-template (CASA, Invest)
product 2021-08-18T06:35:20Z ‘Abhishek <‘<EMAIL>’> # Create table for entity product (SG Retail CASA)
product_variant 2021-08-19T11:03:50Z ‘Abhishek <‘<EMAIL>’> # Create table for entity product (SG Retail CASA - Students)
product_template_parameter 2021-08-26T08:23:50Z ‘Abhishek <‘<EMAIL>’> # Create table for entity product-template-parameter (max_balance)
product_variant_parameter 2021-08-27T05:50:30Z ‘Abhishek <‘<EMAIL>’> # Create table for entity product-variant-parameter (max_balance)
transaction_catalogue 2021-08-28T09:36:16Z ‘Abhishek <‘<EMAIL>’> # Create table for entity transaction_catalogue (payments.send_money.FAST)
product_variant_transaction_catalogue_mapping 2021-08-28T10:41:58Z ‘Abhishek <‘<EMAIL>’> # Create table for entity product_variant_transaction_catalogue_mapping
general_ledger 2021-08-29T17:25:11Z ‘Abhishek <‘<EMAIL>’> # Create table for entity general_ledger
internal_account 2021-08-29T17:42:39Z ‘Abhishek <‘<EMAIL>’> # Create table for entity internal_account
product_variant_transaction_catalogue_internal_account_mapping 2021-08-29T18:10:32Z ‘Abhishek <‘<EMAIL>’> # Create table for entity product_variant_transaction_catalogue_internal_account_mapping
base_interest 2021-08-30T06:58:06Z ‘Abhishek <‘<EMAIL>’> # Create table for entity base-interest
base_interest_version 2021-08-30T06:58:23Z ‘Abhishek <‘<EMAIL>’> # Create table for entity base-interest_version
base_interest_time_slab_rate 2021-08-30T07:44:20Z ‘Abhishek <‘<EMAIL>’> # Create table for entity base_interest_time_slab_rate
deposit_interest 2021-08-30T08:47:32Z ‘Abhishek <‘<EMAIL>’> # Create table for entity deposit_interest
deposit_interest_version 2021-08-30T08:47:54Z ‘Abhishek <‘<EMAIL>’> # Create table for entity deposit_interest_version
deposit_interest_amount_slab_rate 2021-08-31T05:23:08Z ‘Abhishek <‘<EMAIL>’> # Create entity for deposit_interest_amount_slab_rate
pocket_template 2022-04-28T05:23:08Z ‘Komal <‘<EMAIL>’> # Create entity for pocket_template
pocket_template_question 2022-04-28T05:25:08Z ‘Komal <‘<EMAIL>’> # Create entity for pocket_template_question
pocket_template_answer_suggestion 2022-05-05T09:25:08Z ‘Komal <‘<EMAIL>’> # Create entity for pocket_template_answer_suggestion
product_variant_question 2022-07-28T11:49:36Z Naveen Kewalramani <naveen.kewalramani@ITIN000489-MAC> # ProductVariantQuestion
product_variant_answer_suggestion 2022-07-28T11:49:42Z Naveen Kewalramani <naveen.kewalramani@ITIN000489-MAC> # ProductVariantAnswerSuggestion
pocket_template_image_suggestion 2022-10-25T06:13:14Z Kumar Gaurav Pandey <<EMAIL>> # Create entity for pocket_template_image_suggestion
parameter_change_schedule 2022-10-25T06:14:36Z Kumar Gaurav Pandey <<EMAIL>> # Create entity for parameter_change_schedule
loan_interest 2023-03-30T17:46:43Z gourav.suri <<EMAIL>> # loan interest table
loan_interest_version 2023-03-30T17:46:59Z gourav.suri <<EMAIL>> # loan interest version table
loan_interest_slab_rate 2023-03-30T17:47:17Z gourav.suri <<EMAIL>> # loan interest slab rate table
loan_allowed_amount_tenor_slab 2023-03-30T17:48:01Z gourav.suri <<EMAIL>> # loan allowed amount tenor slab  table
loan_past_due_version 2023-03-30T17:48:29Z gourav.suri <<EMAIL>> # loan past due version table
loan_past_due_slab 2023-03-30T17:48:46Z gourav.suri <<EMAIL>> # loan past due slab table
increase_datetime_precision 2023-05-09T07:33:52Z Sheh Jing Tan <<EMAIL>> # Increase datetime precision for all datetime columns
loan_instruction_version 2023-07-12T10:56:27Z ujjwal.dwivedi <<EMAIL>> # Added loan instruction version table
loan_instruction 2023-07-12T10:56:09Z ujjwal.dwivedi <<EMAIL>> # Added loan instruction table
increase_product_variant_code_column_size 2024-10-17T07:38:54Z mradul.agrawal <<EMAIL>> # Modify code column in product variant table
loan_document_submission_option 2025-05-07T17:41:04Z  Bhaskar <root@DBSG-H6R9Q61XHR> # Add loan document submission option
loan_document_submission_option_version 2025-05-07T17:41:04Z Bhaskar <root@DBSG-H6R9Q61XHR> # Add loan document submission option version
loan_document_submission_option_parameters 2025-05-07T17:41:04Z Bhaskar <root@DBSG-H6R9Q61XHR> # Add loan document submission option parameters
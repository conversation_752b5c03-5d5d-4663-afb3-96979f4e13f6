-- Verify product-master:add_code_column on mysql

BEGIN;

insert into
    product_template (
        `id`,
        `public_id`,
        `code`,
        `name`,
        `description`,
        `status`,
        `created_by`,
        `updated_by`
    )
values
    (
        3,
        uuid(),
        "UNSECURED_LINE_OF_CREDIT",
        "Unsecured Line of Credit",
        "The unsecured line of credit product",
        "ACTIVE",
        "MANUAL",
        "MANUAL"
    ),
    (
        4,
        uuid(),
        "UNSECURED_TERM_LOAN",
        "Unsecured Term Loan",
        "The unsecured term loan product",
        "ACTIVE",
        "MANUAL",
        "MANUAL"
    );

INSERT INTO
    product (
        `id`,
        `public_id`,
        `product_template_id`,
        `code`,
        `name`,
        `description`,
        `status`,
        `created_by`,
        `updated_by`
    )
VALUES
    (
        4,
        uuid(),
        3,
        "FLEXI_LOAN_LINE_OF_CREDIT",
        "Flexi Loan Line Of Credit",
        "Flexi LOan Line Of Credit",
        "ACTIVE",
        "MANUAL",
        "MANUAL"
    ),
    (
        5,
        uuid(),
        4,
        "FLEXI_LOAN_TERM_LOAN",
        "Flexi Loan Term Loan",
        "Flexi Loan Term Loan",
        "ACTIVE",
        "MANUAL",
        "MANUAL"
    );

INSERT into
    product_variant (
        `id`,
        `public_id`,
        `product_id`,
        `code`,
        `name`,
        `version`,
        `description`,
        `status`,
        `valid_from`,
        `valid_to`,
        `created_by`,
        `updated_by`
    )
VALUES
    (
        4,
        uuid(),
        4,
        "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT",
        "Default Flexi Loan Line of Credit",
        1,
        "Default Flexi Loan Line of Credit - v1.0 - product variant",
        "ACTIVE",
        '2024-02-19 06:44:07',
        '2024-02-19 06:44:07',
        "MANUAL",
        "MANUAL"
    ),
    (
        5,
        uuid(),
        5,
        "DEFAULT_FLEXI_LOAN_TERM_LOAN",
        "Default Flexi Loan Term Loan",
        1,
        "Default Flexi Loan Term Loan - v1.0 - product variant",
        "ACTIVE",
        '2024-02-19 06:44:07',
        '2024-02-19 06:44:07',
        "MANUAL",
        "MANUAL"
    );

INSERT INTO
        `product_variant_question` (
        `id`,
        `public_id`,
        `product_variant_id`,
        `code`,
        `question_text`,
        `locale`,
        `status`,
        `created_by`,
        `updated_by`
    )
VALUES
    (
        1,
        uuid(),
        4,
        "LOC_CLOSURE_REASON",
        'What’s the reason for closing the account?',
        'EN',
        'ACTIVE',
        'MANUAL',
        'MANUAL'
    ),
    (
        1,
        uuid(),
        5,
        "DRAWDOWN_NAME",
        'What are you borrowing for?',
        'EN',
        'ACTIVE',
        'MANUAL',
        'MANUAL'
    );

ROLLBACK;

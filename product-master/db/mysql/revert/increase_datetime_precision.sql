-- Deploy product-master:increase_datetime_precision to mysql

BEGIN;

-- XXX Add DDLs here.
ALTER TABLE base_interest
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE base_interest_time_slab_rate
    MODIF<PERSON> created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    M<PERSON><PERSON><PERSON> updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE base_interest_version
    MODIFY effective_date datetime NOT NULL,
    <PERSON><PERSON><PERSON><PERSON> created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON> updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE deposit_interest
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON> updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

<PERSON>TER TABLE deposit_interest_amount_slab_rate
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE deposit_interest_version
    MODIFY effective_date datetime NOT NULL,
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE general_ledger
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE internal_account
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE parameter_change_schedule
    MODIFY notification_date datetime NOT NULL,
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE pocket_template
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE pocket_template_answer_suggestion
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE pocket_template_image_suggestion
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE pocket_template_question
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_template
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_template_parameter
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_variant
    MODIFY valid_from datetime NOT NULL,
    MODIFY valid_to datetime NOT NULL,
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_variant_answer_suggestion
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_variant_parameter
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_variant_question
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_variant_transaction_catalogue_internal_account_mapping
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE product_variant_transaction_catalogue_mapping
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

ALTER TABLE transaction_catalogue
    MODIFY created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    MODIFY updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
;

COMMIT;
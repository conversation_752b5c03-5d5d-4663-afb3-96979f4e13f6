-- Deploy product-master:base_interest_time_slab_rate to mysql

BEGIN;

CREATE TABLE `base_interest_time_slab_rate`
(
    id                          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id                   varchar(36) NOT NULL UNIQUE,
    base_interest_version_id    BIGINT UNSIGNED NOT NULL,
    term_unit                   varchar(36) NOT NULL,
    term_value                  int NOT NULL,
    base_rate_percentage        varchar(10) NOT NULL,
    created_at                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                  varchar(36) NOT NULL,
    updated_at                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                  varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_base_interest_version_id_1` FOREIGN KEY (base_interest_version_id) REFERENCES base_interest_version(id),
    KEY `index_created_at` (`created_at`),
    KEY `index_updated_at` (`updated_at`),
    KEY `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
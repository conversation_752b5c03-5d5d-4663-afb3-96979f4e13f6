-- Deploy product-master:pocket_template_image_suggestion to mysql

BEGIN;

CREATE TABLE `pocket_template_image_suggestion`
(
    id                  BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id           varchar(36) NOT NULL,
    pocket_template_id  BIGINT UNSIGNED NOT NULL,
    image_id            varchar(36) NOT NULL,
    status              varchar(36) NOT NULL,
    created_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by          varchar(36) NOT NULL,
    updated_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by          varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_pocket_template_id_2` FOREIGN KEY (pocket_template_id) REFERENCES pocket_template(id),
    KEY `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`),
    UNIQUE KEY `uk_public_id_4` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
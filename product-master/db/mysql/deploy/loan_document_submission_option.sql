-- Deploy product-master:loan_document_submission_option to mysql

CREATE TABLE `loan_document_submission_option`
(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Primary key id field',
    `product_variant_id` BIGINT UNSIGNED NOT NULL,
    `document_type` varchar(36) NOT NULL,
    `salary_type`   varchar(36) NOT NULL,
    `application_type`  varchar(36) NOT NULL,
    `created_at`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Date and time the record was created.',
    `created_by`     VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Created By',
    `updated_at`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date and time the record was last modified.',
    `updated_by`     VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Updated By',
    PRIMARY KEY (`id`),
    CONSTRAINT `fk_product_variant_id` FOR<PERSON><PERSON><PERSON> KEY (product_variant_id) REFERENCES product_variant (id),
    <PERSON><PERSON>Y              `index_product_variant_id` (`product_variant_id`),
    KEY              `index_created_at` (`created_at`),
    KEY              `index_updated_at` (`updated_at`)
)DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
-- Deploy product-master:pocket_template_answer_suggestion to mysql

BEGIN;

CREATE TABLE `pocket_template_answer_suggestion`
(
    id                              BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id                       varchar(36) NOT NULL,
    pocket_template_question_id     BIGINT UNSIGNED NOT NULL,
    answer_suggestion_text          varchar(256) NOT NULL,
    locale                          varchar(10) NOT NULL,
    status                          varchar(36) NOT NULL,
    created_at                      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                      varchar(36) NOT NULL,
    updated_at                      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                      varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_pocket_template_question_id_1` FOREIGN KEY (pocket_template_question_id) REFERENCES pocket_template_question(id),
    <PERSON><PERSON>Y `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`),
    UNIQUE KEY `uk_public_id_3` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
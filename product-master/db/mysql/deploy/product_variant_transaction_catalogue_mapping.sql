-- Deploy product-master:product_variant_transaction_catalogue_mapping to mysql

BEGIN;


CREATE TABLE `product_variant_transaction_catalogue_mapping`
(
    id                          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id                   varchar(36) NOT NULL UNIQUE,
    product_variant_id          BIGINT UNSIGNED NOT NULL,
    transaction_catalogue_id    BIGINT UNSIGNED NOT NULL,
    status                      varchar(20) NOT NULL,
    created_at                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                  varchar(36) NOT NULL,
    updated_at                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                  varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_variant_id_2` FOREIGN KEY (product_variant_id) REFERENCES product_variant(id),
    CONSTRAINT `fk_transaction_catalogue_id_1` FOREI<PERSON>N KEY (transaction_catalogue_id) REFERENCES transaction_catalogue(id),
    <PERSON><PERSON><PERSON> `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`),
    <PERSON><PERSON><PERSON> `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
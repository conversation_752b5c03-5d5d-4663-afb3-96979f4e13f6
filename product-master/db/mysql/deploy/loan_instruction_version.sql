-- Deploy product-master:loan_instruction_version to mysql

BEGIN;

CREATE TABLE `loan_instruction_version`
(
    id                 BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id          varchar(36) NOT NULL,
    product_variant_id BIGINT UNSIGNED NOT NULL,
    version            varchar(36) NOT NULL,
    instruction_type   varchar(36) NOT NULL,
    effective_date     datetime NOT NULL,
    description        varchar(512),
    created_at         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by         varchar(36) NOT NULL,
    updated_at         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by         varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_variant_id_8` FOREIGN KEY (product_variant_id) REFERENCES product_variant(id),
    <PERSON>EY `index_created_at` (`created_at`),
    <PERSON><PERSON>Y `index_updated_at` (`updated_at`),
    UNIQUE KEY `uk_public_id_8` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
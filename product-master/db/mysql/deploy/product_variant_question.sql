-- Deploy product-master:product_variant_question to mysql

BEGIN;

CREATE TABLE `product_variant_question`
(
    id                  BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id           varchar(36) NOT NULL,
    product_variant_id  BIGINT UNSIGNED NOT NULL,
    question_text       varchar(256) NOT NULL,
    locale              varchar(10) NOT NULL,
    status              varchar(36) NOT NULL,
    created_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by          varchar(36) NOT NULL,
    updated_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by          varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_variant_id_4` FOREIGN KEY (product_variant_id) REFERENCES product_variant(id),
    KEY `index_created_at` (`created_at`),
    <PERSON>EY `index_updated_at` (`updated_at`),
    UNIQUE KEY `uk_public_id_5` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
-- Deploy product-master:deposit_interest_version to mysql

BEGIN;

CREATE TABLE `deposit_interest_version`
(
    id                  BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id           varchar(36) NOT NULL UNIQUE,
    deposit_interest_id BIGINT UNSIGNED NOT NULL,
    version             varchar(36) NOT NULL,
    effective_date      datetime NOT NULL,
    description         varchar(512),
    created_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by          varchar(36) NOT NULL,
    updated_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by          varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_deposit_interest_id_1` FOREIGN KEY (deposit_interest_id) REFERENCES deposit_interest(id),
    KEY `index_created_at` (`created_at`),
    <PERSON><PERSON>Y `index_updated_at` (`updated_at`),
    <PERSON><PERSON><PERSON> `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
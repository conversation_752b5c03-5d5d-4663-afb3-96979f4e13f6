-- Deploy deposits-core:deposits_parameter_change_history to mysql

BEGIN;

-- XXX Add DDLs here.

CREATE TABLE `parameter_change_schedule`
(
    id                        BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    parameter_key             varchar(64) NOT NULL,
    parameter_version         varchar(6) NOT NULL,
    smart_contract_version_id varchar(6) NOT NULL,
    is_scheduled              bool NOT NULL,
    notification_date         datetime NOT NULL,
    is_notification_sent      bool NOT NULL,
    created_at                datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                varchar(32) NOT NULL,
    updated_at                datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                varchar(32) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY uk_param_key_version_sc_version (parameter_key,parameter_version,smart_contract_version_id),
    <PERSON><PERSON>Y `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

COMMIT;

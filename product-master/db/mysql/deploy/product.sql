-- Deploy product-master:product to mysql

BEGIN;

CREATE TABLE `product`
(
    id                   BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id            varchar(36) NOT NULL UNIQUE,
    product_template_id  BIGINT UNSIGNED NOT NULL,
    code                 varchar(36) NOT NULL UNIQUE,
    name                 varchar(64) NOT NULL UNIQUE,
    description          varchar(512),
    status               varchar(20) NOT NULL,
    created_at           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by           varchar(36) NOT NULL,
    updated_at           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by           varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_template_id_2` FOREIGN KEY (product_template_id) REFERENCES product_template(id),
    <PERSON><PERSON>Y `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`),
    KEY `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
-- Deploy product-master:product_variant to mysql

BEGIN;

CREATE TABLE `product_variant`
(
    id          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id   varchar(36) NOT NULL UNIQUE,
    product_id  BIGINT UNSIGNED NOT NULL,
    code        varchar(36) NOT NULL UNIQUE,
    version     varchar(36) NOT NULL UNIQUE,
    name        varchar(64) NOT NULL,
    description varchar(512),
    status      varchar(20) NOT NULL,
    valid_from  datetime NOT NULL,
    valid_to    datetime NOT NULL,
    created_at  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by  varchar(36) NOT NULL,
    updated_at  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by  varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_id_1` FOREIGN KEY (product_id) REFERENCES product(id),
    <PERSON>EY `index_created_at` (`created_at`),
    <PERSON><PERSON>Y `index_updated_at` (`updated_at`),
    <PERSON><PERSON><PERSON> `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
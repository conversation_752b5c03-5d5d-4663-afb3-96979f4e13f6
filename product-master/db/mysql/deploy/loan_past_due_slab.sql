-- Deploy product-master:loan_past_due_slab to mysql

BEGIN;

CREATE TABLE `loan_past_due_slab`
(
    id               BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id        varchar(36) NOT NULL UNIQUE,
    loan_past_due_version_id BIGINT UNSIGNED NOT NULL,
    from_unit        int NOT NULL,
    to_unit          int NOT NULL,
    slab_type        varchar(36) NOT NULL,
    bucket_name      varchar(36) NOT NULL,
    created_at       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by       varchar(36) NOT NULL,
    updated_at       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by       varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_loan_past_due_version_id_1` FOREIGN KEY (loan_past_due_version_id) REFERENCES loan_past_due_version(id),
    KEY              `index_created_at` (`created_at`),
    <PERSON>EY              `index_updated_at` (`updated_at`),
    KEY              `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;

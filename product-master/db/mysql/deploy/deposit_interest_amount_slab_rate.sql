-- Deploy product-master:deposit_interest_amount_slab_rate to mysql

BEGIN;

CREATE TABLE `deposit_interest_amount_slab_rate`
(
    id                                      BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id                               varchar(36) NOT NULL UNIQUE,
    deposit_interest_version_id             BIGINT UNSIGNED NOT NULL,
    from_amount                             varchar(16) NOT NULL,
    to_amount                               varchar(16) NOT NULL,
    base_rate_interest_spread_percentage    varchar(10) NOT NULL,
    absolute_interest_rate_percentage       varchar(10) NOT NULL,
    min_tenor                               varchar(16) DEFAULT '0',
    max_tenor                               varchar(16) DEFAULT '999999',
    min_tenor_unit                          varchar(16) DEFAULT 'MONTH',
    max_tenor_unit                          varchar(16) DEFAULT 'MONTH',
    created_at                              datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                              varchar(36) NOT NULL,
    updated_at                              datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                              varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_deposit_interest_version_id_1` FOREIGN KEY (deposit_interest_version_id) REFERENCES deposit_interest_version(id),
    KEY `index_created_at` (`created_at`),
    KEY `index_updated_at` (`updated_at`),
    KEY `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
-- Deploy product-master:internal_account to mysql

BEGIN;

CREATE TABLE `internal_account`
(
    id                  BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id           varchar(36) NOT NULL UNIQUE,
    general_ledger_id   BIGINT UNSIGNED NOT NULL,
    code                varchar(36) NOT NULL UNIQUE,
    name                varchar(128) NOT NULL UNIQUE,
    description         varchar(512),
    currency            varchar(3) NOT NULL,
    status              varchar(20) NOT NULL,
    created_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by          varchar(36) NOT NULL,
    updated_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by          varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_general_ledger_id_1` FOREIGN KEY (general_ledger_id) REFERENCES general_ledger(id),
    <PERSON><PERSON><PERSON> `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`),
    KEY `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
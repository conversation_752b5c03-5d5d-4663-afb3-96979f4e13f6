-- Deploy product-master:loan_interest to mysql

BEGIN;

CREATE TABLE `loan_interest`
(
    id                      BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id               varchar(36) NOT NULL UNIQUE,
    product_variant_id      BIGINT UNSIGNED NOT NULL,
    is_linked_to_base_rate  bool        NOT NULL,
    base_interest_id        BIGINT UNSIGNED,
    code                    varchar(64) NOT NULL UNIQUE,
    name                    varchar(64) NOT NULL,
    description             varchar(512),
    currency                varchar(3)  NOT NULL,
    round_off_type          varchar(36) NOT NULL,
    interest_type           varchar(36) NOT NULL COMMENT 'Denotes type of interest like normal, penal, etc.',
    interest_slab_unit_type varchar(36) NOT NULL COMMENT 'Denotes unit type of interest based on amount, days, month, year, etc.',
    interest_slab_structure varchar(36) NOT NULL COMMENT 'Denotes the structure like incremental, absolute etc.',
    created_at              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by              varchar(36) NOT NULL,
    updated_at              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by              varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_variant_id_6` FOREIGN KEY (product_variant_id) REFERENCES product_variant (id),
    CONSTRAINT `fk_base_interest_id_3` FOREIGN KEY (base_interest_id) REFERENCES base_interest (id),
    KEY                     `index_created_at` (`created_at`),
    KEY                     `index_updated_at` (`updated_at`),
    KEY                     `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
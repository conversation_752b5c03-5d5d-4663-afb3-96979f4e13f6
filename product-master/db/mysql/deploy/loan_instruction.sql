-- Deploy product-master:loan_instruction to mysql

BEGIN;

CREATE TABLE `loan_instruction`
(
    id                          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id                   varchar(36) NOT NULL,
    loan_instruction_version_id BIGINT UNSIGNED NOT NULL,
    code                        varchar(128) NOT NULL,
    name                        varchar(128) NOT NULL,
    created_at                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                  varchar(36) NOT NULL,
    updated_at                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                  varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_loan_instruction_version_1` FOREIGN KEY (loan_instruction_version_id) REFERENCES loan_instruction_version(id),
    <PERSON><PERSON>Y `index_created_at` (`created_at`),
    <PERSON><PERSON>Y `index_updated_at` (`updated_at`),
    UNIQUE KEY `uk_public_id_7` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
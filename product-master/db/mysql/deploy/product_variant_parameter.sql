-- Deploy product-master:product_variant_parameter to mysql

BEGIN;

CREATE TABLE `product_variant_parameter`
(
    id                  BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id           varchar(36) NOT NULL UNIQUE,
    product_variant_id  BIGINT UNSIGNED NOT NULL,
    namespace           varchar(32) NOT NULL,
    parameter_key       varchar(64) NOT NULL,
    parameter_value     varchar(512) NOT NULL,
    data_type           varchar(36) NOT NULL,
    override_level      varchar(20) NOT NULL,
    exception_level     varchar(36),
    description         varchar(512),
    created_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by          varchar(36) NOT NULL,
    updated_at          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by          varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_variant_id_1` FOREIGN KEY (product_variant_id) REFERENCES product_variant(id),
    <PERSON><PERSON><PERSON> `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`),
    <PERSON><PERSON><PERSON> `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
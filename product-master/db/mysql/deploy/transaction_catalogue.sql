-- Deploy product-master:transaction_catalogue to mysql

BEGIN;

CREATE TABLE `transaction_catalogue`
(
    id                      BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id               varchar(36) NOT NULL UNIQUE,
    domain                  varchar(36) NOT NULL,
    is_financial_txn bool   NOT NULL,
    txn_type                varchar(36) NOT NULL,
    txn_sub_type            varchar(36) NOT NULL,
    display_name            varchar(64) NOT NULL ,
    description             varchar(512),
    status                  varchar(20) NOT NULL,
    created_at              datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by              varchar(36) NOT NULL,
    updated_at              datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by              varchar(36) NOT NULL,
    PRIMARY KEY (id),
    KEY `index_created_at` (`created_at`),
    <PERSON><PERSON>Y `index_updated_at` (`updated_at`),
    <PERSON>EY `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
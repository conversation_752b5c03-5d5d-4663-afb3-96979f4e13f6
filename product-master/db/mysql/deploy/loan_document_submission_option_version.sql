-- Deploy product-master:loan_document_submission_option_version to mysql

CREATE TABLE `loan_document_submission_option_version`
(
    `id`             BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `loan_document_submission_option_id` BIGINT UNSIGNED NOT NULL,
    `version_id`   VARCHAR(36) NOT NULL COMMENT 'version id of document submission option',
    `status` VARCHAR(36) NOT NULL COMMENT 'status',
    `created_at`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Date and time the record was created.',
    `created_by`     VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Created By',
    `updated_at`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date and time the record was last modified.',
    `updated_by`     VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Updated By',
    PRIMARY KEY (`id`),
	CONSTRAINT `fk_loan_document_submission_option_id` FOREI<PERSON>N KEY (loan_document_submission_option_id) REFERENCES loan_document_submission_option (id),
    KEY              `index_loan_document_submission_option_id` (`loan_document_submission_option_id`),
    KEY              `index_loan_document_submission_option_id_version_id_status` (`loan_document_submission_option_id`, `version_id`, `status`),
    KEY              `index_created_at` (`created_at`),
    KEY              `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
-- Deploy product-master:product_variant_transaction_catalogue_internal_account_mapping to mysql

BEGIN;

CREATE TABLE `product_variant_transaction_catalogue_internal_account_mapping`
(
    id                                                  BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id                                           varchar(36) NOT NULL UNIQUE,
    product_variant_transaction_catalogue_mapping_id    BIGINT UNSIGNED NOT NULL,
    internal_account_id                                 BIGINT UNSIGNED NOT NULL,
    identifier_key                                      varchar(64) NOT NULL,
    status                                              varchar(20) NOT NULL,
    created_at                                          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                                          varchar(36) NOT NULL,
    updated_at                                          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                                          varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_variant_transaction_catalogue_mapping_id_1` FOREI<PERSON>N KEY (product_variant_transaction_catalogue_mapping_id) REFERENCES product_variant_transaction_catalogue_mapping(id),
    CONSTRAINT `fk_internal_account_id_1` FOREIGN KEY (internal_account_id) REFERENCES internal_account(id),
    KEY `index_created_at` (`created_at`),
    KEY `index_updated_at` (`updated_at`),
    KEY `index_public_id` (`public_id`)
)  ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
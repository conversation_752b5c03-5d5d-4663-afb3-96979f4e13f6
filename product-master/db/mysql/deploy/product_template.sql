-- Deploy product-master:product_template to mysql

BEGIN;

CREATE TABLE `product_template`
(
    id          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id   varchar(36) NOT NULL UNIQUE,
    code        varchar(36) NOT NULL UNIQUE,
    name        varchar(64) NOT NULL,
    description varchar(512),
    status      varchar(20) NOT NULL,
    created_at  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by  varchar(36) NOT NULL,
    updated_at  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by  varchar(36) NOT NULL,
    PRIMARY KEY (`id`),
    <PERSON>EY `index_created_at` (`created_at`),
    KEY `index_updated_at` (`updated_at`),
    KEY `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
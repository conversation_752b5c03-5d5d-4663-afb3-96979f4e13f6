-- Deploy product-master:loan_interest_slab_rate to mysql

BEGIN;

CREATE TABLE `loan_interest_slab_rate`
(
    id                                   BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id                            varchar(36) NOT NULL UNIQUE,
    loan_interest_version_id             BIGINT UNSIGNED NOT NULL,
    slab_type                            varchar(36) NOT NULL COMMENT 'Denotes unit type of interest based on amount, days, month, year, etc.',
    from_unit                            varchar(16) NOT NULL,
    to_unit                              varchar(16) NOT NULL,
    base_rate_interest_spread_percentage varchar(16) NOT NULL,
    absolute_interest_rate_percentage    varchar(16) NOT NULL,
    created_at                           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by                           varchar(36) NOT NULL,
    updated_at                           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by                           varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_loan_interest_version_id_1` FOREIGN KEY (loan_interest_version_id) REFERENCES loan_interest_version (id),
    KEY                                  `index_created_at` (`created_at`),
    KEY                                  `index_updated_at` (`updated_at`),
    KEY                                  `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;

-- Deploy product-master:loan_allowed_amount_tenor_slab to mysql

BEGIN;

CREATE TABLE `loan_allowed_amount_tenor_slab`
(
    id                 BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    product_variant_id BIGINT UNSIGNED NOT NULL,
    currency           varchar(3)  NOT NULL,
    from_amount        varchar(16) NOT NULL,
    to_amount          varchar(16) NOT NULL,
    tenor_unit         varchar(16) NOT NULL,
    min_tenor          varchar(16) NOT NULL,
    max_tenor          varchar(16) NOT NULL,
    created_at         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by         varchar(36) NOT NULL,
    updated_at         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by         varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_variant_id_5` FOREIGN KEY (product_variant_id) REFERENCES product_variant (id),
    KEY                `index_created_at` (`created_at`),
    KEY                `index_updated_at` (`updated_at`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;

-- Deploy product-master:loan_document_submission_option_parameters to mysql

CREATE TABLE `loan_document_submission_option_parameters` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `loan_document_submission_option_version_id` BIGINT UNSIGNED NOT NULL,
    `is_enabled` bool NOT NULL,
    `priority` INT NOT NULL,
    `required_document_number` INT NOT NULL,
    `required_document_unit` VARCHAR(36) NOT NULL,
    `excluded_months`INT NOT NULL,
    `allowed_file_extensions` JSON NOT NULL,
    `max_file_size_in_bytes` BIGINT NOT NULL,
    `max_upload_limit` INT NOT NULL,
    `created_at`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Date and time the record was created.',
    `created_by`     VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Created By',
    `updated_at`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date and time the record was last modified.',
    `updated_by`     VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Updated By',
    PRIMARY KEY (`id`),
    CONSTRAINT `fk_loan_document_submission_option_version_id` FOREIGN KEY (loan_document_submission_option_version_id) REFERENCES loan_document_submission_option_version (id),
    KEY              `index_created_at` (`created_at`),
    KEY              `index_updated_at` (`updated_at`),
    KEY 			 `index_loan_document_submission_option_version_id` (`loan_document_submission_option_version_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

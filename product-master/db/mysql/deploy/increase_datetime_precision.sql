-- Deploy product-master:increase_datetime_precision to mysql

BEGIN;

-- XXX Add DDLs here.
ALTER TABLE base_interest
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE base_interest_time_slab_rate
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE base_interest_version
    MODIFY effective_date datetime(6) NOT NULL,
    M<PERSON><PERSON><PERSON> created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    <PERSON><PERSON><PERSON><PERSON> updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE deposit_interest
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE deposit_interest_amount_slab_rate
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE deposit_interest_version
    MODIFY effective_date datetime(6) NOT NULL,
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE general_ledger
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE internal_account
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE parameter_change_schedule
    MODIFY notification_date datetime(6) NOT NULL,
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE pocket_template
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE pocket_template_answer_suggestion
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE pocket_template_image_suggestion
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE pocket_template_question
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_template
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_template_parameter
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_variant
    MODIFY valid_from datetime(6) NOT NULL,
    MODIFY valid_to datetime(6) NOT NULL,
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_variant_answer_suggestion
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_variant_parameter
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_variant_question
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_variant_transaction_catalogue_internal_account_mapping
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE product_variant_transaction_catalogue_mapping
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

ALTER TABLE transaction_catalogue
    MODIFY created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    MODIFY updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
;

COMMIT;
-- Deploy product-master:loan_past_due_version to mysql

BEGIN;

CREATE TABLE `loan_past_due_version`
(
    id               BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id        varchar(36) NOT NULL UNIQUE,
    product_id       BIGINT UNSIGNED NOT NULL,
    version          varchar(36) NOT NULL,
    purpose          varchar(512) NOT NULL,
    effective_date   datetime    NOT NULL,
    description      varchar(512),
    created_at       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by       varchar(36) NOT NULL,
    updated_at       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by       varchar(36) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT `fk_product_id_2` FOREIGN KEY (product_id) REFERENCES product(id),
    KEY              `index_created_at` (`created_at`),
    KEY              `index_updated_at` (`updated_at`),
    KEY              `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;

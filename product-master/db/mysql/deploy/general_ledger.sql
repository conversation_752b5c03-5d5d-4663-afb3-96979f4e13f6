-- Deploy product-master:general_ledger to mysql

BEGIN;

CREATE TABLE `general_ledger`
(
    id          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    public_id   varchar(36) NOT NULL UNIQUE,
    code        varchar(36) NOT NULL UNIQUE,
    name        varchar(128) NOT NULL UNIQUE,
    description varchar(512),
    currency    varchar(3) NOT NULL,
    status      varchar(20) NOT NULL,
    created_at  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by  varchar(36) NOT NULL,
    updated_at  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by  varchar(36) NOT NULL,
    PRIMARY KEY (id),
    KEY `index_created_at` (`created_at`),
    KEY `index_updated_at` (`updated_at`),
    KEY `index_public_id` (`public_id`)
) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

COMMIT;
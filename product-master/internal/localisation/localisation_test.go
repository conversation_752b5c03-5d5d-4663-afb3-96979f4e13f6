package localisation

import (
	"errors"
	"testing"

	"golang.org/x/text/language"

	"github.com/stretchr/testify/assert"
)

func Test_extractLocale(t *testing.T) {
	tests := []struct {
		name        string
		language    string
		expected    []language.Tag
		expectedErr error
	}{
		{
			name:        "valid-language-en-us",
			language:    "en-US",
			expected:    []language.Tag{language.AmericanEnglish},
			expectedErr: nil,
		},
		{
			name:        "valid-language-en",
			language:    "en",
			expected:    []language.Tag{language.English},
			expectedErr: nil,
		},
		{
			name:        "valid-language-ms",
			language:    "ms",
			expected:    []language.Tag{language.Malay},
			expectedErr: nil,
		},
		{
			name:        "valid-language-ms-my",
			language:    "ms-MY",
			expected:    []language.Tag{language.MustParse("ms-MY")},
			expectedErr: nil,
		},
		{
			name:        "multiple-language",
			language:    "en-MY, ms-MY;q=0.8, ms-BN;q=0.7",
			expected:    []language.Tag{language.MustParse("en-MY"), language.MustParse("ms-MY"), language.MustParse("ms-BN")},
			expectedErr: nil,
		},
		{
			name:        "empty-language",
			language:    "",
			expected:    nil,
			expectedErr: nil,
		},
		{
			name:        "invalid-length",
			language:    "english",
			expected:    []language.Tag{language.English},
			expectedErr: nil,
		},
		{
			name:        "invalid-contain-digits",
			language:    "en1",
			expected:    []language.Tag{language.Und},
			expectedErr: errors.New("language: tag is not well-formed"),
		},
		{
			name:        "invalid-unsupported-language",
			language:    "es",
			expected:    []language.Tag{language.Spanish},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual, err := extractLanguageTag(tt.language)
			if tt.expectedErr != nil {
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, actual)
			}
		})
	}
}

func Test_Configure(t *testing.T) {
	tests := []struct {
		name                       string
		defaultLang                string
		acceptedLangLocalesMapping map[string]string
		expectedErr                error
	}{
		{
			name:        "valid-configuration",
			defaultLang: "en",
			acceptedLangLocalesMapping: map[string]string{
				"en": "EN",
				"ms": "MS",
			},
			expectedErr: nil,
		},
		{
			name:        "invalid-default-language",
			defaultLang: "es",
			acceptedLangLocalesMapping: map[string]string{
				"en": "EN",
				"ms": "MS",
			},
			expectedErr: errors.New("default language es is not in accepted languages"),
		},
		{
			name:        "empty-default-language",
			defaultLang: "",
			acceptedLangLocalesMapping: map[string]string{
				"en": "EN",
				"ms": "MS",
			},
			expectedErr: errors.New("default language is not provided"),
		},
		{
			name:                       "empty-accepted-languages",
			defaultLang:                "en",
			acceptedLangLocalesMapping: map[string]string{},
			expectedErr:                errors.New("default language en is not in accepted languages"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := Configure(tt.defaultLang, tt.acceptedLangLocalesMapping)
			if tt.expectedErr != nil {
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

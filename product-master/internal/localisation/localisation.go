// Package localisation ...
package localisation

import (
	"context"
	"fmt"
	"strings"

	"golang.org/x/text/language"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"github.com/samber/lo"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

type ctxKey string

const (
	// acceptLanguageHeaderKey is the key for the Accept-Language header
	acceptLanguageHeaderKey = "Accept-Language"
	// localeCtxKey is the key for the locale in the context
	localeCtxKey = ctxKey("locale")
)

// Locale represents the locale in the context of localisation
type Locale string

// String ...
func (l Locale) String() string {
	return string(l)
}

const (
	// LocaleEN represents the English locale
	LocaleEN Locale = "EN"
	// LocaleMS represents the Malay locale
	LocaleMS Locale = "MS"
)

var (
	// languageToLocale is the fallback accepted locales mapping in case not configured by user
	languageToLocale = map[string]Locale{
		"en": LocaleEN,
		"ms": LocaleMS,
	}
)

// defaultLocale is the fallback locale in case not configured by user
var defaultLocale = LocaleEN

// Configure ...
func Configure(defaultLang string, acceptedLangLocalesMapping map[string]string) error {
	if lo.IsEmpty(defaultLang) {
		return fmt.Errorf("default language is not provided")
	}

	if !lo.HasKey(acceptedLangLocalesMapping, defaultLang) {
		return fmt.Errorf("default language %s is not in accepted languages", defaultLang)
	}
	defaultLocale = Locale(acceptedLangLocalesMapping[defaultLang])
	languageToLocale = lo.MapValues(acceptedLangLocalesMapping, func(value, key string) Locale {
		return Locale(value)
	})
	return nil
}

// DefaultLocale ...
func DefaultLocale() Locale {
	return defaultLocale
}

// GetLocale returns the locale from the context, by extracting it from the Accept-Language header
func GetLocale(ctx context.Context, defaultLanguage language.Tag) Locale {
	preferredLang := servus.HeaderFromCtx(ctx).Get(acceptLanguageHeaderKey)
	if preferredLang == "" {
		slog.FromContext(ctx).Warn("localise.GetLocale", "no language provided in context header")
		return defaultLocale
	}

	languageTags, err := extractLanguageTag(preferredLang)
	if err != nil {
		slog.FromContext(ctx).Warn("localise.GetLocale", "unable to extract language from context header")
		return defaultLocale
	}

	if len(languageTags) == 0 {
		slog.FromContext(ctx).Warn("localise.GetLocale", "no language provided in context header")
		return defaultLocale
	}

	languageTag := languageTags[0]
	langID := strings.SplitN(languageTags[0].String(), "-", 2)[0]

	if preferredLocale, ok := languageToLocale[langID]; ok {
		return preferredLocale
	}

	slog.FromContext(ctx).Warn("localise.GetLocale", fmt.Sprintf("unsupported lang: %s. Fallback to default locale %s", languageTag.String(), api.Locale_EN))
	return defaultLocale
}

// GetLocaleFromCtx retrieve locale set in context inside middleware ...
func GetLocaleFromCtx(ctx context.Context) Locale {
	locale, ok := ctx.Value(localeCtxKey).(Locale)
	if !ok {
		return LocaleEN
	}
	return locale
}

// NewContextWithLocale returns a new context with the given locale
func NewContextWithLocale(ctx context.Context, locale Locale) context.Context {
	return context.WithValue(ctx, localeCtxKey, locale)
}

// extractLanguageTag extracts language tag from the Accept-Language header
func extractLanguageTag(preferredLang string) ([]language.Tag, error) {
	var tags []language.Tag
	t, _, err := language.ParseAcceptLanguage(preferredLang)
	if err != nil {
		return nil, err
	}
	tags = append(tags, t...)
	return tags, nil
}

// Code generated by mockery v2.26.0. DO NOT EDIT.

package featureflag

import mock "github.com/stretchr/testify/mock"

// MockRepo is an autogenerated mock type for the Repo type
type MockRepo struct {
	mock.Mock
}

// IsAccountPendingActionPublisherEnabled provides a mock function with given fields:
func (_m *MockRepo) IsAccountPendingActionPublisherEnabled() bool {
	ret := _m.Called()

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsR2Enabled provides a mock function with given fields:
func (_m *MockRepo) IsR2Enabled() bool {
	ret := _m.Called()

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

type mockConstructorTestingTNewMockRepo interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockRepo creates a new instance of MockRepo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockRepo(t mockConstructorTestingTNewMockRepo) *MockRepo {
	mock := &MockRepo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

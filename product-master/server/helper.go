package server

import (
	"fmt"
	"reflect"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/localisation"

	"gitlab.myteksi.net/dbmy/common/tenants"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils/client"
	godata "gitlab.myteksi.net/gophers/go/commons/data" // this package must be loaded after servus package

	"gitlab.myteksi.net/dakota/servus/v2"
)

func registerClients(app *servus.Application) {
	app.MustRegister("client.hermes", client.HermesClient)
}

func registerDAOs(app *servus.Application, appCfg *config.AppConfig) {
	godata.SetDatetimePrecision(appCfg.GetMySQLDatetimePrecision())
	app.MustRegister("ProductTemplateDAO", storage.NewProductTemplateDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductDAO", storage.NewProductDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductVariantDAO", storage.NewProductVariantDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductTemplateParameterDAO", storage.NewProductTemplateParameterDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductVariantParameterDAO", storage.NewProductVariantParameterDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("TransactionCatalogueDAO", storage.NewTransactionCatalogueDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductVariantTransactionCatalogueMappingDAO", storage.NewProductVariantTransactionCatalogueMappingDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("GeneralLedgerDAO", storage.NewGeneralLedgerDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("InternalAccountDAO", storage.NewInternalAccountDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductVariantTransactionCatalogueInternalAccountMappingDAO", storage.NewProductVariantTransactionCatalogueInternalAccountMappingDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("BaseInterestDAO", storage.NewBaseInterestDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("BaseInterestVersionDAO", storage.NewBaseInterestVersionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("BaseInterestTimeSlabRateDAO", storage.NewBaseInterestTimeSlabRateDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("DepositInterestDAO", storage.NewDepositInterestDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("DepositInterestVersionDAO", storage.NewDepositInterestVersionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("DepositInterestAmountSlabRateDAO", storage.NewDepositInterestAmountSlabRateDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("PocketTemplateDAO", storage.NewPocketTemplateDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("PocketTemplateQuestionDAO", storage.NewPocketTemplateQuestionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("PocketTemplateAnswerSuggestionDAO", storage.NewPocketTemplateAnswerSuggestionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("PocketTemplateImageSuggestionDAO", storage.NewPocketTemplateImageSuggestionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ParameterChangeScheduleDAO", storage.NewParameterChangeScheduleDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanAllowedAmountTenorSlabDAO", storage.NewLoanAllowedAmountTenorSlabDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanInterestDAO", storage.NewLoanInterestDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanInterestVersionDAO", storage.NewLoanInterestVersionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanInterestSlabRateDAO", storage.NewLoanInterestSlabRateDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanPastDueVersionDAO", storage.NewLoanPastDueVersionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanPastDueSlabDAO", storage.NewLoanPastDueSlabDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanInstructionVersionDAO", storage.NewLoanInstructionVersionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanInstructionDAO", storage.NewLoanInstructionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductVariantQuestionDAO", storage.NewProductVariantQuestionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("ProductVariantAnswerSuggestionDAO", storage.NewProductVariantAnswerSuggestionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanDocumentSubmissionOptionDAO", storage.NewLoanDocumentSubmissionOptionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanDocumentSubmissionOptionVersionDAO", storage.NewLoanDocumentSubmissionOptionVersionDAO(appCfg.Data, app.GetStatsD()))
	app.MustRegister("LoanDocumentSubmissionOptionParametersDAO", storage.NewLoanDocumentSubmissionOptionParametersDAO(appCfg.Data, app.GetStatsD()))
}

func registerDefaults(appCfg *config.AppConfig) {
	emptyLocaleConfig := config.Locale{}
	if reflect.DeepEqual(emptyLocaleConfig, appCfg.Locale) {
		appCfg.Locale.Currency = constants.DefaultCurrency
	}
	err := localisation.Configure(appCfg.Locale.DefaultLanguage, appCfg.Locale.AcceptedLanguagesToLocales)
	if err != nil {
		panic(fmt.Errorf("failed to configure localisation for %v: %w", appCfg.Locale, err))
	}
}

func registerTenant(app *servus.Application, tenantName string) {
	app.MustRegister("tenant", tenants.NewTenant(tenantName))
}

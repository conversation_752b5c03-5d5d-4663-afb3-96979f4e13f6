// Package server ...
package server

import (
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/handlers"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"gitlab.myteksi.net/dakota/servus/v2"
)

// Serve ...
func Serve() {
	appCfg := &config.AppConfig{}
	service := &handlers.ProductMasterService{}

	app := servus.Default(servus.WithAppConfig(appCfg))

	registerClients(app)
	registerDAOs(app, appCfg)
	registerDefaults(appCfg)

	registerTenant(app, appCfg.Tenant)
	app.MustRegister("servus.DataConfig", appCfg.Data)
	app.MustRegister("DBStore", &storage.DBStore{})
	app.MustRegister("service", service)
	service.RegisterRoutes(app)

	app.Run()
}

// Package config maintains the configurations needed for product-master apis.
package config

import (
	"gitlab.myteksi.net/dakota/servus/v2"
)

const (
	mySQLDatetimePrecision = 6
)

// AppConfig ...
type AppConfig struct {
	servus.DefaultAppConfig
	ProductMasterConfig ProductMasterConfig `json:"productMasterConfig"`
	HermesConfig        HermesConfig        `json:"hermesConfig"`
	Locale              Locale              `json:"locale"`
	Tenant              string              `json:"tenant"`
	FeatureFlags        FeatureFlags        `json:"featureFlags"`
}

// ProductMasterConfig ...
type ProductMasterConfig struct {
	ClientConfig ClientConfig `json:"clientConfig"`
}

// ClientConfig ...
type ClientConfig struct {
	MaxIdleConnsPerHost           int `json:"maxIdleConnsPerHost"`
	IdleConnTimeoutInMillis       int `json:"idleConnTimeoutInSMillis"`
	TimeoutInMillis               int `json:"timeoutInMillis"`
	RequestLogLockTimeoutInMillis int `json:"requestLogLockTimeoutInMillis"`
}

// HermesConfig ...
type HermesConfig struct {
	BaseURL string `json:"baseURL"`
}

// Locale ...
type Locale struct {
	Currency                   string            `json:"currency"`
	DefaultLanguage            string            `json:"defaultLanguage"`
	AcceptedLanguagesToLocales map[string]string `json:"acceptedLanguagesToLocales"`
}

// GetMySQLDatetimePrecision returns datetime precision config (0 -> second, 3 -> ms, 6 -> microsecond) for datetime columns
func (c *AppConfig) GetMySQLDatetimePrecision() int {
	return mySQLDatetimePrecision
}

// LogConfig ...
func (c *AppConfig) LogConfig() *servus.LogConfig {
	return c.Logger
}

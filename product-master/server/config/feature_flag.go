package config

import "gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/featureflag"

var _ featureflag.Repo = (*FeatureFlags)(nil)

// FeatureFlags ...
type FeatureFlags struct {
	EnableGetDepositInterestByEffectiveDate bool `json:"enableGetDepositInterestByEffectiveDate"`
	EnableBoostPocketFESpotRate             bool `json:"enableBoostPocketFESpotRate"`
}

// IsGetDepositInterestByEffectiveDateEnabled ...
func (f *FeatureFlags) IsGetDepositInterestByEffectiveDateEnabled() bool {
	return f.EnableGetDepositInterestByEffectiveDate
}

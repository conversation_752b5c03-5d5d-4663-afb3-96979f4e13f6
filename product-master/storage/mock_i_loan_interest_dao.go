// Code generated by mockery v2.9.4. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"
)

// MockILoanInterestDAO is an autogenerated mock type for the ILoanInterestDAO type
type MockILoanInterestDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockILoanInterestDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInterest, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*LoanInterest
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*LoanInterest); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*LoanInterest)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockILoanInterestDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInterest, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*LoanInterest
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*LoanInterest); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*LoanInterest)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockILoanInterestDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInterest, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *LoanInterest
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *LoanInterest); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*LoanInterest)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockILoanInterestDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInterest, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *LoanInterest
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *LoanInterest); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*LoanInterest)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockILoanInterestDAO) Save(ctx context.Context, newData *LoanInterest) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanInterest) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockILoanInterestDAO) SaveBatch(ctx context.Context, newData []*LoanInterest) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*LoanInterest) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockILoanInterestDAO) Update(ctx context.Context, newData *LoanInterest) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanInterest) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockILoanInterestDAO) UpdateEntity(ctx context.Context, preData *LoanInterest, newData *LoanInterest) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanInterest, *LoanInterest) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockILoanInterestDAO) Upsert(ctx context.Context, newData *LoanInterest) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *LoanInterest) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

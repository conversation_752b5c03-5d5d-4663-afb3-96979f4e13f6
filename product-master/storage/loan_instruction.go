package storage

import (
	"encoding/json"
	"time"
)

// LoanInstruction ...
type LoanInstruction struct {
	ID                       uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID                 string          `sql-col:"public_id"`
	LoanInstructionVersionID uint64          `sql-col:"loan_instruction_version_id"`
	Code                     string          `sql-col:"code"`
	Name                     string          `sql-col:"name"`
	CreatedAt                time.Time       `sql-col:"created_at"`
	CreatedBy                string          `sql-col:"created_by"`
	UpdatedAt                time.Time       `sql-col:"updated_at"`
	UpdatedBy                string          `sql-col:"updated_by"`
	Restrictions             json.RawMessage `sql-col:"restrictions" data-type:"json"`
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var productDao = "product_dao"

// GetID implements the GetID function for Entity Interface
func (impl *Product) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *Product) SetID(ID string) {
	// replace the logic to populate unique ID for Product
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *Product) NewEntity() data.Entity {
	return &Product{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *Product) GetTableName() string {
	return "product"
}

// IProductDAO is the dao interface for Product
//go:generate mockery --name IProductDAO --inpackage --case=underscore
type IProductDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*Product, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Product, error)

	// Save ...
	Save(ctx context.Context, newData *Product) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*Product) error

	// Update ...
	Update(ctx context.Context, newData *Product) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *Product, newData *Product) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*Product, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Product, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *Product) error
}

// ProductDAO ...
type ProductDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductDAO creates a data access object for Product
func NewProductDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &Product{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*Product, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Product), err
}

// LoadByIDOnSlave ...
func (dao *ProductDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Product, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Product), err
}

// Save ...
func (dao *ProductDAO) Save(ctx context.Context, entity *Product) error {
	methodName := "save"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductDAO) SaveBatch(ctx context.Context, entities []*Product) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductDAO) Update(ctx context.Context, data *Product) error {
	methodName := "update"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductDAO) UpdateEntity(ctx context.Context, preData *Product, newData *Product) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*Product, error) {
	methodName := "find"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Product, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Product))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Product, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Product, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Product))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductDAO) Upsert(ctx context.Context, data *Product) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(productDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(productDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

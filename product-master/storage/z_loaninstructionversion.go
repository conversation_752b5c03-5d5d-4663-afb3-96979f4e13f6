package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_instruction_versionDao = "loan_instruction_version_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanInstructionVersion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanInstructionVersion) SetID(ID string) {
	// replace the logic to populate unique ID for LoanInstructionVersion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanInstructionVersion) NewEntity() data.Entity {
	return &LoanInstructionVersion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanInstructionVersion) GetTableName() string {
	return "loan_instruction_version"
}

// ILoanInstructionVersionDAO is the dao interface for LoanInstructionVersion
//
//go:generate mockery --name ILoanInstructionVersionDAO --inpackage --case=underscore
type ILoanInstructionVersionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInstructionVersion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInstructionVersion, error)

	// Save ...
	Save(ctx context.Context, newData *LoanInstructionVersion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanInstructionVersion) error

	// Update ...
	Update(ctx context.Context, newData *LoanInstructionVersion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanInstructionVersion, newData *LoanInstructionVersion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInstructionVersion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInstructionVersion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanInstructionVersion) error
}

// LoanInstructionVersionDAO ...
type LoanInstructionVersionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanInstructionVersionDAO creates a data access object for LoanInstructionVersion
func NewLoanInstructionVersionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanInstructionVersionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanInstructionVersionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanInstructionVersion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanInstructionVersionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInstructionVersion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInstructionVersion), err
}

// LoadByIDOnSlave ...
func (dao *LoanInstructionVersionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInstructionVersion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInstructionVersion), err
}

// Save ...
func (dao *LoanInstructionVersionDAO) Save(ctx context.Context, entity *LoanInstructionVersion) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanInstructionVersionDAO) SaveBatch(ctx context.Context, entities []*LoanInstructionVersion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanInstructionVersionDAO) Update(ctx context.Context, data *LoanInstructionVersion) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanInstructionVersionDAO) UpdateEntity(ctx context.Context, preData *LoanInstructionVersion, newData *LoanInstructionVersion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanInstructionVersionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInstructionVersion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInstructionVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInstructionVersion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanInstructionVersionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInstructionVersion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInstructionVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInstructionVersion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanInstructionVersionDAO) Upsert(ctx context.Context, data *LoanInstructionVersion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_instruction_versionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_instruction_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

import "time"

// LoanInstructionVersion ...
type LoanInstructionVersion struct {
	ID               uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID         string    `sql-col:"public_id"`
	ProductVariantID uint64    `sql-col:"product_variant_id"`
	Version          string    `sql-col:"version"`
	InstructionType  string    `sql-col:"instruction_type"`
	EffectiveDate    time.Time `sql-col:"effective_date"`
	Description      string    `sql-col:"description"`
	CreatedAt        time.Time `sql-col:"created_at"`
	CreatedBy        string    `sql-col:"created_by"`
	UpdatedAt        time.Time `sql-col:"updated_at"`
	UpdatedBy        string    `sql-col:"updated_by"`
}

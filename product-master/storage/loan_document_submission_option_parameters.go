package storage

import (
	"encoding/json"
	"time"
)

// LoanDocumentSubmissionOptionParameters represents the "loan_document_submission_option_parameters" storage object.

type LoanDocumentSubmissionOptionParameters struct {
	ID                                    uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
	LoanDocumentSubmissionOptionVersionID uint64          `sql-col:"loan_document_submission_option_version_id"`
	IsEnabled                             bool            `sql-col:"is_enabled"`
	Priority                              int             `sql-col:"priority"`
	RequiredDocumentNumber                int             `sql-col:"required_document_number"`
	RequiredDocumentUnit                  string          `sql-col:"required_document_unit"`
	ExcludedMonths                        int             `sql-col:"excluded_months"`
	AllowedFileExtensions                 json.RawMessage `sql-col:"allowed_file_extensions"` // Assuming JSON stored as string
	MaxFileSizeInBytes                    uint64          `sql-col:"max_file_size_in_bytes"`
	MaxUploadLimit                        int             `sql-col:"max_upload_limit"`
	CreatedAt                             time.Time       `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	CreatedBy                             string          `sql-col:"created_by"`
	UpdatedAt                             time.Time       `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy                             string          `sql-col:"updated_by"`
}

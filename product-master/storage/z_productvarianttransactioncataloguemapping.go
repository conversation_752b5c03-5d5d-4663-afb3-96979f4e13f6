package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var product_variant_transaction_catalogue_mappingDao = "product_variant_transaction_catalogue_mapping_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductVariantTransactionCatalogueMapping) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductVariantTransactionCatalogueMapping) SetID(ID string) {
	// replace the logic to populate unique ID for ProductVariantTransactionCatalogueMapping
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductVariantTransactionCatalogueMapping) NewEntity() data.Entity {
	return &ProductVariantTransactionCatalogueMapping{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductVariantTransactionCatalogueMapping) GetTableName() string {
	return "product_variant_transaction_catalogue_mapping"
}

// IProductVariantTransactionCatalogueMappingDAO is the dao interface for ProductVariantTransactionCatalogueMapping
//go:generate mockery --name IProductVariantTransactionCatalogueMappingDAO --inpackage --case=underscore
type IProductVariantTransactionCatalogueMappingDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueMapping, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueMapping, error)

	// Save ...
	Save(ctx context.Context, newData *ProductVariantTransactionCatalogueMapping) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductVariantTransactionCatalogueMapping) error

	// Update ...
	Update(ctx context.Context, newData *ProductVariantTransactionCatalogueMapping) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductVariantTransactionCatalogueMapping, newData *ProductVariantTransactionCatalogueMapping) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueMapping, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueMapping, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductVariantTransactionCatalogueMapping) error
}

// ProductVariantTransactionCatalogueMappingDAO ...
type ProductVariantTransactionCatalogueMappingDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductVariantTransactionCatalogueMappingDAO creates a data access object for ProductVariantTransactionCatalogueMapping
func NewProductVariantTransactionCatalogueMappingDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductVariantTransactionCatalogueMappingDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductVariantTransactionCatalogueMappingDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductVariantTransactionCatalogueMapping{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueMapping, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantTransactionCatalogueMapping), err
}

// LoadByIDOnSlave ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueMapping, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantTransactionCatalogueMapping), err
}

// Save ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) Save(ctx context.Context, entity *ProductVariantTransactionCatalogueMapping) error {
	methodName := "save"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) SaveBatch(ctx context.Context, entities []*ProductVariantTransactionCatalogueMapping) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) Update(ctx context.Context, data *ProductVariantTransactionCatalogueMapping) error {
	methodName := "update"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) UpdateEntity(ctx context.Context, preData *ProductVariantTransactionCatalogueMapping, newData *ProductVariantTransactionCatalogueMapping) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueMapping, error) {
	methodName := "find"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantTransactionCatalogueMapping, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantTransactionCatalogueMapping))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueMapping, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantTransactionCatalogueMapping, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantTransactionCatalogueMapping))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductVariantTransactionCatalogueMappingDAO) Upsert(ctx context.Context, data *ProductVariantTransactionCatalogueMapping) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_mappingDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var deposit_interestDao = "deposit_interest_dao"

// GetID implements the GetID function for Entity Interface
func (impl *DepositInterest) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *DepositInterest) SetID(ID string) {
	// replace the logic to populate unique ID for DepositInterest
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *DepositInterest) NewEntity() data.Entity {
	return &DepositInterest{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *DepositInterest) GetTableName() string {
	return "deposit_interest"
}

// IDepositInterestDAO is the dao interface for DepositInterest
//go:generate mockery --name IDepositInterestDAO --inpackage --case=underscore
type IDepositInterestDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*DepositInterest, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*DepositInterest, error)

	// Save ...
	Save(ctx context.Context, newData *DepositInterest) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*DepositInterest) error

	// Update ...
	Update(ctx context.Context, newData *DepositInterest) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *DepositInterest, newData *DepositInterest) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*DepositInterest, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*DepositInterest, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *DepositInterest) error
}

// DepositInterestDAO ...
type DepositInterestDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewDepositInterestDAO creates a data access object for DepositInterest
func NewDepositInterestDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *DepositInterestDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &DepositInterestDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &DepositInterest{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *DepositInterestDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*DepositInterest, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*DepositInterest), err
}

// LoadByIDOnSlave ...
func (dao *DepositInterestDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*DepositInterest, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*DepositInterest), err
}

// Save ...
func (dao *DepositInterestDAO) Save(ctx context.Context, entity *DepositInterest) error {
	methodName := "save"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *DepositInterestDAO) SaveBatch(ctx context.Context, entities []*DepositInterest) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *DepositInterestDAO) Update(ctx context.Context, data *DepositInterest) error {
	methodName := "update"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *DepositInterestDAO) UpdateEntity(ctx context.Context, preData *DepositInterest, newData *DepositInterest) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *DepositInterestDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*DepositInterest, error) {
	methodName := "find"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*DepositInterest, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*DepositInterest))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *DepositInterestDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*DepositInterest, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*DepositInterest, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*DepositInterest))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *DepositInterestDAO) Upsert(ctx context.Context, data *DepositInterest) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(deposit_interestDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(deposit_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

import "time"

// ParameterChangeSchedule ...
type ParameterChangeSchedule struct {
	ID                     uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	ParameterKey           string    `sql-col:"parameter_key"`
	ParameterVersion       string    `sql-col:"parameter_version"`
	SmartContractVersionID string    `sql-col:"smart_contract_version_id"`
	IsScheduled            bool      `sql-col:"is_scheduled"`
	NotificationDate       time.Time `sql-col:"notification_date"`
	IsNotificationSent     bool      `sql-col:"is_notification_sent"`
	CreatedBy              string    `sql-col:"created_by"`
	CreatedAt              time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy              string    `sql-col:"updated_by"`
	UpdatedAt              time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

package storage

import "time"

// LoanPastDueSlab ...
type LoanPastDueSlab struct {
	ID                   uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID             string    `sql-col:"public_id"`
	LoanPastDueVersionID uint64    `sql-col:"loan_past_due_version_id"`
	FromUnit             int32     `sql-col:"from_unit"`
	ToUnit               int32     `sql-col:"to_unit"`
	SlabType             string    `sql-col:"slab_type"`
	BucketName           string    `sql-col:"bucket_name"`
	CreatedBy            string    `sql-col:"created_by"`
	CreatedAt            time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy            string    `sql-col:"updated_by"`
	UpdatedAt            time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

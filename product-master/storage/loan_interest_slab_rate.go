package storage

import "time"

// LoanInterestSlabRate ...
type LoanInterestSlabRate struct {
	ID                               uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID                         string    `sql-col:"public_id"`
	LoanInterestVersionID            uint64    `sql-col:"loan_interest_version_id"`
	SlabType                         string    `sql-col:"slab_type"`
	FromUnit                         string    `sql-col:"from_unit"`
	ToUnit                           string    `sql-col:"to_unit"`
	BaseRateInterestSpreadPercentage string    `sql-col:"base_rate_interest_spread_percentage"`
	AbsoluteInterestRatePercentage   string    `sql-col:"absolute_interest_rate_percentage"`
	CreatedBy                        string    `sql-col:"created_by"`
	CreatedAt                        time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy                        string    `sql-col:"updated_by"`
	UpdatedAt                        time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

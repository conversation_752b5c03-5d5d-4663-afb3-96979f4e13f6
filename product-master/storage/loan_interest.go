package storage

import (
	"time"

	"database/sql"
)

// LoanInterest ...
type LoanInterest struct {
	ID                    uint64         `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID              string         `sql-col:"public_id"`
	ProductVariantID      uint64         `sql-col:"product_variant_id"`
	IsLinkedToBaseRate    bool           `sql-col:"is_linked_to_base_rate"`
	BaseInterestID        sql.NullInt64  `sql-col:"base_interest_id"`
	Code                  string         `sql-col:"code"`
	Name                  string         `sql-col:"name"`
	Description           sql.NullString `sql-col:"description"`
	Currency              string         `sql-col:"currency"`
	RoundOffType          string         `sql-col:"round_off_type"`
	InterestType          string         `sql-col:"interest_type"`
	InterestSlabUnitType  string         `sql-col:"interest_slab_unit_type"`
	InterestSlabStructure string         `sql-col:"interest_slab_structure"`
	CreatedBy             string         `sql-col:"created_by"`
	CreatedAt             time.Time      `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy             string         `sql-col:"updated_by"`
	UpdatedAt             time.Time      `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

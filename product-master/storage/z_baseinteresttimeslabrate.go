package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var base_interest_time_slab_rateDao = "base_interest_time_slab_rate_dao"

// GetID implements the GetID function for Entity Interface
func (impl *BaseInterestTimeSlabRate) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *BaseInterestTimeSlabRate) SetID(ID string) {
	// replace the logic to populate unique ID for BaseInterestTimeSlabRate
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *BaseInterestTimeSlabRate) NewEntity() data.Entity {
	return &BaseInterestTimeSlabRate{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *BaseInterestTimeSlabRate) GetTableName() string {
	return "base_interest_time_slab_rate"
}

// IBaseInterestTimeSlabRateDAO is the dao interface for BaseInterestTimeSlabRate
//go:generate mockery --name IBaseInterestTimeSlabRateDAO --inpackage --case=underscore
type IBaseInterestTimeSlabRateDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*BaseInterestTimeSlabRate, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*BaseInterestTimeSlabRate, error)

	// Save ...
	Save(ctx context.Context, newData *BaseInterestTimeSlabRate) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*BaseInterestTimeSlabRate) error

	// Update ...
	Update(ctx context.Context, newData *BaseInterestTimeSlabRate) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *BaseInterestTimeSlabRate, newData *BaseInterestTimeSlabRate) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestTimeSlabRate, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestTimeSlabRate, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *BaseInterestTimeSlabRate) error
}

// BaseInterestTimeSlabRateDAO ...
type BaseInterestTimeSlabRateDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewBaseInterestTimeSlabRateDAO creates a data access object for BaseInterestTimeSlabRate
func NewBaseInterestTimeSlabRateDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *BaseInterestTimeSlabRateDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &BaseInterestTimeSlabRateDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &BaseInterestTimeSlabRate{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *BaseInterestTimeSlabRateDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*BaseInterestTimeSlabRate, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*BaseInterestTimeSlabRate), err
}

// LoadByIDOnSlave ...
func (dao *BaseInterestTimeSlabRateDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*BaseInterestTimeSlabRate, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*BaseInterestTimeSlabRate), err
}

// Save ...
func (dao *BaseInterestTimeSlabRateDAO) Save(ctx context.Context, entity *BaseInterestTimeSlabRate) error {
	methodName := "save"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *BaseInterestTimeSlabRateDAO) SaveBatch(ctx context.Context, entities []*BaseInterestTimeSlabRate) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *BaseInterestTimeSlabRateDAO) Update(ctx context.Context, data *BaseInterestTimeSlabRate) error {
	methodName := "update"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *BaseInterestTimeSlabRateDAO) UpdateEntity(ctx context.Context, preData *BaseInterestTimeSlabRate, newData *BaseInterestTimeSlabRate) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *BaseInterestTimeSlabRateDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestTimeSlabRate, error) {
	methodName := "find"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*BaseInterestTimeSlabRate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*BaseInterestTimeSlabRate))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *BaseInterestTimeSlabRateDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestTimeSlabRate, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*BaseInterestTimeSlabRate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*BaseInterestTimeSlabRate))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *BaseInterestTimeSlabRateDAO) Upsert(ctx context.Context, data *BaseInterestTimeSlabRate) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(base_interest_time_slab_rateDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(base_interest_time_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

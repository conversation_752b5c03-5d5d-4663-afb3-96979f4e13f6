// Code generated by mockery v2.12.3. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"
)

// MockIProductVariantAnswerSuggestionDAO is an autogenerated mock type for the IProductVariantAnswerSuggestionDAO type
type MockIProductVariantAnswerSuggestionDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockIProductVariantAnswerSuggestionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantAnswerSuggestion, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductVariantAnswerSuggestion
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductVariantAnswerSuggestion); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductVariantAnswerSuggestion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIProductVariantAnswerSuggestionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantAnswerSuggestion, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductVariantAnswerSuggestion
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductVariantAnswerSuggestion); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductVariantAnswerSuggestion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductVariantAnswerSuggestionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantAnswerSuggestion, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductVariantAnswerSuggestion
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductVariantAnswerSuggestion); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductVariantAnswerSuggestion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductVariantAnswerSuggestionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantAnswerSuggestion, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductVariantAnswerSuggestion
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductVariantAnswerSuggestion); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductVariantAnswerSuggestion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantAnswerSuggestionDAO) Save(ctx context.Context, newData *ProductVariantAnswerSuggestion) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantAnswerSuggestion) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantAnswerSuggestionDAO) SaveBatch(ctx context.Context, newData []*ProductVariantAnswerSuggestion) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*ProductVariantAnswerSuggestion) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantAnswerSuggestionDAO) Update(ctx context.Context, newData *ProductVariantAnswerSuggestion) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantAnswerSuggestion) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockIProductVariantAnswerSuggestionDAO) UpdateEntity(ctx context.Context, preData *ProductVariantAnswerSuggestion, newData *ProductVariantAnswerSuggestion) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantAnswerSuggestion, *ProductVariantAnswerSuggestion) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantAnswerSuggestionDAO) Upsert(ctx context.Context, newData *ProductVariantAnswerSuggestion) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantAnswerSuggestion) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type NewMockIProductVariantAnswerSuggestionDAOT interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockIProductVariantAnswerSuggestionDAO creates a new instance of MockIProductVariantAnswerSuggestionDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockIProductVariantAnswerSuggestionDAO(t NewMockIProductVariantAnswerSuggestionDAOT) *MockIProductVariantAnswerSuggestionDAO {
	mock := &MockIProductVariantAnswerSuggestionDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

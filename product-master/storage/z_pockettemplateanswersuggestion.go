package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var pocket_template_answer_suggestionDao = "pocket_template_answer_suggestion_dao"

// GetID implements the GetID function for Entity Interface
func (impl *PocketTemplateAnswerSuggestion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *PocketTemplateAnswerSuggestion) SetID(ID string) {
	// replace the logic to populate unique ID for PocketTemplateAnswerSuggestion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *PocketTemplateAnswerSuggestion) NewEntity() data.Entity {
	return &PocketTemplateAnswerSuggestion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *PocketTemplateAnswerSuggestion) GetTableName() string {
	return "pocket_template_answer_suggestion"
}

// IPocketTemplateAnswerSuggestionDAO is the dao interface for PocketTemplateAnswerSuggestion
//go:generate mockery --name IPocketTemplateAnswerSuggestionDAO --inpackage --case=underscore
type IPocketTemplateAnswerSuggestionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateAnswerSuggestion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateAnswerSuggestion, error)

	// Save ...
	Save(ctx context.Context, newData *PocketTemplateAnswerSuggestion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*PocketTemplateAnswerSuggestion) error

	// Update ...
	Update(ctx context.Context, newData *PocketTemplateAnswerSuggestion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *PocketTemplateAnswerSuggestion, newData *PocketTemplateAnswerSuggestion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateAnswerSuggestion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateAnswerSuggestion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *PocketTemplateAnswerSuggestion) error
}

// PocketTemplateAnswerSuggestionDAO ...
type PocketTemplateAnswerSuggestionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewPocketTemplateAnswerSuggestionDAO creates a data access object for PocketTemplateAnswerSuggestion
func NewPocketTemplateAnswerSuggestionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *PocketTemplateAnswerSuggestionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &PocketTemplateAnswerSuggestionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &PocketTemplateAnswerSuggestion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *PocketTemplateAnswerSuggestionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateAnswerSuggestion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplateAnswerSuggestion), err
}

// LoadByIDOnSlave ...
func (dao *PocketTemplateAnswerSuggestionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateAnswerSuggestion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplateAnswerSuggestion), err
}

// Save ...
func (dao *PocketTemplateAnswerSuggestionDAO) Save(ctx context.Context, entity *PocketTemplateAnswerSuggestion) error {
	methodName := "save"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *PocketTemplateAnswerSuggestionDAO) SaveBatch(ctx context.Context, entities []*PocketTemplateAnswerSuggestion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *PocketTemplateAnswerSuggestionDAO) Update(ctx context.Context, data *PocketTemplateAnswerSuggestion) error {
	methodName := "update"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *PocketTemplateAnswerSuggestionDAO) UpdateEntity(ctx context.Context, preData *PocketTemplateAnswerSuggestion, newData *PocketTemplateAnswerSuggestion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *PocketTemplateAnswerSuggestionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateAnswerSuggestion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplateAnswerSuggestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplateAnswerSuggestion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *PocketTemplateAnswerSuggestionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateAnswerSuggestion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplateAnswerSuggestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplateAnswerSuggestion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *PocketTemplateAnswerSuggestionDAO) Upsert(ctx context.Context, data *PocketTemplateAnswerSuggestion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(pocket_template_answer_suggestionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_template_answer_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var parameter_change_scheduleDao = "parameter_change_schedule_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ParameterChangeSchedule) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ParameterChangeSchedule) SetID(ID string) {
	// replace the logic to populate unique ID for ParameterChangeSchedule
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ParameterChangeSchedule) NewEntity() data.Entity {
	return &ParameterChangeSchedule{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ParameterChangeSchedule) GetTableName() string {
	return "parameter_change_schedule"
}

// IParameterChangeScheduleDAO is the dao interface for ParameterChangeSchedule
//go:generate mockery --name IParameterChangeScheduleDAO --inpackage --case=underscore
type IParameterChangeScheduleDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ParameterChangeSchedule, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ParameterChangeSchedule, error)

	// Save ...
	Save(ctx context.Context, newData *ParameterChangeSchedule) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ParameterChangeSchedule) error

	// Update ...
	Update(ctx context.Context, newData *ParameterChangeSchedule) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ParameterChangeSchedule, newData *ParameterChangeSchedule) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ParameterChangeSchedule, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ParameterChangeSchedule, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ParameterChangeSchedule) error
}

// ParameterChangeScheduleDAO ...
type ParameterChangeScheduleDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewParameterChangeScheduleDAO creates a data access object for ParameterChangeSchedule
func NewParameterChangeScheduleDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ParameterChangeScheduleDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ParameterChangeScheduleDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ParameterChangeSchedule{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ParameterChangeScheduleDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ParameterChangeSchedule, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ParameterChangeSchedule), err
}

// LoadByIDOnSlave ...
func (dao *ParameterChangeScheduleDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ParameterChangeSchedule, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ParameterChangeSchedule), err
}

// Save ...
func (dao *ParameterChangeScheduleDAO) Save(ctx context.Context, entity *ParameterChangeSchedule) error {
	methodName := "save"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ParameterChangeScheduleDAO) SaveBatch(ctx context.Context, entities []*ParameterChangeSchedule) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ParameterChangeScheduleDAO) Update(ctx context.Context, data *ParameterChangeSchedule) error {
	methodName := "update"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ParameterChangeScheduleDAO) UpdateEntity(ctx context.Context, preData *ParameterChangeSchedule, newData *ParameterChangeSchedule) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ParameterChangeScheduleDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ParameterChangeSchedule, error) {
	methodName := "find"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ParameterChangeSchedule, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ParameterChangeSchedule))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ParameterChangeScheduleDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ParameterChangeSchedule, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ParameterChangeSchedule, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ParameterChangeSchedule))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ParameterChangeScheduleDAO) Upsert(ctx context.Context, data *ParameterChangeSchedule) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(parameter_change_scheduleDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(parameter_change_scheduleDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

import (
	"time"
)

// BaseInterestTimeSlabRate ...
type BaseInterestTimeSlabRate struct {
	ID                    uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID              string    `sql-col:"public_id"`
	BaseInterestVersionID uint64    `sql-col:"base_interest_version_id"`
	TermUnit              string    `sql-col:"term_unit"`
	TermValue             int32     `sql-col:"term_value"`
	BaseRatePercentage    string    `sql-col:"base_rate_percentage"`
	CreatedBy             string    `sql-col:"created_by"`
	CreatedAt             time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy             string    `sql-col:"updated_by"`
	UpdatedAt             time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

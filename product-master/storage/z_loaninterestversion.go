package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loanInterestVersionDao = "loan_interest_version_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanInterestVersion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanInterestVersion) SetID(ID string) {
	// replace the logic to populate unique ID for LoanInterestVersion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanInterestVersion) NewEntity() data.Entity {
	return &LoanInterestVersion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanInterestVersion) GetTableName() string {
	return "loan_interest_version"
}

// ILoanInterestVersionDAO is the dao interface for LoanInterestVersion
//
//go:generate mockery --name ILoanInterestVersionDAO --inpackage --case=underscore
type ILoanInterestVersionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInterestVersion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInterestVersion, error)

	// Save ...
	Save(ctx context.Context, newData *LoanInterestVersion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanInterestVersion) error

	// Update ...
	Update(ctx context.Context, newData *LoanInterestVersion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanInterestVersion, newData *LoanInterestVersion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestVersion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestVersion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanInterestVersion) error
}

// LoanInterestVersionDAO ...
type LoanInterestVersionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanInterestVersionDAO creates a data access object for LoanInterestVersion
func NewLoanInterestVersionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanInterestVersionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanInterestVersionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanInterestVersion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanInterestVersionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInterestVersion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInterestVersion), err
}

// LoadByIDOnSlave ...
func (dao *LoanInterestVersionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInterestVersion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInterestVersion), err
}

// Save ...
func (dao *LoanInterestVersionDAO) Save(ctx context.Context, entity *LoanInterestVersion) error {
	methodName := "save"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanInterestVersionDAO) SaveBatch(ctx context.Context, entities []*LoanInterestVersion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanInterestVersionDAO) Update(ctx context.Context, data *LoanInterestVersion) error {
	methodName := "update"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanInterestVersionDAO) UpdateEntity(ctx context.Context, preData *LoanInterestVersion, newData *LoanInterestVersion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanInterestVersionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestVersion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInterestVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInterestVersion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanInterestVersionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestVersion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInterestVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInterestVersion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanInterestVersionDAO) Upsert(ctx context.Context, data *LoanInterestVersion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loanInterestVersionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanInterestVersionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

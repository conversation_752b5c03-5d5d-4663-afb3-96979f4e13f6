package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loanInterestSlabRateDao = "loan_interest_slab_rate_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanInterestSlabRate) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanInterestSlabRate) SetID(ID string) {
	// replace the logic to populate unique ID for LoanInterestSlabRate
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanInterestSlabRate) NewEntity() data.Entity {
	return &LoanInterestSlabRate{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanInterestSlabRate) GetTableName() string {
	return "loan_interest_slab_rate"
}

// ILoanInterestSlabRateDAO is the dao interface for LoanInterestSlabRate
//
//go:generate mockery --name ILoanInterestSlabRateDAO --inpackage --case=underscore
type ILoanInterestSlabRateDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInterestSlabRate, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInterestSlabRate, error)

	// Save ...
	Save(ctx context.Context, newData *LoanInterestSlabRate) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanInterestSlabRate) error

	// Update ...
	Update(ctx context.Context, newData *LoanInterestSlabRate) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanInterestSlabRate, newData *LoanInterestSlabRate) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestSlabRate, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestSlabRate, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanInterestSlabRate) error
}

// LoanInterestSlabRateDAO ...
type LoanInterestSlabRateDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanInterestSlabRateDAO creates a data access object for LoanInterestSlabRate
func NewLoanInterestSlabRateDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanInterestSlabRateDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanInterestSlabRateDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanInterestSlabRate{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanInterestSlabRateDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInterestSlabRate, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInterestSlabRate), err
}

// LoadByIDOnSlave ...
func (dao *LoanInterestSlabRateDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInterestSlabRate, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInterestSlabRate), err
}

// Save ...
func (dao *LoanInterestSlabRateDAO) Save(ctx context.Context, entity *LoanInterestSlabRate) error {
	methodName := "save"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanInterestSlabRateDAO) SaveBatch(ctx context.Context, entities []*LoanInterestSlabRate) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanInterestSlabRateDAO) Update(ctx context.Context, data *LoanInterestSlabRate) error {
	methodName := "update"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanInterestSlabRateDAO) UpdateEntity(ctx context.Context, preData *LoanInterestSlabRate, newData *LoanInterestSlabRate) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanInterestSlabRateDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestSlabRate, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInterestSlabRate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInterestSlabRate))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanInterestSlabRateDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInterestSlabRate, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInterestSlabRate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInterestSlabRate))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanInterestSlabRateDAO) Upsert(ctx context.Context, data *LoanInterestSlabRate) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loanInterestSlabRateDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanInterestSlabRateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

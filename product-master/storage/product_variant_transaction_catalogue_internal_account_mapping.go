package storage

import (
	"time"
)

// ProductVariantTransactionCatalogueInternalAccountMapping ...
type ProductVariantTransactionCatalogueInternalAccountMapping struct {
	ID                                          uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID                                    string    `sql-col:"public_id"`
	ProductVariantTransactionCatalogueMappingID uint64    `sql-col:"product_variant_transaction_catalogue_mapping_id"`
	InternalAccountID                           uint64    `sql-col:"internal_account_id"`
	IdentifierKey                               string    `sql-col:"identifier_key"`
	Status                                      string    `sql-col:"status"`
	CreatedBy                                   string    `sql-col:"created_by"`
	CreatedAt                                   time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy                                   string    `sql-col:"updated_by"`
	UpdatedAt                                   time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

package storage

import (
	"time"
)

// LoanDocumentSubmissionOption represents the "loan_document_submission_option" storage object.
type LoanDocumentSubmissionOption struct {
	ID               uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	ProductVariantID uint64    `sql-col:"product_variant_id"`
	DocumentType     string    `sql-col:"document_type"`
	SalaryType       string    `sql-col:"salary_type"`
	ApplicationType  string    `sql-col:"application_type"`
	CreatedAt        time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	CreatedBy        string    `sql-col:"created_by"`
	UpdatedAt        time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy        string    `sql-col:"updated_by"`
}

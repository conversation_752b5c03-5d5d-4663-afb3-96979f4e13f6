package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_past_due_versionDao = "loan_past_due_version_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanPastDueVersion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanPastDueVersion) SetID(ID string) {
	// replace the logic to populate unique ID for LoanPastDueVersion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanPastDueVersion) NewEntity() data.Entity {
	return &LoanPastDueVersion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanPastDueVersion) GetTableName() string {
	return "loan_past_due_version"
}

// ILoanPastDueVersionDAO is the dao interface for LoanPastDueVersion
//
//go:generate mockery --name ILoanPastDueVersionDAO --inpackage --case=underscore
type ILoanPastDueVersionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueVersion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueVersion, error)

	// Save ...
	Save(ctx context.Context, newData *LoanPastDueVersion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanPastDueVersion) error

	// Update ...
	Update(ctx context.Context, newData *LoanPastDueVersion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanPastDueVersion, newData *LoanPastDueVersion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueVersion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueVersion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanPastDueVersion) error
}

// LoanPastDueVersionDAO ...
type LoanPastDueVersionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanPastDueVersionDAO creates a data access object for LoanPastDueVersion
func NewLoanPastDueVersionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanPastDueVersionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanPastDueVersionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanPastDueVersion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanPastDueVersionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueVersion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanPastDueVersion), err
}

// LoadByIDOnSlave ...
func (dao *LoanPastDueVersionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueVersion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanPastDueVersion), err
}

// Save ...
func (dao *LoanPastDueVersionDAO) Save(ctx context.Context, entity *LoanPastDueVersion) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanPastDueVersionDAO) SaveBatch(ctx context.Context, entities []*LoanPastDueVersion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanPastDueVersionDAO) Update(ctx context.Context, data *LoanPastDueVersion) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanPastDueVersionDAO) UpdateEntity(ctx context.Context, preData *LoanPastDueVersion, newData *LoanPastDueVersion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanPastDueVersionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueVersion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanPastDueVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanPastDueVersion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanPastDueVersionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueVersion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanPastDueVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanPastDueVersion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanPastDueVersionDAO) Upsert(ctx context.Context, data *LoanPastDueVersion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_past_due_versionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

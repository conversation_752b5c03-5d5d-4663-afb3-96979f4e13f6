package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_instructionDao = "loan_instruction_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanInstruction) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanInstruction) SetID(ID string) {
	// replace the logic to populate unique ID for LoanInstruction
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanInstruction) NewEntity() data.Entity {
	return &LoanInstruction{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanInstruction) GetTableName() string {
	return "loan_instruction"
}

// ILoanInstructionDAO is the dao interface for LoanInstruction
//
//go:generate mockery --name ILoanInstructionDAO --inpackage --case=underscore
type ILoanInstructionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInstruction, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInstruction, error)

	// Save ...
	Save(ctx context.Context, newData *LoanInstruction) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanInstruction) error

	// Update ...
	Update(ctx context.Context, newData *LoanInstruction) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanInstruction, newData *LoanInstruction) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInstruction, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInstruction, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanInstruction) error
}

// LoanInstructionDAO ...
type LoanInstructionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanInstructionDAO creates a data access object for LoanInstruction
func NewLoanInstructionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanInstructionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanInstructionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanInstruction{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanInstructionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInstruction, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInstruction), err
}

// LoadByIDOnSlave ...
func (dao *LoanInstructionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInstruction, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInstruction), err
}

// Save ...
func (dao *LoanInstructionDAO) Save(ctx context.Context, entity *LoanInstruction) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanInstructionDAO) SaveBatch(ctx context.Context, entities []*LoanInstruction) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanInstructionDAO) Update(ctx context.Context, data *LoanInstruction) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanInstructionDAO) UpdateEntity(ctx context.Context, preData *LoanInstruction, newData *LoanInstruction) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanInstructionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInstruction, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInstruction, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInstruction))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanInstructionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInstruction, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInstruction, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInstruction))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanInstructionDAO) Upsert(ctx context.Context, data *LoanInstruction) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_instructionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_instructionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

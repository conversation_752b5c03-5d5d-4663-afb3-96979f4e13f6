// Code generated by mockery v2.12.3. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/dakota/servus/v2/data"
)

// MockIProductTemplateParameterDAO is an autogenerated mock type for the IProductTemplateParameterDAO type
type MockIProductTemplateParameterDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockIProductTemplateParameterDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplateParameter, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductTemplateParameter
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductTemplateParameter); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductTemplateParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIProductTemplateParameterDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplateParameter, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductTemplateParameter
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductTemplateParameter); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductTemplateParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductTemplateParameterDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductTemplateParameter, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductTemplateParameter
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductTemplateParameter); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductTemplateParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductTemplateParameterDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductTemplateParameter, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductTemplateParameter
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductTemplateParameter); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductTemplateParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockIProductTemplateParameterDAO) Save(ctx context.Context, newData *ProductTemplateParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductTemplateParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockIProductTemplateParameterDAO) SaveBatch(ctx context.Context, newData []*ProductTemplateParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*ProductTemplateParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockIProductTemplateParameterDAO) Update(ctx context.Context, newData *ProductTemplateParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductTemplateParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockIProductTemplateParameterDAO) UpdateEntity(ctx context.Context, preData *ProductTemplateParameter, newData *ProductTemplateParameter) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductTemplateParameter, *ProductTemplateParameter) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockIProductTemplateParameterDAO) Upsert(ctx context.Context, newData *ProductTemplateParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductTemplateParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type NewMockIProductTemplateParameterDAOT interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockIProductTemplateParameterDAO creates a new instance of MockIProductTemplateParameterDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockIProductTemplateParameterDAO(t NewMockIProductTemplateParameterDAOT) *MockIProductTemplateParameterDAO {
	mock := &MockIProductTemplateParameterDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

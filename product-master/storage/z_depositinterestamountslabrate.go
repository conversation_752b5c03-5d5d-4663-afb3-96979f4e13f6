package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var deposit_interest_amount_slab_rateDao = "deposit_interest_amount_slab_rate_dao"

// GetID implements the GetID function for Entity Interface
func (impl *DepositInterestAmountSlabRate) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *DepositInterestAmountSlabRate) SetID(ID string) {
	// replace the logic to populate unique ID for DepositInterestAmountSlabRate
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *DepositInterestAmountSlabRate) NewEntity() data.Entity {
	return &DepositInterestAmountSlabRate{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *DepositInterestAmountSlabRate) GetTableName() string {
	return "deposit_interest_amount_slab_rate"
}

// IDepositInterestAmountSlabRateDAO is the dao interface for DepositInterestAmountSlabRate
//go:generate mockery --name IDepositInterestAmountSlabRateDAO --inpackage --case=underscore
type IDepositInterestAmountSlabRateDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*DepositInterestAmountSlabRate, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*DepositInterestAmountSlabRate, error)

	// Save ...
	Save(ctx context.Context, newData *DepositInterestAmountSlabRate) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*DepositInterestAmountSlabRate) error

	// Update ...
	Update(ctx context.Context, newData *DepositInterestAmountSlabRate) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *DepositInterestAmountSlabRate, newData *DepositInterestAmountSlabRate) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestAmountSlabRate, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestAmountSlabRate, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *DepositInterestAmountSlabRate) error
}

// DepositInterestAmountSlabRateDAO ...
type DepositInterestAmountSlabRateDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewDepositInterestAmountSlabRateDAO creates a data access object for DepositInterestAmountSlabRate
func NewDepositInterestAmountSlabRateDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *DepositInterestAmountSlabRateDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &DepositInterestAmountSlabRateDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &DepositInterestAmountSlabRate{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *DepositInterestAmountSlabRateDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*DepositInterestAmountSlabRate, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*DepositInterestAmountSlabRate), err
}

// LoadByIDOnSlave ...
func (dao *DepositInterestAmountSlabRateDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*DepositInterestAmountSlabRate, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*DepositInterestAmountSlabRate), err
}

// Save ...
func (dao *DepositInterestAmountSlabRateDAO) Save(ctx context.Context, entity *DepositInterestAmountSlabRate) error {
	methodName := "save"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *DepositInterestAmountSlabRateDAO) SaveBatch(ctx context.Context, entities []*DepositInterestAmountSlabRate) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *DepositInterestAmountSlabRateDAO) Update(ctx context.Context, data *DepositInterestAmountSlabRate) error {
	methodName := "update"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *DepositInterestAmountSlabRateDAO) UpdateEntity(ctx context.Context, preData *DepositInterestAmountSlabRate, newData *DepositInterestAmountSlabRate) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *DepositInterestAmountSlabRateDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestAmountSlabRate, error) {
	methodName := "find"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*DepositInterestAmountSlabRate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*DepositInterestAmountSlabRate))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *DepositInterestAmountSlabRateDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestAmountSlabRate, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*DepositInterestAmountSlabRate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*DepositInterestAmountSlabRate))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *DepositInterestAmountSlabRateDAO) Upsert(ctx context.Context, data *DepositInterestAmountSlabRate) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(deposit_interest_amount_slab_rateDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_amount_slab_rateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loanAllowedAmountTenorSlabDao = "loan_allowed_amount_tenor_slab_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanAllowedAmountTenorSlab) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanAllowedAmountTenorSlab) SetID(ID string) {
	// replace the logic to populate unique ID for LoanAllowedAmountTenorSlab
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanAllowedAmountTenorSlab) NewEntity() data.Entity {
	return &LoanAllowedAmountTenorSlab{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanAllowedAmountTenorSlab) GetTableName() string {
	return "loan_allowed_amount_tenor_slab"
}

// ILoanAllowedAmountTenorSlabDAO is the dao interface for LoanAllowedAmountTenorSlab
//
//go:generate mockery --name ILoanAllowedAmountTenorSlabDAO --inpackage --case=underscore
type ILoanAllowedAmountTenorSlabDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanAllowedAmountTenorSlab, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanAllowedAmountTenorSlab, error)

	// Save ...
	Save(ctx context.Context, newData *LoanAllowedAmountTenorSlab) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanAllowedAmountTenorSlab) error

	// Update ...
	Update(ctx context.Context, newData *LoanAllowedAmountTenorSlab) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanAllowedAmountTenorSlab, newData *LoanAllowedAmountTenorSlab) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanAllowedAmountTenorSlab, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanAllowedAmountTenorSlab, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanAllowedAmountTenorSlab) error
}

// LoanAllowedAmountTenorSlabDAO ...
type LoanAllowedAmountTenorSlabDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanAllowedAmountTenorSlabDAO creates a data access object for LoanAllowedAmountTenorSlab
func NewLoanAllowedAmountTenorSlabDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanAllowedAmountTenorSlabDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanAllowedAmountTenorSlabDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanAllowedAmountTenorSlab{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanAllowedAmountTenorSlabDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanAllowedAmountTenorSlab, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanAllowedAmountTenorSlab), err
}

// LoadByIDOnSlave ...
func (dao *LoanAllowedAmountTenorSlabDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanAllowedAmountTenorSlab, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanAllowedAmountTenorSlab), err
}

// Save ...
func (dao *LoanAllowedAmountTenorSlabDAO) Save(ctx context.Context, entity *LoanAllowedAmountTenorSlab) error {
	methodName := "save"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanAllowedAmountTenorSlabDAO) SaveBatch(ctx context.Context, entities []*LoanAllowedAmountTenorSlab) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanAllowedAmountTenorSlabDAO) Update(ctx context.Context, data *LoanAllowedAmountTenorSlab) error {
	methodName := "update"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanAllowedAmountTenorSlabDAO) UpdateEntity(ctx context.Context, preData *LoanAllowedAmountTenorSlab, newData *LoanAllowedAmountTenorSlab) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanAllowedAmountTenorSlabDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanAllowedAmountTenorSlab, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanAllowedAmountTenorSlab, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanAllowedAmountTenorSlab))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanAllowedAmountTenorSlabDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanAllowedAmountTenorSlab, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanAllowedAmountTenorSlab, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanAllowedAmountTenorSlab))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanAllowedAmountTenorSlabDAO) Upsert(ctx context.Context, data *LoanAllowedAmountTenorSlab) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loanAllowedAmountTenorSlabDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanAllowedAmountTenorSlabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

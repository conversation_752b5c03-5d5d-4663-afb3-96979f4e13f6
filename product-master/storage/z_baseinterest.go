package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var base_interestDao = "base_interest_dao"

// GetID implements the GetID function for Entity Interface
func (impl *BaseInterest) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *BaseInterest) SetID(ID string) {
	// replace the logic to populate unique ID for BaseInterest
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *BaseInterest) NewEntity() data.Entity {
	return &BaseInterest{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *BaseInterest) GetTableName() string {
	return "base_interest"
}

// IBaseInterestDAO is the dao interface for BaseInterest
//go:generate mockery --name IBaseInterestDAO --inpackage --case=underscore
type IBaseInterestDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*BaseInterest, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*BaseInterest, error)

	// Save ...
	Save(ctx context.Context, newData *BaseInterest) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*BaseInterest) error

	// Update ...
	Update(ctx context.Context, newData *BaseInterest) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *BaseInterest, newData *BaseInterest) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*BaseInterest, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*BaseInterest, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *BaseInterest) error
}

// BaseInterestDAO ...
type BaseInterestDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewBaseInterestDAO creates a data access object for BaseInterest
func NewBaseInterestDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *BaseInterestDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &BaseInterestDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &BaseInterest{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *BaseInterestDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*BaseInterest, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*BaseInterest), err
}

// LoadByIDOnSlave ...
func (dao *BaseInterestDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*BaseInterest, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*BaseInterest), err
}

// Save ...
func (dao *BaseInterestDAO) Save(ctx context.Context, entity *BaseInterest) error {
	methodName := "save"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *BaseInterestDAO) SaveBatch(ctx context.Context, entities []*BaseInterest) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *BaseInterestDAO) Update(ctx context.Context, data *BaseInterest) error {
	methodName := "update"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *BaseInterestDAO) UpdateEntity(ctx context.Context, preData *BaseInterest, newData *BaseInterest) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *BaseInterestDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*BaseInterest, error) {
	methodName := "find"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*BaseInterest, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*BaseInterest))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *BaseInterestDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*BaseInterest, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*BaseInterest, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*BaseInterest))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *BaseInterestDAO) Upsert(ctx context.Context, data *BaseInterest) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(base_interestDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(base_interestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

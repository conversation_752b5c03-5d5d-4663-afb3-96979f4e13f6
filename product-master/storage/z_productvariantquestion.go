package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var productVariantQuestionDao = "product_variant_question_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductVariantQuestion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductVariantQuestion) SetID(ID string) {
	// replace the logic to populate unique ID for ProductVariantQuestion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductVariantQuestion) NewEntity() data.Entity {
	return &ProductVariantQuestion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductVariantQuestion) GetTableName() string {
	return "product_variant_question"
}

// IProductVariantQuestionDAO is the dao interface for ProductVariantQuestion
//
//go:generate mockery --name IProductVariantQuestionDAO --inpackage --case=underscore
type IProductVariantQuestionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantQuestion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantQuestion, error)

	// Save ...
	Save(ctx context.Context, newData *ProductVariantQuestion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductVariantQuestion) error

	// Update ...
	Update(ctx context.Context, newData *ProductVariantQuestion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductVariantQuestion, newData *ProductVariantQuestion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantQuestion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantQuestion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductVariantQuestion) error
}

// ProductVariantQuestionDAO ...
type ProductVariantQuestionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductVariantQuestionDAO creates a data access object for ProductVariantQuestion
func NewProductVariantQuestionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductVariantQuestionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductVariantQuestionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductVariantQuestion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductVariantQuestionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantQuestion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantQuestion), err
}

// LoadByIDOnSlave ...
func (dao *ProductVariantQuestionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantQuestion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantQuestion), err
}

// Save ...
func (dao *ProductVariantQuestionDAO) Save(ctx context.Context, entity *ProductVariantQuestion) error {
	methodName := "save"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductVariantQuestionDAO) SaveBatch(ctx context.Context, entities []*ProductVariantQuestion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductVariantQuestionDAO) Update(ctx context.Context, data *ProductVariantQuestion) error {
	methodName := "update"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductVariantQuestionDAO) UpdateEntity(ctx context.Context, preData *ProductVariantQuestion, newData *ProductVariantQuestion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductVariantQuestionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantQuestion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantQuestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantQuestion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductVariantQuestionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantQuestion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantQuestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantQuestion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductVariantQuestionDAO) Upsert(ctx context.Context, data *ProductVariantQuestion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(productVariantQuestionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(productVariantQuestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

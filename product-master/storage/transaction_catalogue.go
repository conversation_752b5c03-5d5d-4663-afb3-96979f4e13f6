package storage

import (
	"database/sql"
	"time"
)

// TransactionCatalogue ...
type TransactionCatalogue struct {
	ID             uint64         `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID       string         `sql-col:"public_id"`
	Domain         string         `sql-col:"domain"`
	IsFinancialTxn bool           `sql-col:"is_financial_txn"`
	TxnType        string         `sql-col:"txn_type"`
	TxnSubType     string         `sql-col:"txn_sub_type"`
	DisplayName    string         `sql-col:"display_name"`
	Description    sql.NullString `sql-col:"description"`
	Status         string         `sql-col:"status"`
	CreatedBy      string         `sql-col:"created_by"`
	CreatedAt      time.Time      `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy      string         `sql-col:"updated_by"`
	UpdatedAt      time.Time      `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

package storage

import "time"

// LoanAllowedAmountTenorSlab ...
type LoanAllowedAmountTenorSlab struct {
	ID               uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	ProductVariantID uint64    `sql-col:"product_variant_id"`
	Currency         string    `sql-col:"currency"`
	FromAmount       string    `sql-col:"from_amount"`
	ToAmount         string    `sql-col:"to_amount"`
	TenorUnit        string    `sql-col:"tenor_unit"`
	MinTenor         string    `sql-col:"min_tenor"`
	MaxTenor         string    `sql-col:"max_tenor"`
	CreatedBy        string    `sql-col:"created_by"`
	CreatedAt        time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy        string    `sql-col:"updated_by"`
	UpdatedAt        time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

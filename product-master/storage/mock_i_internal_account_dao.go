// Code generated by mockery v2.12.3. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/dakota/servus/v2/data"
)

// MockIInternalAccountDAO is an autogenerated mock type for the IInternalAccountDAO type
type MockIInternalAccountDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockIInternalAccountDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*InternalAccount, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*InternalAccount
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*InternalAccount); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*InternalAccount)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIInternalAccountDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InternalAccount, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*InternalAccount
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*InternalAccount); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*InternalAccount)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockIInternalAccountDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*InternalAccount, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *InternalAccount
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *InternalAccount); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*InternalAccount)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockIInternalAccountDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InternalAccount, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *InternalAccount
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *InternalAccount); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*InternalAccount)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockIInternalAccountDAO) Save(ctx context.Context, newData *InternalAccount) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InternalAccount) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockIInternalAccountDAO) SaveBatch(ctx context.Context, newData []*InternalAccount) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*InternalAccount) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockIInternalAccountDAO) Update(ctx context.Context, newData *InternalAccount) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InternalAccount) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockIInternalAccountDAO) UpdateEntity(ctx context.Context, preData *InternalAccount, newData *InternalAccount) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InternalAccount, *InternalAccount) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockIInternalAccountDAO) Upsert(ctx context.Context, newData *InternalAccount) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InternalAccount) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type NewMockIInternalAccountDAOT interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockIInternalAccountDAO creates a new instance of MockIInternalAccountDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockIInternalAccountDAO(t NewMockIInternalAccountDAOT) *MockIInternalAccountDAO {
	mock := &MockIInternalAccountDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

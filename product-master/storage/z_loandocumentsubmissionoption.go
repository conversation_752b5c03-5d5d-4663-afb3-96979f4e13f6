package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_document_submission_optionDao = "loan_document_submission_option_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanDocumentSubmissionOption) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanDocumentSubmissionOption) SetID(ID string) {
	// replace the logic to populate unique ID for LoanDocumentSubmissionOption
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanDocumentSubmissionOption) NewEntity() data.Entity {
	return &LoanDocumentSubmissionOption{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanDocumentSubmissionOption) GetTableName() string {
	return "loan_document_submission_option"
}

// ILoanDocumentSubmissionOptionDAO is the dao interface for LoanDocumentSubmissionOption
//
//go:generate mockery --name ILoanDocumentSubmissionOptionDAO --inpackage --case=underscore --replace-type gitlab.myteksi.net/gophers/go/commons/data=gitlab.myteksi.net/dakota/servus/v2/data
type ILoanDocumentSubmissionOptionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOption, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOption, error)

	// Save ...
	Save(ctx context.Context, newData *LoanDocumentSubmissionOption) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanDocumentSubmissionOption) error

	// Update ...
	Update(ctx context.Context, newData *LoanDocumentSubmissionOption) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanDocumentSubmissionOption, newData *LoanDocumentSubmissionOption) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOption, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOption, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanDocumentSubmissionOption) error
}

// LoanDocumentSubmissionOptionDAO ...
type LoanDocumentSubmissionOptionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanDocumentSubmissionOptionDAO creates a data access object for LoanDocumentSubmissionOption
func NewLoanDocumentSubmissionOptionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanDocumentSubmissionOptionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanDocumentSubmissionOptionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanDocumentSubmissionOption{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanDocumentSubmissionOptionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOption, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*LoanDocumentSubmissionOption), err
}

// LoadByIDOnSlave ...
func (dao *LoanDocumentSubmissionOptionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOption, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*LoanDocumentSubmissionOption), err
}

// Save ...
func (dao *LoanDocumentSubmissionOptionDAO) Save(ctx context.Context, entity *LoanDocumentSubmissionOption) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
	}
	return err
}

// SaveBatch ...
func (dao *LoanDocumentSubmissionOptionDAO) SaveBatch(ctx context.Context, entities []*LoanDocumentSubmissionOption) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
	}
	return err
}

// Update ...
func (dao *LoanDocumentSubmissionOptionDAO) Update(ctx context.Context, data *LoanDocumentSubmissionOption) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
	}
	return err
}

// UpdateEntity ...
func (dao *LoanDocumentSubmissionOptionDAO) UpdateEntity(ctx context.Context, preData *LoanDocumentSubmissionOption, newData *LoanDocumentSubmissionOption) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
	}
	return err
}

// Find ...
func (dao *LoanDocumentSubmissionOptionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOption, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
	}
	foundObjects := make([]*LoanDocumentSubmissionOption, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDocumentSubmissionOption))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanDocumentSubmissionOptionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOption, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
	}
	foundObjects := make([]*LoanDocumentSubmissionOption, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDocumentSubmissionOption))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanDocumentSubmissionOptionDAO) Upsert(ctx context.Context, data *LoanDocumentSubmissionOption) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_document_submission_optionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_optionDao, methodName, "status: error")
	}
	return err
}

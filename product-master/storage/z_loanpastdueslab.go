package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_past_due_slabDao = "loan_past_due_slab_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanPastDueSlab) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanPastDueSlab) SetID(ID string) {
	// replace the logic to populate unique ID for LoanPastDueSlab
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanPastDueSlab) NewEntity() data.Entity {
	return &LoanPastDueSlab{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanPastDueSlab) GetTableName() string {
	return "loan_past_due_slab"
}

// ILoanPastDueSlabDAO is the dao interface for LoanPastDueSlab
//
//go:generate mockery --name ILoanPastDueSlabDAO --inpackage --case=underscore
type ILoanPastDueSlabDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueSlab, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueSlab, error)

	// Save ...
	Save(ctx context.Context, newData *LoanPastDueSlab) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanPastDueSlab) error

	// Update ...
	Update(ctx context.Context, newData *LoanPastDueSlab) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanPastDueSlab, newData *LoanPastDueSlab) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueSlab, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueSlab, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanPastDueSlab) error
}

// LoanPastDueSlabDAO ...
type LoanPastDueSlabDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanPastDueSlabDAO creates a data access object for LoanPastDueSlab
func NewLoanPastDueSlabDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanPastDueSlabDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanPastDueSlabDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanPastDueSlab{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanPastDueSlabDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueSlab, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanPastDueSlab), err
}

// LoadByIDOnSlave ...
func (dao *LoanPastDueSlabDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanPastDueSlab, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanPastDueSlab), err
}

// Save ...
func (dao *LoanPastDueSlabDAO) Save(ctx context.Context, entity *LoanPastDueSlab) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanPastDueSlabDAO) SaveBatch(ctx context.Context, entities []*LoanPastDueSlab) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanPastDueSlabDAO) Update(ctx context.Context, data *LoanPastDueSlab) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanPastDueSlabDAO) UpdateEntity(ctx context.Context, preData *LoanPastDueSlab, newData *LoanPastDueSlab) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanPastDueSlabDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueSlab, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanPastDueSlab, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanPastDueSlab))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanPastDueSlabDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanPastDueSlab, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanPastDueSlab, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanPastDueSlab))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanPastDueSlabDAO) Upsert(ctx context.Context, data *LoanPastDueSlab) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_past_due_slabDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_past_due_slabDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

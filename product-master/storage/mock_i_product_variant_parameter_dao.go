// Code generated by mockery v2.12.3. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/dakota/servus/v2/data"
)

// MockIProductVariantParameterDAO is an autogenerated mock type for the IProductVariantParameterDAO type
type MockIProductVariantParameterDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockIProductVariantParameterDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantParameter, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductVariantParameter
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductVariantParameter); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductVariantParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIProductVariantParameterDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantParameter, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductVariantParameter
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductVariantParameter); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductVariantParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductVariantParameterDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantParameter, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductVariantParameter
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductVariantParameter); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductVariantParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductVariantParameterDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantParameter, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductVariantParameter
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductVariantParameter); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductVariantParameter)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantParameterDAO) Save(ctx context.Context, newData *ProductVariantParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantParameterDAO) SaveBatch(ctx context.Context, newData []*ProductVariantParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*ProductVariantParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantParameterDAO) Update(ctx context.Context, newData *ProductVariantParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockIProductVariantParameterDAO) UpdateEntity(ctx context.Context, preData *ProductVariantParameter, newData *ProductVariantParameter) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantParameter, *ProductVariantParameter) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantParameterDAO) Upsert(ctx context.Context, newData *ProductVariantParameter) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantParameter) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type NewMockIProductVariantParameterDAOT interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockIProductVariantParameterDAO creates a new instance of MockIProductVariantParameterDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockIProductVariantParameterDAO(t NewMockIProductVariantParameterDAOT) *MockIProductVariantParameterDAO {
	mock := &MockIProductVariantParameterDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var product_variantDao = "product_variant_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductVariant) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductVariant) SetID(ID string) {
	// replace the logic to populate unique ID for ProductVariant
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductVariant) NewEntity() data.Entity {
	return &ProductVariant{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductVariant) GetTableName() string {
	return "product_variant"
}

// IProductVariantDAO is the dao interface for ProductVariant
//go:generate mockery --name IProductVariantDAO --inpackage --case=underscore
type IProductVariantDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariant, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariant, error)

	// Save ...
	Save(ctx context.Context, newData *ProductVariant) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductVariant) error

	// Update ...
	Update(ctx context.Context, newData *ProductVariant) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductVariant, newData *ProductVariant) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariant, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariant, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductVariant) error
}

// ProductVariantDAO ...
type ProductVariantDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductVariantDAO creates a data access object for ProductVariant
func NewProductVariantDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductVariantDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductVariantDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductVariant{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductVariantDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariant, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariant), err
}

// LoadByIDOnSlave ...
func (dao *ProductVariantDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariant, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariant), err
}

// Save ...
func (dao *ProductVariantDAO) Save(ctx context.Context, entity *ProductVariant) error {
	methodName := "save"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductVariantDAO) SaveBatch(ctx context.Context, entities []*ProductVariant) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductVariantDAO) Update(ctx context.Context, data *ProductVariant) error {
	methodName := "update"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductVariantDAO) UpdateEntity(ctx context.Context, preData *ProductVariant, newData *ProductVariant) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductVariantDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariant, error) {
	methodName := "find"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariant, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariant))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductVariantDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariant, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariant, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariant))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductVariantDAO) Upsert(ctx context.Context, data *ProductVariant) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(product_variantDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variantDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

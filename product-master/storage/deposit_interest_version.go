package storage

import (
	"database/sql"
	"time"
)

// DepositInterestVersion ...
type DepositInterestVersion struct {
	ID                uint64         `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID          string         `sql-col:"public_id"`
	DepositInterestID uint64         `sql-col:"deposit_interest_id"`
	Version           string         `sql-col:"version"`
	EffectiveDate     time.Time      `sql-col:"effective_date"`
	Description       sql.NullString `sql-col:"description"`
	CreatedBy         string         `sql-col:"created_by"`
	CreatedAt         time.Time      `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy         string         `sql-col:"updated_by"`
	UpdatedAt         time.Time      `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

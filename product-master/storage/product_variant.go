package storage

import (
	"database/sql"
	"time"
)

// ProductVariant ...
type ProductVariant struct {
	ID          uint64         `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID    string         `sql-col:"public_id"`
	ProductID   uint64         `sql-col:"product_id"`
	Code        string         `sql-col:"code"`
	Version     string         `sql-col:"version"`
	Name        string         `sql-col:"name"`
	Description sql.NullString `sql-col:"description"`
	Status      string         `sql-col:"status"`
	ValidFrom   time.Time      `sql-col:"valid_from"`
	ValidTo     time.Time      `sql-col:"valid_to"`
	CreatedBy   string         `sql-col:"created_by"`
	CreatedAt   time.Time      `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy   string         `sql-col:"updated_by"`
	UpdatedAt   time.Time      `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_document_submission_option_versionDao = "loan_document_submission_option_version_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanDocumentSubmissionOptionVersion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanDocumentSubmissionOptionVersion) SetID(ID string) {
	// replace the logic to populate unique ID for LoanDocumentSubmissionOptionVersion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanDocumentSubmissionOptionVersion) NewEntity() data.Entity {
	return &LoanDocumentSubmissionOptionVersion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanDocumentSubmissionOptionVersion) GetTableName() string {
	return "loan_document_submission_option_version"
}

// ILoanDocumentSubmissionOptionVersionDAO is the dao interface for LoanDocumentSubmissionOptionVersion
//
//go:generate mockery --name ILoanDocumentSubmissionOptionVersionDAO --inpackage --case=underscore --replace-type gitlab.myteksi.net/gophers/go/commons/data=gitlab.myteksi.net/dakota/servus/v2/data
type ILoanDocumentSubmissionOptionVersionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionVersion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionVersion, error)

	// Save ...
	Save(ctx context.Context, newData *LoanDocumentSubmissionOptionVersion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanDocumentSubmissionOptionVersion) error

	// Update ...
	Update(ctx context.Context, newData *LoanDocumentSubmissionOptionVersion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanDocumentSubmissionOptionVersion, newData *LoanDocumentSubmissionOptionVersion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionVersion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionVersion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanDocumentSubmissionOptionVersion) error
}

// LoanDocumentSubmissionOptionVersionDAO ...
type LoanDocumentSubmissionOptionVersionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanDocumentSubmissionOptionVersionDAO creates a data access object for LoanDocumentSubmissionOptionVersion
func NewLoanDocumentSubmissionOptionVersionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanDocumentSubmissionOptionVersionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanDocumentSubmissionOptionVersionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanDocumentSubmissionOptionVersion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionVersion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*LoanDocumentSubmissionOptionVersion), err
}

// LoadByIDOnSlave ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionVersion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*LoanDocumentSubmissionOptionVersion), err
}

// Save ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) Save(ctx context.Context, entity *LoanDocumentSubmissionOptionVersion) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
	}
	return err
}

// SaveBatch ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) SaveBatch(ctx context.Context, entities []*LoanDocumentSubmissionOptionVersion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
	}
	return err
}

// Update ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) Update(ctx context.Context, data *LoanDocumentSubmissionOptionVersion) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
	}
	return err
}

// UpdateEntity ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) UpdateEntity(ctx context.Context, preData *LoanDocumentSubmissionOptionVersion, newData *LoanDocumentSubmissionOptionVersion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
	}
	return err
}

// Find ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionVersion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
	}
	foundObjects := make([]*LoanDocumentSubmissionOptionVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDocumentSubmissionOptionVersion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionVersion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
	}
	foundObjects := make([]*LoanDocumentSubmissionOptionVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDocumentSubmissionOptionVersion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanDocumentSubmissionOptionVersionDAO) Upsert(ctx context.Context, data *LoanDocumentSubmissionOptionVersion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_document_submission_option_versionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_versionDao, methodName, "status: error")
	}
	return err
}

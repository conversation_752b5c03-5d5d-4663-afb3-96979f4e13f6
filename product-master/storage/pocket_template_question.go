package storage

import "time"

// PocketTemplateQuestion ...
type PocketTemplateQuestion struct {
	ID               uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID         string    `sql-col:"public_id"`
	PocketTemplateID uint64    `sql-col:"pocket_template_id"`
	QuestionText     string    `sql-col:"question_text"`
	Locale           string    `sql-col:"locale"`
	Status           string    `sql-col:"status"`
	CreatedBy        string    `sql-col:"created_by"`
	CreatedAt        time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy        string    `sql-col:"updated_by"`
	UpdatedAt        time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

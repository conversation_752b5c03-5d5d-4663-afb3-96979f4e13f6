package storage

import (
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

type (
	// DBStore ...
	DBStore struct {
		ProductTemplateDAO                                          IProductTemplateDAO                                          `inject:"ProductTemplateDAO"`
		ProductDAO                                                  IProductDAO                                                  `inject:"ProductDAO"`
		ProductVariantDAO                                           IProductVariantDAO                                           `inject:"ProductVariantDAO"`
		ProductTemplateParameterDAO                                 IProductTemplateParameterDAO                                 `inject:"ProductTemplateParameterDAO"`
		ProductVariantParameterDAO                                  IProductVariantParameterDAO                                  `inject:"ProductVariantParameterDAO"`
		TransactionCatalogueDAO                                     ITransactionCatalogueDAO                                     `inject:"TransactionCatalogueDAO"`
		ProductVariantTransactionCatalogueMappingDAO                IProductVariantTransactionCatalogueMappingDAO                `inject:"ProductVariantTransactionCatalogueMappingDAO"`
		GeneralLedgerDAO                                            IGeneralLedgerDAO                                            `inject:"GeneralLedgerDAO"`
		InternalAccountDAO                                          IInternalAccountDAO                                          `inject:"InternalAccountDAO"`
		ProductVariantTransactionCatalogueInternalAccountMappingDAO IProductVariantTransactionCatalogueInternalAccountMappingDAO `inject:"ProductVariantTransactionCatalogueInternalAccountMappingDAO"`
		BaseInterestDAO                                             IBaseInterestDAO                                             `inject:"BaseInterestDAO"`
		BaseInterestVersionDAO                                      IBaseInterestVersionDAO                                      `inject:"BaseInterestVersionDAO"`
		BaseInterestTimeSlabRateDAO                                 IBaseInterestTimeSlabRateDAO                                 `inject:"BaseInterestTimeSlabRateDAO"`
		DepositInterestDAO                                          IDepositInterestDAO                                          `inject:"DepositInterestDAO"`
		DepositInterestVersionDAO                                   IDepositInterestVersionDAO                                   `inject:"DepositInterestVersionDAO"`
		DepositInterestAmountSlabRateDAO                            IDepositInterestAmountSlabRateDAO                            `inject:"DepositInterestAmountSlabRateDAO"`
		PocketTemplateDAO                                           IPocketTemplateDAO                                           `inject:"PocketTemplateDAO"`
		PocketTemplateQuestionDAO                                   IPocketTemplateQuestionDAO                                   `inject:"PocketTemplateQuestionDAO"`
		PocketTemplateAnswerSuggestionDAO                           IPocketTemplateAnswerSuggestionDAO                           `inject:"PocketTemplateAnswerSuggestionDAO"`
		PocketTemplateImageSuggestionDAO                            IPocketTemplateImageSuggestionDAO                            `inject:"PocketTemplateImageSuggestionDAO"`
		ParameterChangeScheduleDAO                                  IParameterChangeScheduleDAO                                  `inject:"ParameterChangeScheduleDAO"`
		LoanAllowedAmountTenorSlabDAO                               ILoanAllowedAmountTenorSlabDAO                               `inject:"LoanAllowedAmountTenorSlabDAO"`
		LoanInterestDAO                                             ILoanInterestDAO                                             `inject:"LoanInterestDAO"`
		LoanInterestVersionDAO                                      ILoanInterestVersionDAO                                      `inject:"LoanInterestVersionDAO"`
		LoanInterestSlabRateDAO                                     ILoanInterestSlabRateDAO                                     `inject:"LoanInterestSlabRateDAO"`
		LoanPastDueVersionDAO                                       ILoanPastDueVersionDAO                                       `inject:"LoanPastDueVersionDAO"`
		LoanPastDueSlabDAO                                          ILoanPastDueSlabDAO                                          `inject:"LoanPastDueSlabDAO"`
		LoanInstructionVersionDAO                                   ILoanInstructionVersionDAO                                   `inject:"LoanInstructionVersionDAO"`
		LoanInstructionDAO                                          ILoanInstructionDAO                                          `inject:"LoanInstructionDAO"`
		ProductVariantQuestionDAO                                   IProductVariantQuestionDAO                                   `inject:"ProductVariantQuestionDAO"`
		ProductVariantAnswerSuggestionDAO                           IProductVariantAnswerSuggestionDAO                           `inject:"ProductVariantAnswerSuggestionDAO"`
		Statsd                                                      statsd.Client                                                `inject:"statsD"`
		LoanDocumentSubmissionOptionDAO                             ILoanDocumentSubmissionOptionDAO                             `inject:"LoanDocumentSubmissionOptionDAO"`
		LoanDocumentSubmissionOptionVersionDAO                      ILoanDocumentSubmissionOptionVersionDAO                      `inject:"LoanDocumentSubmissionOptionVersionDAO"`
		LoanDocumentSubmissionOptionParametersDAO                   ILoanDocumentSubmissionOptionParametersDAO                   `inject:"LoanDocumentSubmissionOptionParametersDAO"`
	}
)

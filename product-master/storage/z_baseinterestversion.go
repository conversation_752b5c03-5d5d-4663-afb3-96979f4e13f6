package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var base_interest_versionDao = "base_interest_version_dao"

// GetID implements the GetID function for Entity Interface
func (impl *BaseInterestVersion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *BaseInterestVersion) SetID(ID string) {
	// replace the logic to populate unique ID for BaseInterestVersion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *BaseInterestVersion) NewEntity() data.Entity {
	return &BaseInterestVersion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *BaseInterestVersion) GetTableName() string {
	return "base_interest_version"
}

// IBaseInterestVersionDAO is the dao interface for BaseInterestVersion
//go:generate mockery --name IBaseInterestVersionDAO --inpackage --case=underscore
type IBaseInterestVersionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*BaseInterestVersion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*BaseInterestVersion, error)

	// Save ...
	Save(ctx context.Context, newData *BaseInterestVersion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*BaseInterestVersion) error

	// Update ...
	Update(ctx context.Context, newData *BaseInterestVersion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *BaseInterestVersion, newData *BaseInterestVersion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestVersion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestVersion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *BaseInterestVersion) error
}

// BaseInterestVersionDAO ...
type BaseInterestVersionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewBaseInterestVersionDAO creates a data access object for BaseInterestVersion
func NewBaseInterestVersionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *BaseInterestVersionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &BaseInterestVersionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &BaseInterestVersion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *BaseInterestVersionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*BaseInterestVersion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*BaseInterestVersion), err
}

// LoadByIDOnSlave ...
func (dao *BaseInterestVersionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*BaseInterestVersion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*BaseInterestVersion), err
}

// Save ...
func (dao *BaseInterestVersionDAO) Save(ctx context.Context, entity *BaseInterestVersion) error {
	methodName := "save"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *BaseInterestVersionDAO) SaveBatch(ctx context.Context, entities []*BaseInterestVersion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *BaseInterestVersionDAO) Update(ctx context.Context, data *BaseInterestVersion) error {
	methodName := "update"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *BaseInterestVersionDAO) UpdateEntity(ctx context.Context, preData *BaseInterestVersion, newData *BaseInterestVersion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *BaseInterestVersionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestVersion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*BaseInterestVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*BaseInterestVersion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *BaseInterestVersionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*BaseInterestVersion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*BaseInterestVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*BaseInterestVersion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *BaseInterestVersionDAO) Upsert(ctx context.Context, data *BaseInterestVersion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(base_interest_versionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(base_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

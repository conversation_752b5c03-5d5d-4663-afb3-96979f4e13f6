package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var product_variant_parameterDao = "product_variant_parameter_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductVariantParameter) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductVariantParameter) SetID(ID string) {
	// replace the logic to populate unique ID for ProductVariantParameter
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductVariantParameter) NewEntity() data.Entity {
	return &ProductVariantParameter{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductVariantParameter) GetTableName() string {
	return "product_variant_parameter"
}

// IProductVariantParameterDAO is the dao interface for ProductVariantParameter
//go:generate mockery --name IProductVariantParameterDAO --inpackage --case=underscore
type IProductVariantParameterDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantParameter, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantParameter, error)

	// Save ...
	Save(ctx context.Context, newData *ProductVariantParameter) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductVariantParameter) error

	// Update ...
	Update(ctx context.Context, newData *ProductVariantParameter) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductVariantParameter, newData *ProductVariantParameter) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantParameter, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantParameter, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductVariantParameter) error
}

// ProductVariantParameterDAO ...
type ProductVariantParameterDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductVariantParameterDAO creates a data access object for ProductVariantParameter
func NewProductVariantParameterDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductVariantParameterDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductVariantParameterDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductVariantParameter{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductVariantParameterDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantParameter, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantParameter), err
}

// LoadByIDOnSlave ...
func (dao *ProductVariantParameterDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantParameter, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantParameter), err
}

// Save ...
func (dao *ProductVariantParameterDAO) Save(ctx context.Context, entity *ProductVariantParameter) error {
	methodName := "save"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductVariantParameterDAO) SaveBatch(ctx context.Context, entities []*ProductVariantParameter) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductVariantParameterDAO) Update(ctx context.Context, data *ProductVariantParameter) error {
	methodName := "update"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductVariantParameterDAO) UpdateEntity(ctx context.Context, preData *ProductVariantParameter, newData *ProductVariantParameter) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductVariantParameterDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantParameter, error) {
	methodName := "find"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantParameter, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantParameter))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductVariantParameterDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantParameter, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantParameter, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantParameter))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductVariantParameterDAO) Upsert(ctx context.Context, data *ProductVariantParameter) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(product_variant_parameterDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variant_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

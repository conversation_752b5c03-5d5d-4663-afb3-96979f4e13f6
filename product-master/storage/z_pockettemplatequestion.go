package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var pocket_template_questionDao = "pocket_template_question_dao"

// GetID implements the GetID function for Entity Interface
func (impl *PocketTemplateQuestion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *PocketTemplateQuestion) SetID(ID string) {
	// replace the logic to populate unique ID for PocketTemplateQuestion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *PocketTemplateQuestion) NewEntity() data.Entity {
	return &PocketTemplateQuestion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *PocketTemplateQuestion) GetTableName() string {
	return "pocket_template_question"
}

// IPocketTemplateQuestionDAO is the dao interface for PocketTemplateQuestion
//go:generate mockery --name IPocketTemplateQuestionDAO --inpackage --case=underscore
type IPocketTemplateQuestionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateQuestion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateQuestion, error)

	// Save ...
	Save(ctx context.Context, newData *PocketTemplateQuestion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*PocketTemplateQuestion) error

	// Update ...
	Update(ctx context.Context, newData *PocketTemplateQuestion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *PocketTemplateQuestion, newData *PocketTemplateQuestion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateQuestion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateQuestion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *PocketTemplateQuestion) error
}

// PocketTemplateQuestionDAO ...
type PocketTemplateQuestionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewPocketTemplateQuestionDAO creates a data access object for PocketTemplateQuestion
func NewPocketTemplateQuestionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *PocketTemplateQuestionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &PocketTemplateQuestionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &PocketTemplateQuestion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *PocketTemplateQuestionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateQuestion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplateQuestion), err
}

// LoadByIDOnSlave ...
func (dao *PocketTemplateQuestionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateQuestion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplateQuestion), err
}

// Save ...
func (dao *PocketTemplateQuestionDAO) Save(ctx context.Context, entity *PocketTemplateQuestion) error {
	methodName := "save"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *PocketTemplateQuestionDAO) SaveBatch(ctx context.Context, entities []*PocketTemplateQuestion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *PocketTemplateQuestionDAO) Update(ctx context.Context, data *PocketTemplateQuestion) error {
	methodName := "update"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *PocketTemplateQuestionDAO) UpdateEntity(ctx context.Context, preData *PocketTemplateQuestion, newData *PocketTemplateQuestion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *PocketTemplateQuestionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateQuestion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplateQuestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplateQuestion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *PocketTemplateQuestionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateQuestion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplateQuestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplateQuestion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *PocketTemplateQuestionDAO) Upsert(ctx context.Context, data *PocketTemplateQuestion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(pocket_template_questionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_template_questionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

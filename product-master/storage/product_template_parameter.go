package storage

import (
	"database/sql"
	"time"
)

// ProductTemplateParameter ...
type ProductTemplateParameter struct {
	ID                uint64         `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID          string         `sql-col:"public_id"`
	ProductTemplateID uint64         `sql-col:"product_template_id"`
	Namespace         string         `sql-col:"namespace"`
	ParameterKey      string         `sql-col:"parameter_key"`
	ParameterValue    string         `sql-col:"parameter_value"`
	DataType          string         `sql-col:"data_type"`
	OverrideLevel     string         `sql-col:"override_level"`
	ExceptionLevel    sql.NullString `sql-col:"exception_level"`
	Description       sql.NullString `sql-col:"description"`
	CreatedBy         string         `sql-col:"created_by"`
	CreatedAt         time.Time      `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy         string         `sql-col:"updated_by"`
	UpdatedAt         time.Time      `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

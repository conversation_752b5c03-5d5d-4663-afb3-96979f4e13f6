package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loan_document_submission_option_parametersDao = "loan_document_submission_option_parameters_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanDocumentSubmissionOptionParameters) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanDocumentSubmissionOptionParameters) SetID(ID string) {
	// replace the logic to populate unique ID for LoanDocumentSubmissionOptionParameters
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanDocumentSubmissionOptionParameters) NewEntity() data.Entity {
	return &LoanDocumentSubmissionOptionParameters{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanDocumentSubmissionOptionParameters) GetTableName() string {
	return "loan_document_submission_option_parameters"
}

// ILoanDocumentSubmissionOptionParametersDAO is the dao interface for LoanDocumentSubmissionOptionParameters
//
//go:generate mockery --name ILoanDocumentSubmissionOptionParametersDAO --inpackage --case=underscore --replace-type gitlab.myteksi.net/gophers/go/commons/data=gitlab.myteksi.net/dakota/servus/v2/data
type ILoanDocumentSubmissionOptionParametersDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionParameters, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionParameters, error)

	// Save ...
	Save(ctx context.Context, newData *LoanDocumentSubmissionOptionParameters) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanDocumentSubmissionOptionParameters) error

	// Update ...
	Update(ctx context.Context, newData *LoanDocumentSubmissionOptionParameters) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanDocumentSubmissionOptionParameters, newData *LoanDocumentSubmissionOptionParameters) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionParameters, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionParameters, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanDocumentSubmissionOptionParameters) error
}

// LoanDocumentSubmissionOptionParametersDAO ...
type LoanDocumentSubmissionOptionParametersDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanDocumentSubmissionOptionParametersDAO creates a data access object for LoanDocumentSubmissionOptionParameters
func NewLoanDocumentSubmissionOptionParametersDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanDocumentSubmissionOptionParametersDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanDocumentSubmissionOptionParametersDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanDocumentSubmissionOptionParameters{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionParameters, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*LoanDocumentSubmissionOptionParameters), err
}

// LoadByIDOnSlave ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanDocumentSubmissionOptionParameters, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
		return nil, err
	}
	return entity.(*LoanDocumentSubmissionOptionParameters), err
}

// Save ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) Save(ctx context.Context, entity *LoanDocumentSubmissionOptionParameters) error {
	methodName := "save"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
	}
	return err
}

// SaveBatch ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) SaveBatch(ctx context.Context, entities []*LoanDocumentSubmissionOptionParameters) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
	}
	return err
}

// Update ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) Update(ctx context.Context, data *LoanDocumentSubmissionOptionParameters) error {
	methodName := "update"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
	}
	return err
}

// UpdateEntity ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) UpdateEntity(ctx context.Context, preData *LoanDocumentSubmissionOptionParameters, newData *LoanDocumentSubmissionOptionParameters) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
	}
	return err
}

// Find ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionParameters, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
	}
	foundObjects := make([]*LoanDocumentSubmissionOptionParameters, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDocumentSubmissionOptionParameters))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanDocumentSubmissionOptionParameters, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
	}
	foundObjects := make([]*LoanDocumentSubmissionOptionParameters, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanDocumentSubmissionOptionParameters))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanDocumentSubmissionOptionParametersDAO) Upsert(ctx context.Context, data *LoanDocumentSubmissionOptionParameters) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loan_document_submission_option_parametersDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loan_document_submission_option_parametersDao, methodName, "status: error")
	}
	return err
}

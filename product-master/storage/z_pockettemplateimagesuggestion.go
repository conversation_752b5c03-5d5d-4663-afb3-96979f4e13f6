package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var pocket_template_image_suggestionDao = "pocket_template_image_suggestion_dao"

// GetID implements the GetID function for Entity Interface
func (impl *PocketTemplateImageSuggestion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *PocketTemplateImageSuggestion) SetID(ID string) {
	// replace the logic to populate unique ID for PocketTemplateImageSuggestion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *PocketTemplateImageSuggestion) NewEntity() data.Entity {
	return &PocketTemplateImageSuggestion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *PocketTemplateImageSuggestion) GetTableName() string {
	return "pocket_template_image_suggestion"
}

// IPocketTemplateImageSuggestionDAO is the dao interface for PocketTemplateImageSuggestion
//go:generate mockery --name IPocketTemplateImageSuggestionDAO --inpackage --case=underscore
type IPocketTemplateImageSuggestionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateImageSuggestion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateImageSuggestion, error)

	// Save ...
	Save(ctx context.Context, newData *PocketTemplateImageSuggestion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*PocketTemplateImageSuggestion) error

	// Update ...
	Update(ctx context.Context, newData *PocketTemplateImageSuggestion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *PocketTemplateImageSuggestion, newData *PocketTemplateImageSuggestion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateImageSuggestion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateImageSuggestion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *PocketTemplateImageSuggestion) error
}

// PocketTemplateImageSuggestionDAO ...
type PocketTemplateImageSuggestionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewPocketTemplateImageSuggestionDAO creates a data access object for PocketTemplateImageSuggestion
func NewPocketTemplateImageSuggestionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *PocketTemplateImageSuggestionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &PocketTemplateImageSuggestionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &PocketTemplateImageSuggestion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *PocketTemplateImageSuggestionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateImageSuggestion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplateImageSuggestion), err
}

// LoadByIDOnSlave ...
func (dao *PocketTemplateImageSuggestionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplateImageSuggestion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplateImageSuggestion), err
}

// Save ...
func (dao *PocketTemplateImageSuggestionDAO) Save(ctx context.Context, entity *PocketTemplateImageSuggestion) error {
	methodName := "save"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *PocketTemplateImageSuggestionDAO) SaveBatch(ctx context.Context, entities []*PocketTemplateImageSuggestion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *PocketTemplateImageSuggestionDAO) Update(ctx context.Context, data *PocketTemplateImageSuggestion) error {
	methodName := "update"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *PocketTemplateImageSuggestionDAO) UpdateEntity(ctx context.Context, preData *PocketTemplateImageSuggestion, newData *PocketTemplateImageSuggestion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *PocketTemplateImageSuggestionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateImageSuggestion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplateImageSuggestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplateImageSuggestion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *PocketTemplateImageSuggestionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplateImageSuggestion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplateImageSuggestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplateImageSuggestion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *PocketTemplateImageSuggestionDAO) Upsert(ctx context.Context, data *PocketTemplateImageSuggestion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(pocket_template_image_suggestionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_template_image_suggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var deposit_interest_versionDao = "deposit_interest_version_dao"

// GetID implements the GetID function for Entity Interface
func (impl *DepositInterestVersion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *DepositInterestVersion) SetID(ID string) {
	// replace the logic to populate unique ID for DepositInterestVersion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *DepositInterestVersion) NewEntity() data.Entity {
	return &DepositInterestVersion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *DepositInterestVersion) GetTableName() string {
	return "deposit_interest_version"
}

// IDepositInterestVersionDAO is the dao interface for DepositInterestVersion
//go:generate mockery --name IDepositInterestVersionDAO --inpackage --case=underscore
type IDepositInterestVersionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*DepositInterestVersion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*DepositInterestVersion, error)

	// Save ...
	Save(ctx context.Context, newData *DepositInterestVersion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*DepositInterestVersion) error

	// Update ...
	Update(ctx context.Context, newData *DepositInterestVersion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *DepositInterestVersion, newData *DepositInterestVersion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestVersion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestVersion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *DepositInterestVersion) error
}

// DepositInterestVersionDAO ...
type DepositInterestVersionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewDepositInterestVersionDAO creates a data access object for DepositInterestVersion
func NewDepositInterestVersionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *DepositInterestVersionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &DepositInterestVersionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &DepositInterestVersion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *DepositInterestVersionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*DepositInterestVersion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*DepositInterestVersion), err
}

// LoadByIDOnSlave ...
func (dao *DepositInterestVersionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*DepositInterestVersion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*DepositInterestVersion), err
}

// Save ...
func (dao *DepositInterestVersionDAO) Save(ctx context.Context, entity *DepositInterestVersion) error {
	methodName := "save"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *DepositInterestVersionDAO) SaveBatch(ctx context.Context, entities []*DepositInterestVersion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *DepositInterestVersionDAO) Update(ctx context.Context, data *DepositInterestVersion) error {
	methodName := "update"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *DepositInterestVersionDAO) UpdateEntity(ctx context.Context, preData *DepositInterestVersion, newData *DepositInterestVersion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *DepositInterestVersionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestVersion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*DepositInterestVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*DepositInterestVersion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *DepositInterestVersionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*DepositInterestVersion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*DepositInterestVersion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*DepositInterestVersion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *DepositInterestVersionDAO) Upsert(ctx context.Context, data *DepositInterestVersion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(deposit_interest_versionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(deposit_interest_versionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

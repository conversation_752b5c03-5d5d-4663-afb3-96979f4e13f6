package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var internal_accountDao = "internal_account_dao"

// GetID implements the GetID function for Entity Interface
func (impl *InternalAccount) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *InternalAccount) SetID(ID string) {
	// replace the logic to populate unique ID for InternalAccount
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *InternalAccount) NewEntity() data.Entity {
	return &InternalAccount{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *InternalAccount) GetTableName() string {
	return "internal_account"
}

// IInternalAccountDAO is the dao interface for InternalAccount
//go:generate mockery --name IInternalAccountDAO --inpackage --case=underscore
type IInternalAccountDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*InternalAccount, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InternalAccount, error)

	// Save ...
	Save(ctx context.Context, newData *InternalAccount) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*InternalAccount) error

	// Update ...
	Update(ctx context.Context, newData *InternalAccount) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *InternalAccount, newData *InternalAccount) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*InternalAccount, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InternalAccount, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *InternalAccount) error
}

// InternalAccountDAO ...
type InternalAccountDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewInternalAccountDAO creates a data access object for InternalAccount
func NewInternalAccountDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *InternalAccountDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &InternalAccountDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &InternalAccount{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *InternalAccountDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*InternalAccount, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*InternalAccount), err
}

// LoadByIDOnSlave ...
func (dao *InternalAccountDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*InternalAccount, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*InternalAccount), err
}

// Save ...
func (dao *InternalAccountDAO) Save(ctx context.Context, entity *InternalAccount) error {
	methodName := "save"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *InternalAccountDAO) SaveBatch(ctx context.Context, entities []*InternalAccount) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *InternalAccountDAO) Update(ctx context.Context, data *InternalAccount) error {
	methodName := "update"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *InternalAccountDAO) UpdateEntity(ctx context.Context, preData *InternalAccount, newData *InternalAccount) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *InternalAccountDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*InternalAccount, error) {
	methodName := "find"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*InternalAccount, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*InternalAccount))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *InternalAccountDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*InternalAccount, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*InternalAccount, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*InternalAccount))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *InternalAccountDAO) Upsert(ctx context.Context, data *InternalAccount) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(internal_accountDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(internal_accountDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

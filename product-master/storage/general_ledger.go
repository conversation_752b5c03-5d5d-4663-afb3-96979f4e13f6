package storage

import (
	"database/sql"
	"time"
)

// GeneralLedger ...
type GeneralLedger struct {
	ID          uint64         `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID    string         `sql-col:"public_id"`
	Code        string         `sql-col:"code"`
	Name        string         `sql-col:"name"`
	Description sql.NullString `sql-col:"description"`
	Currency    string         `sql-col:"currency"`
	Status      string         `sql-col:"status"`
	CreatedBy   string         `sql-col:"created_by"`
	CreatedAt   time.Time      `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy   string         `sql-col:"updated_by"`
	UpdatedAt   time.Time      `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var product_template_parameterDao = "product_template_parameter_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductTemplateParameter) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductTemplateParameter) SetID(ID string) {
	// replace the logic to populate unique ID for ProductTemplateParameter
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductTemplateParameter) NewEntity() data.Entity {
	return &ProductTemplateParameter{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductTemplateParameter) GetTableName() string {
	return "product_template_parameter"
}

// IProductTemplateParameterDAO is the dao interface for ProductTemplateParameter
//go:generate mockery --name IProductTemplateParameterDAO --inpackage --case=underscore
type IProductTemplateParameterDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductTemplateParameter, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductTemplateParameter, error)

	// Save ...
	Save(ctx context.Context, newData *ProductTemplateParameter) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductTemplateParameter) error

	// Update ...
	Update(ctx context.Context, newData *ProductTemplateParameter) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductTemplateParameter, newData *ProductTemplateParameter) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplateParameter, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplateParameter, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductTemplateParameter) error
}

// ProductTemplateParameterDAO ...
type ProductTemplateParameterDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductTemplateParameterDAO creates a data access object for ProductTemplateParameter
func NewProductTemplateParameterDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductTemplateParameterDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductTemplateParameterDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductTemplateParameter{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductTemplateParameterDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductTemplateParameter, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductTemplateParameter), err
}

// LoadByIDOnSlave ...
func (dao *ProductTemplateParameterDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductTemplateParameter, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductTemplateParameter), err
}

// Save ...
func (dao *ProductTemplateParameterDAO) Save(ctx context.Context, entity *ProductTemplateParameter) error {
	methodName := "save"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductTemplateParameterDAO) SaveBatch(ctx context.Context, entities []*ProductTemplateParameter) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductTemplateParameterDAO) Update(ctx context.Context, data *ProductTemplateParameter) error {
	methodName := "update"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductTemplateParameterDAO) UpdateEntity(ctx context.Context, preData *ProductTemplateParameter, newData *ProductTemplateParameter) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductTemplateParameterDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplateParameter, error) {
	methodName := "find"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductTemplateParameter, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductTemplateParameter))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductTemplateParameterDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplateParameter, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductTemplateParameter, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductTemplateParameter))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductTemplateParameterDAO) Upsert(ctx context.Context, data *ProductTemplateParameter) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(product_template_parameterDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_template_parameterDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

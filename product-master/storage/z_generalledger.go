package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var general_ledgerDao = "general_ledger_dao"

// GetID implements the GetID function for Entity Interface
func (impl *GeneralLedger) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *GeneralLedger) SetID(ID string) {
	// replace the logic to populate unique ID for GeneralLedger
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *GeneralLedger) NewEntity() data.Entity {
	return &GeneralLedger{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *GeneralLedger) GetTableName() string {
	return "general_ledger"
}

// IGeneralLedgerDAO is the dao interface for GeneralLedger
//go:generate mockery --name IGeneralLedgerDAO --inpackage --case=underscore
type IGeneralLedgerDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*GeneralLedger, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*GeneralLedger, error)

	// Save ...
	Save(ctx context.Context, newData *GeneralLedger) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*GeneralLedger) error

	// Update ...
	Update(ctx context.Context, newData *GeneralLedger) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *GeneralLedger, newData *GeneralLedger) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*GeneralLedger, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*GeneralLedger, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *GeneralLedger) error
}

// GeneralLedgerDAO ...
type GeneralLedgerDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewGeneralLedgerDAO creates a data access object for GeneralLedger
func NewGeneralLedgerDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *GeneralLedgerDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &GeneralLedgerDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &GeneralLedger{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *GeneralLedgerDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*GeneralLedger, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*GeneralLedger), err
}

// LoadByIDOnSlave ...
func (dao *GeneralLedgerDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*GeneralLedger, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*GeneralLedger), err
}

// Save ...
func (dao *GeneralLedgerDAO) Save(ctx context.Context, entity *GeneralLedger) error {
	methodName := "save"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *GeneralLedgerDAO) SaveBatch(ctx context.Context, entities []*GeneralLedger) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *GeneralLedgerDAO) Update(ctx context.Context, data *GeneralLedger) error {
	methodName := "update"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *GeneralLedgerDAO) UpdateEntity(ctx context.Context, preData *GeneralLedger, newData *GeneralLedger) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *GeneralLedgerDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*GeneralLedger, error) {
	methodName := "find"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*GeneralLedger, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*GeneralLedger))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *GeneralLedgerDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*GeneralLedger, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*GeneralLedger, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*GeneralLedger))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *GeneralLedgerDAO) Upsert(ctx context.Context, data *GeneralLedger) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(general_ledgerDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(general_ledgerDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

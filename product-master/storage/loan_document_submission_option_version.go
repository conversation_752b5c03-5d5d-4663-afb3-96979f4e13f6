package storage

import "time"

// LoanDocumentSubmissionOptionV<PERSON><PERSON> represents the "loan_document_submission_option_version" storage object.
type LoanDocumentSubmissionOptionVersion struct {
	ID                             uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	LoanDocumentSubmissionOptionID uint64    `sql-col:"loan_document_submission_option_id"`
	VersionID                      string    `sql-col:"version_id"`
	Status                         string    `sql-col:"status"`
	CreatedAt                      time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	CreatedBy                      string    `sql-col:"created_by"`
	UpdatedAt                      time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy                      string    `sql-col:"updated_by"`
}

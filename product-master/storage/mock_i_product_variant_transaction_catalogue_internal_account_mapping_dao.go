// Code generated by mockery v2.12.3. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/dakota/servus/v2/data"
)

// MockIProductVariantTransactionCatalogueInternalAccountMappingDAO is an autogenerated mock type for the IProductVariantTransactionCatalogueInternalAccountMappingDAO type
type MockIProductVariantTransactionCatalogueInternalAccountMappingDAO struct {
	mock.Mock
}

// Find provides a mock function with given fields: ctx, conditions
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductVariantTransactionCatalogueInternalAccountMapping
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductVariantTransactionCatalogueInternalAccountMapping); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductVariantTransactionCatalogueInternalAccountMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*ProductVariantTransactionCatalogueInternalAccountMapping
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) []*ProductVariantTransactionCatalogueInternalAccountMapping); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ProductVariantTransactionCatalogueInternalAccountMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByID provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductVariantTransactionCatalogueInternalAccountMapping
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductVariantTransactionCatalogueInternalAccountMapping); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductVariantTransactionCatalogueInternalAccountMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoadByIDOnSlave provides a mock function with given fields: ctx, ID, fields
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	_va := make([]interface{}, len(fields))
	for _i := range fields {
		_va[_i] = fields[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, ID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *ProductVariantTransactionCatalogueInternalAccountMapping
	if rf, ok := ret.Get(0).(func(context.Context, uint64, ...string) *ProductVariantTransactionCatalogueInternalAccountMapping); ok {
		r0 = rf(ctx, ID, fields...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ProductVariantTransactionCatalogueInternalAccountMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, uint64, ...string) error); ok {
		r1 = rf(ctx, ID, fields...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) Save(ctx context.Context, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantTransactionCatalogueInternalAccountMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveBatch provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) SaveBatch(ctx context.Context, newData []*ProductVariantTransactionCatalogueInternalAccountMapping) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*ProductVariantTransactionCatalogueInternalAccountMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) Update(ctx context.Context, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantTransactionCatalogueInternalAccountMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, preData, newData
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) UpdateEntity(ctx context.Context, preData *ProductVariantTransactionCatalogueInternalAccountMapping, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	ret := _m.Called(ctx, preData, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantTransactionCatalogueInternalAccountMapping, *ProductVariantTransactionCatalogueInternalAccountMapping) error); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Upsert provides a mock function with given fields: ctx, newData
func (_m *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO) Upsert(ctx context.Context, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	ret := _m.Called(ctx, newData)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *ProductVariantTransactionCatalogueInternalAccountMapping) error); ok {
		r0 = rf(ctx, newData)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type NewMockIProductVariantTransactionCatalogueInternalAccountMappingDAOT interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockIProductVariantTransactionCatalogueInternalAccountMappingDAO creates a new instance of MockIProductVariantTransactionCatalogueInternalAccountMappingDAO. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockIProductVariantTransactionCatalogueInternalAccountMappingDAO(t NewMockIProductVariantTransactionCatalogueInternalAccountMappingDAOT) *MockIProductVariantTransactionCatalogueInternalAccountMappingDAO {
	mock := &MockIProductVariantTransactionCatalogueInternalAccountMappingDAO{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

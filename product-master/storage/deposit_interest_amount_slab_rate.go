package storage

import (
	"time"
)

// DepositInterestAmountSlabRate ...
type DepositInterestAmountSlabRate struct {
	ID                               uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	PublicID                         string    `sql-col:"public_id"`
	DepositInterestVersionID         uint64    `sql-col:"deposit_interest_version_id"`
	FromAmount                       string    `sql-col:"from_amount"`
	ToAmount                         string    `sql-col:"to_amount"`
	MinTenor                         string    `sql-col:"min_tenor"`
	MaxTenor                         string    `sql-col:"max_tenor"`
	MinTenorUnit                     string    `sql-col:"min_tenor_unit"`
	MaxTenorUnit                     string    `sql-col:"max_tenor_unit"`
	BaseRateInterestSpreadPercentage string    `sql-col:"base_rate_interest_spread_percentage"`
	AbsoluteInterestRatePercentage   string    `sql-col:"absolute_interest_rate_percentage"`
	CreatedBy                        string    `sql-col:"created_by"`
	CreatedAt                        time.Time `sql-col:"created_at" sql-insert:"false" sql-update:"false"`
	UpdatedBy                        string    `sql-col:"updated_by"`
	UpdatedAt                        time.Time `sql-col:"updated_at" sql-insert:"false" sql-update:"false"`
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var product_variant_transaction_catalogue_internal_account_mappingDao = "product_variant_transaction_catalogue_internal_account_mapping_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductVariantTransactionCatalogueInternalAccountMapping) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductVariantTransactionCatalogueInternalAccountMapping) SetID(ID string) {
	// replace the logic to populate unique ID for ProductVariantTransactionCatalogueInternalAccountMapping
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductVariantTransactionCatalogueInternalAccountMapping) NewEntity() data.Entity {
	return &ProductVariantTransactionCatalogueInternalAccountMapping{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductVariantTransactionCatalogueInternalAccountMapping) GetTableName() string {
	return "product_variant_transaction_catalogue_internal_account_mapping"
}

// IProductVariantTransactionCatalogueInternalAccountMappingDAO is the dao interface for ProductVariantTransactionCatalogueInternalAccountMapping
//go:generate mockery --name IProductVariantTransactionCatalogueInternalAccountMappingDAO --inpackage --case=underscore
type IProductVariantTransactionCatalogueInternalAccountMappingDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueInternalAccountMapping, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueInternalAccountMapping, error)

	// Save ...
	Save(ctx context.Context, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductVariantTransactionCatalogueInternalAccountMapping) error

	// Update ...
	Update(ctx context.Context, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductVariantTransactionCatalogueInternalAccountMapping, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueInternalAccountMapping, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueInternalAccountMapping, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error
}

// ProductVariantTransactionCatalogueInternalAccountMappingDAO ...
type ProductVariantTransactionCatalogueInternalAccountMappingDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductVariantTransactionCatalogueInternalAccountMappingDAO creates a data access object for ProductVariantTransactionCatalogueInternalAccountMapping
func NewProductVariantTransactionCatalogueInternalAccountMappingDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductVariantTransactionCatalogueInternalAccountMappingDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductVariantTransactionCatalogueInternalAccountMappingDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductVariantTransactionCatalogueInternalAccountMapping{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantTransactionCatalogueInternalAccountMapping), err
}

// LoadByIDOnSlave ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantTransactionCatalogueInternalAccountMapping), err
}

// Save ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) Save(ctx context.Context, entity *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	methodName := "save"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) SaveBatch(ctx context.Context, entities []*ProductVariantTransactionCatalogueInternalAccountMapping) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) Update(ctx context.Context, data *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	methodName := "update"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) UpdateEntity(ctx context.Context, preData *ProductVariantTransactionCatalogueInternalAccountMapping, newData *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	methodName := "find"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantTransactionCatalogueInternalAccountMapping, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantTransactionCatalogueInternalAccountMapping))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantTransactionCatalogueInternalAccountMapping, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantTransactionCatalogueInternalAccountMapping, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantTransactionCatalogueInternalAccountMapping))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductVariantTransactionCatalogueInternalAccountMappingDAO) Upsert(ctx context.Context, data *ProductVariantTransactionCatalogueInternalAccountMapping) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_variant_transaction_catalogue_internal_account_mappingDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

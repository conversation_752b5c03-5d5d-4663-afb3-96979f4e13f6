package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var transaction_catalogueDao = "transaction_catalogue_dao"

// GetID implements the GetID function for Entity Interface
func (impl *TransactionCatalogue) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *TransactionCatalogue) SetID(ID string) {
	// replace the logic to populate unique ID for TransactionCatalogue
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *TransactionCatalogue) NewEntity() data.Entity {
	return &TransactionCatalogue{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *TransactionCatalogue) GetTableName() string {
	return "transaction_catalogue"
}

// ITransactionCatalogueDAO is the dao interface for TransactionCatalogue
//go:generate mockery --name ITransactionCatalogueDAO --inpackage --case=underscore
type ITransactionCatalogueDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*TransactionCatalogue, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*TransactionCatalogue, error)

	// Save ...
	Save(ctx context.Context, newData *TransactionCatalogue) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*TransactionCatalogue) error

	// Update ...
	Update(ctx context.Context, newData *TransactionCatalogue) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *TransactionCatalogue, newData *TransactionCatalogue) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*TransactionCatalogue, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*TransactionCatalogue, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *TransactionCatalogue) error
}

// TransactionCatalogueDAO ...
type TransactionCatalogueDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewTransactionCatalogueDAO creates a data access object for TransactionCatalogue
func NewTransactionCatalogueDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *TransactionCatalogueDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &TransactionCatalogueDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &TransactionCatalogue{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *TransactionCatalogueDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*TransactionCatalogue, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*TransactionCatalogue), err
}

// LoadByIDOnSlave ...
func (dao *TransactionCatalogueDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*TransactionCatalogue, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*TransactionCatalogue), err
}

// Save ...
func (dao *TransactionCatalogueDAO) Save(ctx context.Context, entity *TransactionCatalogue) error {
	methodName := "save"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *TransactionCatalogueDAO) SaveBatch(ctx context.Context, entities []*TransactionCatalogue) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *TransactionCatalogueDAO) Update(ctx context.Context, data *TransactionCatalogue) error {
	methodName := "update"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *TransactionCatalogueDAO) UpdateEntity(ctx context.Context, preData *TransactionCatalogue, newData *TransactionCatalogue) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *TransactionCatalogueDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*TransactionCatalogue, error) {
	methodName := "find"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*TransactionCatalogue, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*TransactionCatalogue))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *TransactionCatalogueDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*TransactionCatalogue, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*TransactionCatalogue, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*TransactionCatalogue))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *TransactionCatalogueDAO) Upsert(ctx context.Context, data *TransactionCatalogue) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(transaction_catalogueDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(transaction_catalogueDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

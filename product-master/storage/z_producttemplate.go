package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var product_templateDao = "product_template_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductTemplate) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductTemplate) SetID(ID string) {
	// replace the logic to populate unique ID for ProductTemplate
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductTemplate) NewEntity() data.Entity {
	return &ProductTemplate{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductTemplate) GetTableName() string {
	return "product_template"
}

// IProductTemplateDAO is the dao interface for ProductTemplate
//go:generate mockery --name IProductTemplateDAO --inpackage --case=underscore
type IProductTemplateDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductTemplate, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductTemplate, error)

	// Save ...
	Save(ctx context.Context, newData *ProductTemplate) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductTemplate) error

	// Update ...
	Update(ctx context.Context, newData *ProductTemplate) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductTemplate, newData *ProductTemplate) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplate, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplate, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductTemplate) error
}

// ProductTemplateDAO ...
type ProductTemplateDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductTemplateDAO creates a data access object for ProductTemplate
func NewProductTemplateDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductTemplateDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductTemplateDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductTemplate{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductTemplateDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductTemplate, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductTemplate), err
}

// LoadByIDOnSlave ...
func (dao *ProductTemplateDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductTemplate, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductTemplate), err
}

// Save ...
func (dao *ProductTemplateDAO) Save(ctx context.Context, entity *ProductTemplate) error {
	methodName := "save"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductTemplateDAO) SaveBatch(ctx context.Context, entities []*ProductTemplate) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductTemplateDAO) Update(ctx context.Context, data *ProductTemplate) error {
	methodName := "update"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductTemplateDAO) UpdateEntity(ctx context.Context, preData *ProductTemplate, newData *ProductTemplate) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductTemplateDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplate, error) {
	methodName := "find"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductTemplate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductTemplate))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductTemplateDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductTemplate, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductTemplate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductTemplate))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductTemplateDAO) Upsert(ctx context.Context, data *ProductTemplate) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(product_templateDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(product_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

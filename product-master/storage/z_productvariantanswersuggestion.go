package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var productVariantAnswerSuggestionDao = "product_variant_answer_suggestion_dao"

// GetID implements the GetID function for Entity Interface
func (impl *ProductVariantAnswerSuggestion) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *ProductVariantAnswerSuggestion) SetID(ID string) {
	// replace the logic to populate unique ID for ProductVariantAnswerSuggestion
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *ProductVariantAnswerSuggestion) NewEntity() data.Entity {
	return &ProductVariantAnswerSuggestion{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *ProductVariantAnswerSuggestion) GetTableName() string {
	return "product_variant_answer_suggestion"
}

// IProductVariantAnswerSuggestionDAO is the dao interface for ProductVariantAnswerSuggestion
//
//go:generate mockery --name IProductVariantAnswerSuggestionDAO --inpackage --case=underscore
type IProductVariantAnswerSuggestionDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantAnswerSuggestion, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantAnswerSuggestion, error)

	// Save ...
	Save(ctx context.Context, newData *ProductVariantAnswerSuggestion) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*ProductVariantAnswerSuggestion) error

	// Update ...
	Update(ctx context.Context, newData *ProductVariantAnswerSuggestion) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *ProductVariantAnswerSuggestion, newData *ProductVariantAnswerSuggestion) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantAnswerSuggestion, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantAnswerSuggestion, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *ProductVariantAnswerSuggestion) error
}

// ProductVariantAnswerSuggestionDAO ...
type ProductVariantAnswerSuggestionDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewProductVariantAnswerSuggestionDAO creates a data access object for ProductVariantAnswerSuggestion
func NewProductVariantAnswerSuggestionDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ProductVariantAnswerSuggestionDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ProductVariantAnswerSuggestionDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &ProductVariantAnswerSuggestion{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ProductVariantAnswerSuggestionDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*ProductVariantAnswerSuggestion, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantAnswerSuggestion), err
}

// LoadByIDOnSlave ...
func (dao *ProductVariantAnswerSuggestionDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*ProductVariantAnswerSuggestion, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*ProductVariantAnswerSuggestion), err
}

// Save ...
func (dao *ProductVariantAnswerSuggestionDAO) Save(ctx context.Context, entity *ProductVariantAnswerSuggestion) error {
	methodName := "save"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ProductVariantAnswerSuggestionDAO) SaveBatch(ctx context.Context, entities []*ProductVariantAnswerSuggestion) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ProductVariantAnswerSuggestionDAO) Update(ctx context.Context, data *ProductVariantAnswerSuggestion) error {
	methodName := "update"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ProductVariantAnswerSuggestionDAO) UpdateEntity(ctx context.Context, preData *ProductVariantAnswerSuggestion, newData *ProductVariantAnswerSuggestion) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ProductVariantAnswerSuggestionDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantAnswerSuggestion, error) {
	methodName := "find"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantAnswerSuggestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantAnswerSuggestion))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ProductVariantAnswerSuggestionDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*ProductVariantAnswerSuggestion, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*ProductVariantAnswerSuggestion, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*ProductVariantAnswerSuggestion))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ProductVariantAnswerSuggestionDAO) Upsert(ctx context.Context, data *ProductVariantAnswerSuggestion) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(productVariantAnswerSuggestionDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(productVariantAnswerSuggestionDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var pocket_templateDao = "pocket_template_dao"

// GetID implements the GetID function for Entity Interface
func (impl *PocketTemplate) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *PocketTemplate) SetID(ID string) {
	// replace the logic to populate unique ID for PocketTemplate
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *PocketTemplate) NewEntity() data.Entity {
	return &PocketTemplate{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *PocketTemplate) GetTableName() string {
	return "pocket_template"
}

// IPocketTemplateDAO is the dao interface for PocketTemplate
//go:generate mockery --name IPocketTemplateDAO --inpackage --case=underscore
type IPocketTemplateDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplate, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplate, error)

	// Save ...
	Save(ctx context.Context, newData *PocketTemplate) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*PocketTemplate) error

	// Update ...
	Update(ctx context.Context, newData *PocketTemplate) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *PocketTemplate, newData *PocketTemplate) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplate, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplate, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *PocketTemplate) error
}

// PocketTemplateDAO ...
type PocketTemplateDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewPocketTemplateDAO creates a data access object for PocketTemplate
func NewPocketTemplateDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *PocketTemplateDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &PocketTemplateDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &PocketTemplate{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *PocketTemplateDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*PocketTemplate, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplate), err
}

// LoadByIDOnSlave ...
func (dao *PocketTemplateDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*PocketTemplate, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*PocketTemplate), err
}

// Save ...
func (dao *PocketTemplateDAO) Save(ctx context.Context, entity *PocketTemplate) error {
	methodName := "save"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *PocketTemplateDAO) SaveBatch(ctx context.Context, entities []*PocketTemplate) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *PocketTemplateDAO) Update(ctx context.Context, data *PocketTemplate) error {
	methodName := "update"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *PocketTemplateDAO) UpdateEntity(ctx context.Context, preData *PocketTemplate, newData *PocketTemplate) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *PocketTemplateDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplate, error) {
	methodName := "find"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplate))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *PocketTemplateDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*PocketTemplate, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*PocketTemplate, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*PocketTemplate))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *PocketTemplateDAO) Upsert(ctx context.Context, data *PocketTemplate) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(pocket_templateDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(pocket_templateDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var loanInterestDao = "loan_interest_dao"

// GetID implements the GetID function for Entity Interface
func (impl *LoanInterest) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *LoanInterest) SetID(ID string) {
	// replace the logic to populate unique ID for LoanInterest
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *LoanInterest) NewEntity() data.Entity {
	return &LoanInterest{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *LoanInterest) GetTableName() string {
	return "loan_interest"
}

// ILoanInterestDAO is the dao interface for LoanInterest
//
//go:generate mockery --name ILoanInterestDAO --inpackage --case=underscore
type ILoanInterestDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInterest, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInterest, error)

	// Save ...
	Save(ctx context.Context, newData *LoanInterest) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*LoanInterest) error

	// Update ...
	Update(ctx context.Context, newData *LoanInterest) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *LoanInterest, newData *LoanInterest) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInterest, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInterest, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *LoanInterest) error
}

// LoanInterestDAO ...
type LoanInterestDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewLoanInterestDAO creates a data access object for LoanInterest
func NewLoanInterestDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *LoanInterestDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &LoanInterestDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &LoanInterest{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *LoanInterestDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*LoanInterest, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInterest), err
}

// LoadByIDOnSlave ...
func (dao *LoanInterestDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*LoanInterest, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*LoanInterest), err
}

// Save ...
func (dao *LoanInterestDAO) Save(ctx context.Context, entity *LoanInterest) error {
	methodName := "save"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *LoanInterestDAO) SaveBatch(ctx context.Context, entities []*LoanInterest) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *LoanInterestDAO) Update(ctx context.Context, data *LoanInterest) error {
	methodName := "update"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *LoanInterestDAO) UpdateEntity(ctx context.Context, preData *LoanInterest, newData *LoanInterest) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *LoanInterestDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*LoanInterest, error) {
	methodName := "find"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInterest, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInterest))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *LoanInterestDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*LoanInterest, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*LoanInterest, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*LoanInterest))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *LoanInterestDAO) Upsert(ctx context.Context, data *LoanInterest) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(loanInterestDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(loanInterestDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

include:
  - project: gx-regional/dbmy/ci
    ref: master
    file: pipelines/go/service.yml

variables:
  CMD_NAME: product-master
  ECR_URI: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/product-master
  MODULE_PATH: product-master
  UNITTEST_MIN_COVERAGE: 85
  ALL_DEPRECATED_INITS_MIGRATED: 'true'
#  DISABLE_TAG_RC: "true" # disable this until we figure out how to tag our own api module
  DISABLE_VERIFY_MYSQL: "true"
  REPO_SPECIFIC_IGNORE_PATHS: /mock_|storage/z_|storage/database_store\.go|.*routes\.go|test/responses/.*|cmd/.*|example/*|server/.*|/mockservices/.*|cmd/.*|utils/client/.*
  DISABLE_BUNDLE_MYSQL: "true"
  DISABLE_PACKAGE_DOCKER_MYSQL: "true"
  DISABLE_SAST: "true"
  SEMVER_SUFFIX: "-dbmy"
  DISABLE_DEPLOY_DEV: 'true'
  GO_VERSION: 1.23.3a
  GOLINT_VERSION: 'v1.62.2-alpine-a'
  DISABLE_TAG_RC: "true"
  KUBERNETES_MEMORY_REQUEST: '2Gi'
  KUBERNETES_MEMORY_LIMIT: '3Gi'

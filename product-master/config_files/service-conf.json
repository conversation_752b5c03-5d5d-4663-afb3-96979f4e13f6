{"name": "product-master Service", "serviceName": "product-master", "host": "0.0.0.0", "port": 8080, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "root:root1234@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "root:root1234@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "localhost", "port": 8125}, "logger": {"syslogTag": "structuredlog.productmaster", "workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 0, "logFormat": "cglsdebug", "development": true}, "productMasterConfig": {"clientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}}, "hermesConfig": {"baseURL": "https://backend.dev.gbank.app/hermes"}, "locale": {"currency": "IDR", "defaultLanguage": "en", "acceptedLanguagesToLocales": {"en": "EN", "ms": "MS"}}, "tenant": "MY", "featureFlags": {"enableGetDepositInterestByEffectiveDate": true, "_enableGetInterestByEffectiveDate": "this is a release feature flag, it should be clean up after release", "enableBoostPocketFESpotRate": true}}
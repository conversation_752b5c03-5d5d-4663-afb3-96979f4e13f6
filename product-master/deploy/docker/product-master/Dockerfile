ARG BASE_IMAGE="gcr.io/distroless/base:nonroot-amd64"
FROM $BASE_IMAGE

LABEL name="product-master"
LABEL version="1.0"
LABEL description="Service to manage configurations of all products"
LABEL tech_owner="komal.bansal"
LABEL team_alias="Corebanking"
LABEL slack="gxs-deposits-squads"
LABEL gitlab_repo="https://gitlab.myteksi.net/bersama/core-banking/-/tree/master/product-master"
LABEL wiki="https://wiki.grab.com/display/Digibank/Product-Master"

COPY product-master .
COPY db ./

USER nonroot

CMD ["./product-master"]

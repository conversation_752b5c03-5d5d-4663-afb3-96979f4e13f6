// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package client

import (
	bytes "bytes"
	context "context"
	fmt "fmt"

	_go "github.com/json-iterator/go"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	http "net/http"

	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
)

// ProductMasterClient makes calls to ProductMaster service.
type ProductMasterClient struct {
	machinery klient.RoundTripper
}

// MakeProductMasterClient instantiates a new ProductMasterClient.
// Deprecated: Use NewProductMasterClient instead
func MakeProductMasterClient(initializer klient.Initializer) (*ProductMasterClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &ProductMasterClient{
		machinery: roundTripper,
	}, nil
}

// NewProductMasterClient instantiates a new ProductMasterClient.
func NewProductMasterClient(baseURL string, options ...klient.Option) (*ProductMasterClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &ProductMasterClient{
		machinery: roundTripper,
	}, nil
}

// CreateProductTemplate creates a new Product Template
func (p *ProductMasterClient) CreateProductTemplate(ctx context.Context, req *api.CreateProductTemplateRequest) (*api.CreateProductTemplateResponse, error) {
	reqShell := (*CreateProductTemplateRequestShell)(req)
	resShell := &CreateProductTemplateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductTemplateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductTemplateResponse)(resShell), err
}

// GetProductTemplate fetches a product template by id
func (p *ProductMasterClient) GetProductTemplate(ctx context.Context, req *api.GetProductTemplateRequest) (*api.GetProductTemplateResponse, error) {
	reqShell := (*GetProductTemplateRequestShell)(req)
	resShell := &GetProductTemplateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductTemplateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductTemplateResponse)(resShell), err
}

// UpdateProductTemplateStatus updates the status of Product Template
func (p *ProductMasterClient) UpdateProductTemplateStatus(ctx context.Context, req *api.UpdateProductTemplateStatusRequest) (*api.UpdateProductTemplateStatusResponse, error) {
	reqShell := (*UpdateProductTemplateStatusRequestShell)(req)
	resShell := &UpdateProductTemplateStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateProductTemplateStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateProductTemplateStatusResponse)(resShell), err
}

// CreateProductVariant creates a new Product Variant
func (p *ProductMasterClient) CreateProductVariant(ctx context.Context, req *api.CreateProductVariantRequest) (*api.CreateProductVariantResponse, error) {
	reqShell := (*CreateProductVariantRequestShell)(req)
	resShell := &CreateProductVariantResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductVariantDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductVariantResponse)(resShell), err
}

// ListProductVariants fetches a product variant by id
func (p *ProductMasterClient) ListProductVariants(ctx context.Context, req *api.ListProductVariantsRequest) (*api.ListProductVariantsResponse, error) {
	reqShell := (*ListProductVariantsRequestShell)(req)
	resShell := &ListProductVariantsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listProductVariantsDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListProductVariantsResponse)(resShell), err
}

// GetProductVariant fetches a product variant by id
func (p *ProductMasterClient) GetProductVariant(ctx context.Context, req *api.GetProductVariantRequest) (*api.GetProductVariantResponse, error) {
	reqShell := (*GetProductVariantRequestShell)(req)
	resShell := &GetProductVariantResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductVariantDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductVariantResponse)(resShell), err
}

// GetProductVariantByCode fetches a product variant by code
func (p *ProductMasterClient) GetProductVariantByCode(ctx context.Context, req *api.GetProductVariantByCodeRequest) (*api.GetProductVariantByCodeResponse, error) {
	reqShell := (*GetProductVariantByCodeRequestShell)(req)
	resShell := &GetProductVariantByCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductVariantByCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductVariantByCodeResponse)(resShell), err
}

// UpdateProductVariantStatus updates the status of Product Variant
func (p *ProductMasterClient) UpdateProductVariantStatus(ctx context.Context, req *api.UpdateProductVariantStatusRequest) (*api.UpdateProductVariantStatusResponse, error) {
	reqShell := (*UpdateProductVariantStatusRequestShell)(req)
	resShell := &UpdateProductVariantStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateProductVariantStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateProductVariantStatusResponse)(resShell), err
}

// CreateProduct creates a new Product
func (p *ProductMasterClient) CreateProduct(ctx context.Context, req *api.CreateProductRequest) (*api.CreateProductResponse, error) {
	reqShell := (*CreateProductRequestShell)(req)
	resShell := &CreateProductResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductResponse)(resShell), err
}

// GetProduct fetches a product by id
func (p *ProductMasterClient) GetProduct(ctx context.Context, req *api.GetProductRequest) (*api.GetProductResponse, error) {
	reqShell := (*GetProductRequestShell)(req)
	resShell := &GetProductResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductResponse)(resShell), err
}

// GetProduct fetches a product by code
func (p *ProductMasterClient) GetProductByCode(ctx context.Context, req *api.GetProductByCodeRequest) (*api.GetProductByCodeResponse, error) {
	reqShell := (*GetProductByCodeRequestShell)(req)
	resShell := &GetProductByCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductByCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductByCodeResponse)(resShell), err
}

// UpdateProductStatus updates the status of Product Template
func (p *ProductMasterClient) UpdateProductStatus(ctx context.Context, req *api.UpdateProductStatusRequest) (*api.UpdateProductStatusResponse, error) {
	reqShell := (*UpdateProductStatusRequestShell)(req)
	resShell := &UpdateProductStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateProductStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateProductStatusResponse)(resShell), err
}

// CreateProductTemplateParameter creates a new Product Template Parameter
func (p *ProductMasterClient) CreateProductTemplateParameter(ctx context.Context, req *api.CreateProductTemplateParameterRequest) (*api.CreateProductTemplateParameterResponse, error) {
	reqShell := (*CreateProductTemplateParameterRequestShell)(req)
	resShell := &CreateProductTemplateParameterResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductTemplateParameterDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductTemplateParameterResponse)(resShell), err
}

// GetProductTemplateParameter fetches a product template parameter by id
func (p *ProductMasterClient) GetProductTemplateParameter(ctx context.Context, req *api.GetProductTemplateParameterRequest) (*api.GetProductTemplateParameterResponse, error) {
	reqShell := (*GetProductTemplateParameterRequestShell)(req)
	resShell := &GetProductTemplateParameterResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductTemplateParameterDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductTemplateParameterResponse)(resShell), err
}

// ListProductTemplateParameters fetches all product template parameters for a product variant
func (p *ProductMasterClient) ListProductTemplateParameters(ctx context.Context, req *api.ListProductTemplateParametersRequest) (*api.ListProductTemplateParametersResponse, error) {
	reqShell := (*ListProductTemplateParametersRequestShell)(req)
	resShell := &ListProductTemplateParametersResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listProductTemplateParametersDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListProductTemplateParametersResponse)(resShell), err
}

// UpdateProductTemplateParameterValue updates the value of Product Template Parameter Value
func (p *ProductMasterClient) UpdateProductTemplateParameterValue(ctx context.Context, req *api.UpdateProductTemplateParameterValueRequest) (*api.UpdateProductTemplateParameterValueResponse, error) {
	reqShell := (*UpdateProductTemplateParameterValueRequestShell)(req)
	resShell := &UpdateProductTemplateParameterValueResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateProductTemplateParameterValueDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateProductTemplateParameterValueResponse)(resShell), err
}

// CreateProductVariantParameter creates a new Product Variant Parameter
func (p *ProductMasterClient) CreateProductVariantParameter(ctx context.Context, req *api.CreateProductVariantParameterRequest) (*api.CreateProductVariantParameterResponse, error) {
	reqShell := (*CreateProductVariantParameterRequestShell)(req)
	resShell := &CreateProductVariantParameterResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductVariantParameterDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductVariantParameterResponse)(resShell), err
}

// GetProductVariantParameter fetches a product variant parameter by id
func (p *ProductMasterClient) GetProductVariantParameter(ctx context.Context, req *api.GetProductVariantParameterRequest) (*api.GetProductVariantParameterResponse, error) {
	reqShell := (*GetProductVariantParameterRequestShell)(req)
	resShell := &GetProductVariantParameterResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductVariantParameterDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductVariantParameterResponse)(resShell), err
}

// ListProductVariantParameters fetches all product variant parameters for a product variant
func (p *ProductMasterClient) ListProductVariantParameters(ctx context.Context, req *api.ListProductVariantParametersRequest) (*api.ListProductVariantParametersResponse, error) {
	reqShell := (*ListProductVariantParametersRequestShell)(req)
	resShell := &ListProductVariantParametersResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listProductVariantParametersDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListProductVariantParametersResponse)(resShell), err
}

// ListEffectiveProductVariantParameters fetches all product variant parameters for a product variant (includes the overrides from product-template-parameters
func (p *ProductMasterClient) ListEffectiveProductVariantParameters(ctx context.Context, req *api.ListEffectiveProductVariantParametersRequest) (*api.ListEffectiveProductVariantParametersResponse, error) {
	reqShell := (*ListEffectiveProductVariantParametersRequestShell)(req)
	resShell := &ListEffectiveProductVariantParametersResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listEffectiveProductVariantParametersDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListEffectiveProductVariantParametersResponse)(resShell), err
}

// UpdateProductVariantParameterValue updates the value of Product Variant Parameter Value
func (p *ProductMasterClient) UpdateProductVariantParameterValue(ctx context.Context, req *api.UpdateProductVariantParameterValueRequest) (*api.UpdateProductVariantParameterValueResponse, error) {
	reqShell := (*UpdateProductVariantParameterValueRequestShell)(req)
	resShell := &UpdateProductVariantParameterValueResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateProductVariantParameterValueDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateProductVariantParameterValueResponse)(resShell), err
}

// CreateTransactionCatalogue creates a new Transaction catalogue
func (p *ProductMasterClient) CreateTransactionCatalogue(ctx context.Context, req *api.CreateTransactionCatalogueRequest) (*api.CreateTransactionCatalogueResponse, error) {
	reqShell := (*CreateTransactionCatalogueRequestShell)(req)
	resShell := &CreateTransactionCatalogueResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createTransactionCatalogueDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateTransactionCatalogueResponse)(resShell), err
}

// GetTransactionCatalogue fetches a transaction catalogue by id
func (p *ProductMasterClient) GetTransactionCatalogue(ctx context.Context, req *api.GetTransactionCatalogueRequest) (*api.GetTransactionCatalogueResponse, error) {
	reqShell := (*GetTransactionCatalogueRequestShell)(req)
	resShell := &GetTransactionCatalogueResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getTransactionCatalogueDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetTransactionCatalogueResponse)(resShell), err
}

// UpdateTransactionCatalogueStatus updates the status of Transaction catalogue
func (p *ProductMasterClient) UpdateTransactionCatalogueStatus(ctx context.Context, req *api.UpdateTransactionCatalogueStatusRequest) (*api.UpdateTransactionCatalogueStatusResponse, error) {
	reqShell := (*UpdateTransactionCatalogueStatusRequestShell)(req)
	resShell := &UpdateTransactionCatalogueStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateTransactionCatalogueStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateTransactionCatalogueStatusResponse)(resShell), err
}

// CreateProductVariantTransactionCatalogueMapping creates a new Product Variant Transaction catalogue mapping
func (p *ProductMasterClient) CreateProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueMappingRequest) (*api.CreateProductVariantTransactionCatalogueMappingResponse, error) {
	reqShell := (*CreateProductVariantTransactionCatalogueMappingRequestShell)(req)
	resShell := &CreateProductVariantTransactionCatalogueMappingResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductVariantTransactionCatalogueMappingDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductVariantTransactionCatalogueMappingResponse)(resShell), err
}

// GetProductVariantTransactionCatalogueMapping fetches a Product Variant Transaction catalogue mapping by id
func (p *ProductMasterClient) GetProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueMappingRequest) (*api.GetProductVariantTransactionCatalogueMappingResponse, error) {
	reqShell := (*GetProductVariantTransactionCatalogueMappingRequestShell)(req)
	resShell := &GetProductVariantTransactionCatalogueMappingResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductVariantTransactionCatalogueMappingDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductVariantTransactionCatalogueMappingResponse)(resShell), err
}

// UpdateProductVariantTransactionCatalogueMappingStatus updates the status of Product Variant Transaction catalogue mapping
func (p *ProductMasterClient) UpdateProductVariantTransactionCatalogueMappingStatus(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueMappingStatusRequest) (*api.UpdateProductVariantTransactionCatalogueMappingStatusResponse, error) {
	reqShell := (*UpdateProductVariantTransactionCatalogueMappingStatusRequestShell)(req)
	resShell := &UpdateProductVariantTransactionCatalogueMappingStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateProductVariantTransactionCatalogueMappingStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateProductVariantTransactionCatalogueMappingStatusResponse)(resShell), err
}

// CreateGeneralLedger creates a new GeneralLedger
func (p *ProductMasterClient) CreateGeneralLedger(ctx context.Context, req *api.CreateGeneralLedgerRequest) (*api.CreateGeneralLedgerResponse, error) {
	reqShell := (*CreateGeneralLedgerRequestShell)(req)
	resShell := &CreateGeneralLedgerResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createGeneralLedgerDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateGeneralLedgerResponse)(resShell), err
}

// GetGeneralLedger fetches a general-ledger by id
func (p *ProductMasterClient) GetGeneralLedger(ctx context.Context, req *api.GetGeneralLedgerRequest) (*api.GetGeneralLedgerResponse, error) {
	reqShell := (*GetGeneralLedgerRequestShell)(req)
	resShell := &GetGeneralLedgerResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getGeneralLedgerDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetGeneralLedgerResponse)(resShell), err
}

// GetGeneralLedgerByCode fetches a general-ledger by code
func (p *ProductMasterClient) GetGeneralLedgerByCode(ctx context.Context, req *api.GetGeneralLedgerByCodeRequest) (*api.GetGeneralLedgerByCodeResponse, error) {
	reqShell := (*GetGeneralLedgerByCodeRequestShell)(req)
	resShell := &GetGeneralLedgerByCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getGeneralLedgerByCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetGeneralLedgerByCodeResponse)(resShell), err
}

// UpdateGeneralLedgerStatus updates the status of general-ledger
func (p *ProductMasterClient) UpdateGeneralLedgerStatus(ctx context.Context, req *api.UpdateGeneralLedgerStatusRequest) (*api.UpdateGeneralLedgerStatusResponse, error) {
	reqShell := (*UpdateGeneralLedgerStatusRequestShell)(req)
	resShell := &UpdateGeneralLedgerStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateGeneralLedgerStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateGeneralLedgerStatusResponse)(resShell), err
}

// CreateInternalAccount creates a new Internal Account
func (p *ProductMasterClient) CreateInternalAccount(ctx context.Context, req *api.CreateInternalAccountRequest) (*api.CreateInternalAccountResponse, error) {
	reqShell := (*CreateInternalAccountRequestShell)(req)
	resShell := &CreateInternalAccountResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createInternalAccountDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateInternalAccountResponse)(resShell), err
}

// GetInternalAccount fetches a internal account by id
func (p *ProductMasterClient) GetInternalAccount(ctx context.Context, req *api.GetInternalAccountRequest) (*api.GetInternalAccountResponse, error) {
	reqShell := (*GetInternalAccountRequestShell)(req)
	resShell := &GetInternalAccountResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getInternalAccountDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetInternalAccountResponse)(resShell), err
}

// ListInternalAccounts fetches a list of internal account by code and generalLedgerID
func (p *ProductMasterClient) ListInternalAccounts(ctx context.Context, req *api.ListInternalAccountsRequest) (*api.ListInternalAccountsResponse, error) {
	reqShell := (*ListInternalAccountsRequestShell)(req)
	resShell := &ListInternalAccountsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listInternalAccountsDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListInternalAccountsResponse)(resShell), err
}

// UpdateInternalAccountStatus updates the status of internal account
func (p *ProductMasterClient) UpdateInternalAccountStatus(ctx context.Context, req *api.UpdateInternalAccountStatusRequest) (*api.UpdateInternalAccountStatusResponse, error) {
	reqShell := (*UpdateInternalAccountStatusRequestShell)(req)
	resShell := &UpdateInternalAccountStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateInternalAccountStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateInternalAccountStatusResponse)(resShell), err
}

// CreateProductVariantTransactionCatalogueInternalAccountMapping creates a new product-variant-transaction-catalogue-internal-account-mapping
func (p *ProductMasterClient) CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	reqShell := (*CreateProductVariantTransactionCatalogueInternalAccountMappingRequestShell)(req)
	resShell := &CreateProductVariantTransactionCatalogueInternalAccountMappingResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductVariantTransactionCatalogueInternalAccountMappingDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse)(resShell), err
}

// GetProductVariantTransactionCatalogueInternalAccountMapping fetches a product-variant-transaction-catalogue-internal-account-mapping by id
func (p *ProductMasterClient) GetProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	reqShell := (*GetProductVariantTransactionCatalogueInternalAccountMappingRequestShell)(req)
	resShell := &GetProductVariantTransactionCatalogueInternalAccountMappingResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductVariantTransactionCatalogueInternalAccountMappingDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse)(resShell), err
}

// GetProductVariantTransactionCatalogueInternalAccountMappingByKey fetches a product-variant-transaction-catalogue-internal-account-mapping by identifer key
func (p *ProductMasterClient) GetProductVariantTransactionCatalogueInternalAccountMappingByKey(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse, error) {
	reqShell := (*GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequestShell)(req)
	resShell := &GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getProductVariantTransactionCatalogueInternalAccountMappingByKeyDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse)(resShell), err
}

// UpdateProductVariantTransactionCatalogueInternalAccountMapping updates the status of product-variant-transaction-catalogue-internal-account-mapping
func (p *ProductMasterClient) UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) (*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse, error) {
	reqShell := (*UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequestShell)(req)
	resShell := &UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateProductVariantTransactionCatalogueInternalAccountMappingStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse)(resShell), err
}

// CreateBaseInterest creates a new base-interest
func (p *ProductMasterClient) CreateBaseInterest(ctx context.Context, req *api.CreateBaseInterestRequest) (*api.CreateBaseInterestResponse, error) {
	reqShell := (*CreateBaseInterestRequestShell)(req)
	resShell := &CreateBaseInterestResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createBaseInterestDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateBaseInterestResponse)(resShell), err
}

// GetBaseInterest fetches a base interest by id
func (p *ProductMasterClient) GetBaseInterest(ctx context.Context, req *api.GetBaseInterestRequest) (*api.GetBaseInterestResponse, error) {
	reqShell := (*GetBaseInterestRequestShell)(req)
	resShell := &GetBaseInterestResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getBaseInterestDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetBaseInterestResponse)(resShell), err
}

// GetBaseInterestByCode fetches a base interest by code
func (p *ProductMasterClient) GetBaseInterestByCode(ctx context.Context, req *api.GetBaseInterestByCodeRequest) (*api.GetBaseInterestByCodeResponse, error) {
	reqShell := (*GetBaseInterestByCodeRequestShell)(req)
	resShell := &GetBaseInterestByCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getBaseInterestByCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetBaseInterestByCodeResponse)(resShell), err
}

// CreateBaseInterestVersion creates a new base interest version
func (p *ProductMasterClient) CreateBaseInterestVersion(ctx context.Context, req *api.CreateBaseInterestVersionRequest) (*api.CreateBaseInterestVersionResponse, error) {
	reqShell := (*CreateBaseInterestVersionRequestShell)(req)
	resShell := &CreateBaseInterestVersionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createBaseInterestVersionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateBaseInterestVersionResponse)(resShell), err
}

// GetBaseInterestVersion fetches a base interest version by id
func (p *ProductMasterClient) GetBaseInterestVersion(ctx context.Context, req *api.GetBaseInterestVersionRequest) (*api.GetBaseInterestVersionResponse, error) {
	reqShell := (*GetBaseInterestVersionRequestShell)(req)
	resShell := &GetBaseInterestVersionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getBaseInterestVersionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetBaseInterestVersionResponse)(resShell), err
}

// CreateBaseInterestTimeSlabRate creates a new base interest time slab rate
func (p *ProductMasterClient) CreateBaseInterestTimeSlabRate(ctx context.Context, req *api.CreateBaseInterestTimeSlabRateRequest) (*api.CreateBaseInterestTimeSlabRateResponse, error) {
	reqShell := (*CreateBaseInterestTimeSlabRateRequestShell)(req)
	resShell := &CreateBaseInterestTimeSlabRateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createBaseInterestTimeSlabRateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateBaseInterestTimeSlabRateResponse)(resShell), err
}

// GetBaseInterestTimeSlabRate fetches a base interest time slab rate by id
func (p *ProductMasterClient) GetBaseInterestTimeSlabRate(ctx context.Context, req *api.GetBaseInterestTimeSlabRateRequest) (*api.GetBaseInterestTimeSlabRateResponse, error) {
	reqShell := (*GetBaseInterestTimeSlabRateRequestShell)(req)
	resShell := &GetBaseInterestTimeSlabRateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getBaseInterestTimeSlabRateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetBaseInterestTimeSlabRateResponse)(resShell), err
}

// CreateDepositInterest creates a new deposit interest
func (p *ProductMasterClient) CreateDepositInterest(ctx context.Context, req *api.CreateDepositInterestRequest) (*api.CreateDepositInterestResponse, error) {
	reqShell := (*CreateDepositInterestRequestShell)(req)
	resShell := &CreateDepositInterestResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createDepositInterestDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateDepositInterestResponse)(resShell), err
}

// GetDepositInterest fetches a deposit interest by id
func (p *ProductMasterClient) GetDepositInterest(ctx context.Context, req *api.GetDepositInterestRequest) (*api.GetDepositInterestResponse, error) {
	reqShell := (*GetDepositInterestRequestShell)(req)
	resShell := &GetDepositInterestResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getDepositInterestDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetDepositInterestResponse)(resShell), err
}

// GetDepositInterestByCode fetches a deposit interest by code
func (p *ProductMasterClient) GetDepositInterestByCode(ctx context.Context, req *api.GetDepositInterestByCodeRequest) (*api.GetDepositInterestByCodeResponse, error) {
	reqShell := (*GetDepositInterestByCodeRequestShell)(req)
	resShell := &GetDepositInterestByCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getDepositInterestByCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetDepositInterestByCodeResponse)(resShell), err
}

// CreateDepositInterestVersion creates a new deposit interest version
func (p *ProductMasterClient) CreateDepositInterestVersion(ctx context.Context, req *api.CreateDepositInterestVersionRequest) (*api.CreateDepositInterestVersionRequest, error) {
	reqShell := (*CreateDepositInterestVersionRequestShell)(req)
	resShell := &CreateDepositInterestVersionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createDepositInterestVersionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateDepositInterestVersionRequest)(resShell), err
}

// GetDepositInterestVersion fetches a deposit interest version by id
func (p *ProductMasterClient) GetDepositInterestVersion(ctx context.Context, req *api.GetDepositInterestVersionRequest) (*api.GetDepositInterestVersionResponse, error) {
	reqShell := (*GetDepositInterestVersionRequestShell)(req)
	resShell := &GetDepositInterestVersionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getDepositInterestVersionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetDepositInterestVersionResponse)(resShell), err
}

// CreateDepositInterestAmountSlabRate creates a new deposit interest amount slab rate
func (p *ProductMasterClient) CreateDepositInterestAmountSlabRate(ctx context.Context, req *api.CreateDepositInterestAmountSlabRateRequest) (*api.CreateDepositInterestAmountSlabRateResponse, error) {
	reqShell := (*CreateDepositInterestAmountSlabRateRequestShell)(req)
	resShell := &CreateDepositInterestAmountSlabRateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createDepositInterestAmountSlabRateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateDepositInterestAmountSlabRateResponse)(resShell), err
}

// GetDepositInterestAmountSlabRate fetches a deposit interest amount slab rate by id
func (p *ProductMasterClient) GetDepositInterestAmountSlabRate(ctx context.Context, req *api.GetDepositInterestAmountSlabRateRequest) (*api.GetDepositInterestAmountSlabRateResponse, error) {
	reqShell := (*GetDepositInterestAmountSlabRateRequestShell)(req)
	resShell := &GetDepositInterestAmountSlabRateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getDepositInterestAmountSlabRateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetDepositInterestAmountSlabRateResponse)(resShell), err
}

// CreatePocketTemplate creates a new pocket template
func (p *ProductMasterClient) CreatePocketTemplate(ctx context.Context, req *api.CreatePocketTemplateRequest) (*api.CreatePocketTemplateResponse, error) {
	reqShell := (*CreatePocketTemplateRequestShell)(req)
	resShell := &CreatePocketTemplateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createPocketTemplateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreatePocketTemplateResponse)(resShell), err
}

// ListPocketTemplates fetches list of pocket templates
func (p *ProductMasterClient) ListPocketTemplates(ctx context.Context, req *api.ListPocketTemplatesRequest) (*api.ListPocketTemplatesResponse, error) {
	reqShell := (*ListPocketTemplatesRequestShell)(req)
	resShell := &ListPocketTemplatesResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listPocketTemplatesDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListPocketTemplatesResponse)(resShell), err
}

// GetPocketTemplate fetches pocket template details
func (p *ProductMasterClient) GetPocketTemplate(ctx context.Context, req *api.GetPocketTemplateRequest) (*api.GetPocketTemplateResponse, error) {
	reqShell := (*GetPocketTemplateRequestShell)(req)
	resShell := &GetPocketTemplateResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getPocketTemplateDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetPocketTemplateResponse)(resShell), err
}

// CreatePocketTemplateQuestions creates new pocket template questions
func (p *ProductMasterClient) CreatePocketTemplateQuestions(ctx context.Context, req *api.CreatePocketTemplateQuestionsRequest) (*api.CreatePocketTemplateQuestionsResponse, error) {
	reqShell := (*CreatePocketTemplateQuestionsRequestShell)(req)
	resShell := &CreatePocketTemplateQuestionsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createPocketTemplateQuestionsDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreatePocketTemplateQuestionsResponse)(resShell), err
}

// ListPocketTemplateQuestions fetches list of pocket template questions
func (p *ProductMasterClient) ListPocketTemplateQuestions(ctx context.Context, req *api.ListPocketTemplateQuestionsRequest) (*api.ListPocketTemplateQuestionsResponse, error) {
	reqShell := (*ListPocketTemplateQuestionsRequestShell)(req)
	resShell := &ListPocketTemplateQuestionsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listPocketTemplateQuestionsDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListPocketTemplateQuestionsResponse)(resShell), err
}

// GetInterestParameters fetches all product params whose values are updated
func (p *ProductMasterClient) GetInterestParameters(ctx context.Context, req *api.GetInterestParametersRequest) (*api.GetInterestParametersResponse, error) {
	reqShell := (*GetInterestParametersRequestShell)(req)
	resShell := &GetInterestParametersResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getInterestParametersDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetInterestParametersResponse)(resShell), err
}

// UpdateInterestParameterScheduleStatus keep the status whether parameter is scheduled in TM or not
func (p *ProductMasterClient) UpdateInterestParameterScheduleStatus(ctx context.Context, req *api.UpdateInterestParameterScheduleStatusRequest) (*api.UpdateInterestParameterScheduleStatusResponse, error) {
	reqShell := (*UpdateInterestParameterScheduleStatusRequestShell)(req)
	resShell := &UpdateInterestParameterScheduleStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateInterestParameterScheduleStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateInterestParameterScheduleStatusResponse)(resShell), err
}

// UpdateInterestParameterNotificationStatus keep the status whether noti is sent to the customer
func (p *ProductMasterClient) UpdateInterestParameterNotificationStatus(ctx context.Context, req *api.UpdateInterestParameterNotificationStatusRequest) (*api.UpdateInterestParameterNotificationStatusResponse, error) {
	reqShell := (*UpdateInterestParameterNotificationStatusRequestShell)(req)
	resShell := &UpdateInterestParameterNotificationStatusResponseShell{}
	clientCtx := klient.MakeContext(ctx, &updateInterestParameterNotificationStatusDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.UpdateInterestParameterNotificationStatusResponse)(resShell), err
}

// GetInterestParametersByProductVariant fetches interest parameters by product variant
func (p *ProductMasterClient) GetInterestParametersByProductVariant(ctx context.Context, req *api.GetInterestParametersByProductVariantRequest) (*api.GetInterestParametersByProductVariantResponse, error) {
	reqShell := (*GetInterestParametersByProductVariantRequestShell)(req)
	resShell := &GetInterestParametersByProductVariantResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getInterestParametersByProductVariantDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetInterestParametersByProductVariantResponse)(resShell), err
}

// CreateProductVariantQuestions : creates new questions based on productVariantCode
func (p *ProductMasterClient) CreateProductVariantQuestions(ctx context.Context, req *api.CreateProductVariantQuestionRequest) (*api.CreateProductVariantQuestionResponse, error) {
	reqShell := (*CreateProductVariantQuestionsRequestShell)(req)
	resShell := &CreateProductVariantQuestionsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createProductVariantQuestionsDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateProductVariantQuestionResponse)(resShell), err
}

// ListProductVariantQuestions : list the array of  questions and respective answers based on productVariantCode
func (p *ProductMasterClient) ListProductVariantQuestions(ctx context.Context, req *api.ListProductVariantQuestionsRequest) (*api.ListProductVariantQuestionsResponse, error) {
	reqShell := (*ListProductVariantQuestionsRequestShell)(req)
	resShell := &ListProductVariantQuestionsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listProductVariantQuestionsDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListProductVariantQuestionsResponse)(resShell), err
}

// CreateLoanInterest creates a new loan interest
func (p *ProductMasterClient) CreateLoanInterest(ctx context.Context, req *api.CreateLoanInterestRequest) (*api.CreateLoanInterestResponse, error) {
	reqShell := (*CreateLoanInterestRequestShell)(req)
	resShell := &CreateLoanInterestResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createLoanInterestDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateLoanInterestResponse)(resShell), err
}

// GetLoanInterest fetches a loan interest by id
func (p *ProductMasterClient) GetLoanInterest(ctx context.Context, req *api.GetLoanInterestByIDRequest) (*api.GetLoanInterestByIDResponse, error) {
	reqShell := (*GetLoanInterestRequestShell)(req)
	resShell := &GetLoanInterestResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLoanInterestDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLoanInterestByIDResponse)(resShell), err
}

// GetLoanInterestByCode fetches a loan interest by code
func (p *ProductMasterClient) GetLoanInterestByCode(ctx context.Context, req *api.GetLoanInterestByCodeRequest) (*api.GetLoanInterestByCodeResponse, error) {
	reqShell := (*GetLoanInterestByCodeRequestShell)(req)
	resShell := &GetLoanInterestByCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLoanInterestByCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLoanInterestByCodeResponse)(resShell), err
}

// GetLoanPastDueParametersByProductCode fetches a loan past due parameters by product code
func (p *ProductMasterClient) GetLoanPastDueParametersByProductCode(ctx context.Context, req *api.GetLoanPastDueParametersByProductCodeRequest) (*api.GetLoanPastDueParametersByProductCodeResponse, error) {
	reqShell := (*GetLoanPastDueParametersByProductCodeRequestShell)(req)
	resShell := &GetLoanPastDueParametersByProductCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLoanPastDueParametersByProductCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLoanPastDueParametersByProductCodeResponse)(resShell), err
}

// CreateLoanInstructionVersion creates a new version of loan instructions
func (p *ProductMasterClient) CreateLoanInstructionVersion(ctx context.Context, req *api.CreateLoanInstructionVersionRequest) (*api.CreateLoanInstructionVersionResponse, error) {
	reqShell := (*CreateLoanInstructionVersionRequestShell)(req)
	resShell := &CreateLoanInstructionVersionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createLoanInstructionVersionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateLoanInstructionVersionResponse)(resShell), err
}

// CreateLoanInstructionRequest creates a new loan instruction
func (p *ProductMasterClient) CreateLoanInstruction(ctx context.Context, req *api.CreateLoanInstructionRequest) (*api.CreateLoanInstructionResponse, error) {
	reqShell := (*CreateLoanInstructionRequestShell)(req)
	resShell := &CreateLoanInstructionResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createLoanInstructionDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.CreateLoanInstructionResponse)(resShell), err
}

// GetLoanInstructionsByCode fetches a loan instruction parameters by product variant code and instruction type
func (p *ProductMasterClient) GetLoanInstructionsByCode(ctx context.Context, req *api.GetLoanInstructionsByCodeRequest) (*api.GetLoanInstructionsByCodeResponse, error) {
	reqShell := (*GetLoanInstructionsByCodeRequestShell)(req)
	resShell := &GetLoanInstructionsByCodeResponseShell{}
	clientCtx := klient.MakeContext(ctx, &getLoanInstructionsByCodeDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GetLoanInstructionsByCodeResponse)(resShell), err
}

// ListLoanDocumentOptionsByProductVariant returns list of loan document options for a given product variant code
func (p *ProductMasterClient) ListLoanDocumentOptionsByProductVariant(ctx context.Context, req *api.ListLoanDocumentOptionsByProductVariantRequest) (*api.ListLoanDocumentOptionsByProductVariantResponse, error) {
	reqShell := (*ListLoanDocumentOptionsByProductVariantRequestShell)(req)
	resShell := &ListLoanDocumentOptionsByProductVariantResponseShell{}
	clientCtx := klient.MakeContext(ctx, &listLoanDocumentOptionsByProductVariantDescriptor)
	err := p.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.ListLoanDocumentOptionsByProductVariantResponse)(resShell), err
}

// CreateProductTemplateRequestShell is a wrapper to make the object a klient.Request
type CreateProductTemplateRequestShell api.CreateProductTemplateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductTemplateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-templates"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductTemplateResponseShell is a wrapper to make the object a klient.Request
type CreateProductTemplateResponseShell api.CreateProductTemplateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductTemplateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetProductTemplateRequestShell is a wrapper to make the object a klient.Request
type GetProductTemplateRequestShell api.GetProductTemplateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductTemplateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-templates/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductTemplateResponseShell is a wrapper to make the object a klient.Request
type GetProductTemplateResponseShell api.GetProductTemplateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductTemplateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateProductTemplateStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateProductTemplateStatusRequestShell api.UpdateProductTemplateStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateProductTemplateStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-templates/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateProductTemplateStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateProductTemplateStatusResponseShell api.UpdateProductTemplateStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateProductTemplateStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateProductVariantRequestShell is a wrapper to make the object a klient.Request
type CreateProductVariantRequestShell api.CreateProductVariantRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductVariantRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variants"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductVariantResponseShell is a wrapper to make the object a klient.Request
type CreateProductVariantResponseShell api.CreateProductVariantResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductVariantResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// ListProductVariantsRequestShell is a wrapper to make the object a klient.Request
type ListProductVariantsRequestShell api.ListProductVariantsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListProductVariantsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v2/product-variants"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListProductVariantsResponseShell is a wrapper to make the object a klient.Request
type ListProductVariantsResponseShell api.ListProductVariantsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListProductVariantsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// GetProductVariantRequestShell is a wrapper to make the object a klient.Request
type GetProductVariantRequestShell api.GetProductVariantRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductVariantRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variants/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductVariantResponseShell is a wrapper to make the object a klient.Request
type GetProductVariantResponseShell api.GetProductVariantResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductVariantResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetProductVariantByCodeRequestShell is a wrapper to make the object a klient.Request
type GetProductVariantByCodeRequestShell api.GetProductVariantByCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductVariantByCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variants"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductVariantByCodeResponseShell is a wrapper to make the object a klient.Request
type GetProductVariantByCodeResponseShell api.GetProductVariantByCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductVariantByCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateProductVariantStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateProductVariantStatusRequestShell api.UpdateProductVariantStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateProductVariantStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variants/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateProductVariantStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateProductVariantStatusResponseShell api.UpdateProductVariantStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateProductVariantStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateProductRequestShell is a wrapper to make the object a klient.Request
type CreateProductRequestShell api.CreateProductRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/products"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductResponseShell is a wrapper to make the object a klient.Request
type CreateProductResponseShell api.CreateProductResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetProductRequestShell is a wrapper to make the object a klient.Request
type GetProductRequestShell api.GetProductRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/products/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductResponseShell is a wrapper to make the object a klient.Request
type GetProductResponseShell api.GetProductResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetProductByCodeRequestShell is a wrapper to make the object a klient.Request
type GetProductByCodeRequestShell api.GetProductByCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductByCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/products"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductByCodeResponseShell is a wrapper to make the object a klient.Request
type GetProductByCodeResponseShell api.GetProductByCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductByCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateProductStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateProductStatusRequestShell api.UpdateProductStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateProductStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/products/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateProductStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateProductStatusResponseShell api.UpdateProductStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateProductStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateProductTemplateParameterRequestShell is a wrapper to make the object a klient.Request
type CreateProductTemplateParameterRequestShell api.CreateProductTemplateParameterRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductTemplateParameterRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-template-parameters"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductTemplateParameterResponseShell is a wrapper to make the object a klient.Request
type CreateProductTemplateParameterResponseShell api.CreateProductTemplateParameterResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductTemplateParameterResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetProductTemplateParameterRequestShell is a wrapper to make the object a klient.Request
type GetProductTemplateParameterRequestShell api.GetProductTemplateParameterRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductTemplateParameterRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-template-parameters/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductTemplateParameterResponseShell is a wrapper to make the object a klient.Request
type GetProductTemplateParameterResponseShell api.GetProductTemplateParameterResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductTemplateParameterResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// ListProductTemplateParametersRequestShell is a wrapper to make the object a klient.Request
type ListProductTemplateParametersRequestShell api.ListProductTemplateParametersRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListProductTemplateParametersRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-template-parameters"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListProductTemplateParametersResponseShell is a wrapper to make the object a klient.Request
type ListProductTemplateParametersResponseShell api.ListProductTemplateParametersResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListProductTemplateParametersResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// UpdateProductTemplateParameterValueRequestShell is a wrapper to make the object a klient.Request
type UpdateProductTemplateParameterValueRequestShell api.UpdateProductTemplateParameterValueRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateProductTemplateParameterValueRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-template-parameters/" + fmt.Sprint(u.Id) + "/value"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateProductTemplateParameterValueResponseShell is a wrapper to make the object a klient.Request
type UpdateProductTemplateParameterValueResponseShell api.UpdateProductTemplateParameterValueResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateProductTemplateParameterValueResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateProductVariantParameterRequestShell is a wrapper to make the object a klient.Request
type CreateProductVariantParameterRequestShell api.CreateProductVariantParameterRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductVariantParameterRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-parameters"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductVariantParameterResponseShell is a wrapper to make the object a klient.Request
type CreateProductVariantParameterResponseShell api.CreateProductVariantParameterResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductVariantParameterResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetProductVariantParameterRequestShell is a wrapper to make the object a klient.Request
type GetProductVariantParameterRequestShell api.GetProductVariantParameterRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductVariantParameterRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-parameters/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductVariantParameterResponseShell is a wrapper to make the object a klient.Request
type GetProductVariantParameterResponseShell api.GetProductVariantParameterResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductVariantParameterResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// ListProductVariantParametersRequestShell is a wrapper to make the object a klient.Request
type ListProductVariantParametersRequestShell api.ListProductVariantParametersRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListProductVariantParametersRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-parameters"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListProductVariantParametersResponseShell is a wrapper to make the object a klient.Request
type ListProductVariantParametersResponseShell api.ListProductVariantParametersResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListProductVariantParametersResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// ListEffectiveProductVariantParametersRequestShell is a wrapper to make the object a klient.Request
type ListEffectiveProductVariantParametersRequestShell api.ListEffectiveProductVariantParametersRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListEffectiveProductVariantParametersRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v2/effective-product-variant-parameters/get"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListEffectiveProductVariantParametersResponseShell is a wrapper to make the object a klient.Request
type ListEffectiveProductVariantParametersResponseShell api.ListEffectiveProductVariantParametersResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListEffectiveProductVariantParametersResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// UpdateProductVariantParameterValueRequestShell is a wrapper to make the object a klient.Request
type UpdateProductVariantParameterValueRequestShell api.UpdateProductVariantParameterValueRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateProductVariantParameterValueRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-parameters/" + fmt.Sprint(u.Id) + "/value"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateProductVariantParameterValueResponseShell is a wrapper to make the object a klient.Request
type UpdateProductVariantParameterValueResponseShell api.UpdateProductVariantParameterValueResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateProductVariantParameterValueResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateTransactionCatalogueRequestShell is a wrapper to make the object a klient.Request
type CreateTransactionCatalogueRequestShell api.CreateTransactionCatalogueRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateTransactionCatalogueRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/transaction-catalogue"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateTransactionCatalogueResponseShell is a wrapper to make the object a klient.Request
type CreateTransactionCatalogueResponseShell api.CreateTransactionCatalogueResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateTransactionCatalogueResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetTransactionCatalogueRequestShell is a wrapper to make the object a klient.Request
type GetTransactionCatalogueRequestShell api.GetTransactionCatalogueRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetTransactionCatalogueRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/transaction-catalogue/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetTransactionCatalogueResponseShell is a wrapper to make the object a klient.Request
type GetTransactionCatalogueResponseShell api.GetTransactionCatalogueResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetTransactionCatalogueResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateTransactionCatalogueStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateTransactionCatalogueStatusRequestShell api.UpdateTransactionCatalogueStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateTransactionCatalogueStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/transaction-catalogue/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateTransactionCatalogueStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateTransactionCatalogueStatusResponseShell api.UpdateTransactionCatalogueStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateTransactionCatalogueStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateProductVariantTransactionCatalogueMappingRequestShell is a wrapper to make the object a klient.Request
type CreateProductVariantTransactionCatalogueMappingRequestShell api.CreateProductVariantTransactionCatalogueMappingRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductVariantTransactionCatalogueMappingRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-transaction-catalogue-mapping"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductVariantTransactionCatalogueMappingResponseShell is a wrapper to make the object a klient.Request
type CreateProductVariantTransactionCatalogueMappingResponseShell api.CreateProductVariantTransactionCatalogueMappingResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductVariantTransactionCatalogueMappingResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetProductVariantTransactionCatalogueMappingRequestShell is a wrapper to make the object a klient.Request
type GetProductVariantTransactionCatalogueMappingRequestShell api.GetProductVariantTransactionCatalogueMappingRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductVariantTransactionCatalogueMappingRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-transaction-catalogue-mapping/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductVariantTransactionCatalogueMappingResponseShell is a wrapper to make the object a klient.Request
type GetProductVariantTransactionCatalogueMappingResponseShell api.GetProductVariantTransactionCatalogueMappingResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductVariantTransactionCatalogueMappingResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateProductVariantTransactionCatalogueMappingStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateProductVariantTransactionCatalogueMappingStatusRequestShell api.UpdateProductVariantTransactionCatalogueMappingStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateProductVariantTransactionCatalogueMappingStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-transaction-catalogue-mapping/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateProductVariantTransactionCatalogueMappingStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateProductVariantTransactionCatalogueMappingStatusResponseShell api.UpdateProductVariantTransactionCatalogueMappingStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateProductVariantTransactionCatalogueMappingStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateGeneralLedgerRequestShell is a wrapper to make the object a klient.Request
type CreateGeneralLedgerRequestShell api.CreateGeneralLedgerRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateGeneralLedgerRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/general-ledgers"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateGeneralLedgerResponseShell is a wrapper to make the object a klient.Request
type CreateGeneralLedgerResponseShell api.CreateGeneralLedgerResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateGeneralLedgerResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetGeneralLedgerRequestShell is a wrapper to make the object a klient.Request
type GetGeneralLedgerRequestShell api.GetGeneralLedgerRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetGeneralLedgerRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/general-ledgers/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetGeneralLedgerResponseShell is a wrapper to make the object a klient.Request
type GetGeneralLedgerResponseShell api.GetGeneralLedgerResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetGeneralLedgerResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetGeneralLedgerByCodeRequestShell is a wrapper to make the object a klient.Request
type GetGeneralLedgerByCodeRequestShell api.GetGeneralLedgerByCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetGeneralLedgerByCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/general-ledgers"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetGeneralLedgerByCodeResponseShell is a wrapper to make the object a klient.Request
type GetGeneralLedgerByCodeResponseShell api.GetGeneralLedgerByCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetGeneralLedgerByCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateGeneralLedgerStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateGeneralLedgerStatusRequestShell api.UpdateGeneralLedgerStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateGeneralLedgerStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/general-ledgers/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateGeneralLedgerStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateGeneralLedgerStatusResponseShell api.UpdateGeneralLedgerStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateGeneralLedgerStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateInternalAccountRequestShell is a wrapper to make the object a klient.Request
type CreateInternalAccountRequestShell api.CreateInternalAccountRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateInternalAccountRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal-accounts"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateInternalAccountResponseShell is a wrapper to make the object a klient.Request
type CreateInternalAccountResponseShell api.CreateInternalAccountResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateInternalAccountResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetInternalAccountRequestShell is a wrapper to make the object a klient.Request
type GetInternalAccountRequestShell api.GetInternalAccountRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetInternalAccountRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal-accounts/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetInternalAccountResponseShell is a wrapper to make the object a klient.Request
type GetInternalAccountResponseShell api.GetInternalAccountResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetInternalAccountResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// ListInternalAccountsRequestShell is a wrapper to make the object a klient.Request
type ListInternalAccountsRequestShell api.ListInternalAccountsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListInternalAccountsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal-accounts"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListInternalAccountsResponseShell is a wrapper to make the object a klient.Request
type ListInternalAccountsResponseShell api.ListInternalAccountsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListInternalAccountsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// UpdateInternalAccountStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateInternalAccountStatusRequestShell api.UpdateInternalAccountStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateInternalAccountStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/internal-accounts/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateInternalAccountStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateInternalAccountStatusResponseShell api.UpdateInternalAccountStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateInternalAccountStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateProductVariantTransactionCatalogueInternalAccountMappingRequestShell is a wrapper to make the object a klient.Request
type CreateProductVariantTransactionCatalogueInternalAccountMappingRequestShell api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductVariantTransactionCatalogueInternalAccountMappingRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-transaction-catalogue-internal-account-mapping"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductVariantTransactionCatalogueInternalAccountMappingResponseShell is a wrapper to make the object a klient.Request
type CreateProductVariantTransactionCatalogueInternalAccountMappingResponseShell api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductVariantTransactionCatalogueInternalAccountMappingResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetProductVariantTransactionCatalogueInternalAccountMappingRequestShell is a wrapper to make the object a klient.Request
type GetProductVariantTransactionCatalogueInternalAccountMappingRequestShell api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductVariantTransactionCatalogueInternalAccountMappingRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-transaction-catalogue-internal-account-mapping/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductVariantTransactionCatalogueInternalAccountMappingResponseShell is a wrapper to make the object a klient.Request
type GetProductVariantTransactionCatalogueInternalAccountMappingResponseShell api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductVariantTransactionCatalogueInternalAccountMappingResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequestShell is a wrapper to make the object a klient.Request
type GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequestShell api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-transaction-catalogue-internal-account-mapping"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponseShell is a wrapper to make the object a klient.Request
type GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponseShell api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequestShell api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-transaction-catalogue-internal-account-mapping/" + fmt.Sprint(u.Id) + "/status"
	fullURL := baseURL + filledPath

	pathVar0 := u.Id
	u.Id = ""

	defer func() {
		u.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponseShell api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// CreateBaseInterestRequestShell is a wrapper to make the object a klient.Request
type CreateBaseInterestRequestShell api.CreateBaseInterestRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateBaseInterestRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/base-interest"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateBaseInterestResponseShell is a wrapper to make the object a klient.Request
type CreateBaseInterestResponseShell api.CreateBaseInterestResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateBaseInterestResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetBaseInterestRequestShell is a wrapper to make the object a klient.Request
type GetBaseInterestRequestShell api.GetBaseInterestRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetBaseInterestRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/base-interest/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetBaseInterestResponseShell is a wrapper to make the object a klient.Request
type GetBaseInterestResponseShell api.GetBaseInterestResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetBaseInterestResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetBaseInterestByCodeRequestShell is a wrapper to make the object a klient.Request
type GetBaseInterestByCodeRequestShell api.GetBaseInterestByCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetBaseInterestByCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/base-interest"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetBaseInterestByCodeResponseShell is a wrapper to make the object a klient.Request
type GetBaseInterestByCodeResponseShell api.GetBaseInterestByCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetBaseInterestByCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateBaseInterestVersionRequestShell is a wrapper to make the object a klient.Request
type CreateBaseInterestVersionRequestShell api.CreateBaseInterestVersionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateBaseInterestVersionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/base-interest-version"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateBaseInterestVersionResponseShell is a wrapper to make the object a klient.Request
type CreateBaseInterestVersionResponseShell api.CreateBaseInterestVersionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateBaseInterestVersionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetBaseInterestVersionRequestShell is a wrapper to make the object a klient.Request
type GetBaseInterestVersionRequestShell api.GetBaseInterestVersionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetBaseInterestVersionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/base-interest-version/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetBaseInterestVersionResponseShell is a wrapper to make the object a klient.Request
type GetBaseInterestVersionResponseShell api.GetBaseInterestVersionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetBaseInterestVersionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateBaseInterestTimeSlabRateRequestShell is a wrapper to make the object a klient.Request
type CreateBaseInterestTimeSlabRateRequestShell api.CreateBaseInterestTimeSlabRateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateBaseInterestTimeSlabRateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/base-interest-time-slab-rate"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateBaseInterestTimeSlabRateResponseShell is a wrapper to make the object a klient.Request
type CreateBaseInterestTimeSlabRateResponseShell api.CreateBaseInterestTimeSlabRateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateBaseInterestTimeSlabRateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetBaseInterestTimeSlabRateRequestShell is a wrapper to make the object a klient.Request
type GetBaseInterestTimeSlabRateRequestShell api.GetBaseInterestTimeSlabRateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetBaseInterestTimeSlabRateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/base-interest-time-slab-rate/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetBaseInterestTimeSlabRateResponseShell is a wrapper to make the object a klient.Request
type GetBaseInterestTimeSlabRateResponseShell api.GetBaseInterestTimeSlabRateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetBaseInterestTimeSlabRateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateDepositInterestRequestShell is a wrapper to make the object a klient.Request
type CreateDepositInterestRequestShell api.CreateDepositInterestRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateDepositInterestRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/deposit-interest"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateDepositInterestResponseShell is a wrapper to make the object a klient.Request
type CreateDepositInterestResponseShell api.CreateDepositInterestResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateDepositInterestResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetDepositInterestRequestShell is a wrapper to make the object a klient.Request
type GetDepositInterestRequestShell api.GetDepositInterestRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetDepositInterestRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/deposit-interest/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetDepositInterestResponseShell is a wrapper to make the object a klient.Request
type GetDepositInterestResponseShell api.GetDepositInterestResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetDepositInterestResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetDepositInterestByCodeRequestShell is a wrapper to make the object a klient.Request
type GetDepositInterestByCodeRequestShell api.GetDepositInterestByCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetDepositInterestByCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/deposit-interest"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetDepositInterestByCodeResponseShell is a wrapper to make the object a klient.Request
type GetDepositInterestByCodeResponseShell api.GetDepositInterestByCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetDepositInterestByCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateDepositInterestVersionRequestShell is a wrapper to make the object a klient.Request
type CreateDepositInterestVersionRequestShell api.CreateDepositInterestVersionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateDepositInterestVersionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/deposit-interest-version"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateDepositInterestVersionResponseShell is a wrapper to make the object a klient.Request
type CreateDepositInterestVersionResponseShell api.CreateDepositInterestVersionRequest

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateDepositInterestVersionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetDepositInterestVersionRequestShell is a wrapper to make the object a klient.Request
type GetDepositInterestVersionRequestShell api.GetDepositInterestVersionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetDepositInterestVersionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/deposit-interest-version/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetDepositInterestVersionResponseShell is a wrapper to make the object a klient.Request
type GetDepositInterestVersionResponseShell api.GetDepositInterestVersionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetDepositInterestVersionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateDepositInterestAmountSlabRateRequestShell is a wrapper to make the object a klient.Request
type CreateDepositInterestAmountSlabRateRequestShell api.CreateDepositInterestAmountSlabRateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateDepositInterestAmountSlabRateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/deposit-interest-amount-slab-rate"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateDepositInterestAmountSlabRateResponseShell is a wrapper to make the object a klient.Request
type CreateDepositInterestAmountSlabRateResponseShell api.CreateDepositInterestAmountSlabRateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateDepositInterestAmountSlabRateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetDepositInterestAmountSlabRateRequestShell is a wrapper to make the object a klient.Request
type GetDepositInterestAmountSlabRateRequestShell api.GetDepositInterestAmountSlabRateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetDepositInterestAmountSlabRateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/deposit-interest-amount-slab-rate/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetDepositInterestAmountSlabRateResponseShell is a wrapper to make the object a klient.Request
type GetDepositInterestAmountSlabRateResponseShell api.GetDepositInterestAmountSlabRateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetDepositInterestAmountSlabRateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreatePocketTemplateRequestShell is a wrapper to make the object a klient.Request
type CreatePocketTemplateRequestShell api.CreatePocketTemplateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreatePocketTemplateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-templates"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreatePocketTemplateResponseShell is a wrapper to make the object a klient.Request
type CreatePocketTemplateResponseShell api.CreatePocketTemplateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreatePocketTemplateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// ListPocketTemplatesRequestShell is a wrapper to make the object a klient.Request
type ListPocketTemplatesRequestShell api.ListPocketTemplatesRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListPocketTemplatesRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-templates"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListPocketTemplatesResponseShell is a wrapper to make the object a klient.Request
type ListPocketTemplatesResponseShell api.ListPocketTemplatesResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListPocketTemplatesResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// GetPocketTemplateRequestShell is a wrapper to make the object a klient.Request
type GetPocketTemplateRequestShell api.GetPocketTemplateRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetPocketTemplateRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-templates/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetPocketTemplateResponseShell is a wrapper to make the object a klient.Request
type GetPocketTemplateResponseShell api.GetPocketTemplateResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetPocketTemplateResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreatePocketTemplateQuestionsRequestShell is a wrapper to make the object a klient.Request
type CreatePocketTemplateQuestionsRequestShell api.CreatePocketTemplateQuestionsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreatePocketTemplateQuestionsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-template-questions"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreatePocketTemplateQuestionsResponseShell is a wrapper to make the object a klient.Request
type CreatePocketTemplateQuestionsResponseShell api.CreatePocketTemplateQuestionsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreatePocketTemplateQuestionsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// ListPocketTemplateQuestionsRequestShell is a wrapper to make the object a klient.Request
type ListPocketTemplateQuestionsRequestShell api.ListPocketTemplateQuestionsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListPocketTemplateQuestionsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/pocket-template-questions"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListPocketTemplateQuestionsResponseShell is a wrapper to make the object a klient.Request
type ListPocketTemplateQuestionsResponseShell api.ListPocketTemplateQuestionsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListPocketTemplateQuestionsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// GetInterestParametersRequestShell is a wrapper to make the object a klient.Request
type GetInterestParametersRequestShell api.GetInterestParametersRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetInterestParametersRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/interest-parameters"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetInterestParametersResponseShell is a wrapper to make the object a klient.Request
type GetInterestParametersResponseShell api.GetInterestParametersResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetInterestParametersResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// UpdateInterestParameterScheduleStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateInterestParameterScheduleStatusRequestShell api.UpdateInterestParameterScheduleStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateInterestParameterScheduleStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/interest-parameters/schedule-status"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateInterestParameterScheduleStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateInterestParameterScheduleStatusResponseShell api.UpdateInterestParameterScheduleStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateInterestParameterScheduleStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// UpdateInterestParameterNotificationStatusRequestShell is a wrapper to make the object a klient.Request
type UpdateInterestParameterNotificationStatusRequestShell api.UpdateInterestParameterNotificationStatusRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (u *UpdateInterestParameterNotificationStatusRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/interest-parameters/notification-status"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(u)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("PUT", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// UpdateInterestParameterNotificationStatusResponseShell is a wrapper to make the object a klient.Request
type UpdateInterestParameterNotificationStatusResponseShell api.UpdateInterestParameterNotificationStatusResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (u *UpdateInterestParameterNotificationStatusResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(u)
}

// GetInterestParametersByProductVariantRequestShell is a wrapper to make the object a klient.Request
type GetInterestParametersByProductVariantRequestShell api.GetInterestParametersByProductVariantRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetInterestParametersByProductVariantRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/get-interest-parameters"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetInterestParametersByProductVariantResponseShell is a wrapper to make the object a klient.Request
type GetInterestParametersByProductVariantResponseShell api.GetInterestParametersByProductVariantResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetInterestParametersByProductVariantResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateProductVariantQuestionsRequestShell is a wrapper to make the object a klient.Request
type CreateProductVariantQuestionsRequestShell api.CreateProductVariantQuestionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateProductVariantQuestionsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-question"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateProductVariantQuestionsResponseShell is a wrapper to make the object a klient.Request
type CreateProductVariantQuestionsResponseShell api.CreateProductVariantQuestionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateProductVariantQuestionsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// ListProductVariantQuestionsRequestShell is a wrapper to make the object a klient.Request
type ListProductVariantQuestionsRequestShell api.ListProductVariantQuestionsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListProductVariantQuestionsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/product-variant-question"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListProductVariantQuestionsResponseShell is a wrapper to make the object a klient.Request
type ListProductVariantQuestionsResponseShell api.ListProductVariantQuestionsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListProductVariantQuestionsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

// CreateLoanInterestRequestShell is a wrapper to make the object a klient.Request
type CreateLoanInterestRequestShell api.CreateLoanInterestRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateLoanInterestRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-interest"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateLoanInterestResponseShell is a wrapper to make the object a klient.Request
type CreateLoanInterestResponseShell api.CreateLoanInterestResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateLoanInterestResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetLoanInterestRequestShell is a wrapper to make the object a klient.Request
type GetLoanInterestRequestShell api.GetLoanInterestByIDRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLoanInterestRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-interest/" + fmt.Sprint(g.Id)
	fullURL := baseURL + filledPath

	pathVar0 := g.Id
	g.Id = ""

	defer func() {
		g.Id = pathVar0
	}()

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLoanInterestResponseShell is a wrapper to make the object a klient.Request
type GetLoanInterestResponseShell api.GetLoanInterestByIDResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLoanInterestResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetLoanInterestByCodeRequestShell is a wrapper to make the object a klient.Request
type GetLoanInterestByCodeRequestShell api.GetLoanInterestByCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLoanInterestByCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-interest"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLoanInterestByCodeResponseShell is a wrapper to make the object a klient.Request
type GetLoanInterestByCodeResponseShell api.GetLoanInterestByCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLoanInterestByCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// GetLoanPastDueParametersByProductCodeRequestShell is a wrapper to make the object a klient.Request
type GetLoanPastDueParametersByProductCodeRequestShell api.GetLoanPastDueParametersByProductCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLoanPastDueParametersByProductCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-past-due-parameters"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLoanPastDueParametersByProductCodeResponseShell is a wrapper to make the object a klient.Request
type GetLoanPastDueParametersByProductCodeResponseShell api.GetLoanPastDueParametersByProductCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLoanPastDueParametersByProductCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// CreateLoanInstructionVersionRequestShell is a wrapper to make the object a klient.Request
type CreateLoanInstructionVersionRequestShell api.CreateLoanInstructionVersionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateLoanInstructionVersionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-instruction-versions"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateLoanInstructionVersionResponseShell is a wrapper to make the object a klient.Request
type CreateLoanInstructionVersionResponseShell api.CreateLoanInstructionVersionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateLoanInstructionVersionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// CreateLoanInstructionRequestShell is a wrapper to make the object a klient.Request
type CreateLoanInstructionRequestShell api.CreateLoanInstructionRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateLoanInstructionRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-instructions"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateLoanInstructionResponseShell is a wrapper to make the object a klient.Request
type CreateLoanInstructionResponseShell api.CreateLoanInstructionResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateLoanInstructionResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// GetLoanInstructionsByCodeRequestShell is a wrapper to make the object a klient.Request
type GetLoanInstructionsByCodeRequestShell api.GetLoanInstructionsByCodeRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GetLoanInstructionsByCodeRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-instructions"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GetLoanInstructionsByCodeResponseShell is a wrapper to make the object a klient.Request
type GetLoanInstructionsByCodeResponseShell api.GetLoanInstructionsByCodeResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GetLoanInstructionsByCodeResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 500 {
		return fmt.Errorf("server_generic_%d", res.StatusCode)
	}

	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

// ListLoanDocumentOptionsByProductVariantRequestShell is a wrapper to make the object a klient.Request
type ListLoanDocumentOptionsByProductVariantRequestShell api.ListLoanDocumentOptionsByProductVariantRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (l *ListLoanDocumentOptionsByProductVariantRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/loan-document-submission-options"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(l)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ListLoanDocumentOptionsByProductVariantResponseShell is a wrapper to make the object a klient.Request
type ListLoanDocumentOptionsByProductVariantResponseShell api.ListLoanDocumentOptionsByProductVariantResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (l *ListLoanDocumentOptionsByProductVariantResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(l)
}

var createProductTemplateDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProductTemplate",
	Description: "CreateProductTemplate creates a new Product Template",
	Method:      "POST",
	Path:        "/v1/product-templates",
}

var getProductTemplateDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductTemplate",
	Description: "GetProductTemplate fetches a product template by id",
	Method:      "GET",
	Path:        "/v1/product-templates/{id}",
}

var updateProductTemplateStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateProductTemplateStatus",
	Description: "UpdateProductTemplateStatus updates the status of Product Template",
	Method:      "PUT",
	Path:        "/v1/product-templates/{id}/status",
}

var createProductVariantDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProductVariant",
	Description: "CreateProductVariant creates a new Product Variant",
	Method:      "POST",
	Path:        "/v1/product-variants",
}

var listProductVariantsDescriptor = klient.EndpointDescriptor{
	Name:        "ListProductVariants",
	Description: "ListProductVariants fetches a product variant by id",
	Method:      "GET",
	Path:        "/v2/product-variants",
}

var getProductVariantDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductVariant",
	Description: "GetProductVariant fetches a product variant by id",
	Method:      "GET",
	Path:        "/v1/product-variants/{id}",
}

var getProductVariantByCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductVariantByCode",
	Description: "GetProductVariantByCode fetches a product variant by code",
	Method:      "GET",
	Path:        "/v1/product-variants",
}

var updateProductVariantStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateProductVariantStatus",
	Description: "UpdateProductVariantStatus updates the status of Product Variant",
	Method:      "PUT",
	Path:        "/v1/product-variants/{id}/status",
}

var createProductDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProduct",
	Description: "CreateProduct creates a new Product",
	Method:      "POST",
	Path:        "/v1/products",
}

var getProductDescriptor = klient.EndpointDescriptor{
	Name:        "GetProduct",
	Description: "GetProduct fetches a product by id",
	Method:      "GET",
	Path:        "/v1/products/{id}",
}

var getProductByCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductByCode",
	Description: "GetProduct fetches a product by code",
	Method:      "GET",
	Path:        "/v1/products",
}

var updateProductStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateProductStatus",
	Description: "UpdateProductStatus updates the status of Product Template",
	Method:      "PUT",
	Path:        "/v1/products/{id}/status",
}

var createProductTemplateParameterDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProductTemplateParameter",
	Description: "CreateProductTemplateParameter creates a new Product Template Parameter",
	Method:      "POST",
	Path:        "/v1/product-template-parameters",
}

var getProductTemplateParameterDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductTemplateParameter",
	Description: "GetProductTemplateParameter fetches a product template parameter by id",
	Method:      "GET",
	Path:        "/v1/product-template-parameters/{id}",
}

var listProductTemplateParametersDescriptor = klient.EndpointDescriptor{
	Name:        "ListProductTemplateParameters",
	Description: "ListProductTemplateParameters fetches all product template parameters for a product variant",
	Method:      "GET",
	Path:        "/v1/product-template-parameters",
}

var updateProductTemplateParameterValueDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateProductTemplateParameterValue",
	Description: "UpdateProductTemplateParameterValue updates the value of Product Template Parameter Value",
	Method:      "PUT",
	Path:        "/v1/product-template-parameters/{id}/value",
}

var createProductVariantParameterDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProductVariantParameter",
	Description: "CreateProductVariantParameter creates a new Product Variant Parameter",
	Method:      "POST",
	Path:        "/v1/product-variant-parameters",
}

var getProductVariantParameterDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductVariantParameter",
	Description: "GetProductVariantParameter fetches a product variant parameter by id",
	Method:      "GET",
	Path:        "/v1/product-variant-parameters/{id}",
}

var listProductVariantParametersDescriptor = klient.EndpointDescriptor{
	Name:        "ListProductVariantParameters",
	Description: "ListProductVariantParameters fetches all product variant parameters for a product variant",
	Method:      "GET",
	Path:        "/v1/product-variant-parameters",
}

var listEffectiveProductVariantParametersDescriptor = klient.EndpointDescriptor{
	Name:        "ListEffectiveProductVariantParameters",
	Description: "ListEffectiveProductVariantParameters fetches all product variant parameters for a product variant (includes the overrides from product-template-parameters",
	Method:      "POST",
	Path:        "/v2/effective-product-variant-parameters/get",
}

var updateProductVariantParameterValueDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateProductVariantParameterValue",
	Description: "UpdateProductVariantParameterValue updates the value of Product Variant Parameter Value",
	Method:      "PUT",
	Path:        "/v1/product-variant-parameters/{id}/value",
}

var createTransactionCatalogueDescriptor = klient.EndpointDescriptor{
	Name:        "CreateTransactionCatalogue",
	Description: "CreateTransactionCatalogue creates a new Transaction catalogue",
	Method:      "POST",
	Path:        "/v1/transaction-catalogue",
}

var getTransactionCatalogueDescriptor = klient.EndpointDescriptor{
	Name:        "GetTransactionCatalogue",
	Description: "GetTransactionCatalogue fetches a transaction catalogue by id",
	Method:      "GET",
	Path:        "/v1/transaction-catalogue/{id}",
}

var updateTransactionCatalogueStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateTransactionCatalogueStatus",
	Description: "UpdateTransactionCatalogueStatus updates the status of Transaction catalogue",
	Method:      "PUT",
	Path:        "/v1/transaction-catalogue/{id}/status",
}

var createProductVariantTransactionCatalogueMappingDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProductVariantTransactionCatalogueMapping",
	Description: "CreateProductVariantTransactionCatalogueMapping creates a new Product Variant Transaction catalogue mapping",
	Method:      "POST",
	Path:        "/v1/product-variant-transaction-catalogue-mapping",
}

var getProductVariantTransactionCatalogueMappingDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductVariantTransactionCatalogueMapping",
	Description: "GetProductVariantTransactionCatalogueMapping fetches a Product Variant Transaction catalogue mapping by id",
	Method:      "GET",
	Path:        "/v1/product-variant-transaction-catalogue-mapping/{id}",
}

var updateProductVariantTransactionCatalogueMappingStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateProductVariantTransactionCatalogueMappingStatus",
	Description: "UpdateProductVariantTransactionCatalogueMappingStatus updates the status of Product Variant Transaction catalogue mapping",
	Method:      "PUT",
	Path:        "/v1/product-variant-transaction-catalogue-mapping/{id}/status",
}

var createGeneralLedgerDescriptor = klient.EndpointDescriptor{
	Name:        "CreateGeneralLedger",
	Description: "CreateGeneralLedger creates a new GeneralLedger",
	Method:      "POST",
	Path:        "/v1/general-ledgers",
}

var getGeneralLedgerDescriptor = klient.EndpointDescriptor{
	Name:        "GetGeneralLedger",
	Description: "GetGeneralLedger fetches a general-ledger by id",
	Method:      "GET",
	Path:        "/v1/general-ledgers/{id}",
}

var getGeneralLedgerByCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetGeneralLedgerByCode",
	Description: "GetGeneralLedgerByCode fetches a general-ledger by code",
	Method:      "GET",
	Path:        "/v1/general-ledgers",
}

var updateGeneralLedgerStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateGeneralLedgerStatus",
	Description: "UpdateGeneralLedgerStatus updates the status of general-ledger",
	Method:      "PUT",
	Path:        "/v1/general-ledgers/{id}/status",
}

var createInternalAccountDescriptor = klient.EndpointDescriptor{
	Name:        "CreateInternalAccount",
	Description: "CreateInternalAccount creates a new Internal Account",
	Method:      "POST",
	Path:        "/v1/internal-accounts",
}

var getInternalAccountDescriptor = klient.EndpointDescriptor{
	Name:        "GetInternalAccount",
	Description: "GetInternalAccount fetches a internal account by id",
	Method:      "GET",
	Path:        "/v1/internal-accounts/{id}",
}

var listInternalAccountsDescriptor = klient.EndpointDescriptor{
	Name:        "ListInternalAccounts",
	Description: "ListInternalAccounts fetches a list of internal account by code and generalLedgerID",
	Method:      "GET",
	Path:        "/v1/internal-accounts",
}

var updateInternalAccountStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateInternalAccountStatus",
	Description: "UpdateInternalAccountStatus updates the status of internal account",
	Method:      "PUT",
	Path:        "/v1/internal-accounts/{id}/status",
}

var createProductVariantTransactionCatalogueInternalAccountMappingDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProductVariantTransactionCatalogueInternalAccountMapping",
	Description: "CreateProductVariantTransactionCatalogueInternalAccountMapping creates a new product-variant-transaction-catalogue-internal-account-mapping",
	Method:      "POST",
	Path:        "/v1/product-variant-transaction-catalogue-internal-account-mapping",
}

var getProductVariantTransactionCatalogueInternalAccountMappingDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductVariantTransactionCatalogueInternalAccountMapping",
	Description: "GetProductVariantTransactionCatalogueInternalAccountMapping fetches a product-variant-transaction-catalogue-internal-account-mapping by id",
	Method:      "GET",
	Path:        "/v1/product-variant-transaction-catalogue-internal-account-mapping/{id}",
}

var getProductVariantTransactionCatalogueInternalAccountMappingByKeyDescriptor = klient.EndpointDescriptor{
	Name:        "GetProductVariantTransactionCatalogueInternalAccountMappingByKey",
	Description: "GetProductVariantTransactionCatalogueInternalAccountMappingByKey fetches a product-variant-transaction-catalogue-internal-account-mapping by identifer key",
	Method:      "GET",
	Path:        "/v1/product-variant-transaction-catalogue-internal-account-mapping",
}

var updateProductVariantTransactionCatalogueInternalAccountMappingStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus",
	Description: "UpdateProductVariantTransactionCatalogueInternalAccountMapping updates the status of product-variant-transaction-catalogue-internal-account-mapping",
	Method:      "PUT",
	Path:        "/v1/product-variant-transaction-catalogue-internal-account-mapping/{id}/status",
}

var createBaseInterestDescriptor = klient.EndpointDescriptor{
	Name:        "CreateBaseInterest",
	Description: "CreateBaseInterest creates a new base-interest",
	Method:      "POST",
	Path:        "/v1/base-interest",
}

var getBaseInterestDescriptor = klient.EndpointDescriptor{
	Name:        "GetBaseInterest",
	Description: "GetBaseInterest fetches a base interest by id",
	Method:      "GET",
	Path:        "/v1/base-interest/{id}",
}

var getBaseInterestByCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetBaseInterestByCode",
	Description: "GetBaseInterestByCode fetches a base interest by code",
	Method:      "GET",
	Path:        "/v1/base-interest",
}

var createBaseInterestVersionDescriptor = klient.EndpointDescriptor{
	Name:        "CreateBaseInterestVersion",
	Description: "CreateBaseInterestVersion creates a new base interest version",
	Method:      "POST",
	Path:        "/v1/base-interest-version",
}

var getBaseInterestVersionDescriptor = klient.EndpointDescriptor{
	Name:        "GetBaseInterestVersion",
	Description: "GetBaseInterestVersion fetches a base interest version by id",
	Method:      "GET",
	Path:        "/v1/base-interest-version/{id}",
}

var createBaseInterestTimeSlabRateDescriptor = klient.EndpointDescriptor{
	Name:        "CreateBaseInterestTimeSlabRate",
	Description: "CreateBaseInterestTimeSlabRate creates a new base interest time slab rate",
	Method:      "POST",
	Path:        "/v1/base-interest-time-slab-rate",
}

var getBaseInterestTimeSlabRateDescriptor = klient.EndpointDescriptor{
	Name:        "GetBaseInterestTimeSlabRate",
	Description: "GetBaseInterestTimeSlabRate fetches a base interest time slab rate by id",
	Method:      "GET",
	Path:        "/v1/base-interest-time-slab-rate/{id}",
}

var createDepositInterestDescriptor = klient.EndpointDescriptor{
	Name:        "CreateDepositInterest",
	Description: "CreateDepositInterest creates a new deposit interest",
	Method:      "POST",
	Path:        "/v1/deposit-interest",
}

var getDepositInterestDescriptor = klient.EndpointDescriptor{
	Name:        "GetDepositInterest",
	Description: "GetDepositInterest fetches a deposit interest by id",
	Method:      "GET",
	Path:        "/v1/deposit-interest/{id}",
}

var getDepositInterestByCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetDepositInterestByCode",
	Description: "GetDepositInterestByCode fetches a deposit interest by code",
	Method:      "GET",
	Path:        "/v1/deposit-interest",
}

var createDepositInterestVersionDescriptor = klient.EndpointDescriptor{
	Name:        "CreateDepositInterestVersion",
	Description: "CreateDepositInterestVersion creates a new deposit interest version",
	Method:      "POST",
	Path:        "/v1/deposit-interest-version",
}

var getDepositInterestVersionDescriptor = klient.EndpointDescriptor{
	Name:        "GetDepositInterestVersion",
	Description: "GetDepositInterestVersion fetches a deposit interest version by id",
	Method:      "GET",
	Path:        "/v1/deposit-interest-version/{id}",
}

var createDepositInterestAmountSlabRateDescriptor = klient.EndpointDescriptor{
	Name:        "CreateDepositInterestAmountSlabRate",
	Description: "CreateDepositInterestAmountSlabRate creates a new deposit interest amount slab rate",
	Method:      "POST",
	Path:        "/v1/deposit-interest-amount-slab-rate",
}

var getDepositInterestAmountSlabRateDescriptor = klient.EndpointDescriptor{
	Name:        "GetDepositInterestAmountSlabRate",
	Description: "GetDepositInterestAmountSlabRate fetches a deposit interest amount slab rate by id",
	Method:      "GET",
	Path:        "/v1/deposit-interest-amount-slab-rate/{id}",
}

var createPocketTemplateDescriptor = klient.EndpointDescriptor{
	Name:        "CreatePocketTemplate",
	Description: "CreatePocketTemplate creates a new pocket template",
	Method:      "POST",
	Path:        "/v1/pocket-templates",
}

var listPocketTemplatesDescriptor = klient.EndpointDescriptor{
	Name:        "ListPocketTemplates",
	Description: "ListPocketTemplates fetches list of pocket templates",
	Method:      "GET",
	Path:        "/v1/pocket-templates",
}

var getPocketTemplateDescriptor = klient.EndpointDescriptor{
	Name:        "GetPocketTemplate",
	Description: "GetPocketTemplate fetches pocket template details",
	Method:      "GET",
	Path:        "/v1/pocket-templates/{id}",
}

var createPocketTemplateQuestionsDescriptor = klient.EndpointDescriptor{
	Name:        "CreatePocketTemplateQuestions",
	Description: "CreatePocketTemplateQuestions creates new pocket template questions",
	Method:      "POST",
	Path:        "/v1/pocket-template-questions",
}

var listPocketTemplateQuestionsDescriptor = klient.EndpointDescriptor{
	Name:        "ListPocketTemplateQuestions",
	Description: "ListPocketTemplateQuestions fetches list of pocket template questions",
	Method:      "GET",
	Path:        "/v1/pocket-template-questions",
}

var getInterestParametersDescriptor = klient.EndpointDescriptor{
	Name:        "GetInterestParameters",
	Description: "GetInterestParameters fetches all product params whose values are updated",
	Method:      "GET",
	Path:        "/v1/interest-parameters",
}

var updateInterestParameterScheduleStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateInterestParameterScheduleStatus",
	Description: "UpdateInterestParameterScheduleStatus keep the status whether parameter is scheduled in TM or not",
	Method:      "PUT",
	Path:        "/v1/interest-parameters/schedule-status",
}

var updateInterestParameterNotificationStatusDescriptor = klient.EndpointDescriptor{
	Name:        "UpdateInterestParameterNotificationStatus",
	Description: "UpdateInterestParameterNotificationStatus keep the status whether noti is sent to the customer",
	Method:      "PUT",
	Path:        "/v1/interest-parameters/notification-status",
}

var getInterestParametersByProductVariantDescriptor = klient.EndpointDescriptor{
	Name:        "GetInterestParametersByProductVariant",
	Description: "GetInterestParametersByProductVariant fetches interest parameters by product variant",
	Method:      "POST",
	Path:        "/v1/get-interest-parameters",
}

var createProductVariantQuestionsDescriptor = klient.EndpointDescriptor{
	Name:        "CreateProductVariantQuestions",
	Description: "CreateProductVariantQuestions : creates new questions based on productVariantCode",
	Method:      "POST",
	Path:        "/v1/product-variant-question",
}

var listProductVariantQuestionsDescriptor = klient.EndpointDescriptor{
	Name:        "ListProductVariantQuestions",
	Description: "ListProductVariantQuestions : list the array of  questions and respective answers based on productVariantCode",
	Method:      "GET",
	Path:        "/v1/product-variant-question",
}

var createLoanInterestDescriptor = klient.EndpointDescriptor{
	Name:        "CreateLoanInterest",
	Description: "CreateLoanInterest creates a new loan interest",
	Method:      "POST",
	Path:        "/v1/loan-interest",
}

var getLoanInterestDescriptor = klient.EndpointDescriptor{
	Name:        "GetLoanInterest",
	Description: "GetLoanInterest fetches a loan interest by id",
	Method:      "GET",
	Path:        "/v1/loan-interest/{id}",
}

var getLoanInterestByCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetLoanInterestByCode",
	Description: "GetLoanInterestByCode fetches a loan interest by code",
	Method:      "GET",
	Path:        "/v1/loan-interest",
}

var getLoanPastDueParametersByProductCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetLoanPastDueParametersByProductCode",
	Description: "GetLoanPastDueParametersByProductCode fetches a loan past due parameters by product code",
	Method:      "GET",
	Path:        "/v1/loan-past-due-parameters",
}

var createLoanInstructionVersionDescriptor = klient.EndpointDescriptor{
	Name:        "CreateLoanInstructionVersion",
	Description: "CreateLoanInstructionVersion creates a new version of loan instructions",
	Method:      "POST",
	Path:        "/v1/loan-instruction-versions",
}

var createLoanInstructionDescriptor = klient.EndpointDescriptor{
	Name:        "CreateLoanInstruction",
	Description: "CreateLoanInstructionRequest creates a new loan instruction",
	Method:      "POST",
	Path:        "/v1/loan-instructions",
}

var getLoanInstructionsByCodeDescriptor = klient.EndpointDescriptor{
	Name:        "GetLoanInstructionsByCode",
	Description: "GetLoanInstructionsByCode fetches a loan instruction parameters by product variant code and instruction type",
	Method:      "GET",
	Path:        "/v1/loan-instructions",
}

var listLoanDocumentOptionsByProductVariantDescriptor = klient.EndpointDescriptor{
	Name:        "ListLoanDocumentOptionsByProductVariant",
	Description: "ListLoanDocumentOptionsByProductVariant returns list of loan document options for a given product variant code",
	Method:      "GET",
	Path:        "/v1/loan-document-submission-options",
}

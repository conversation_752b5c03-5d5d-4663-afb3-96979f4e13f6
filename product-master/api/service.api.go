// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package api

import (
	context "context"
	time "time"
)

type EntityStatus string

const (
	EntityStatus_ACTIVE   EntityStatus = "ACTIVE"
	EntityStatus_INACTIVE EntityStatus = "INACTIVE"
)

type ParameterOverrideLevel string

const (
	ParameterOverrideLevel_NO_OVERRIDE        ParameterOverrideLevel = "NO_OVERRIDE"
	ParameterOverrideLevel_AT_PRODUCT_VARIANT ParameterOverrideLevel = "AT_PRODUCT_VARIANT"
	ParameterOverrideLevel_AT_CUSTOMER        ParameterOverrideLevel = "AT_CUSTOMER"
	ParameterOverrideLevel_AT_ACCOUNT         ParameterOverrideLevel = "AT_ACCOUNT"
)

type ParameterDataType string

const (
	ParameterDataType_INT    ParameterDataType = "INT"
	ParameterDataType_FLOAT  ParameterDataType = "FLOAT"
	ParameterDataType_BOOL   ParameterDataType = "BOOL"
	ParameterDataType_STRING ParameterDataType = "STRING"
	ParameterDataType_ARRAY  ParameterDataType = "ARRAY"
	ParameterDataType_JSON   ParameterDataType = "JSON"
)

type Currency string

const (
	Currency_SGD Currency = "SGD"
	Currency_IDR Currency = "IDR"
	Currency_MYR Currency = "MYR"
)

type RoundOffType string

const (
	RoundOffType_FLOOR RoundOffType = "FLOOR"
	RoundOffType_CEIL  RoundOffType = "CEIL"
	RoundOffType_ROUND RoundOffType = "ROUND"
)

type InterestSlabType string

const (
	InterestSlabType_AMOUNT InterestSlabType = "AMOUNT"
	InterestSlabType_TIME   InterestSlabType = "TIME"
	InterestSlabType_BOTH   InterestSlabType = "BOTH"
)

type InterestSlabStructure string

const (
	InterestSlabStructure_ABSOLUTE    InterestSlabStructure = "ABSOLUTE"
	InterestSlabStructure_INCREMENTAL InterestSlabStructure = "INCREMENTAL"
)

type TermUnit string

const (
	TermUnit_DAY   TermUnit = "DAY"
	TermUnit_WEEK  TermUnit = "WEEK"
	TermUnit_MONTH TermUnit = "MONTH"
)

type PocketType string

const (
	PocketType_SAVINGS PocketType = "SAVINGS"
	PocketType_SAVINGS_POCKET PocketType = "SAVINGS_POCKET"
	PocketType_BOOST_POCKET   PocketType = "BOOST_POCKET"
)

type Locale string

const (
	Locale_EN Locale = "EN"
	Locale_MS Locale = "MS"
)

type InterestRateType string

const (
	InterestRateType_flat InterestRateType = "flat"
	InterestRateType_tier InterestRateType = "tier"
)

type TenorType string

const (
	TenorType_SPOT  TenorType = "SPOT"
	TenorType_RANGE TenorType = "RANGE"
)

type ProductVariantCode string

const (
	ProductVariantCode_DEPOSITS_ACCOUNT                        ProductVariantCode = "DEPOSITS_ACCOUNT"
	ProductVariantCode_SAVINGS_POCKET                          ProductVariantCode = "SAVINGS_POCKET"
	ProductVariantCode_DEFAULT_FLEXI_LOAN_TERM_LOAN            ProductVariantCode = "DEFAULT_FLEXI_LOAN_TERM_LOAN"
	ProductVariantCode_BIZ_DEPOSIT_ACCOUNT                     ProductVariantCode = "BIZ_DEPOSIT_ACCOUNT"
	ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT       ProductVariantCode = "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
	ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT ProductVariantCode = "DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT"
	ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN      ProductVariantCode = "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN"
	ProductVariantCode_BOOST_POCKET                            ProductVariantCode = "BOOST_POCKET"
)

type ProductCode string

const (
	ProductCode_FLEXI_LOAN_TERM_LOAN       ProductCode = "FLEXI_LOAN_TERM_LOAN"
	ProductCode_BIZ_FLEXI_CREDIT_TERM_LOAN ProductCode = "BIZ_FLEXI_CREDIT_TERM_LOAN"
)

type InterestType string

const (
	InterestType_NORMAL InterestType = "NORMAL"
	InterestType_PENAL  InterestType = "PENAL"
)

type Status string

const (
	Status_N Status = "N"
	Status_Y Status = "Y"
)

type ProductTemplate struct {
	Id          string       `json:"id,omitempty"`
	Code        string       `json:"code,omitempty"`
	Name        string       `json:"name,omitempty"`
	Description string       `json:"description,omitempty"`
	Status      EntityStatus `json:"status,omitempty"`
	CreatedBy   string       `json:"createdBy,omitempty"`
	CreatedAt   time.Time    `json:"createdAt,omitempty"`
	UpdatedBy   string       `json:"updatedBy,omitempty"`
	UpdatedAt   time.Time    `json:"updatedAt,omitempty"`
}

type ProductVariant struct {
	Id          string       `json:"id,omitempty"`
	ProductID   string       `json:"productID,omitempty"`
	Code        string       `json:"code,omitempty"`
	Version     string       `json:"version,omitempty"`
	Name        string       `json:"name,omitempty"`
	Description string       `json:"description,omitempty"`
	Status      EntityStatus `json:"status,omitempty"`
	ValidFrom   time.Time    `json:"validFrom,omitempty"`
	ValidTo     time.Time    `json:"validTo,omitempty"`
	CreatedBy   string       `json:"createdBy,omitempty"`
	CreatedAt   time.Time    `json:"createdAt,omitempty"`
	UpdatedBy   string       `json:"updatedBy,omitempty"`
	UpdatedAt   time.Time    `json:"updatedAt,omitempty"`
}

type Product struct {
	Id                string       `json:"id,omitempty"`
	ProductTemplateID string       `json:"productTemplateID,omitempty"`
	Code              string       `json:"code,omitempty"`
	Name              string       `json:"name,omitempty"`
	Description       string       `json:"description,omitempty"`
	Status            EntityStatus `json:"status,omitempty"`
	CreatedBy         string       `json:"createdBy,omitempty"`
	CreatedAt         time.Time    `json:"createdAt,omitempty"`
	UpdatedBy         string       `json:"updatedBy,omitempty"`
	UpdatedAt         time.Time    `json:"updatedAt,omitempty"`
}

type ProductTemplateParameter struct {
	Id                string                 `json:"id,omitempty"`
	ProductTemplateID string                 `json:"productTemplateID,omitempty"`
	Namespace         string                 `json:"namespace,omitempty"`
	ParameterKey      string                 `json:"parameterKey,omitempty"`
	ParameterValue    string                 `json:"parameterValue,omitempty"`
	DataType          ParameterDataType      `json:"dataType,omitempty"`
	OverrideLevel     ParameterOverrideLevel `json:"overrideLevel,omitempty"`
	ExceptionLevel    string                 `json:"exceptionLevel,omitempty"`
	Description       string                 `json:"description,omitempty"`
	CreatedBy         string                 `json:"createdBy,omitempty"`
	CreatedAt         time.Time              `json:"createdAt,omitempty"`
	UpdatedBy         string                 `json:"updatedBy,omitempty"`
	UpdatedAt         time.Time              `json:"updatedAt,omitempty"`
}

type ProductVariantParameter struct {
	Id               string                 `json:"id,omitempty"`
	ProductVariantID string                 `json:"productVariantID,omitempty"`
	Namespace        string                 `json:"namespace,omitempty"`
	ParameterKey     string                 `json:"parameterKey,omitempty"`
	ParameterValue   string                 `json:"parameterValue,omitempty"`
	DataType         ParameterDataType      `json:"dataType,omitempty"`
	OverrideLevel    ParameterOverrideLevel `json:"overrideLevel,omitempty"`
	ExceptionLevel   string                 `json:"exceptionLevel,omitempty"`
	Description      string                 `json:"description,omitempty"`
	CreatedBy        string                 `json:"createdBy,omitempty"`
	CreatedAt        time.Time              `json:"createdAt,omitempty"`
	UpdatedBy        string                 `json:"updatedBy,omitempty"`
	UpdatedAt        time.Time              `json:"updatedAt,omitempty"`
}

type InterestRateParameter struct {
	Namespace      string            `json:"namespace,omitempty"`
	ParameterKey   string            `json:"parameterKey,omitempty"`
	ParameterValue string            `json:"parameterValue,omitempty"`
	DataType       ParameterDataType `json:"dataType,omitempty"`
	Description    string            `json:"description,omitempty"`
}

type TransactionCatalogue struct {
	Id             string       `json:"id,omitempty"`
	Domain         string       `json:"domain,omitempty"`
	IsFinancialTxn bool         `json:"isFinancialTxn,omitempty"`
	TxnType        string       `json:"txnType,omitempty"`
	TxnSubType     string       `json:"txnSubType,omitempty"`
	DisplayName    string       `json:"displayName,omitempty"`
	Description    string       `json:"description,omitempty"`
	Status         EntityStatus `json:"status,omitempty"`
	CreatedBy      string       `json:"createdBy,omitempty"`
	CreatedAt      time.Time    `json:"createdAt,omitempty"`
	UpdatedBy      string       `json:"updatedBy,omitempty"`
	UpdatedAt      time.Time    `json:"updatedAt,omitempty"`
}

type ProductVariantTransactionCatalogueMapping struct {
	Id                     string       `json:"id,omitempty"`
	ProductVariantID       string       `json:"productVariantID,omitempty"`
	TransactionCatalogueID string       `json:"transactionCatalogueID,omitempty"`
	Status                 EntityStatus `json:"status,omitempty"`
	CreatedBy              string       `json:"createdBy,omitempty"`
	CreatedAt              time.Time    `json:"createdAt,omitempty"`
	UpdatedBy              string       `json:"updatedBy,omitempty"`
	UpdatedAt              time.Time    `json:"updatedAt,omitempty"`
}

type GeneralLedger struct {
	Id          string       `json:"id,omitempty"`
	Code        string       `json:"code,omitempty"`
	Name        string       `json:"name,omitempty"`
	Description string       `json:"description,omitempty"`
	Currency    Currency     `json:"currency,omitempty"`
	Status      EntityStatus `json:"status,omitempty"`
	CreatedBy   string       `json:"createdBy,omitempty"`
	CreatedAt   time.Time    `json:"createdAt,omitempty"`
	UpdatedBy   string       `json:"updatedBy,omitempty"`
	UpdatedAt   time.Time    `json:"updatedAt,omitempty"`
}

type InternalAccount struct {
	Id              string       `json:"id,omitempty"`
	GeneralLedgerID string       `json:"generalLedgerID,omitempty"`
	Code            string       `json:"code,omitempty"`
	Name            string       `json:"name,omitempty"`
	Description     string       `json:"description,omitempty"`
	Currency        Currency     `json:"currency,omitempty"`
	Status          EntityStatus `json:"status,omitempty"`
	CreatedBy       string       `json:"createdBy,omitempty"`
	CreatedAt       time.Time    `json:"createdAt,omitempty"`
	UpdatedBy       string       `json:"updatedBy,omitempty"`
	UpdatedAt       time.Time    `json:"updatedAt,omitempty"`
}

type ProductVariantTransactionCatalogueInternalAccountMapping struct {
	Id                                          string       `json:"id,omitempty"`
	InternalAccountID                           string       `json:"internalAccountID,omitempty"`
	ProductVariantTransactionCatalogueMappingID string       `json:"productVariantTransactionCatalogueMappingID,omitempty"`
	IdentifierKey                               string       `json:"identifierKey,omitempty"`
	Status                                      EntityStatus `json:"status,omitempty"`
	CreatedBy                                   string       `json:"createdBy,omitempty"`
	CreatedAt                                   time.Time    `json:"createdAt,omitempty"`
	UpdatedBy                                   string       `json:"updatedBy,omitempty"`
	UpdatedAt                                   time.Time    `json:"updatedAt,omitempty"`
}

type BaseInterest struct {
	Id          string    `json:"id,omitempty"`
	Code        string    `json:"code,omitempty"`
	Name        string    `json:"name,omitempty"`
	Description string    `json:"description,omitempty"`
	Currency    Currency  `json:"currency,omitempty"`
	CreatedBy   string    `json:"createdBy,omitempty"`
	CreatedAt   time.Time `json:"createdAt,omitempty"`
	UpdatedBy   string    `json:"updatedBy,omitempty"`
	UpdatedAt   time.Time `json:"updatedAt,omitempty"`
}

type BaseInterestVersion struct {
	Id             string    `json:"id,omitempty"`
	BaseInterestID string    `json:"baseInterestID,omitempty"`
	Version        string    `json:"version,omitempty"`
	EffectiveDate  time.Time `json:"effectiveDate,omitempty"`
	Description    string    `json:"description,omitempty"`
	CreatedBy      string    `json:"createdBy,omitempty"`
	CreatedAt      time.Time `json:"createdAt,omitempty"`
	UpdatedBy      string    `json:"updatedBy,omitempty"`
	UpdatedAt      time.Time `json:"updatedAt,omitempty"`
}

type BaseInterestTimeSlabRate struct {
	Id                     string    `json:"id,omitempty"`
	BaseInterestVersionID  string    `json:"baseInterestVersionID,omitempty"`
	TermUnit               TermUnit  `json:"termUnit,omitempty"`
	TermValue              int32     `json:"termValue,omitempty"`
	BaseInterestPercentage string    `json:"baseInterestPercentage,omitempty"`
	CreatedBy              string    `json:"createdBy,omitempty"`
	CreatedAt              time.Time `json:"createdAt,omitempty"`
	UpdatedBy              string    `json:"updatedBy,omitempty"`
	UpdatedAt              time.Time `json:"updatedAt,omitempty"`
}

type DepositInterest struct {
	Id                    string                `json:"id,omitempty"`
	ProductVariantID      string                `json:"productVariantID,omitempty"`
	IsLinkedToBaseRate    bool                  `json:"isLinkedToBaseRate,omitempty"`
	BaseInterestID        string                `json:"baseInterestID,omitempty"`
	Code                  string                `json:"code,omitempty"`
	Name                  string                `json:"name,omitempty"`
	Description           string                `json:"description,omitempty"`
	Currency              Currency              `json:"currency,omitempty"`
	RoundOffType          RoundOffType          `json:"roundOffType,omitempty"`
	InterestSlabType      InterestSlabType      `json:"interestSlabType,omitempty"`
	InterestSlabStructure InterestSlabStructure `json:"interestSlabStructure,omitempty"`
	CreatedBy             string                `json:"createdBy,omitempty"`
	CreatedAt             time.Time             `json:"createdAt,omitempty"`
	UpdatedBy             string                `json:"updatedBy,omitempty"`
	UpdatedAt             time.Time             `json:"updatedAt,omitempty"`
}

type DepositInterestVersion struct {
	Id                string    `json:"id,omitempty"`
	DepositInterestID string    `json:"depositInterestID,omitempty"`
	Version           string    `json:"version,omitempty"`
	EffectiveDate     time.Time `json:"effectiveDate,omitempty"`
	Description       string    `json:"description,omitempty"`
	CreatedBy         string    `json:"createdBy,omitempty"`
	CreatedAt         time.Time `json:"createdAt,omitempty"`
	UpdatedBy         string    `json:"updatedBy,omitempty"`
	UpdatedAt         time.Time `json:"updatedAt,omitempty"`
}

type DepositInterestAmountSlabRate struct {
	Id                               string    `json:"id,omitempty"`
	DepositInterestVersionID         string    `json:"depositInterestVersionID,omitempty"`
	FromAmount                       string    `json:"fromAmount,omitempty"`
	ToAmount                         string    `json:"toAmount,omitempty"`
	BaseRateInterestSpreadPercentage string    `json:"baseRateInterestSpreadPercentage,omitempty"`
	AbsoluteInterestRatePercentage   string    `json:"absoluteInterestRatePercentage,omitempty"`
	CreatedBy                        string    `json:"createdBy,omitempty"`
	CreatedAt                        time.Time `json:"createdAt,omitempty"`
	UpdatedBy                        string    `json:"updatedBy,omitempty"`
	UpdatedAt                        time.Time `json:"updatedAt,omitempty"`
}

type DepositsInterestAmountSlabRate struct {
	// Min is deprecated. Will be removed in v2. Use MinAmount property instead.
	Min          string `json:"min,omitempty"`
	// Max is deprecated. Will be removed in v2. Use MaxAmount property instead.
	Max          string `json:"max,omitempty"`
	Rate         string `json:"rate,omitempty"`
	MinTenor     string `json:"minTenor,omitempty"`
	MaxTenor     string `json:"maxTenor,omitempty"`
	MinTenorUnit string `json:"minTenorUnit,omitempty"`
	MaxTenorUnit string `json:"maxTenorUnit,omitempty"`
	MinAmount    string `json:"minAmount,omitempty"`
	MaxAmount    string `json:"maxAmount,omitempty"`
}

type DepositsAccountInterestParameters struct {
	InterestRateType InterestRateType             `json:"interestRateType,omitempty"`
	FlatInterest     *DepositsAccountFlatInterest `json:"flatInterest,omitempty"`
}

type DepositsAccountFlatInterest struct {
	Rate       int32 `json:"rate,omitempty"`
	Multiplier int32 `json:"multiplier,omitempty"`
}

type SavingsPocketInterestParameters struct {
	InterestRateType InterestRateType           `json:"interestRateType,omitempty"`
	FlatInterest     *SavingsPocketFlatInterest `json:"flatInterest,omitempty"`
}

type SavingsPocketFlatInterest struct {
	Rate       int32 `json:"rate,omitempty"`
	Multiplier int32 `json:"multiplier,omitempty"`
}

type PocketTemplate struct {
	ID           string     `json:"ID,omitempty"`
	Type         PocketType `json:"type,omitempty"`
	Name         string     `json:"name,omitempty"`
	Images       []Image    `json:"images,omitempty"`
	DefaultImage *Image     `json:"defaultImage,omitempty"`
	CreatedBy    string     `json:"createdBy,omitempty"`
	CreatedAt    time.Time  `json:"createdAt,omitempty"`
	UpdatedBy    string     `json:"updatedBy,omitempty"`
	UpdatedAt    time.Time  `json:"updatedAt,omitempty"`
}

type PocketTemplateQuestionsDetail struct {
	PocketTemplateID    string                        `json:"pocketTemplateID,omitempty"`
	Locale              Locale                        `json:"locale,omitempty"`
	QuestionAnswerPairs []QuestionAnswerPairsResponse `json:"questionAnswerPairs,omitempty"`
	Images              []Image                       `json:"images,omitempty"`
	DefaultImage        *Image                        `json:"defaultImage,omitempty"`
	CreatedBy           string                        `json:"createdBy,omitempty"`
	CreatedAt           time.Time                     `json:"createdAt,omitempty"`
	UpdatedBy           string                        `json:"updatedBy,omitempty"`
	UpdatedAt           time.Time                     `json:"updatedAt,omitempty"`
}

type QuestionAnswerPairsRequest struct {
	QuestionText      string   `json:"questionText,omitempty"`
	AnswerSuggestions []string `json:"answerSuggestions,omitempty"`
}

type QuestionAnswerPairsResponse struct {
	ID                string   `json:"ID,omitempty"`
	QuestionText      string   `json:"questionText,omitempty"`
	AnswerSuggestions []string `json:"answerSuggestions,omitempty"`
}

type Image struct {
	ID  string `json:"ID,omitempty"`
	URL string `json:"URL,omitempty"`
}

// Represents an amount of money with its currency type.
type Money struct {
	CurrencyCode string `json:"currencyCode"`
	Val          int64  `json:"val"`
}

type CreateProductTemplateRequest struct {
	Code        string `json:"code,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	CreatedBy   string `json:"createdBy,omitempty"`
}

type CreateProductTemplateResponse struct {
	ProductTemplate *ProductTemplate `json:"productTemplate,omitempty"`
}

type GetProductTemplateRequest struct {
	Id string `json:"id,omitempty"`
}

type GetProductTemplateResponse struct {
	ProductTemplate *ProductTemplate `json:"productTemplate,omitempty"`
}

type UpdateProductTemplateStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateProductTemplateStatusResponse struct {
	ProductTemplate *ProductTemplate `json:"productTemplate,omitempty"`
}

type CreateProductVariantRequest struct {
	Code        string    `json:"code,omitempty"`
	Version     string    `json:"version,omitempty"`
	Name        string    `json:"name,omitempty"`
	ProductID   string    `json:"productID,omitempty"`
	Description string    `json:"description,omitempty"`
	ValidFrom   time.Time `json:"validFrom,omitempty"`
	ValidTo     time.Time `json:"validTo,omitempty"`
	CreatedBy   string    `json:"createdBy,omitempty"`
}

type CreateProductVariantResponse struct {
	ProductVariant *ProductVariant `json:"productVariant,omitempty"`
}

type ListProductVariantsRequest struct {
	ProductTemplateCode string `json:"productTemplateCode,omitempty"`
	ProductCode         string `json:"productCode,omitempty"`
	Code                string `json:"code,omitempty"`
}

type ListProductVariantsResponse struct {
	ProductVariants []ProductVariant `json:"productVariants,omitempty"`
}

type GetProductVariantRequest struct {
	Id string `json:"id,omitempty"`
}

type GetProductVariantResponse struct {
	ProductVariant *ProductVariant `json:"productVariant,omitempty"`
}

type GetProductVariantByCodeRequest struct {
	Code string `json:"code,omitempty"`
}

type GetProductVariantByCodeResponse struct {
	ProductVariant *ProductVariant `json:"productVariant,omitempty"`
}

type UpdateProductVariantStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateProductVariantStatusResponse struct {
	ProductVariant *ProductVariant `json:"productVariant,omitempty"`
}

type CreateProductRequest struct {
	ProductTemplateID string `json:"productTemplateID,omitempty"`
	Code              string `json:"code,omitempty"`
	Name              string `json:"name,omitempty"`
	Description       string `json:"description,omitempty"`
	CreatedBy         string `json:"createdBy,omitempty"`
}

type CreateProductResponse struct {
	Product *Product `json:"product,omitempty"`
}

type GetProductRequest struct {
	Id string `json:"id,omitempty"`
}

type GetProductResponse struct {
	Product *Product `json:"product,omitempty"`
}

type GetProductByCodeRequest struct {
	Code string `json:"code,omitempty"`
}

type GetProductByCodeResponse struct {
	Product *Product `json:"product,omitempty"`
}

type UpdateProductStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateProductStatusResponse struct {
	Product *Product `json:"product,omitempty"`
}

type CreateProductTemplateParameterRequest struct {
	ProductTemplateID string                 `json:"productTemplateID,omitempty"`
	Namespace         string                 `json:"namespace,omitempty"`
	ParameterKey      string                 `json:"parameterKey,omitempty"`
	ParameterValue    string                 `json:"parameterValue,omitempty"`
	DataType          ParameterDataType      `json:"dataType,omitempty"`
	OverrideLevel     ParameterOverrideLevel `json:"overrideLevel,omitempty"`
	ExceptionLevel    string                 `json:"exceptionLevel,omitempty"`
	Description       string                 `json:"description,omitempty"`
	CreatedBy         string                 `json:"createdBy,omitempty"`
}

type CreateProductTemplateParameterResponse struct {
	ProductTemplateParameter *ProductTemplateParameter `json:"productTemplateParameter,omitempty"`
}

type GetProductTemplateParameterRequest struct {
	Id string `json:"id,omitempty"`
}

type GetProductTemplateParameterResponse struct {
	ProductTemplateParameter *ProductTemplateParameter `json:"productTemplateParameter,omitempty"`
}

type ListProductTemplateParametersRequest struct {
	ProductTemplateID string `json:"productTemplateID,omitempty"`
}

type ListProductTemplateParametersResponse struct {
	ProductTemplateParameters []ProductTemplateParameter `json:"productTemplateParameters,omitempty"`
}

type UpdateProductTemplateParameterValueRequest struct {
	Id             string `json:"id,omitempty"`
	ParameterValue string `json:"parameterValue,omitempty"`
	UpdatedBy      string `json:"updatedBy,omitempty"`
}

type UpdateProductTemplateParameterValueResponse struct {
	ProductTemplateParameter *ProductTemplateParameter `json:"productTemplateParameter,omitempty"`
}

type CreateProductVariantParameterRequest struct {
	ProductVariantID string                 `json:"productVariantID,omitempty"`
	Namespace        string                 `json:"namespace,omitempty"`
	ParameterKey     string                 `json:"parameterKey,omitempty"`
	ParameterValue   string                 `json:"parameterValue,omitempty"`
	DataType         ParameterDataType      `json:"dataType,omitempty"`
	OverrideLevel    ParameterOverrideLevel `json:"overrideLevel,omitempty"`
	ExceptionLevel   string                 `json:"exceptionLevel,omitempty"`
	Description      string                 `json:"description,omitempty"`
	CreatedBy        string                 `json:"createdBy,omitempty"`
}

type CreateProductVariantParameterResponse struct {
	ProductVariantParameter *ProductVariantParameter `json:"productVariantParameter,omitempty"`
}

type GetProductVariantParameterRequest struct {
	Id string `json:"id,omitempty"`
}

type GetProductVariantParameterResponse struct {
	ProductVariantParameter *ProductVariantParameter `json:"productVariantParameter,omitempty"`
}

type ListProductVariantParametersRequest struct {
	ProductVariantID string `json:"productVariantID,omitempty"`
}

type ListProductVariantParametersResponse struct {
	ProductVariantParameters []ProductVariantParameter `json:"productVariantParameters,omitempty"`
}

type ListEffectiveProductVariantParametersRequest struct {
	ProductVariantCode              string `json:"productVariantCode,omitempty"`
	IncludeInterestRateParameters   bool   `json:"includeInterestRateParameters,omitempty"`
	IncludeAdditionalLoanParameters bool   `json:"includeAdditionalLoanParameters,omitempty"`
}

type ListEffectiveProductVariantParametersResponse struct {
	ProductVariantParameters []ProductVariantParameter `json:"productVariantParameters,omitempty"`
	InterestRateParameters   []InterestRateParameter   `json:"interestRateParameters,omitempty"`
	LoanParameters           *LoanParametersDetail     `json:"loanParameters,omitempty"`
}

type LoanParametersDetail struct {
	LoanAllowedAmountTenorSlab []LoanAllowedAmountTenorSlab `json:"loanAllowedAmountTenorSlab,omitempty"`
}

type LoanAllowedAmountTenorSlab struct {
	FromAmount string `json:"fromAmount,omitempty"`
	ToAmount   string `json:"toAmount,omitempty"`
	TenorUnit  string `json:"tenorUnit,omitempty"`
	MinTenor   string `json:"minTenor,omitempty"`
	MaxTenor   string `json:"maxTenor,omitempty"`
	Currency   string `json:"currency,omitempty"`
}

type UpdateProductVariantParameterValueRequest struct {
	Id             string `json:"id,omitempty"`
	ParameterValue string `json:"parameterValue,omitempty"`
	UpdatedBy      string `json:"updatedBy,omitempty"`
}

type UpdateProductVariantParameterValueResponse struct {
	ProductVariantParameter *ProductVariantParameter `json:"productVariantParameter,omitempty"`
}

type CreateTransactionCatalogueRequest struct {
	Domain         string `json:"domain,omitempty"`
	IsFinancialTxn bool   `json:"isFinancialTxn,omitempty"`
	TxnType        string `json:"txnType,omitempty"`
	TxnSubType     string `json:"txnSubType,omitempty"`
	DisplayName    string `json:"displayName,omitempty"`
	Description    string `json:"description,omitempty"`
	CreatedBy      string `json:"createdBy,omitempty"`
}

type CreateTransactionCatalogueResponse struct {
	TransactionCatalogue *TransactionCatalogue `json:"transactionCatalogue,omitempty"`
}

type GetTransactionCatalogueRequest struct {
	Id string `json:"id,omitempty"`
}

type GetTransactionCatalogueResponse struct {
	TransactionCatalogue *TransactionCatalogue `json:"transactionCatalogue,omitempty"`
}

type UpdateTransactionCatalogueStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateTransactionCatalogueStatusResponse struct {
	TransactionCatalogue *TransactionCatalogue `json:"transactionCatalogue,omitempty"`
}

type CreateProductVariantTransactionCatalogueMappingRequest struct {
	ProductVariantID       string `json:"productVariantID,omitempty"`
	TransactionCatalogueID string `json:"transactionCatalogueID,omitempty"`
	CreatedBy              string `json:"createdBy,omitempty"`
}

type CreateProductVariantTransactionCatalogueMappingResponse struct {
	ProductVariantTransactionCatalogueMapping *ProductVariantTransactionCatalogueMapping `json:"productVariantTransactionCatalogueMapping,omitempty"`
}

type GetProductVariantTransactionCatalogueMappingRequest struct {
	Id string `json:"id,omitempty"`
}

type GetProductVariantTransactionCatalogueMappingResponse struct {
	ProductVariantTransactionCatalogueMapping *ProductVariantTransactionCatalogueMapping `json:"productVariantTransactionCatalogueMapping,omitempty"`
}

type UpdateProductVariantTransactionCatalogueMappingStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateProductVariantTransactionCatalogueMappingStatusResponse struct {
	ProductVariantTransactionCatalogueMapping *ProductVariantTransactionCatalogueMapping `json:"productVariantTransactionCatalogueMapping,omitempty"`
}

type CreateGeneralLedgerRequest struct {
	Code        string   `json:"code,omitempty"`
	Name        string   `json:"name,omitempty"`
	Description string   `json:"description,omitempty"`
	Currency    Currency `json:"currency,omitempty"`
	CreatedBy   string   `json:"createdBy,omitempty"`
}

type CreateGeneralLedgerResponse struct {
	GeneralLedger *GeneralLedger `json:"generalLedger,omitempty"`
}

type GetGeneralLedgerRequest struct {
	Id string `json:"id,omitempty"`
}

type GetGeneralLedgerResponse struct {
	GeneralLedger *GeneralLedger `json:"generalLedger,omitempty"`
}

type GetGeneralLedgerByCodeRequest struct {
	Code string `json:"code,omitempty"`
}

type GetGeneralLedgerByCodeResponse struct {
	GeneralLedger *GeneralLedger `json:"generalLedger,omitempty"`
}

type UpdateGeneralLedgerStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateGeneralLedgerStatusResponse struct {
	GeneralLedger *GeneralLedger `json:"generalLedger,omitempty"`
}

type CreateInternalAccountRequest struct {
	GeneralLedgerID string   `json:"generalLedgerID,omitempty"`
	Code            string   `json:"code,omitempty"`
	Name            string   `json:"name,omitempty"`
	Description     string   `json:"description,omitempty"`
	Currency        Currency `json:"currency,omitempty"`
	CreatedBy       string   `json:"createdBy,omitempty"`
}

type CreateInternalAccountResponse struct {
	InternalAccount *InternalAccount `json:"internalAccount,omitempty"`
}

type GetInternalAccountRequest struct {
	Id string `json:"id,omitempty"`
}

type GetInternalAccountResponse struct {
	InternalAccount *InternalAccount `json:"internalAccount,omitempty"`
}

type ListInternalAccountsRequest struct {
	Code            string `json:"code,omitempty"`
	GeneralLedgerID string `json:"generalLedgerID,omitempty"`
}

type ListInternalAccountsResponse struct {
	InternalAccounts []InternalAccount `json:"internalAccounts,omitempty"`
}

type UpdateInternalAccountStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateInternalAccountStatusResponse struct {
	InternalAccount *InternalAccount `json:"internalAccount,omitempty"`
}

type CreateProductVariantTransactionCatalogueInternalAccountMappingRequest struct {
	InternalAccountID                           string `json:"internalAccountID,omitempty"`
	ProductVariantTransactionCatalogueMappingID string `json:"productVariantTransactionCatalogueMappingID,omitempty"`
	IdentifierKey                               string `json:"identifierKey,omitempty"`
	CreatedBy                                   string `json:"createdBy,omitempty"`
}

type CreateProductVariantTransactionCatalogueInternalAccountMappingResponse struct {
	ProductVariantTransactionCatalogueInternalAccountMapping *ProductVariantTransactionCatalogueInternalAccountMapping `json:"productVariantTransactionCatalogueInternalAccountMapping,omitempty"`
}

type GetProductVariantTransactionCatalogueInternalAccountMappingRequest struct {
	Id string `json:"id,omitempty"`
}

type GetProductVariantTransactionCatalogueInternalAccountMappingResponse struct {
	ProductVariantTransactionCatalogueInternalAccountMapping *ProductVariantTransactionCatalogueInternalAccountMapping `json:"productVariantTransactionCatalogueInternalAccountMapping,omitempty"`
}

type GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest struct {
	IdentifierKey string `json:"identifierKey,omitempty"`
}

type GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse struct {
	ProductVariantTransactionCatalogueInternalAccountMapping *ProductVariantTransactionCatalogueInternalAccountMapping `json:"productVariantTransactionCatalogueInternalAccountMapping,omitempty"`
}

type UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest struct {
	Id        string       `json:"id,omitempty"`
	Status    EntityStatus `json:"status,omitempty"`
	UpdatedBy string       `json:"updatedBy,omitempty"`
}

type UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse struct {
	ProductVariantTransactionCatalogueInternalAccountMapping *ProductVariantTransactionCatalogueInternalAccountMapping `json:"productVariantTransactionCatalogueInternalAccountMapping,omitempty"`
}

type CreateBaseInterestRequest struct {
	Code        string   `json:"code,omitempty"`
	Name        string   `json:"name,omitempty"`
	Description string   `json:"description,omitempty"`
	Currency    Currency `json:"currency,omitempty"`
	CreatedBy   string   `json:"createdBy,omitempty"`
}

type CreateBaseInterestResponse struct {
	BaseInterest *BaseInterest `json:"baseInterest,omitempty"`
}

type GetBaseInterestRequest struct {
	Id string `json:"id,omitempty"`
}

type GetBaseInterestResponse struct {
	BaseInterest *BaseInterest `json:"baseInterest,omitempty"`
}

type GetBaseInterestByCodeRequest struct {
	Code string `json:"code,omitempty"`
}

type GetBaseInterestByCodeResponse struct {
	BaseInterest *BaseInterest `json:"baseInterest,omitempty"`
}

type CreateBaseInterestVersionRequest struct {
	BaseInterestID string    `json:"baseInterestID,omitempty"`
	Version        string    `json:"version,omitempty"`
	EffectiveDate  time.Time `json:"effectiveDate,omitempty"`
	Description    string    `json:"description,omitempty"`
	CreatedBy      string    `json:"createdBy,omitempty"`
}

type CreateBaseInterestVersionResponse struct {
	BaseInterestVersion *BaseInterestVersion `json:"baseInterestVersion,omitempty"`
}

type GetBaseInterestVersionRequest struct {
	Id string `json:"id,omitempty"`
}

type GetBaseInterestVersionResponse struct {
	BaseInterestVersion *BaseInterestVersion `json:"baseInterestVersion,omitempty"`
}

type CreateBaseInterestTimeSlabRateRequest struct {
	BaseInterestVersionID  string   `json:"baseInterestVersionID,omitempty"`
	TermUnit               TermUnit `json:"termUnit,omitempty"`
	TermValue              int32    `json:"termValue,omitempty"`
	BaseInterestPercentage string   `json:"baseInterestPercentage,omitempty"`
	CreatedBy              string   `json:"createdBy,omitempty"`
}

type CreateBaseInterestTimeSlabRateResponse struct {
	BaseInterestTimeSlabRate *BaseInterestTimeSlabRate `json:"baseInterestTimeSlabRate,omitempty"`
}

type GetBaseInterestTimeSlabRateRequest struct {
	Id string `json:"id,omitempty"`
}

type GetBaseInterestTimeSlabRateResponse struct {
	BaseInterestTimeSlabRate *BaseInterestTimeSlabRate `json:"baseInterestTimeSlabRate,omitempty"`
}

type CreateDepositInterestRequest struct {
	ProductVariantID      string                `json:"productVariantID,omitempty"`
	IsLinkedToBaseRate    bool                  `json:"isLinkedToBaseRate,omitempty"`
	BaseInterestID        string                `json:"baseInterestID,omitempty"`
	Code                  string                `json:"code,omitempty"`
	Name                  string                `json:"name,omitempty"`
	Description           string                `json:"description,omitempty"`
	Currency              Currency              `json:"currency,omitempty"`
	RoundOffType          RoundOffType          `json:"roundOffType,omitempty"`
	InterestSlabType      InterestSlabType      `json:"interestSlabType,omitempty"`
	InterestSlabStructure InterestSlabStructure `json:"interestSlabStructure,omitempty"`
	CreatedBy             string                `json:"createdBy,omitempty"`
}

type CreateDepositInterestResponse struct {
	DepositInterest *DepositInterest `json:"depositInterest,omitempty"`
}

type GetDepositInterestRequest struct {
	Id string `json:"id,omitempty"`
}

type GetDepositInterestResponse struct {
	DepositInterest *DepositInterest `json:"depositInterest,omitempty"`
}

type GetDepositInterestByCodeRequest struct {
	Code string `json:"code,omitempty"`
}

type GetDepositInterestByCodeResponse struct {
	DepositInterest *DepositInterest `json:"depositInterest,omitempty"`
}

type CreateDepositInterestVersionRequest struct {
	DepositInterestID string    `json:"depositInterestID,omitempty"`
	Version           string    `json:"version,omitempty"`
	EffectiveDate     time.Time `json:"effectiveDate,omitempty"`
	Description       string    `json:"description,omitempty"`
	CreatedBy         string    `json:"createdBy,omitempty"`
}

type CreateDepositInterestVersionResponse struct {
	DepositInterestVersion *DepositInterestVersion `json:"depositInterestVersion,omitempty"`
}

type GetDepositInterestVersionRequest struct {
	Id string `json:"id,omitempty"`
}

type GetDepositInterestVersionResponse struct {
	DepositInterestVersion *DepositInterestVersion `json:"depositInterestVersion,omitempty"`
}

type CreateDepositInterestAmountSlabRateRequest struct {
	DepositInterestVersionID         string `json:"depositInterestVersionID,omitempty"`
	FromAmount                       string `json:"fromAmount,omitempty"`
	ToAmount                         string `json:"toAmount,omitempty"`
	BaseRateInterestSpreadPercentage string `json:"baseRateInterestSpreadPercentage,omitempty"`
	AbsoluteInterestRatePercentage   string `json:"absoluteInterestRatePercentage,omitempty"`
	CreatedBy                        string `json:"createdBy,omitempty"`
}

type CreateDepositInterestAmountSlabRateResponse struct {
	DepositInterestAmountSlabRate *DepositInterestAmountSlabRate `json:"depositInterestAmountSlabRate,omitempty"`
}

type GetDepositInterestAmountSlabRateRequest struct {
	Id string `json:"id,omitempty"`
}

type GetDepositInterestAmountSlabRateResponse struct {
	DepositInterestAmountSlabRate *DepositInterestAmountSlabRate `json:"depositInterestAmountSlabRate,omitempty"`
}

type CreatePocketTemplateRequest struct {
	Type      PocketType `json:"type,omitempty"`
	Name      string     `json:"name,omitempty"`
	ImageIDs  []string   `json:"imageIDs,omitempty"`
	CreatedBy string     `json:"createdBy,omitempty"`
}

type CreatePocketTemplateResponse struct {
	PocketTemplate *PocketTemplate `json:"pocketTemplate,omitempty"`
}

type ListPocketTemplatesRequest struct {
	Type PocketType `json:"type,omitempty"`
}

type ListPocketTemplatesResponse struct {
	PocketTemplates []PocketTemplate `json:"pocketTemplates,omitempty"`
}

type GetPocketTemplateRequest struct {
	Id string `json:"id,omitempty"`
}

type GetPocketTemplateResponse struct {
	PocketTemplate *PocketTemplate `json:"pocketTemplate,omitempty"`
}

type CreatePocketTemplateQuestionsRequest struct {
	PocketTemplateID    string                       `json:"pocketTemplateID,omitempty"`
	Locale              Locale                       `json:"locale,omitempty"`
	QuestionAnswerPairs []QuestionAnswerPairsRequest `json:"questionAnswerPairs,omitempty"`
	CreatedBy           string                       `json:"createdBy,omitempty"`
}

type CreatePocketTemplateQuestionsResponse struct {
	PocketTemplateQuestionsDetail *PocketTemplateQuestionsDetail `json:"pocketTemplateQuestionsDetail,omitempty"`
}

type ListPocketTemplateQuestionsRequest struct {
	PocketTemplateID string `json:"pocketTemplateID,omitempty"`
}

type ListPocketTemplateQuestionsResponse struct {
	PocketTemplateQuestionsDetail *PocketTemplateQuestionsDetail `json:"pocketTemplateQuestionsDetail,omitempty"`
}

type GetInterestParametersRequest struct {
	ParameterKey string `json:"parameterKey,omitempty"`
	IsNotified   Status `json:"isNotified,omitempty"`
	IsScheduled  Status `json:"isScheduled,omitempty"`
}

type GetInterestParametersResponse struct {
	ParameterKey              string    `json:"parameterKey,omitempty"`
	ParameterValue            string    `json:"parameterValue,omitempty"`
	ParameterVersion          string    `json:"parameterVersion,omitempty"`
	SmartContractVersionID    string    `json:"smartContractVersionID,omitempty"`
	EffectiveScheduleDate     time.Time `json:"effectiveScheduleDate,omitempty"`
	EffectiveNotificationDate time.Time `json:"effectiveNotificationDate,omitempty"`
}

type UpdateInterestParameterNotificationStatusRequest struct {
	ParameterKey           string `json:"parameterKey,omitempty"`
	ParameterVersion       string `json:"parameterVersion,omitempty"`
	SmartContractVersionID string `json:"smartContractVersionID,omitempty"`
	UpdatedBy              string `json:"updatedBy,omitempty"`
	IsNotified             Status `json:"isNotified,omitempty"`
}

type UpdateInterestParameterNotificationStatusResponse struct {
	IsNotified Status `json:"isNotified,omitempty"`
}

type UpdateInterestParameterScheduleStatusRequest struct {
	ParameterKey           string `json:"parameterKey,omitempty"`
	ParameterVersion       string `json:"parameterVersion,omitempty"`
	SmartContractVersionID string `json:"smartContractVersionID,omitempty"`
	UpdatedBy              string `json:"updatedBy,omitempty"`
	IsScheduled            Status `json:"isScheduled,omitempty"`
}

type UpdateInterestParameterScheduleStatusResponse struct {
	IsScheduled Status `json:"isScheduled,omitempty"`
}

type GetInterestParametersByProductVariantRequest struct {
	InterestParamRequest []InterestParametersByProductVariantRequest `json:"interestParamRequest,omitempty"`
}

type InterestParametersByProductVariantRequest struct {
	ProductVariantCode    ProductVariantCode `json:"productVariantCode,omitempty"`
	ProductVariantVersion string             `json:"productVariantVersion,omitempty"`
	InterestVersion       string             `json:"interestVersion,omitempty"`
}

type GetInterestParametersByProductVariantResponse struct {
	DepositsAccount *DepositsAccountInterestParameters `json:"depositsAccount,omitempty"`
	SavingsPocket   *SavingsPocketInterestParameters   `json:"savingsPocket,omitempty"`
	LoanAccount     *LoanAccountInterestParameters     `json:"loanAccount,omitempty"`
	BoostPocket     *BoostPocketInterestParameters     `json:"boostPocket,omitempty"`
}

// QuestionAnswerPairsDetailsResponse ...
type QuestionAnswerPairsDetailsResponse struct {
	ID                string                           `json:"ID,omitempty"`
	QuestionText      string                           `json:"questionText,omitempty"`
	AnswerSuggestions []ProductVariantAnswerSuggestion `json:"answerSuggestions,omitempty"`
	CreatedBy         string                           `json:"createdBy,omitempty"`
	CreatedAt         time.Time                        `json:"createdAt,omitempty"`
	UpdatedBy         string                           `json:"updatedBy,omitempty"`
	UpdatedAt         time.Time                        `json:"updatedAt,omitempty"`
	Locale            Locale                           `json:"locale,omitempty"`
	Code              string                           `json:"code,omitempty"`
}

type ProductVariantAnswerSuggestion struct {
	Code string `json:"code,omitempty"`
	Text string `json:"text,omitempty"`
}

// ProductVariantQuestionsDetail ...
type ProductVariantQuestionsDetail struct {
	ProductVariantCode    string                               `json:"productVariantCode,omitempty"`
	Locale                Locale                               `json:"locale,omitempty"`
	QuestionAnswerPairs   []QuestionAnswerPairsDetailsResponse `json:"questionAnswerPairs,omitempty"`
	ProductVariantVersion string                               `json:"productVariantVersion,omitempty"`
}

// ProductVariantQuestionAnswerPairsRequest
type ProductVariantQuestionAnswerPairsRequest struct {
	Code              string                           `json:"code,omitempty"`
	QuestionText      string                           `json:"questionText,omitempty"`
	AnswerSuggestions []ProductVariantAnswerSuggestion `json:"answerSuggestions,omitempty"`
}

// CreateProductVariantQuestionRequest ...
type CreateProductVariantQuestionRequest struct {
	ProductVariantCode    string                                     `json:"productVariantCode,omitempty"`
	ProductVariantVersion string                                     `json:"productVariantVersion,omitempty"`
	Locale                Locale                                     `json:"locale,omitempty"`
	QuestionAnswerPairs   []ProductVariantQuestionAnswerPairsRequest `json:"questionAnswerPairs,omitempty"`
	CreatedBy             string                                     `json:"createdBy,omitempty"`
}

// CreateProductVariantQuestionResponse ...
type CreateProductVariantQuestionResponse struct {
	QuestionsDetail *ProductVariantQuestionsDetail `json:"questionsDetail,omitempty"`
}

// ListProductVariantQuestionsRequest ...
type ListProductVariantQuestionsRequest struct {
	ProductVariantCode    string `json:"productVariantCode,omitempty"`
	ProductVariantVersion string `json:"productVariantVersion,omitempty"`
	Code                  string `json:"code,omitempty"`
}

// ListProductVariantQuestionsResponse ...
type ListProductVariantQuestionsResponse struct {
	QuestionsDetail *ProductVariantQuestionsDetail `json:"questionsDetail,omitempty"`
}

// CreateLoanInterestRequest request format to create loan interest
type CreateLoanInterestRequest struct {
	ProductVariantID      string                `json:"productVariantID,omitempty"`
	IsLinkedToBaseRate    bool                  `json:"isLinkedToBaseRate,omitempty"`
	BaseInterestID        string                `json:"baseInterestID,omitempty"`
	Code                  string                `json:"code,omitempty"`
	Name                  string                `json:"name,omitempty"`
	Description           string                `json:"description,omitempty"`
	Currency              Currency              `json:"currency,omitempty"`
	RoundOffType          RoundOffType          `json:"roundOffType,omitempty"`
	InterestType          InterestType          `json:"interestType,omitempty"`
	InterestSlabUnitType  InterestSlabType      `json:"interestSlabUnitType,omitempty"`
	InterestSlabStructure InterestSlabStructure `json:"interestSlabStructure,omitempty"`
	CreatedBy             string                `json:"createdBy,omitempty"`
}

// CreateLoanInterestResponse ...
type CreateLoanInterestResponse struct {
	LoanInterest *LoanInterest `json:"loanInterest,omitempty"`
}

// LoanInterest : format of loan interest response
type LoanInterest struct {
	Id                    string                `json:"id,omitempty"`
	ProductVariantID      string                `json:"productVariantID,omitempty"`
	IsLinkedToBaseRate    bool                  `json:"isLinkedToBaseRate,omitempty"`
	BaseInterestID        string                `json:"baseInterestID,omitempty"`
	Code                  string                `json:"code,omitempty"`
	Name                  string                `json:"name,omitempty"`
	Description           string                `json:"description,omitempty"`
	Currency              Currency              `json:"currency,omitempty"`
	RoundOffType          RoundOffType          `json:"roundOffType,omitempty"`
	InterestType          InterestType          `json:"interestType,omitempty"`
	InterestSlabUnitType  InterestSlabType      `json:"interestSlabUnitType,omitempty"`
	InterestSlabStructure InterestSlabStructure `json:"interestSlabStructure,omitempty"`
	CreatedBy             string                `json:"createdBy,omitempty"`
	CreatedAt             time.Time             `json:"createdAt,omitempty"`
	UpdatedBy             string                `json:"updatedBy,omitempty"`
	UpdatedAt             time.Time             `json:"updatedAt,omitempty"`
}

// GetLoanInterestByIDRequest : request format to get interest by ID
type GetLoanInterestByIDRequest struct {
	Id string `json:"id,omitempty"`
}

// GetLoanInterestByIDResponse : response format containing loan interest
type GetLoanInterestByIDResponse struct {
	LoanInterest *LoanInterest `json:"loanInterest,omitempty"`
}

// GetLoanInterestByCodeRequest : request format to get interest by code
type GetLoanInterestByCodeRequest struct {
	Code string `json:"code,omitempty"`
}

// GetLoanInterestByCodeResponse : ...
type GetLoanInterestByCodeResponse struct {
	LoanInterest *LoanInterest `json:"loanInterest,omitempty"`
}

// GetLoanPastDueParametersByProductCodeRequest : request format to get loan past due parameters by product code
type GetLoanPastDueParametersByProductCodeRequest struct {
	ProductCode        ProductCode `json:"productCode,omitempty"`
	LoanPastDueVersion string      `json:"loanPastDueVersion,omitempty"`
}

// GetLoanPastDueParametersByProductCodeResponse : response format to get loan past due parameters by product code
type GetLoanPastDueParametersByProductCodeResponse struct {
	ProductCode        ProductCode       `json:"productCode,omitempty"`
	LoanPastDueVersion string            `json:"loanPastDueVersion,omitempty"`
	LoanPastDueSlab    []LoanPastDueSlab `json:"loanPastDueSlab,omitempty"`
}

type LoanPastDueSlab struct {
	Id         string    `json:"id,omitempty"`
	FromUnit   int32     `json:"fromUnit,omitempty"`
	ToUnit     int32     `json:"toUnit,omitempty"`
	SlabType   string    `json:"slabType,omitempty"`
	BucketName string    `json:"bucketName,omitempty"`
	CreatedBy  string    `json:"createdBy,omitempty"`
	CreatedAt  time.Time `json:"createdAt,omitempty"`
	UpdatedBy  string    `json:"updatedBy,omitempty"`
	UpdatedAt  time.Time `json:"updatedAt,omitempty"`
}

// CreateLoanInstructionVersionRequest : request format to create loan instruction version
type CreateLoanInstructionVersionRequest struct {
	ProductVariantCode    string    `json:"productVariantCode,omitempty"`
	ProductVariantVersion string    `json:"productVariantVersion,omitempty"`
	Version               string    `json:"version,omitempty"`
	InstructionType       string    `json:"instructionType,omitempty"`
	Description           string    `json:"description,omitempty"`
	EffectiveDate         time.Time `json:"effectiveDate,omitempty"`
	CreatedBy             string    `json:"createdBy,omitempty"`
}

// CreateLoanInstructionVersionResponse : response format to create loan instruction version
type CreateLoanInstructionVersionResponse struct {
	LoanInstructionVersion *LoanInstructionVersion `json:"loanInstructionVersion,omitempty"`
}

// CreateLoanInstructionRequest : request format to create loan instruction
type CreateLoanInstructionRequest struct {
	LoanInstructionVersionID string `json:"loanInstructionVersionID,omitempty"`
	Code                     string `json:"code,omitempty"`
	Name                     string `json:"name,omitempty"`
	CreatedBy                string `json:"createdBy,omitempty"`
}

// CreateLoanInstructionResponse : response format to create loan instruction
type CreateLoanInstructionResponse struct {
	LoanInstruction *LoanInstruction `json:"loanInstruction,omitempty"`
}

// GetLoanInstructionsByCodeRequest : request format to get loan instructions by product variant code and instruction type
type GetLoanInstructionsByCodeRequest struct {
	ProductVariantCode string `json:"productVariantCode,omitempty"`
	Version            string `json:"version,omitempty"`
	InstructionType    string `json:"instructionType,omitempty"`
}

// GetLoanInstructionsByCodeResponse : response format to get loan instructions by product variant code and instruction type
type GetLoanInstructionsByCodeResponse struct {
	ProductVariantCode string            `json:"productVariantCode,omitempty"`
	Version            string            `json:"version,omitempty"`
	InstructionType    string            `json:"instructionType,omitempty"`
	LoanInstruction    []LoanInstruction `json:"loanInstruction,omitempty"`
}

type LoanInstructionVersion struct {
	Id                 string    `json:"id,omitempty"`
	ProductVariantCode string    `json:"productVariantCode,omitempty"`
	Version            string    `json:"version,omitempty"`
	InstructionType    string    `json:"instructionType,omitempty"`
	Description        string    `json:"description,omitempty"`
	EffectiveDate      time.Time `json:"effectiveDate,omitempty"`
	CreatedBy          string    `json:"createdBy,omitempty"`
	CreatedAt          time.Time `json:"createdAt,omitempty"`
	UpdatedBy          string    `json:"updatedBy,omitempty"`
	UpdatedAt          time.Time `json:"updatedAt,omitempty"`
}

type LoanInstruction struct {
	Id           string                 `json:"id,omitempty"`
	Code         string                 `json:"code,omitempty"`
	Name         string                 `json:"name,omitempty"`
	CreatedBy    string                 `json:"createdBy,omitempty"`
	CreatedAt    time.Time              `json:"createdAt,omitempty"`
	UpdatedBy    string                 `json:"updatedBy,omitempty"`
	UpdatedAt    time.Time              `json:"updatedAt,omitempty"`
	Restrictions map[string]interface{} `json:"restrictions,omitempty"`
}

type LoanAccountInterestParameters struct {
	NormalInterest    []LoanAccountFlatInterest `json:"normalInterest,omitempty"`
	PenalInterest     []LoanAccountFlatInterest `json:"penalInterest,omitempty"`
	DiscoveryInterest []LoanAccountFlatInterest `json:"discoveryInterest,omitempty"`
}

type LoanAccountFlatInterest struct {
	Rate       int32  `json:"rate,omitempty"`
	Multiplier int32  `json:"multiplier,omitempty"`
	SlabType   string `json:"slabType,omitempty"`
	FromUnit   string `json:"fromUnit,omitempty"`
	ToUnit     string `json:"toUnit,omitempty"`
}

type BoostPocketInterestParameters struct {
	MaxInterestRateOffered string                    `json:"maxInterestRateOffered,omitempty"`
	InterestRateType       InterestRateType          `json:"interestRateType,omitempty"`
	TenorType              TenorType                 `json:"tenorType,omitempty"`
	TierInterest           []BoostPocketTierInterest `json:"tierInterest,omitempty"`
}

type BoostPocketTierInterest struct {
	BaseInterestRate  int32  `json:"baseInterestRate,omitempty"`
	BonusInterestRate int32  `json:"bonusInterestRate,omitempty"`
	TotalInterestRate int32  `json:"totalInterestRate,omitempty"`
	Multiplier        int32  `json:"multiplier,omitempty"`
	FromAmount        *Money `json:"fromAmount"`
	ToAmount          *Money `json:"toAmount"`
	Tenor             string `json:"tenor"`
	TenorUnit         string `json:"tenorUnit"`
	MaxTenor          string `json:"maxTenor,omitempty"`
	MaxTenorUnit      string `json:"maxTenorUnit,omitempty"`
}

// ProductMaster ...
type ProductMaster interface {
	// CreateProductTemplate creates a new Product Template
	CreateProductTemplate(ctx context.Context, req *CreateProductTemplateRequest) (*CreateProductTemplateResponse, error)
	// GetProductTemplate fetches a product template by id
	GetProductTemplate(ctx context.Context, req *GetProductTemplateRequest) (*GetProductTemplateResponse, error)
	// UpdateProductTemplateStatus updates the status of Product Template
	UpdateProductTemplateStatus(ctx context.Context, req *UpdateProductTemplateStatusRequest) (*UpdateProductTemplateStatusResponse, error)
	// CreateProductVariant creates a new Product Variant
	CreateProductVariant(ctx context.Context, req *CreateProductVariantRequest) (*CreateProductVariantResponse, error)
	// ListProductVariants fetches a product variant by id
	ListProductVariants(ctx context.Context, req *ListProductVariantsRequest) (*ListProductVariantsResponse, error)
	// GetProductVariant fetches a product variant by id
	GetProductVariant(ctx context.Context, req *GetProductVariantRequest) (*GetProductVariantResponse, error)
	// GetProductVariantByCode fetches a product variant by code
	GetProductVariantByCode(ctx context.Context, req *GetProductVariantByCodeRequest) (*GetProductVariantByCodeResponse, error)
	// UpdateProductVariantStatus updates the status of Product Variant
	UpdateProductVariantStatus(ctx context.Context, req *UpdateProductVariantStatusRequest) (*UpdateProductVariantStatusResponse, error)
	// CreateProduct creates a new Product
	CreateProduct(ctx context.Context, req *CreateProductRequest) (*CreateProductResponse, error)
	// GetProduct fetches a product by id
	GetProduct(ctx context.Context, req *GetProductRequest) (*GetProductResponse, error)
	// GetProduct fetches a product by code
	GetProductByCode(ctx context.Context, req *GetProductByCodeRequest) (*GetProductByCodeResponse, error)
	// UpdateProductStatus updates the status of Product Template
	UpdateProductStatus(ctx context.Context, req *UpdateProductStatusRequest) (*UpdateProductStatusResponse, error)
	// CreateProductTemplateParameter creates a new Product Template Parameter
	CreateProductTemplateParameter(ctx context.Context, req *CreateProductTemplateParameterRequest) (*CreateProductTemplateParameterResponse, error)
	// GetProductTemplateParameter fetches a product template parameter by id
	GetProductTemplateParameter(ctx context.Context, req *GetProductTemplateParameterRequest) (*GetProductTemplateParameterResponse, error)
	// ListProductTemplateParameters fetches all product template parameters for a product variant
	ListProductTemplateParameters(ctx context.Context, req *ListProductTemplateParametersRequest) (*ListProductTemplateParametersResponse, error)
	// UpdateProductTemplateParameterValue updates the value of Product Template Parameter Value
	UpdateProductTemplateParameterValue(ctx context.Context, req *UpdateProductTemplateParameterValueRequest) (*UpdateProductTemplateParameterValueResponse, error)
	// CreateProductVariantParameter creates a new Product Variant Parameter
	CreateProductVariantParameter(ctx context.Context, req *CreateProductVariantParameterRequest) (*CreateProductVariantParameterResponse, error)
	// GetProductVariantParameter fetches a product variant parameter by id
	GetProductVariantParameter(ctx context.Context, req *GetProductVariantParameterRequest) (*GetProductVariantParameterResponse, error)
	// ListProductVariantParameters fetches all product variant parameters for a product variant
	ListProductVariantParameters(ctx context.Context, req *ListProductVariantParametersRequest) (*ListProductVariantParametersResponse, error)
	// ListEffectiveProductVariantParameters fetches all product variant parameters for a product variant (includes the overrides from product-template-parameters
	ListEffectiveProductVariantParameters(ctx context.Context, req *ListEffectiveProductVariantParametersRequest) (*ListEffectiveProductVariantParametersResponse, error)
	// UpdateProductVariantParameterValue updates the value of Product Variant Parameter Value
	UpdateProductVariantParameterValue(ctx context.Context, req *UpdateProductVariantParameterValueRequest) (*UpdateProductVariantParameterValueResponse, error)
	// CreateTransactionCatalogue creates a new Transaction catalogue
	CreateTransactionCatalogue(ctx context.Context, req *CreateTransactionCatalogueRequest) (*CreateTransactionCatalogueResponse, error)
	// GetTransactionCatalogue fetches a transaction catalogue by id
	GetTransactionCatalogue(ctx context.Context, req *GetTransactionCatalogueRequest) (*GetTransactionCatalogueResponse, error)
	// UpdateTransactionCatalogueStatus updates the status of Transaction catalogue
	UpdateTransactionCatalogueStatus(ctx context.Context, req *UpdateTransactionCatalogueStatusRequest) (*UpdateTransactionCatalogueStatusResponse, error)
	// CreateProductVariantTransactionCatalogueMapping creates a new Product Variant Transaction catalogue mapping
	CreateProductVariantTransactionCatalogueMapping(ctx context.Context, req *CreateProductVariantTransactionCatalogueMappingRequest) (*CreateProductVariantTransactionCatalogueMappingResponse, error)
	// GetProductVariantTransactionCatalogueMapping fetches a Product Variant Transaction catalogue mapping by id
	GetProductVariantTransactionCatalogueMapping(ctx context.Context, req *GetProductVariantTransactionCatalogueMappingRequest) (*GetProductVariantTransactionCatalogueMappingResponse, error)
	// UpdateProductVariantTransactionCatalogueMappingStatus updates the status of Product Variant Transaction catalogue mapping
	UpdateProductVariantTransactionCatalogueMappingStatus(ctx context.Context, req *UpdateProductVariantTransactionCatalogueMappingStatusRequest) (*UpdateProductVariantTransactionCatalogueMappingStatusResponse, error)
	// CreateGeneralLedger creates a new GeneralLedger
	CreateGeneralLedger(ctx context.Context, req *CreateGeneralLedgerRequest) (*CreateGeneralLedgerResponse, error)
	// GetGeneralLedger fetches a general-ledger by id
	GetGeneralLedger(ctx context.Context, req *GetGeneralLedgerRequest) (*GetGeneralLedgerResponse, error)
	// GetGeneralLedgerByCode fetches a general-ledger by code
	GetGeneralLedgerByCode(ctx context.Context, req *GetGeneralLedgerByCodeRequest) (*GetGeneralLedgerByCodeResponse, error)
	// UpdateGeneralLedgerStatus updates the status of general-ledger
	UpdateGeneralLedgerStatus(ctx context.Context, req *UpdateGeneralLedgerStatusRequest) (*UpdateGeneralLedgerStatusResponse, error)
	// CreateInternalAccount creates a new Internal Account
	CreateInternalAccount(ctx context.Context, req *CreateInternalAccountRequest) (*CreateInternalAccountResponse, error)
	// GetInternalAccount fetches a internal account by id
	GetInternalAccount(ctx context.Context, req *GetInternalAccountRequest) (*GetInternalAccountResponse, error)
	// ListInternalAccounts fetches a list of internal account by code and generalLedgerID
	ListInternalAccounts(ctx context.Context, req *ListInternalAccountsRequest) (*ListInternalAccountsResponse, error)
	// UpdateInternalAccountStatus updates the status of internal account
	UpdateInternalAccountStatus(ctx context.Context, req *UpdateInternalAccountStatusRequest) (*UpdateInternalAccountStatusResponse, error)
	// CreateProductVariantTransactionCatalogueInternalAccountMapping creates a new product-variant-transaction-catalogue-internal-account-mapping
	CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) (*CreateProductVariantTransactionCatalogueInternalAccountMappingResponse, error)
	// GetProductVariantTransactionCatalogueInternalAccountMapping fetches a product-variant-transaction-catalogue-internal-account-mapping by id
	GetProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *GetProductVariantTransactionCatalogueInternalAccountMappingRequest) (*GetProductVariantTransactionCatalogueInternalAccountMappingResponse, error)
	// GetProductVariantTransactionCatalogueInternalAccountMappingByKey fetches a product-variant-transaction-catalogue-internal-account-mapping by identifer key
	GetProductVariantTransactionCatalogueInternalAccountMappingByKey(ctx context.Context, req *GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) (*GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse, error)
	// UpdateProductVariantTransactionCatalogueInternalAccountMapping updates the status of product-variant-transaction-catalogue-internal-account-mapping
	UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(ctx context.Context, req *UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) (*UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse, error)
	// CreateBaseInterest creates a new base-interest
	CreateBaseInterest(ctx context.Context, req *CreateBaseInterestRequest) (*CreateBaseInterestResponse, error)
	// GetBaseInterest fetches a base interest by id
	GetBaseInterest(ctx context.Context, req *GetBaseInterestRequest) (*GetBaseInterestResponse, error)
	// GetBaseInterestByCode fetches a base interest by code
	GetBaseInterestByCode(ctx context.Context, req *GetBaseInterestByCodeRequest) (*GetBaseInterestByCodeResponse, error)
	// CreateBaseInterestVersion creates a new base interest version
	CreateBaseInterestVersion(ctx context.Context, req *CreateBaseInterestVersionRequest) (*CreateBaseInterestVersionResponse, error)
	// GetBaseInterestVersion fetches a base interest version by id
	GetBaseInterestVersion(ctx context.Context, req *GetBaseInterestVersionRequest) (*GetBaseInterestVersionResponse, error)
	// CreateBaseInterestTimeSlabRate creates a new base interest time slab rate
	CreateBaseInterestTimeSlabRate(ctx context.Context, req *CreateBaseInterestTimeSlabRateRequest) (*CreateBaseInterestTimeSlabRateResponse, error)
	// GetBaseInterestTimeSlabRate fetches a base interest time slab rate by id
	GetBaseInterestTimeSlabRate(ctx context.Context, req *GetBaseInterestTimeSlabRateRequest) (*GetBaseInterestTimeSlabRateResponse, error)
	// CreateDepositInterest creates a new deposit interest
	CreateDepositInterest(ctx context.Context, req *CreateDepositInterestRequest) (*CreateDepositInterestResponse, error)
	// GetDepositInterest fetches a deposit interest by id
	GetDepositInterest(ctx context.Context, req *GetDepositInterestRequest) (*GetDepositInterestResponse, error)
	// GetDepositInterestByCode fetches a deposit interest by code
	GetDepositInterestByCode(ctx context.Context, req *GetDepositInterestByCodeRequest) (*GetDepositInterestByCodeResponse, error)
	// CreateDepositInterestVersion creates a new deposit interest version
	CreateDepositInterestVersion(ctx context.Context, req *CreateDepositInterestVersionRequest) (*CreateDepositInterestVersionRequest, error)
	// GetDepositInterestVersion fetches a deposit interest version by id
	GetDepositInterestVersion(ctx context.Context, req *GetDepositInterestVersionRequest) (*GetDepositInterestVersionResponse, error)
	// CreateDepositInterestAmountSlabRate creates a new deposit interest amount slab rate
	CreateDepositInterestAmountSlabRate(ctx context.Context, req *CreateDepositInterestAmountSlabRateRequest) (*CreateDepositInterestAmountSlabRateResponse, error)
	// GetDepositInterestAmountSlabRate fetches a deposit interest amount slab rate by id
	GetDepositInterestAmountSlabRate(ctx context.Context, req *GetDepositInterestAmountSlabRateRequest) (*GetDepositInterestAmountSlabRateResponse, error)
	// CreatePocketTemplate creates a new pocket template
	CreatePocketTemplate(ctx context.Context, req *CreatePocketTemplateRequest) (*CreatePocketTemplateResponse, error)
	// ListPocketTemplates fetches list of pocket templates
	ListPocketTemplates(ctx context.Context, req *ListPocketTemplatesRequest) (*ListPocketTemplatesResponse, error)
	// GetPocketTemplate fetches pocket template details
	GetPocketTemplate(ctx context.Context, req *GetPocketTemplateRequest) (*GetPocketTemplateResponse, error)
	// CreatePocketTemplateQuestions creates new pocket template questions
	CreatePocketTemplateQuestions(ctx context.Context, req *CreatePocketTemplateQuestionsRequest) (*CreatePocketTemplateQuestionsResponse, error)
	// ListPocketTemplateQuestions fetches list of pocket template questions
	ListPocketTemplateQuestions(ctx context.Context, req *ListPocketTemplateQuestionsRequest) (*ListPocketTemplateQuestionsResponse, error)
	// GetInterestParameters fetches all product params whose values are updated
	GetInterestParameters(ctx context.Context, req *GetInterestParametersRequest) (*GetInterestParametersResponse, error)
	// UpdateInterestParameterScheduleStatus keep the status whether parameter is scheduled in TM or not
	UpdateInterestParameterScheduleStatus(ctx context.Context, req *UpdateInterestParameterScheduleStatusRequest) (*UpdateInterestParameterScheduleStatusResponse, error)
	// UpdateInterestParameterNotificationStatus keep the status whether noti is sent to the customer
	UpdateInterestParameterNotificationStatus(ctx context.Context, req *UpdateInterestParameterNotificationStatusRequest) (*UpdateInterestParameterNotificationStatusResponse, error)
	// GetInterestParametersByProductVariant fetches interest parameters by product variant
	GetInterestParametersByProductVariant(ctx context.Context, req *GetInterestParametersByProductVariantRequest) (*GetInterestParametersByProductVariantResponse, error)
	// CreateProductVariantQuestions : creates new questions based on productVariantCode
	CreateProductVariantQuestions(ctx context.Context, req *CreateProductVariantQuestionRequest) (*CreateProductVariantQuestionResponse, error)
	// ListProductVariantQuestions : list the array of  questions and respective answers based on productVariantCode
	ListProductVariantQuestions(ctx context.Context, req *ListProductVariantQuestionsRequest) (*ListProductVariantQuestionsResponse, error)
	// CreateLoanInterest creates a new loan interest
	CreateLoanInterest(ctx context.Context, req *CreateLoanInterestRequest) (*CreateLoanInterestResponse, error)
	// GetLoanInterest fetches a loan interest by id
	GetLoanInterest(ctx context.Context, req *GetLoanInterestByIDRequest) (*GetLoanInterestByIDResponse, error)
	// GetLoanInterestByCode fetches a loan interest by code
	GetLoanInterestByCode(ctx context.Context, req *GetLoanInterestByCodeRequest) (*GetLoanInterestByCodeResponse, error)
	// GetLoanPastDueParametersByProductCode fetches a loan past due parameters by product code
	GetLoanPastDueParametersByProductCode(ctx context.Context, req *GetLoanPastDueParametersByProductCodeRequest) (*GetLoanPastDueParametersByProductCodeResponse, error)
	// CreateLoanInstructionVersion creates a new version of loan instructions
	CreateLoanInstructionVersion(ctx context.Context, req *CreateLoanInstructionVersionRequest) (*CreateLoanInstructionVersionResponse, error)
	// CreateLoanInstructionRequest creates a new loan instruction
	CreateLoanInstruction(ctx context.Context, req *CreateLoanInstructionRequest) (*CreateLoanInstructionResponse, error)
	// GetLoanInstructionsByCode fetches a loan instruction parameters by product variant code and instruction type
	GetLoanInstructionsByCode(ctx context.Context, req *GetLoanInstructionsByCodeRequest) (*GetLoanInstructionsByCodeResponse, error)
	// ListLoanDocumentOptionsByProductVariant returns list of loan document options for a given product variant code
	ListLoanDocumentOptionsByProductVariant(ctx context.Context, req *ListLoanDocumentOptionsByProductVariantRequest) (*ListLoanDocumentOptionsByProductVariantResponse, error)
}

// Code generated by mockery v2.33.3. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	mock "github.com/stretchr/testify/mock"
)

// ProductMaster is an autogenerated mock type for the ProductMaster type
type ProductMaster struct {
	mock.Mock
}

// CreateBaseInterest provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateBaseInterest(ctx context.Context, req *api.CreateBaseInterestRequest) (*api.CreateBaseInterestResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateBaseInterestResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateBaseInterestRequest) (*api.CreateBaseInterestResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateBaseInterestRequest) *api.CreateBaseInterestResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateBaseInterestResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateBaseInterestRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateBaseInterestTimeSlabRate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateBaseInterestTimeSlabRate(ctx context.Context, req *api.CreateBaseInterestTimeSlabRateRequest) (*api.CreateBaseInterestTimeSlabRateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateBaseInterestTimeSlabRateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateBaseInterestTimeSlabRateRequest) (*api.CreateBaseInterestTimeSlabRateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateBaseInterestTimeSlabRateRequest) *api.CreateBaseInterestTimeSlabRateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateBaseInterestTimeSlabRateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateBaseInterestTimeSlabRateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateBaseInterestVersion provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateBaseInterestVersion(ctx context.Context, req *api.CreateBaseInterestVersionRequest) (*api.CreateBaseInterestVersionResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateBaseInterestVersionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateBaseInterestVersionRequest) (*api.CreateBaseInterestVersionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateBaseInterestVersionRequest) *api.CreateBaseInterestVersionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateBaseInterestVersionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateBaseInterestVersionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateDepositInterest provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateDepositInterest(ctx context.Context, req *api.CreateDepositInterestRequest) (*api.CreateDepositInterestResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateDepositInterestResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDepositInterestRequest) (*api.CreateDepositInterestResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDepositInterestRequest) *api.CreateDepositInterestResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateDepositInterestResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateDepositInterestRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateDepositInterestAmountSlabRate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateDepositInterestAmountSlabRate(ctx context.Context, req *api.CreateDepositInterestAmountSlabRateRequest) (*api.CreateDepositInterestAmountSlabRateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateDepositInterestAmountSlabRateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDepositInterestAmountSlabRateRequest) (*api.CreateDepositInterestAmountSlabRateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDepositInterestAmountSlabRateRequest) *api.CreateDepositInterestAmountSlabRateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateDepositInterestAmountSlabRateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateDepositInterestAmountSlabRateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateDepositInterestVersion provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateDepositInterestVersion(ctx context.Context, req *api.CreateDepositInterestVersionRequest) (*api.CreateDepositInterestVersionRequest, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateDepositInterestVersionRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDepositInterestVersionRequest) (*api.CreateDepositInterestVersionRequest, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateDepositInterestVersionRequest) *api.CreateDepositInterestVersionRequest); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateDepositInterestVersionRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateDepositInterestVersionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateGeneralLedger provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateGeneralLedger(ctx context.Context, req *api.CreateGeneralLedgerRequest) (*api.CreateGeneralLedgerResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateGeneralLedgerResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateGeneralLedgerRequest) (*api.CreateGeneralLedgerResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateGeneralLedgerRequest) *api.CreateGeneralLedgerResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateGeneralLedgerResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateGeneralLedgerRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateInternalAccount provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateInternalAccount(ctx context.Context, req *api.CreateInternalAccountRequest) (*api.CreateInternalAccountResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateInternalAccountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateInternalAccountRequest) (*api.CreateInternalAccountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateInternalAccountRequest) *api.CreateInternalAccountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateInternalAccountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateInternalAccountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateLoanInstruction provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateLoanInstruction(ctx context.Context, req *api.CreateLoanInstructionRequest) (*api.CreateLoanInstructionResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateLoanInstructionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateLoanInstructionRequest) (*api.CreateLoanInstructionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateLoanInstructionRequest) *api.CreateLoanInstructionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateLoanInstructionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateLoanInstructionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateLoanInstructionVersion provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateLoanInstructionVersion(ctx context.Context, req *api.CreateLoanInstructionVersionRequest) (*api.CreateLoanInstructionVersionResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateLoanInstructionVersionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateLoanInstructionVersionRequest) (*api.CreateLoanInstructionVersionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateLoanInstructionVersionRequest) *api.CreateLoanInstructionVersionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateLoanInstructionVersionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateLoanInstructionVersionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateLoanInterest provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateLoanInterest(ctx context.Context, req *api.CreateLoanInterestRequest) (*api.CreateLoanInterestResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateLoanInterestResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateLoanInterestRequest) (*api.CreateLoanInterestResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateLoanInterestRequest) *api.CreateLoanInterestResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateLoanInterestResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateLoanInterestRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreatePocketTemplate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreatePocketTemplate(ctx context.Context, req *api.CreatePocketTemplateRequest) (*api.CreatePocketTemplateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreatePocketTemplateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePocketTemplateRequest) (*api.CreatePocketTemplateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePocketTemplateRequest) *api.CreatePocketTemplateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreatePocketTemplateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreatePocketTemplateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreatePocketTemplateQuestions provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreatePocketTemplateQuestions(ctx context.Context, req *api.CreatePocketTemplateQuestionsRequest) (*api.CreatePocketTemplateQuestionsResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreatePocketTemplateQuestionsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePocketTemplateQuestionsRequest) (*api.CreatePocketTemplateQuestionsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreatePocketTemplateQuestionsRequest) *api.CreatePocketTemplateQuestionsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreatePocketTemplateQuestionsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreatePocketTemplateQuestionsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProduct provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProduct(ctx context.Context, req *api.CreateProductRequest) (*api.CreateProductResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductRequest) (*api.CreateProductResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductRequest) *api.CreateProductResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProductTemplate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProductTemplate(ctx context.Context, req *api.CreateProductTemplateRequest) (*api.CreateProductTemplateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductTemplateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductTemplateRequest) (*api.CreateProductTemplateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductTemplateRequest) *api.CreateProductTemplateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductTemplateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductTemplateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProductTemplateParameter provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProductTemplateParameter(ctx context.Context, req *api.CreateProductTemplateParameterRequest) (*api.CreateProductTemplateParameterResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductTemplateParameterResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductTemplateParameterRequest) (*api.CreateProductTemplateParameterResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductTemplateParameterRequest) *api.CreateProductTemplateParameterResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductTemplateParameterResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductTemplateParameterRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProductVariant provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProductVariant(ctx context.Context, req *api.CreateProductVariantRequest) (*api.CreateProductVariantResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductVariantResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantRequest) (*api.CreateProductVariantResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantRequest) *api.CreateProductVariantResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductVariantResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductVariantRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListProductVariants provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListProductVariants(ctx context.Context, req *api.ListProductVariantsRequest) (*api.ListProductVariantsResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListProductVariantsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductVariantsRequest) (*api.ListProductVariantsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductVariantsRequest) *api.ListProductVariantsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListProductVariantsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListProductVariantsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProductVariantParameter provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProductVariantParameter(ctx context.Context, req *api.CreateProductVariantParameterRequest) (*api.CreateProductVariantParameterResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductVariantParameterResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantParameterRequest) (*api.CreateProductVariantParameterResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantParameterRequest) *api.CreateProductVariantParameterResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductVariantParameterResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductVariantParameterRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProductVariantQuestions provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProductVariantQuestions(ctx context.Context, req *api.CreateProductVariantQuestionRequest) (*api.CreateProductVariantQuestionResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductVariantQuestionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantQuestionRequest) (*api.CreateProductVariantQuestionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantQuestionRequest) *api.CreateProductVariantQuestionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductVariantQuestionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductVariantQuestionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProductVariantTransactionCatalogueInternalAccountMapping provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) *api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateProductVariantTransactionCatalogueMapping provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueMappingRequest) (*api.CreateProductVariantTransactionCatalogueMappingResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateProductVariantTransactionCatalogueMappingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantTransactionCatalogueMappingRequest) (*api.CreateProductVariantTransactionCatalogueMappingResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateProductVariantTransactionCatalogueMappingRequest) *api.CreateProductVariantTransactionCatalogueMappingResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateProductVariantTransactionCatalogueMappingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateProductVariantTransactionCatalogueMappingRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateTransactionCatalogue provides a mock function with given fields: ctx, req
func (_m *ProductMaster) CreateTransactionCatalogue(ctx context.Context, req *api.CreateTransactionCatalogueRequest) (*api.CreateTransactionCatalogueResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.CreateTransactionCatalogueResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateTransactionCatalogueRequest) (*api.CreateTransactionCatalogueResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateTransactionCatalogueRequest) *api.CreateTransactionCatalogueResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateTransactionCatalogueResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateTransactionCatalogueRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBaseInterest provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetBaseInterest(ctx context.Context, req *api.GetBaseInterestRequest) (*api.GetBaseInterestResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetBaseInterestResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestRequest) (*api.GetBaseInterestResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestRequest) *api.GetBaseInterestResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetBaseInterestResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetBaseInterestRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBaseInterestByCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetBaseInterestByCode(ctx context.Context, req *api.GetBaseInterestByCodeRequest) (*api.GetBaseInterestByCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetBaseInterestByCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestByCodeRequest) (*api.GetBaseInterestByCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestByCodeRequest) *api.GetBaseInterestByCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetBaseInterestByCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetBaseInterestByCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBaseInterestTimeSlabRate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetBaseInterestTimeSlabRate(ctx context.Context, req *api.GetBaseInterestTimeSlabRateRequest) (*api.GetBaseInterestTimeSlabRateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetBaseInterestTimeSlabRateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestTimeSlabRateRequest) (*api.GetBaseInterestTimeSlabRateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestTimeSlabRateRequest) *api.GetBaseInterestTimeSlabRateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetBaseInterestTimeSlabRateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetBaseInterestTimeSlabRateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBaseInterestVersion provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetBaseInterestVersion(ctx context.Context, req *api.GetBaseInterestVersionRequest) (*api.GetBaseInterestVersionResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetBaseInterestVersionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestVersionRequest) (*api.GetBaseInterestVersionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetBaseInterestVersionRequest) *api.GetBaseInterestVersionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetBaseInterestVersionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetBaseInterestVersionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDepositInterest provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetDepositInterest(ctx context.Context, req *api.GetDepositInterestRequest) (*api.GetDepositInterestResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetDepositInterestResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestRequest) (*api.GetDepositInterestResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestRequest) *api.GetDepositInterestResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetDepositInterestResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetDepositInterestRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDepositInterestAmountSlabRate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetDepositInterestAmountSlabRate(ctx context.Context, req *api.GetDepositInterestAmountSlabRateRequest) (*api.GetDepositInterestAmountSlabRateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetDepositInterestAmountSlabRateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestAmountSlabRateRequest) (*api.GetDepositInterestAmountSlabRateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestAmountSlabRateRequest) *api.GetDepositInterestAmountSlabRateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetDepositInterestAmountSlabRateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetDepositInterestAmountSlabRateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDepositInterestByCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetDepositInterestByCode(ctx context.Context, req *api.GetDepositInterestByCodeRequest) (*api.GetDepositInterestByCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetDepositInterestByCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestByCodeRequest) (*api.GetDepositInterestByCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestByCodeRequest) *api.GetDepositInterestByCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetDepositInterestByCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetDepositInterestByCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDepositInterestVersion provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetDepositInterestVersion(ctx context.Context, req *api.GetDepositInterestVersionRequest) (*api.GetDepositInterestVersionResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetDepositInterestVersionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestVersionRequest) (*api.GetDepositInterestVersionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetDepositInterestVersionRequest) *api.GetDepositInterestVersionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetDepositInterestVersionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetDepositInterestVersionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetGeneralLedger provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetGeneralLedger(ctx context.Context, req *api.GetGeneralLedgerRequest) (*api.GetGeneralLedgerResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetGeneralLedgerResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetGeneralLedgerRequest) (*api.GetGeneralLedgerResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetGeneralLedgerRequest) *api.GetGeneralLedgerResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetGeneralLedgerResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetGeneralLedgerRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetGeneralLedgerByCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetGeneralLedgerByCode(ctx context.Context, req *api.GetGeneralLedgerByCodeRequest) (*api.GetGeneralLedgerByCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetGeneralLedgerByCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetGeneralLedgerByCodeRequest) (*api.GetGeneralLedgerByCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetGeneralLedgerByCodeRequest) *api.GetGeneralLedgerByCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetGeneralLedgerByCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetGeneralLedgerByCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInterestParameters provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetInterestParameters(ctx context.Context, req *api.GetInterestParametersRequest) (*api.GetInterestParametersResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetInterestParametersResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInterestParametersRequest) (*api.GetInterestParametersResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInterestParametersRequest) *api.GetInterestParametersResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetInterestParametersResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetInterestParametersRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInterestParametersByProductVariant provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetInterestParametersByProductVariant(ctx context.Context, req *api.GetInterestParametersByProductVariantRequest) (*api.GetInterestParametersByProductVariantResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetInterestParametersByProductVariantResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInterestParametersByProductVariantRequest) (*api.GetInterestParametersByProductVariantResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInterestParametersByProductVariantRequest) *api.GetInterestParametersByProductVariantResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetInterestParametersByProductVariantResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetInterestParametersByProductVariantRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInternalAccount provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetInternalAccount(ctx context.Context, req *api.GetInternalAccountRequest) (*api.GetInternalAccountResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetInternalAccountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInternalAccountRequest) (*api.GetInternalAccountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetInternalAccountRequest) *api.GetInternalAccountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetInternalAccountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetInternalAccountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLoanInstructionsByCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetLoanInstructionsByCode(ctx context.Context, req *api.GetLoanInstructionsByCodeRequest) (*api.GetLoanInstructionsByCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetLoanInstructionsByCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanInstructionsByCodeRequest) (*api.GetLoanInstructionsByCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanInstructionsByCodeRequest) *api.GetLoanInstructionsByCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLoanInstructionsByCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLoanInstructionsByCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLoanInterest provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetLoanInterest(ctx context.Context, req *api.GetLoanInterestByIDRequest) (*api.GetLoanInterestByIDResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetLoanInterestByIDResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanInterestByIDRequest) (*api.GetLoanInterestByIDResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanInterestByIDRequest) *api.GetLoanInterestByIDResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLoanInterestByIDResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLoanInterestByIDRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLoanInterestByCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetLoanInterestByCode(ctx context.Context, req *api.GetLoanInterestByCodeRequest) (*api.GetLoanInterestByCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetLoanInterestByCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanInterestByCodeRequest) (*api.GetLoanInterestByCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanInterestByCodeRequest) *api.GetLoanInterestByCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLoanInterestByCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLoanInterestByCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLoanPastDueParametersByProductCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetLoanPastDueParametersByProductCode(ctx context.Context, req *api.GetLoanPastDueParametersByProductCodeRequest) (*api.GetLoanPastDueParametersByProductCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetLoanPastDueParametersByProductCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanPastDueParametersByProductCodeRequest) (*api.GetLoanPastDueParametersByProductCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetLoanPastDueParametersByProductCodeRequest) *api.GetLoanPastDueParametersByProductCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetLoanPastDueParametersByProductCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetLoanPastDueParametersByProductCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPocketTemplate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetPocketTemplate(ctx context.Context, req *api.GetPocketTemplateRequest) (*api.GetPocketTemplateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetPocketTemplateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketTemplateRequest) (*api.GetPocketTemplateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetPocketTemplateRequest) *api.GetPocketTemplateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetPocketTemplateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetPocketTemplateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProduct provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProduct(ctx context.Context, req *api.GetProductRequest) (*api.GetProductResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductRequest) (*api.GetProductResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductRequest) *api.GetProductResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductByCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductByCode(ctx context.Context, req *api.GetProductByCodeRequest) (*api.GetProductByCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductByCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductByCodeRequest) (*api.GetProductByCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductByCodeRequest) *api.GetProductByCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductByCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductByCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductTemplate provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductTemplate(ctx context.Context, req *api.GetProductTemplateRequest) (*api.GetProductTemplateResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductTemplateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductTemplateRequest) (*api.GetProductTemplateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductTemplateRequest) *api.GetProductTemplateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductTemplateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductTemplateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductTemplateParameter provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductTemplateParameter(ctx context.Context, req *api.GetProductTemplateParameterRequest) (*api.GetProductTemplateParameterResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductTemplateParameterResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductTemplateParameterRequest) (*api.GetProductTemplateParameterResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductTemplateParameterRequest) *api.GetProductTemplateParameterResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductTemplateParameterResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductTemplateParameterRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductVariant provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductVariant(ctx context.Context, req *api.GetProductVariantRequest) (*api.GetProductVariantResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductVariantResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantRequest) (*api.GetProductVariantResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantRequest) *api.GetProductVariantResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductVariantResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductVariantRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductVariantByCode provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductVariantByCode(ctx context.Context, req *api.GetProductVariantByCodeRequest) (*api.GetProductVariantByCodeResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductVariantByCodeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantByCodeRequest) (*api.GetProductVariantByCodeResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantByCodeRequest) *api.GetProductVariantByCodeResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductVariantByCodeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductVariantByCodeRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductVariantParameter provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductVariantParameter(ctx context.Context, req *api.GetProductVariantParameterRequest) (*api.GetProductVariantParameterResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductVariantParameterResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantParameterRequest) (*api.GetProductVariantParameterResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantParameterRequest) *api.GetProductVariantParameterResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductVariantParameterResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductVariantParameterRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductVariantTransactionCatalogueInternalAccountMapping provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest) *api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductVariantTransactionCatalogueInternalAccountMappingByKey provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductVariantTransactionCatalogueInternalAccountMappingByKey(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductVariantTransactionCatalogueMapping provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueMappingRequest) (*api.GetProductVariantTransactionCatalogueMappingResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetProductVariantTransactionCatalogueMappingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantTransactionCatalogueMappingRequest) (*api.GetProductVariantTransactionCatalogueMappingResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetProductVariantTransactionCatalogueMappingRequest) *api.GetProductVariantTransactionCatalogueMappingResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetProductVariantTransactionCatalogueMappingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetProductVariantTransactionCatalogueMappingRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTransactionCatalogue provides a mock function with given fields: ctx, req
func (_m *ProductMaster) GetTransactionCatalogue(ctx context.Context, req *api.GetTransactionCatalogueRequest) (*api.GetTransactionCatalogueResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.GetTransactionCatalogueResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionCatalogueRequest) (*api.GetTransactionCatalogueResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetTransactionCatalogueRequest) *api.GetTransactionCatalogueResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetTransactionCatalogueResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetTransactionCatalogueRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListEffectiveProductVariantParameters provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListEffectiveProductVariantParameters(ctx context.Context, req *api.ListEffectiveProductVariantParametersRequest) (*api.ListEffectiveProductVariantParametersResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListEffectiveProductVariantParametersResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListEffectiveProductVariantParametersRequest) (*api.ListEffectiveProductVariantParametersResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListEffectiveProductVariantParametersRequest) *api.ListEffectiveProductVariantParametersResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListEffectiveProductVariantParametersResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListEffectiveProductVariantParametersRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListInternalAccounts provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListInternalAccounts(ctx context.Context, req *api.ListInternalAccountsRequest) (*api.ListInternalAccountsResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListInternalAccountsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListInternalAccountsRequest) (*api.ListInternalAccountsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListInternalAccountsRequest) *api.ListInternalAccountsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListInternalAccountsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListInternalAccountsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListPocketTemplateQuestions provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListPocketTemplateQuestions(ctx context.Context, req *api.ListPocketTemplateQuestionsRequest) (*api.ListPocketTemplateQuestionsResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListPocketTemplateQuestionsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListPocketTemplateQuestionsRequest) (*api.ListPocketTemplateQuestionsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListPocketTemplateQuestionsRequest) *api.ListPocketTemplateQuestionsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListPocketTemplateQuestionsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListPocketTemplateQuestionsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListPocketTemplates provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListPocketTemplates(ctx context.Context, req *api.ListPocketTemplatesRequest) (*api.ListPocketTemplatesResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListPocketTemplatesResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListPocketTemplatesRequest) (*api.ListPocketTemplatesResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListPocketTemplatesRequest) *api.ListPocketTemplatesResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListPocketTemplatesResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListPocketTemplatesRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListProductTemplateParameters provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListProductTemplateParameters(ctx context.Context, req *api.ListProductTemplateParametersRequest) (*api.ListProductTemplateParametersResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListProductTemplateParametersResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductTemplateParametersRequest) (*api.ListProductTemplateParametersResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductTemplateParametersRequest) *api.ListProductTemplateParametersResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListProductTemplateParametersResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListProductTemplateParametersRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListProductVariantParameters provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListProductVariantParameters(ctx context.Context, req *api.ListProductVariantParametersRequest) (*api.ListProductVariantParametersResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListProductVariantParametersResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductVariantParametersRequest) (*api.ListProductVariantParametersResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductVariantParametersRequest) *api.ListProductVariantParametersResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListProductVariantParametersResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListProductVariantParametersRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListProductVariantQuestions provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListProductVariantQuestions(ctx context.Context, req *api.ListProductVariantQuestionsRequest) (*api.ListProductVariantQuestionsResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.ListProductVariantQuestionsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductVariantQuestionsRequest) (*api.ListProductVariantQuestionsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListProductVariantQuestionsRequest) *api.ListProductVariantQuestionsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListProductVariantQuestionsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListProductVariantQuestionsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateGeneralLedgerStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateGeneralLedgerStatus(ctx context.Context, req *api.UpdateGeneralLedgerStatusRequest) (*api.UpdateGeneralLedgerStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateGeneralLedgerStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateGeneralLedgerStatusRequest) (*api.UpdateGeneralLedgerStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateGeneralLedgerStatusRequest) *api.UpdateGeneralLedgerStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateGeneralLedgerStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateGeneralLedgerStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateInterestParameterNotificationStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateInterestParameterNotificationStatus(ctx context.Context, req *api.UpdateInterestParameterNotificationStatusRequest) (*api.UpdateInterestParameterNotificationStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateInterestParameterNotificationStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateInterestParameterNotificationStatusRequest) (*api.UpdateInterestParameterNotificationStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateInterestParameterNotificationStatusRequest) *api.UpdateInterestParameterNotificationStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateInterestParameterNotificationStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateInterestParameterNotificationStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateInterestParameterScheduleStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateInterestParameterScheduleStatus(ctx context.Context, req *api.UpdateInterestParameterScheduleStatusRequest) (*api.UpdateInterestParameterScheduleStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateInterestParameterScheduleStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateInterestParameterScheduleStatusRequest) (*api.UpdateInterestParameterScheduleStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateInterestParameterScheduleStatusRequest) *api.UpdateInterestParameterScheduleStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateInterestParameterScheduleStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateInterestParameterScheduleStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateInternalAccountStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateInternalAccountStatus(ctx context.Context, req *api.UpdateInternalAccountStatusRequest) (*api.UpdateInternalAccountStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateInternalAccountStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateInternalAccountStatusRequest) (*api.UpdateInternalAccountStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateInternalAccountStatusRequest) *api.UpdateInternalAccountStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateInternalAccountStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateInternalAccountStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProductStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateProductStatus(ctx context.Context, req *api.UpdateProductStatusRequest) (*api.UpdateProductStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateProductStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductStatusRequest) (*api.UpdateProductStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductStatusRequest) *api.UpdateProductStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateProductStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateProductStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProductTemplateParameterValue provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateProductTemplateParameterValue(ctx context.Context, req *api.UpdateProductTemplateParameterValueRequest) (*api.UpdateProductTemplateParameterValueResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateProductTemplateParameterValueResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductTemplateParameterValueRequest) (*api.UpdateProductTemplateParameterValueResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductTemplateParameterValueRequest) *api.UpdateProductTemplateParameterValueResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateProductTemplateParameterValueResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateProductTemplateParameterValueRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProductTemplateStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateProductTemplateStatus(ctx context.Context, req *api.UpdateProductTemplateStatusRequest) (*api.UpdateProductTemplateStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateProductTemplateStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductTemplateStatusRequest) (*api.UpdateProductTemplateStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductTemplateStatusRequest) *api.UpdateProductTemplateStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateProductTemplateStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateProductTemplateStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProductVariantParameterValue provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateProductVariantParameterValue(ctx context.Context, req *api.UpdateProductVariantParameterValueRequest) (*api.UpdateProductVariantParameterValueResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateProductVariantParameterValueResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantParameterValueRequest) (*api.UpdateProductVariantParameterValueResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantParameterValueRequest) *api.UpdateProductVariantParameterValueResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateProductVariantParameterValueResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateProductVariantParameterValueRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProductVariantStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateProductVariantStatus(ctx context.Context, req *api.UpdateProductVariantStatusRequest) (*api.UpdateProductVariantStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateProductVariantStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantStatusRequest) (*api.UpdateProductVariantStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantStatusRequest) *api.UpdateProductVariantStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateProductVariantStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateProductVariantStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) (*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) (*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateProductVariantTransactionCatalogueMappingStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateProductVariantTransactionCatalogueMappingStatus(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueMappingStatusRequest) (*api.UpdateProductVariantTransactionCatalogueMappingStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateProductVariantTransactionCatalogueMappingStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantTransactionCatalogueMappingStatusRequest) (*api.UpdateProductVariantTransactionCatalogueMappingStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateProductVariantTransactionCatalogueMappingStatusRequest) *api.UpdateProductVariantTransactionCatalogueMappingStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateProductVariantTransactionCatalogueMappingStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateProductVariantTransactionCatalogueMappingStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateTransactionCatalogueStatus provides a mock function with given fields: ctx, req
func (_m *ProductMaster) UpdateTransactionCatalogueStatus(ctx context.Context, req *api.UpdateTransactionCatalogueStatusRequest) (*api.UpdateTransactionCatalogueStatusResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.UpdateTransactionCatalogueStatusResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateTransactionCatalogueStatusRequest) (*api.UpdateTransactionCatalogueStatusResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateTransactionCatalogueStatusRequest) *api.UpdateTransactionCatalogueStatusResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.UpdateTransactionCatalogueStatusResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateTransactionCatalogueStatusRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListLoanDocumentOptionsByProductVariant provides a mock function with given fields: ctx, req
func (_m *ProductMaster) ListLoanDocumentOptionsByProductVariant(ctx context.Context, req *api.ListLoanDocumentOptionsByProductVariantRequest) (*api.ListLoanDocumentOptionsByProductVariantResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListLoanDocumentOptionsByProductVariant")
	}

	var r0 *api.ListLoanDocumentOptionsByProductVariantResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListLoanDocumentOptionsByProductVariantRequest) (*api.ListLoanDocumentOptionsByProductVariantResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ListLoanDocumentOptionsByProductVariantRequest) *api.ListLoanDocumentOptionsByProductVariantResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ListLoanDocumentOptionsByProductVariantResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ListLoanDocumentOptionsByProductVariantRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewProductMaster creates a new instance of ProductMaster. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewProductMaster(t interface {
	mock.TestingT
	Cleanup(func())
}) *ProductMaster {
	mock := &ProductMaster{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

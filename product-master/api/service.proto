syntax = "proto3";

package productMaster;

option go_package = "gitlab.com/gx-regional/dbmy/core-banking/product-master/api";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "gxs/api/annotations.proto";
import "lending.proto";

message ProductTemplate {
  string id = 1;
  string code = 2;
  string name = 3;
  string description = 4;
  EntityStatus status = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message ProductVariant {
  string id = 1;
  string productID = 2;
  string code = 3;
  string version = 4;
  string name = 5;
  string description = 6;
  EntityStatus status = 7;
  google.protobuf.Timestamp validFrom = 8;
  google.protobuf.Timestamp validTo = 9;
  string createdBy = 10;
  google.protobuf.Timestamp createdAt = 11;
  string updatedBy = 12;
  google.protobuf.Timestamp updatedAt = 13;
}

message Product {
  string id = 1;
  string productTemplateID = 2;
  string code = 3;
  string name = 4;
  string description = 5;
  EntityStatus status = 6;
  string createdBy = 7;
  google.protobuf.Timestamp createdAt = 8;
  string updatedBy = 9;
  google.protobuf.Timestamp updatedAt = 10;
}

message ProductTemplateParameter {
  string id = 1;
  string productTemplateID = 2;
  string namespace = 3;
  string parameterKey = 4;
  string parameterValue = 5;
  ParameterDataType dataType = 6;
  ParameterOverrideLevel overrideLevel = 7;
  string exceptionLevel = 8;
  string description = 9;
  string createdBy = 10;
  google.protobuf.Timestamp createdAt = 11;
  string updatedBy = 12;
  google.protobuf.Timestamp updatedAt = 13;
}

message ProductVariantParameter {
  string id = 1;
  string productVariantID = 2;
  string namespace = 3;
  string parameterKey = 4;
  string parameterValue = 5;
  ParameterDataType dataType = 6;
  ParameterOverrideLevel overrideLevel = 7;
  string exceptionLevel = 8;
  string description = 9;
  string createdBy = 10;
  google.protobuf.Timestamp createdAt = 11;
  string updatedBy = 12;
  google.protobuf.Timestamp updatedAt = 13;
}

message InterestRateParameter {
  string namespace = 3;
  string parameterKey = 4;
  string parameterValue = 5;
  ParameterDataType dataType = 6;
  string description = 9;
}

message TransactionCatalogue {
  string id = 1;
  string domain = 2;
  bool isFinancialTxn = 3;
  string txnType = 4;
  string txnSubType = 5;
  string displayName = 6;
  string description = 7;
  EntityStatus status = 8;
  string createdBy = 9;
  google.protobuf.Timestamp createdAt = 10;
  string updatedBy = 11;
  google.protobuf.Timestamp updatedAt = 12;
}

message ProductVariantTransactionCatalogueMapping {
  string id = 1;
  string productVariantID = 2;
  string transactionCatalogueID = 3;
  EntityStatus status = 4;
  string createdBy = 5;
  google.protobuf.Timestamp createdAt = 6;
  string updatedBy = 7;
  google.protobuf.Timestamp updatedAt = 8;
}

message GeneralLedger {
  string id = 1;
  string code = 2;
  string name = 3;
  string description = 4;
  Currency currency = 5;
  EntityStatus status = 6;
  string createdBy = 7;
  google.protobuf.Timestamp createdAt = 8;
  string updatedBy = 9;
  google.protobuf.Timestamp updatedAt = 10;
}

message InternalAccount {
  string id = 1;
  string generalLedgerID = 2;
  string code = 3;
  string name = 4;
  string description = 5;
  Currency currency = 6;
  EntityStatus status = 7;
  string createdBy = 8;
  google.protobuf.Timestamp createdAt = 9;
  string updatedBy = 10;
  google.protobuf.Timestamp updatedAt = 11;
}

message ProductVariantTransactionCatalogueInternalAccountMapping{
  string id = 1;
  string internalAccountID = 2;
  string productVariantTransactionCatalogueMappingID = 3;
  string identifierKey = 4;
  EntityStatus status = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message BaseInterest {
  string id = 1;
  string code = 2;
  string name = 3;
  string description = 4;
  Currency currency = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message BaseInterestVersion {
  string id = 1;
  string baseInterestID = 2;
  string version = 3;
  google.protobuf.Timestamp effectiveDate = 4;
  string description = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message BaseInterestTimeSlabRate {
  string id = 1;
  string baseInterestVersionID = 2;
  TermUnit termUnit = 3;
  int32 termValue = 4;
  string baseInterestPercentage = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message DepositInterest {
  string id = 1;
  string productVariantID = 2;
  bool isLinkedToBaseRate = 3;
  string baseInterestID = 4;
  string code = 5;
  string name = 6;
  string description = 7;
  Currency currency = 8;
  RoundOffType roundOffType = 9;
  InterestSlabType interestSlabType = 10;
  InterestSlabStructure interestSlabStructure = 11;
  string createdBy = 12;
  google.protobuf.Timestamp createdAt = 13;
  string updatedBy = 14;
  google.protobuf.Timestamp updatedAt = 15;
}

message DepositInterestVersion {
  string id = 1;
  string depositInterestID = 2;
  string version = 3;
  google.protobuf.Timestamp effectiveDate = 4;
  string description = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message DepositInterestAmountSlabRate {
  string id = 1;
  string depositInterestVersionID = 2;
  string fromAmount = 3;
  string toAmount = 4;
  string baseRateInterestSpreadPercentage = 5;
  string absoluteInterestRatePercentage = 6;
  string createdBy = 7;
  google.protobuf.Timestamp createdAt = 8;
  string updatedBy = 9;
  google.protobuf.Timestamp updatedAt = 10;
}

message DepositsInterestAmountSlabRate {
  // Min is deprecated. Will be removed in v2. Use minAmount property instead.
  string min = 1 [deprecated = true];
  // Max is deprecated. Will be removed in v2. Use maxAmount property instead.
  string max = 2 [deprecated = true];
  string rate = 3;
  string minTenor = 4;
  string maxTenor = 5;
  string minTenorUnit = 6;
  string maxTenorUnit = 7;
  string minAmount = 8;
  string maxAmount = 9;
}

message DepositsAccountInterestParameters {
  InterestRateType interestRateType = 1;
  DepositsAccountFlatInterest flatInterest = 2;
}

message DepositsAccountFlatInterest {
  int32 rate = 1;
  int32 multiplier = 2;
}

message SavingsPocketInterestParameters {
  InterestRateType interestRateType = 1;
  SavingsPocketFlatInterest flatInterest = 2;
}

message SavingsPocketFlatInterest {
  int32 rate = 1;
  int32 multiplier = 2;
}

message PocketTemplate {
  string ID = 1;
  PocketType type = 2;
  string name = 3;
  repeated Image images = 4;
  Image defaultImage = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message PocketTemplateQuestionsDetail {
  string pocketTemplateID = 1;
  Locale locale = 2;
  repeated QuestionAnswerPairsResponse questionAnswerPairs = 3;
  repeated Image images = 4;
  Image defaultImage = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}

message QuestionAnswerPairsRequest {
  string questionText = 1;
  repeated string answerSuggestions = 2;
}

message QuestionAnswerPairsResponse {
  string ID = 1;
  string questionText = 2;
  repeated string answerSuggestions = 3;
}

message Image {
  string ID = 1;
  string URL = 2;
}

enum EntityStatus {
  ACTIVE = 0;
  INACTIVE = 1;
}

enum ParameterOverrideLevel{
  NO_OVERRIDE = 0;
  AT_PRODUCT_VARIANT = 1;
  AT_CUSTOMER = 2;
  AT_ACCOUNT = 3;
}

enum ParameterDataType{
  INT = 0;
  FLOAT = 1;
  BOOL = 2;
  STRING = 3;
  ARRAY = 4;
  JSON = 5;
}

enum Currency{
  SGD = 0;
  IDR = 1;
  MYR = 2;
}

enum RoundOffType {
  FLOOR = 0;
  CEIL = 1;
  ROUND = 2;
}

enum InterestSlabType {
  AMOUNT = 0;
  TIME = 1;
  BOTH = 2;
}

enum InterestSlabStructure {
  ABSOLUTE = 0;
  INCREMENTAL = 1;
}

enum TermUnit{
  DAY = 0;
  WEEK = 1;
  MONTH = 2;
}

enum PocketType {
  SAVINGS = 0;
  SAVINGS_POCKET = 1;
  BOOST_POCKET = 2;
}

enum Locale {
  EN = 0;
  MS = 1;
}

enum InterestRateType {
  flat = 0;
  tier = 1;
}

enum TenorType {
  SPOT = 0;
  RANGE = 1;
}

// Represents an amount of money with its currency type.
message Money {
  // The three-letter currency code defined in ISO 4217.
  string currencyCode = 1 [(gxs.api.noomit)=true];
  // Monetary value using a currency's smallest unit
  int64 val = 2 [(gxs.api.noomit)=true];
}

enum ProductVariantCode {
  DEPOSITS_ACCOUNT = 0;
  SAVINGS_POCKET = 1;
  DEFAULT_FLEXI_LOAN_TERM_LOAN = 2;
  BIZ_DEPOSIT_ACCOUNT = 3;
  DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT = 4;
  DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT = 5;
  DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN = 6;
  BOOST_POCKET = 7;
}

enum ProductCode {
  FLEXI_LOAN_TERM_LOAN = 0;
  BIZ_FLEXI_CREDIT_TERM_LOAN=1;
}

enum InterestType {
  NORMAL = 0;
  PENAL = 1;
}

message CreateProductTemplateRequest {
  string code = 1;
  string name = 2;
  string description = 3;
  string createdBy = 4;
}

message CreateProductTemplateResponse {
  ProductTemplate productTemplate = 1;
}

message GetProductTemplateRequest {
  string id = 1;
}

message GetProductTemplateResponse {
  ProductTemplate productTemplate = 1;
}

message UpdateProductTemplateStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateProductTemplateStatusResponse {
  ProductTemplate productTemplate = 1;
}

message CreateProductVariantRequest {
  string code = 1;
  string version = 2;
  string name = 3;
  string productID = 4;
  string description = 5;
  google.protobuf.Timestamp validFrom = 6;
  google.protobuf.Timestamp validTo = 7;
  string  createdBy = 8;
}

message CreateProductVariantResponse {
  ProductVariant productVariant = 1;
}

message ListProductVariantsRequest {
  string productTemplateCode = 1;
  string productCode = 2;
  string code = 3;
}

message ListProductVariantsResponse {
  repeated ProductVariant productVariants = 1;
}

message GetProductVariantRequest {
  string id = 1;
}

message GetProductVariantResponse {
  ProductVariant productVariant = 1;
}

message GetProductVariantByCodeRequest {
  string code = 1;
}

message GetProductVariantByCodeResponse {
  ProductVariant productVariant = 1;
}

message UpdateProductVariantStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateProductVariantStatusResponse {
  ProductVariant productVariant = 1;
}

message CreateProductRequest {
  string productTemplateID = 1;
  string code = 2;
  string name = 3;
  string description = 4;
  string createdBy = 5;
}

message CreateProductResponse {
  Product product = 1;
}

message GetProductRequest {
  string id = 1;
}

message GetProductResponse {
  Product product = 1;
}

message GetProductByCodeRequest {
  string code = 1;
}

message GetProductByCodeResponse {
  Product product = 1;
}

message UpdateProductStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateProductStatusResponse {
  Product product = 1;
}


message CreateProductTemplateParameterRequest {
  string productTemplateID = 1;
  string namespace = 2;
  string parameterKey = 3;
  string parameterValue = 4;
  ParameterDataType dataType = 5;
  ParameterOverrideLevel overrideLevel = 6;
  string exceptionLevel = 7;
  string description = 8;
  string createdBy = 9;
}

message CreateProductTemplateParameterResponse {
  ProductTemplateParameter productTemplateParameter = 1;
}

message GetProductTemplateParameterRequest {
  string id = 1;
}

message GetProductTemplateParameterResponse {
  ProductTemplateParameter productTemplateParameter = 1;
}

message ListProductTemplateParametersRequest {
  string productTemplateID = 1;
}

message ListProductTemplateParametersResponse {
  repeated ProductTemplateParameter productTemplateParameters = 1;
}

message UpdateProductTemplateParameterValueRequest {
  string id = 1;
  string parameterValue = 2;
  string updatedBy = 3;
}

message UpdateProductTemplateParameterValueResponse {
  ProductTemplateParameter productTemplateParameter = 1;
}

message CreateProductVariantParameterRequest {
  string productVariantID = 1;
  string namespace = 2;
  string parameterKey = 3;
  string parameterValue = 4;
  ParameterDataType dataType = 5;
  ParameterOverrideLevel overrideLevel = 6;
  string exceptionLevel = 7;
  string description = 8;
  string createdBy = 9;
}

message CreateProductVariantParameterResponse {
  ProductVariantParameter productVariantParameter = 1;
}

message GetProductVariantParameterRequest {
  string id = 1;
}

message GetProductVariantParameterResponse {
  ProductVariantParameter productVariantParameter = 1;
}

message ListProductVariantParametersRequest {
  string productVariantID = 1;
}

message ListProductVariantParametersResponse {
  repeated ProductVariantParameter productVariantParameters = 1;
}

message ListEffectiveProductVariantParametersRequest {
  string productVariantCode = 1;
  bool includeInterestRateParameters = 2;
  bool includeAdditionalLoanParameters = 3;
}

message ListEffectiveProductVariantParametersResponse {
  repeated ProductVariantParameter productVariantParameters = 1;
  repeated InterestRateParameter interestRateParameters = 2;
  LoanParametersDetail loanParameters = 3;
}

message LoanParametersDetail  {
  repeated LoanAllowedAmountTenorSlab loanAllowedAmountTenorSlab = 1;
}

message LoanAllowedAmountTenorSlab {
  string fromAmount = 1;
  string toAmount = 2;
  string tenorUnit = 3;
  string minTenor = 4;
  string maxTenor = 5;
  string currency = 6;
}

message UpdateProductVariantParameterValueRequest {
  string id = 1;
  string parameterValue = 2;
  string updatedBy = 3;
}

message UpdateProductVariantParameterValueResponse {
  ProductVariantParameter productVariantParameter = 1;
}

message CreateTransactionCatalogueRequest {
  string domain = 1;
  bool isFinancialTxn = 2;
  string txnType = 3;
  string txnSubType = 4;
  string displayName = 5;
  string description = 6;
  string createdBy = 8;
}

message CreateTransactionCatalogueResponse {
  TransactionCatalogue transactionCatalogue = 1;
}

message GetTransactionCatalogueRequest {
  string id = 1;
}

message GetTransactionCatalogueResponse {
  TransactionCatalogue transactionCatalogue = 1;
}

message UpdateTransactionCatalogueStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateTransactionCatalogueStatusResponse {
  TransactionCatalogue transactionCatalogue = 1;
}

message CreateProductVariantTransactionCatalogueMappingRequest {
  string productVariantID = 1;
  string transactionCatalogueID = 2;
  string createdBy = 3;
}

message CreateProductVariantTransactionCatalogueMappingResponse {
  ProductVariantTransactionCatalogueMapping productVariantTransactionCatalogueMapping = 1;
}

message GetProductVariantTransactionCatalogueMappingRequest {
  string id = 1;
}

message GetProductVariantTransactionCatalogueMappingResponse {
  ProductVariantTransactionCatalogueMapping productVariantTransactionCatalogueMapping = 1;
}

message UpdateProductVariantTransactionCatalogueMappingStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateProductVariantTransactionCatalogueMappingStatusResponse {
  ProductVariantTransactionCatalogueMapping productVariantTransactionCatalogueMapping = 1;
}

message CreateGeneralLedgerRequest {
  string code = 1;
  string name = 2;
  string description = 3;
  Currency currency = 4;
  string createdBy = 5;
}

message CreateGeneralLedgerResponse {
  GeneralLedger generalLedger = 1;
}

message GetGeneralLedgerRequest {
  string id = 1;
}

message GetGeneralLedgerResponse {
  GeneralLedger generalLedger = 1;
}

message GetGeneralLedgerByCodeRequest {
  string code = 1;
}

message GetGeneralLedgerByCodeResponse {
  GeneralLedger generalLedger = 1;
}

message UpdateGeneralLedgerStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateGeneralLedgerStatusResponse {
  GeneralLedger generalLedger = 1;
}

message CreateInternalAccountRequest {
  string generalLedgerID = 1;
  string code = 2;
  string name = 3;
  string description = 4;
  Currency currency = 5;
  string createdBy = 6;
}

message CreateInternalAccountResponse {
  InternalAccount internalAccount = 1;
}

message GetInternalAccountRequest {
  string id = 1;
}

message GetInternalAccountResponse {
  InternalAccount internalAccount = 1;
}

message ListInternalAccountsRequest {
  string code = 1;
  string generalLedgerID = 2;
}

message ListInternalAccountsResponse {
  repeated InternalAccount internalAccounts = 1;
}

message UpdateInternalAccountStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateInternalAccountStatusResponse {
  InternalAccount internalAccount = 1;
}

message CreateProductVariantTransactionCatalogueInternalAccountMappingRequest {
  string internalAccountID = 1;
  string productVariantTransactionCatalogueMappingID = 2;
  string identifierKey = 3;
  string createdBy = 6;
}

message CreateProductVariantTransactionCatalogueInternalAccountMappingResponse {
  ProductVariantTransactionCatalogueInternalAccountMapping productVariantTransactionCatalogueInternalAccountMapping = 1;
}

message GetProductVariantTransactionCatalogueInternalAccountMappingRequest {
  string id = 1;
}

message GetProductVariantTransactionCatalogueInternalAccountMappingResponse {
  ProductVariantTransactionCatalogueInternalAccountMapping productVariantTransactionCatalogueInternalAccountMapping = 1;
}

message GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest {
  string identifierKey = 1;
}

message GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse {
  ProductVariantTransactionCatalogueInternalAccountMapping productVariantTransactionCatalogueInternalAccountMapping = 1;
}

message UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest {
  string id = 1;
  EntityStatus status = 2;
  string updatedBy = 3;
}

message UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse {
  ProductVariantTransactionCatalogueInternalAccountMapping productVariantTransactionCatalogueInternalAccountMapping = 1;
}

message CreateBaseInterestRequest {
  string code = 1;
  string name = 2;
  string description = 3;
  Currency currency = 4;
  string createdBy = 5;
}

message CreateBaseInterestResponse {
  BaseInterest baseInterest = 1;
}

message GetBaseInterestRequest {
  string id = 1;
}

message GetBaseInterestResponse {
  BaseInterest baseInterest = 1;
}

message GetBaseInterestByCodeRequest {
  string code = 1;
}

message GetBaseInterestByCodeResponse {
  BaseInterest baseInterest = 1;
}

message CreateBaseInterestVersionRequest {
  string baseInterestID = 1;
  string version = 2;
  google.protobuf.Timestamp effectiveDate = 3;
  string description = 4;
  string createdBy = 5;
}

message CreateBaseInterestVersionResponse {
  BaseInterestVersion baseInterestVersion = 1;
}

message GetBaseInterestVersionRequest {
  string id = 1;
}

message GetBaseInterestVersionResponse {
  BaseInterestVersion baseInterestVersion = 1;
}

message CreateBaseInterestTimeSlabRateRequest {
  string baseInterestVersionID = 1;
  TermUnit termUnit = 2;
  int32 termValue = 3;
  string baseInterestPercentage = 4;
  string createdBy = 5;
}

message CreateBaseInterestTimeSlabRateResponse {
  BaseInterestTimeSlabRate baseInterestTimeSlabRate = 1;
}

message GetBaseInterestTimeSlabRateRequest {
  string id = 1;
}

message GetBaseInterestTimeSlabRateResponse {
  BaseInterestTimeSlabRate baseInterestTimeSlabRate = 1;
}

message CreateDepositInterestRequest {
  string productVariantID = 1;
  bool isLinkedToBaseRate = 2;
  string baseInterestID = 3;
  string code = 4;
  string name = 5;
  string description = 6;
  Currency currency = 7;
  RoundOffType roundOffType = 8;
  InterestSlabType interestSlabType = 9;
  InterestSlabStructure interestSlabStructure = 10;
  string createdBy = 11;
}

message CreateDepositInterestResponse {
  DepositInterest depositInterest = 1;
}

message GetDepositInterestRequest {
  string id = 1;
}

message GetDepositInterestResponse {
  DepositInterest depositInterest = 1;
}

message GetDepositInterestByCodeRequest {
  string code = 1;
}

message GetDepositInterestByCodeResponse {
  DepositInterest depositInterest = 1;
}

message CreateDepositInterestVersionRequest {
  string depositInterestID = 1;
  string version = 2;
  google.protobuf.Timestamp effectiveDate = 3;
  string description = 4;
  string createdBy = 5;
}

message CreateDepositInterestVersionResponse {
  DepositInterestVersion depositInterestVersion = 1;
}

message GetDepositInterestVersionRequest {
  string id = 1;
}

message GetDepositInterestVersionResponse {
  DepositInterestVersion depositInterestVersion = 1;
}


message CreateDepositInterestAmountSlabRateRequest {
  string depositInterestVersionID = 1;
  string fromAmount = 2;
  string toAmount = 3;
  string baseRateInterestSpreadPercentage = 4;
  string absoluteInterestRatePercentage = 5;
  string createdBy = 6;
}

message CreateDepositInterestAmountSlabRateResponse {
  DepositInterestAmountSlabRate depositInterestAmountSlabRate = 1;
}

message GetDepositInterestAmountSlabRateRequest {
  string id = 1;
}

message GetDepositInterestAmountSlabRateResponse {
  DepositInterestAmountSlabRate depositInterestAmountSlabRate = 1;
}

message CreatePocketTemplateRequest {
  PocketType type = 1;
  string name = 2;
  repeated string imageIDs = 3;
  string createdBy = 4;
}

message CreatePocketTemplateResponse {
  PocketTemplate pocketTemplate = 1;
}

message ListPocketTemplatesRequest {
  PocketType type = 1;
}

message ListPocketTemplatesResponse {
  repeated PocketTemplate pocketTemplates = 1;
}

message GetPocketTemplateRequest {
  string id = 1;
}

message GetPocketTemplateResponse {
  PocketTemplate pocketTemplate = 1;
}

message CreatePocketTemplateQuestionsRequest {
  string pocketTemplateID = 1;
  Locale locale = 2;
  repeated QuestionAnswerPairsRequest questionAnswerPairs = 3;
  string createdBy = 4;
}

message CreatePocketTemplateQuestionsResponse {
  PocketTemplateQuestionsDetail pocketTemplateQuestionsDetail = 1;
}

message ListPocketTemplateQuestionsRequest {
  string pocketTemplateID = 1;
}

message  ListPocketTemplateQuestionsResponse{
  PocketTemplateQuestionsDetail pocketTemplateQuestionsDetail = 1;
}

enum Status {
  N = 0;
  Y = 1;
}

message GetInterestParametersRequest {
  string parameterKey = 1;
  Status isNotified = 2;
  Status isScheduled = 3;
}

message GetInterestParametersResponse {
  string parameterKey = 1;
  string parameterValue = 2;
  string parameterVersion = 3;
  string smartContractVersionID = 4;
  google.protobuf.Timestamp effectiveScheduleDate = 5;
  google.protobuf.Timestamp effectiveNotificationDate = 6;
}

message UpdateInterestParameterNotificationStatusRequest {
  string parameterKey = 1;
  string parameterVersion = 2;
  string smartContractVersionID = 3;
  string updatedBy=4;
  Status isNotified=5;
}

message UpdateInterestParameterNotificationStatusResponse {
  Status isNotified = 1;
}

message UpdateInterestParameterScheduleStatusRequest {
  string parameterKey = 1;
  string parameterVersion = 2;
  string smartContractVersionID = 3;
  string updatedBy=4;
  Status isScheduled=5;
}

message UpdateInterestParameterScheduleStatusResponse {
  Status isScheduled = 1;
}

message GetInterestParametersByProductVariantRequest {
  repeated InterestParametersByProductVariantRequest interestParamRequest = 1;
}

message InterestParametersByProductVariantRequest {
  ProductVariantCode productVariantCode = 1;
  string productVariantVersion = 2;
  string interestVersion = 3;
}

message GetInterestParametersByProductVariantResponse {
  DepositsAccountInterestParameters depositsAccount = 1;
  SavingsPocketInterestParameters savingsPocket = 2;
  LoanAccountInterestParameters loanAccount = 3;
  BoostPocketInterestParameters boostPocket = 4;
}

// QuestionAnswerPairsDetailsResponse ...
message QuestionAnswerPairsDetailsResponse {
  string ID = 1;
  string questionText = 2;
  repeated ProductVariantAnswerSuggestion answerSuggestions = 3;
  string createdBy = 4;
  google.protobuf.Timestamp createdAt = 5;
  string updatedBy = 6;
  google.protobuf.Timestamp updatedAt = 7;
  Locale locale = 8;
  string code = 9;
}

message ProductVariantAnswerSuggestion  {
  string code = 1;
  string text = 2;
}

// ProductVariantQuestionsDetail ...
message ProductVariantQuestionsDetail {
  string productVariantCode = 1;
  Locale locale = 2;
  repeated QuestionAnswerPairsDetailsResponse questionAnswerPairs = 3;
  string productVariantVersion = 4;
}

// ProductVariantQuestionAnswerPairsRequest
message ProductVariantQuestionAnswerPairsRequest {
  string code = 1;
  string questionText = 2;
  repeated ProductVariantAnswerSuggestion answerSuggestions = 3;
}

// CreateProductVariantQuestionRequest ...
message CreateProductVariantQuestionRequest {
  string productVariantCode = 1;
  string productVariantVersion = 2;
  Locale locale = 3;
  repeated ProductVariantQuestionAnswerPairsRequest questionAnswerPairs = 4;
  string createdBy = 5;
}

// CreateProductVariantQuestionResponse ...
message CreateProductVariantQuestionResponse {
  ProductVariantQuestionsDetail questionsDetail = 1;
}

// ListProductVariantQuestionsRequest ...
message ListProductVariantQuestionsRequest {
  string productVariantCode = 1;
  string productVariantVersion = 2;
  string code = 3;
}

// ListProductVariantQuestionsResponse ...
message  ListProductVariantQuestionsResponse{
  ProductVariantQuestionsDetail questionsDetail = 1;
}

// CreateLoanInterestRequest request format to create loan interest
message CreateLoanInterestRequest {
  string productVariantID = 1;
  bool isLinkedToBaseRate = 2;
  string baseInterestID = 3;
  string code = 4;
  string name = 5;
  string description = 6;
  Currency currency = 7;
  RoundOffType roundOffType = 8;
  InterestType interestType = 9;
  InterestSlabType interestSlabUnitType = 10;
  InterestSlabStructure interestSlabStructure = 11;
  string createdBy = 12;
}

// CreateLoanInterestResponse ...
message CreateLoanInterestResponse {
  LoanInterest loanInterest = 1;
}

// LoanInterest : format of loan interest response
message LoanInterest {
  string id = 1;
  string productVariantID = 2;
  bool isLinkedToBaseRate = 3;
  string baseInterestID = 4;
  string code = 5;
  string name = 6;
  string description = 7;
  Currency currency = 8;
  RoundOffType roundOffType = 9;
  InterestType interestType = 10;
  InterestSlabType interestSlabUnitType = 11;
  InterestSlabStructure interestSlabStructure = 12;
  string createdBy = 13;
  google.protobuf.Timestamp createdAt = 14;
  string updatedBy = 15;
  google.protobuf.Timestamp updatedAt = 16;
}

// GetLoanInterestByIDRequest : request format to get interest by ID
message GetLoanInterestByIDRequest {
  string id = 1;
}

// GetLoanInterestByIDResponse : response format containing loan interest
message GetLoanInterestByIDResponse {
  LoanInterest loanInterest = 1;
}

// GetLoanInterestByCodeRequest : request format to get interest by code
message GetLoanInterestByCodeRequest {
  string code = 1;
}

// GetLoanInterestByCodeResponse : ...
message GetLoanInterestByCodeResponse {
  LoanInterest loanInterest = 1;
}

// GetLoanPastDueParametersByProductCodeRequest : request format to get loan past due parameters by product code
message GetLoanPastDueParametersByProductCodeRequest {
  ProductCode productCode = 1;
  string loanPastDueVersion = 2;
}

// GetLoanPastDueParametersByProductCodeResponse : response format to get loan past due parameters by product code
message GetLoanPastDueParametersByProductCodeResponse {
  ProductCode productCode = 1;
  string loanPastDueVersion = 2;
  repeated LoanPastDueSlab loanPastDueSlab = 3;
}

message LoanPastDueSlab {
  string id = 1;
  int32  fromUnit = 2;
  int32  toUnit = 3;
  string slabType = 4;
  string bucketName = 5;
  string createdBy = 6;
  google.protobuf.Timestamp createdAt = 7;
  string updatedBy = 8;
  google.protobuf.Timestamp updatedAt = 9;
}


// CreateLoanInstructionVersionRequest : request format to create loan instruction version
message CreateLoanInstructionVersionRequest {
  string productVariantCode = 1;
  string productVariantVersion = 2;
  string version = 3;
  string instructionType = 4;
  string description = 5;
  google.protobuf.Timestamp effectiveDate = 6;
  string createdBy = 7;
}

// CreateLoanInstructionVersionResponse : response format to create loan instruction version
message CreateLoanInstructionVersionResponse {
  LoanInstructionVersion loanInstructionVersion = 1;
}

// CreateLoanInstructionRequest : request format to create loan instruction
message CreateLoanInstructionRequest {
  string loanInstructionVersionID = 1;
  string code = 2;
  string name = 3;
  string createdBy = 4;
}

// CreateLoanInstructionResponse : response format to create loan instruction
message CreateLoanInstructionResponse {
  LoanInstruction loanInstruction = 1;
}

// GetLoanInstructionsByCodeRequest : request format to get loan instructions by product variant code and instruction type
message GetLoanInstructionsByCodeRequest {
  string productVariantCode = 1;
  string version = 2;
  string instructionType = 3;
}

// GetLoanInstructionsByCodeResponse : response format to get loan instructions by product variant code and instruction type
message GetLoanInstructionsByCodeResponse {
  string productVariantCode = 1;
  string version = 2;
  string instructionType = 3;
  repeated LoanInstruction loanInstruction = 4;
}

message LoanInstructionVersion{
  string id = 1;
  string productVariantCode = 2;
  string version = 3;
  string instructionType = 5;
  string description = 6;
  google.protobuf.Timestamp effectiveDate = 7;
  string createdBy = 8;
  google.protobuf.Timestamp createdAt = 9;
  string updatedBy = 10;
  google.protobuf.Timestamp updatedAt = 11;
}

message LoanInstruction {
  string id = 1;
  string code = 2;
  string name = 3;
  string createdBy = 4;
  google.protobuf.Timestamp createdAt = 5;
  string updatedBy = 6;
  google.protobuf.Timestamp updatedAt = 7;
  google.protobuf.Struct restrictions = 8;
}

message LoanAccountInterestParameters {
  repeated LoanAccountFlatInterest normalInterest = 1;
  repeated LoanAccountFlatInterest penalInterest = 2;
  repeated LoanAccountFlatInterest discoveryInterest = 3;
}

message LoanAccountFlatInterest {
  int32 rate = 1;
  int32 multiplier = 2;
  string slabType = 3;
  string fromUnit = 4;
  string toUnit = 5;
}

message BoostPocketInterestParameters {
  string maxInterestRateOffered = 1;
  InterestRateType interestRateType = 2;
  TenorType tenorType = 3;
  repeated BoostPocketTierInterest tierInterest = 4;
}

message BoostPocketTierInterest {
  int32 baseInterestRate = 1;
  int32 bonusInterestRate = 2;
  int32 totalInterestRate = 3;
  int32 multiplier = 4;
  Money fromAmount = 5 [(gxs.api.noomit)=true];
  Money toAmount = 6 [(gxs.api.noomit)=true];
  string tenor = 7 [(gxs.api.noomit)=true];
  string tenorUnit = 8 [(gxs.api.noomit)=true];
  string maxTenor = 9;
  string maxTenorUnit = 10;
}

// ProductMaster ...
service ProductMaster {
  // CreateProductTemplate creates a new Product Template
  rpc CreateProductTemplate(CreateProductTemplateRequest) returns (CreateProductTemplateResponse) {
    option (google.api.http) = {
      post: "/v1/product-templates",
      body: "*",
    };
  }

  // GetProductTemplate fetches a product template by id
  rpc GetProductTemplate (GetProductTemplateRequest) returns (GetProductTemplateResponse) {
    option (google.api.http) = {
      get: "/v1/product-templates/{id}",
      body: "*",
    };
  }

  // UpdateProductTemplateStatus updates the status of Product Template
  rpc UpdateProductTemplateStatus(UpdateProductTemplateStatusRequest) returns (UpdateProductTemplateStatusResponse) {
    option (google.api.http) = {
      put: "/v1/product-templates/{id}/status",
      body: "*",
    };
  }

  // CreateProductVariant creates a new Product Variant
  rpc CreateProductVariant(CreateProductVariantRequest) returns (CreateProductVariantResponse) {
    option (google.api.http) = {
      post: "/v1/product-variants",
      body: "*",
    };
  }

  // ListProductVariants fetches a product variant by id
  rpc ListProductVariants (ListProductVariantsRequest) returns (ListProductVariantsResponse) {
    option (google.api.http) = {
      get: "/v2/product-variants"
    };
  }

  // GetProductVariant fetches a product variant by id
  rpc GetProductVariant (GetProductVariantRequest) returns (GetProductVariantResponse) {
    option (google.api.http) = {
      get: "/v1/product-variants/{id}",
      body: "*",
    };
  }

  // GetProductVariantByCode fetches a product variant by code
  rpc GetProductVariantByCode (GetProductVariantByCodeRequest) returns (GetProductVariantByCodeResponse) {
    option (google.api.http) = {
      get: "/v1/product-variants",
      body: "*",
    };
  }

  // UpdateProductVariantStatus updates the status of Product Variant
  rpc UpdateProductVariantStatus(UpdateProductVariantStatusRequest) returns (UpdateProductVariantStatusResponse) {
    option (google.api.http) = {
      put: "/v1/product-variants/{id}/status",
      body: "*",
    };
  }

  // CreateProduct creates a new Product
  rpc CreateProduct(CreateProductRequest) returns (CreateProductResponse) {
    option (google.api.http) = {
      post: "/v1/products",
      body: "*",
    };
  }

  // GetProduct fetches a product by id
  rpc GetProduct (GetProductRequest) returns (GetProductResponse) {
    option (google.api.http) = {
      get: "/v1/products/{id}",
      body: "*",
    };
  }

  // GetProduct fetches a product by code
  rpc GetProductByCode (GetProductByCodeRequest) returns (GetProductByCodeResponse) {
    option (google.api.http) = {
      get: "/v1/products",
      body: "*",
    };
  }

  // UpdateProductStatus updates the status of Product Template
  rpc UpdateProductStatus(UpdateProductStatusRequest) returns (UpdateProductStatusResponse) {
    option (google.api.http) = {
      put: "/v1/products/{id}/status",
      body: "*",
    };
  }

  // CreateProductTemplateParameter creates a new Product Template Parameter
  rpc CreateProductTemplateParameter(CreateProductTemplateParameterRequest) returns (CreateProductTemplateParameterResponse) {
    option (google.api.http) = {
      post: "/v1/product-template-parameters",
      body: "*",
    };
  }

  // GetProductTemplateParameter fetches a product template parameter by id
  rpc GetProductTemplateParameter (GetProductTemplateParameterRequest) returns (GetProductTemplateParameterResponse) {
    option (google.api.http) = {
      get: "/v1/product-template-parameters/{id}",
      body: "*",
    };
  }
  // ListProductTemplateParameters fetches all product template parameters for a product variant
  rpc ListProductTemplateParameters (ListProductTemplateParametersRequest) returns (ListProductTemplateParametersResponse) {
    option (google.api.http) = {
      get: "/v1/product-template-parameters",
      body: "*",
    };
  }

  // UpdateProductTemplateParameterValue updates the value of Product Template Parameter Value
  rpc UpdateProductTemplateParameterValue(UpdateProductTemplateParameterValueRequest) returns (UpdateProductTemplateParameterValueResponse) {
    option (google.api.http) = {
      put: "/v1/product-template-parameters/{id}/value",
      body: "*",
    };
  }

  // CreateProductVariantParameter creates a new Product Variant Parameter
  rpc CreateProductVariantParameter(CreateProductVariantParameterRequest) returns (CreateProductVariantParameterResponse) {
    option (google.api.http) = {
      post: "/v1/product-variant-parameters",
      body: "*",
    };
  }

  // GetProductVariantParameter fetches a product variant parameter by id
  rpc GetProductVariantParameter (GetProductVariantParameterRequest) returns (GetProductVariantParameterResponse) {
    option (google.api.http) = {
      get: "/v1/product-variant-parameters/{id}",
      body: "*",
    };
  }

  // ListProductVariantParameters fetches all product variant parameters for a product variant
  rpc ListProductVariantParameters (ListProductVariantParametersRequest) returns (ListProductVariantParametersResponse) {
    option (google.api.http) = {
      get: "/v1/product-variant-parameters",
      body: "*",
    };
  }

  // ListEffectiveProductVariantParameters fetches all product variant parameters for a product variant (includes the overrides from product-template-parameters
  rpc ListEffectiveProductVariantParameters (ListEffectiveProductVariantParametersRequest) returns (ListEffectiveProductVariantParametersResponse) {
    option (google.api.http) = {
      post: "/v2/effective-product-variant-parameters/get",
      body: "*",
    };
  }

  // UpdateProductVariantParameterValue updates the value of Product Variant Parameter Value
  rpc UpdateProductVariantParameterValue(UpdateProductVariantParameterValueRequest) returns (UpdateProductVariantParameterValueResponse) {
    option (google.api.http) = {
      put: "/v1/product-variant-parameters/{id}/value",
      body: "*",
    };
  }

  // CreateTransactionCatalogue creates a new Transaction catalogue
  rpc CreateTransactionCatalogue (CreateTransactionCatalogueRequest) returns (CreateTransactionCatalogueResponse) {
    option (google.api.http) = {
      post: "/v1/transaction-catalogue",
      body: "*",
    };
  }

  // GetTransactionCatalogue fetches a transaction catalogue by id
  rpc GetTransactionCatalogue (GetTransactionCatalogueRequest) returns (GetTransactionCatalogueResponse) {
    option (google.api.http) = {
      get: "/v1/transaction-catalogue/{id}",
      body: "*",
    };
  }

  // UpdateTransactionCatalogueStatus updates the status of Transaction catalogue
  rpc UpdateTransactionCatalogueStatus(UpdateTransactionCatalogueStatusRequest) returns (UpdateTransactionCatalogueStatusResponse) {
    option (google.api.http) = {
      put: "/v1/transaction-catalogue/{id}/status",
      body: "*",
    };
  }

  // CreateProductVariantTransactionCatalogueMapping creates a new Product Variant Transaction catalogue mapping
  rpc CreateProductVariantTransactionCatalogueMapping (CreateProductVariantTransactionCatalogueMappingRequest) returns (CreateProductVariantTransactionCatalogueMappingResponse) {
    option (google.api.http) = {
      post: "/v1/product-variant-transaction-catalogue-mapping",
      body: "*",
    };
  }

  // GetProductVariantTransactionCatalogueMapping fetches a Product Variant Transaction catalogue mapping by id
  rpc GetProductVariantTransactionCatalogueMapping (GetProductVariantTransactionCatalogueMappingRequest) returns (GetProductVariantTransactionCatalogueMappingResponse) {
    option (google.api.http) = {
      get: "/v1/product-variant-transaction-catalogue-mapping/{id}",
      body: "*",
    };
  }

  // UpdateProductVariantTransactionCatalogueMappingStatus updates the status of Product Variant Transaction catalogue mapping
  rpc UpdateProductVariantTransactionCatalogueMappingStatus(UpdateProductVariantTransactionCatalogueMappingStatusRequest) returns (UpdateProductVariantTransactionCatalogueMappingStatusResponse) {
    option (google.api.http) = {
      put: "/v1/product-variant-transaction-catalogue-mapping/{id}/status",
      body: "*",
    };
  }

  // CreateGeneralLedger creates a new GeneralLedger
  rpc CreateGeneralLedger(CreateGeneralLedgerRequest) returns (CreateGeneralLedgerResponse) {
    option (google.api.http) = {
      post: "/v1/general-ledgers",
      body: "*",
    };
  }

  // GetGeneralLedger fetches a general-ledger by id
  rpc GetGeneralLedger (GetGeneralLedgerRequest) returns (GetGeneralLedgerResponse) {
    option (google.api.http) = {
      get: "/v1/general-ledgers/{id}",
      body: "*",
    };
  }

  // GetGeneralLedgerByCode fetches a general-ledger by code
  rpc GetGeneralLedgerByCode (GetGeneralLedgerByCodeRequest) returns (GetGeneralLedgerByCodeResponse) {
    option (google.api.http) = {
      get: "/v1/general-ledgers",
      body: "*",
    };
  }

  // UpdateGeneralLedgerStatus updates the status of general-ledger
  rpc UpdateGeneralLedgerStatus(UpdateGeneralLedgerStatusRequest) returns (UpdateGeneralLedgerStatusResponse) {
    option (google.api.http) = {
      put: "/v1/general-ledgers/{id}/status",
      body: "*",
    };
  }

  // CreateInternalAccount creates a new Internal Account
  rpc CreateInternalAccount(CreateInternalAccountRequest) returns (CreateInternalAccountResponse) {
    option (google.api.http) = {
      post: "/v1/internal-accounts",
      body: "*",
    };
  }

  // GetInternalAccount fetches a internal account by id
  rpc GetInternalAccount (GetInternalAccountRequest) returns (GetInternalAccountResponse) {
    option (google.api.http) = {
      get: "/v1/internal-accounts/{id}",
      body: "*",
    };
  }

  // ListInternalAccounts fetches a list of internal account by code and generalLedgerID
  rpc ListInternalAccounts (ListInternalAccountsRequest) returns (ListInternalAccountsResponse) {
    option (google.api.http) = {
      get: "/v1/internal-accounts",
      body: "*",
    };
  }

  // UpdateInternalAccountStatus updates the status of internal account
  rpc UpdateInternalAccountStatus(UpdateInternalAccountStatusRequest) returns (UpdateInternalAccountStatusResponse) {
    option (google.api.http) = {
      put: "/v1/internal-accounts/{id}/status",
      body: "*",
    };
  }

  // CreateProductVariantTransactionCatalogueInternalAccountMapping creates a new product-variant-transaction-catalogue-internal-account-mapping
  rpc CreateProductVariantTransactionCatalogueInternalAccountMapping(CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) returns (CreateProductVariantTransactionCatalogueInternalAccountMappingResponse) {
    option (google.api.http) = {
      post: "/v1/product-variant-transaction-catalogue-internal-account-mapping",
      body: "*",
    };
  }

  // GetProductVariantTransactionCatalogueInternalAccountMapping fetches a product-variant-transaction-catalogue-internal-account-mapping by id
  rpc GetProductVariantTransactionCatalogueInternalAccountMapping (GetProductVariantTransactionCatalogueInternalAccountMappingRequest) returns (GetProductVariantTransactionCatalogueInternalAccountMappingResponse) {
    option (google.api.http) = {
      get: "/v1/product-variant-transaction-catalogue-internal-account-mapping/{id}",
      body: "*",
    };
  }

  // GetProductVariantTransactionCatalogueInternalAccountMappingByKey fetches a product-variant-transaction-catalogue-internal-account-mapping by identifer key
  rpc GetProductVariantTransactionCatalogueInternalAccountMappingByKey (GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) returns (GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse) {
    option (google.api.http) = {
      get: "/v1/product-variant-transaction-catalogue-internal-account-mapping",
      body: "*",
    };
  }

  // UpdateProductVariantTransactionCatalogueInternalAccountMapping updates the status of product-variant-transaction-catalogue-internal-account-mapping
  rpc UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) returns (UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse) {
    option (google.api.http) = {
      put: "/v1/product-variant-transaction-catalogue-internal-account-mapping/{id}/status",
      body: "*",
    };
  }

  // CreateBaseInterest creates a new base-interest
  rpc CreateBaseInterest(CreateBaseInterestRequest) returns (CreateBaseInterestResponse) {
    option (google.api.http) = {
      post: "/v1/base-interest",
      body: "*",
    };
  }

  // GetBaseInterest fetches a base interest by id
  rpc GetBaseInterest (GetBaseInterestRequest) returns (GetBaseInterestResponse) {
    option (google.api.http) = {
      get: "/v1/base-interest/{id}",
      body: "*",
    };
  }

  // GetBaseInterestByCode fetches a base interest by code
  rpc GetBaseInterestByCode (GetBaseInterestByCodeRequest) returns (GetBaseInterestByCodeResponse) {
    option (google.api.http) = {
      get: "/v1/base-interest",
      body: "*",
    };
  }

  // CreateBaseInterestVersion creates a new base interest version
  rpc CreateBaseInterestVersion(CreateBaseInterestVersionRequest) returns (CreateBaseInterestVersionResponse) {
    option (google.api.http) = {
      post: "/v1/base-interest-version",
      body: "*",
    };
  }

  // GetBaseInterestVersion fetches a base interest version by id
  rpc GetBaseInterestVersion (GetBaseInterestVersionRequest) returns (GetBaseInterestVersionResponse) {
    option (google.api.http) = {
      get: "/v1/base-interest-version/{id}",
      body: "*",
    };
  }

  // CreateBaseInterestTimeSlabRate creates a new base interest time slab rate
  rpc CreateBaseInterestTimeSlabRate(CreateBaseInterestTimeSlabRateRequest) returns (CreateBaseInterestTimeSlabRateResponse) {
    option (google.api.http) = {
      post: "/v1/base-interest-time-slab-rate",
      body: "*",
    };
  }

  // GetBaseInterestTimeSlabRate fetches a base interest time slab rate by id
  rpc GetBaseInterestTimeSlabRate (GetBaseInterestTimeSlabRateRequest) returns (GetBaseInterestTimeSlabRateResponse) {
    option (google.api.http) = {
      get: "/v1/base-interest-time-slab-rate/{id}",
      body: "*",
    };
  }

  // CreateDepositInterest creates a new deposit interest
  rpc CreateDepositInterest(CreateDepositInterestRequest) returns (CreateDepositInterestResponse) {
    option (google . api. http) = {
      post: "/v1/deposit-interest",
      body: "*",
    };
  }

  // GetDepositInterest fetches a deposit interest by id
  rpc GetDepositInterest (GetDepositInterestRequest) returns (GetDepositInterestResponse) {
    option (google.api.http) = {
      get: "/v1/deposit-interest/{id}",
      body: "*",
    };
  }

  // GetDepositInterestByCode fetches a deposit interest by code
  rpc GetDepositInterestByCode (GetDepositInterestByCodeRequest) returns (GetDepositInterestByCodeResponse) {
    option (google.api.http) = {
      get: "/v1/deposit-interest",
      body: "*",
    };
  }

  // CreateDepositInterestVersion creates a new deposit interest version
  rpc CreateDepositInterestVersion(CreateDepositInterestVersionRequest) returns (CreateDepositInterestVersionRequest) {
    option (google.api.http) = {
      post: "/v1/deposit-interest-version",
      body: "*",
    };
  }

  // GetDepositInterestVersion fetches a deposit interest version by id
  rpc GetDepositInterestVersion (GetDepositInterestVersionRequest) returns (GetDepositInterestVersionResponse) {
    option (google.api.http) = {
      get: "/v1/deposit-interest-version/{id}",
      body: "*",
    };
  }

  // CreateDepositInterestAmountSlabRate creates a new deposit interest amount slab rate
  rpc CreateDepositInterestAmountSlabRate(CreateDepositInterestAmountSlabRateRequest) returns (CreateDepositInterestAmountSlabRateResponse) {
    option (google . api. http) = {
      post: "/v1/deposit-interest-amount-slab-rate",
      body: "*",
    };
  }

  // GetDepositInterestAmountSlabRate fetches a deposit interest amount slab rate by id
  rpc GetDepositInterestAmountSlabRate (GetDepositInterestAmountSlabRateRequest) returns (GetDepositInterestAmountSlabRateResponse) {
    option (google.api.http) = {
      get: "/v1/deposit-interest-amount-slab-rate/{id}",
      body: "*",
    };
  }

  // CreatePocketTemplate creates a new pocket template
  rpc CreatePocketTemplate (CreatePocketTemplateRequest) returns (CreatePocketTemplateResponse) {
    option (google.api.http) = {
      post: "/v1/pocket-templates",
      body: "*",
    };
  }

  // ListPocketTemplates fetches list of pocket templates
  rpc ListPocketTemplates (ListPocketTemplatesRequest) returns (ListPocketTemplatesResponse) {
    option (google.api.http) = {
      get: "/v1/pocket-templates",
      body: "*",
    };
  }

  // GetPocketTemplate fetches pocket template details
  rpc GetPocketTemplate (GetPocketTemplateRequest) returns (GetPocketTemplateResponse) {
    option (google.api.http) = {
      get: "/v1/pocket-templates/{id}",
      body: "*",
    };
  }

  // CreatePocketTemplateQuestions creates new pocket template questions
  rpc CreatePocketTemplateQuestions (CreatePocketTemplateQuestionsRequest) returns (CreatePocketTemplateQuestionsResponse) {
    option (google.api.http) = {
      post: "/v1/pocket-template-questions",
      body: "*",
    };
  }

  // ListPocketTemplateQuestions fetches list of pocket template questions
  rpc ListPocketTemplateQuestions (ListPocketTemplateQuestionsRequest) returns (ListPocketTemplateQuestionsResponse) {
    option (google.api.http) = {
      get: "/v1/pocket-template-questions",
      body: "*",
    };
  }

  // GetInterestParameters fetches all product params whose values are updated
  rpc GetInterestParameters (GetInterestParametersRequest) returns (GetInterestParametersResponse) {
    option (google.api.http) = {
      get: "/v1/interest-parameters",
      body: "*",
    };
  }

  // UpdateInterestParameterScheduleStatus keep the status whether parameter is scheduled in TM or not
  rpc UpdateInterestParameterScheduleStatus (UpdateInterestParameterScheduleStatusRequest) returns (UpdateInterestParameterScheduleStatusResponse) {
    option (google.api.http) = {
      put: "/v1/interest-parameters/schedule-status",
      body: "*",
    };
  }

  // UpdateInterestParameterNotificationStatus keep the status whether noti is sent to the customer
  rpc UpdateInterestParameterNotificationStatus (UpdateInterestParameterNotificationStatusRequest) returns (UpdateInterestParameterNotificationStatusResponse) {
    option (google.api.http) = {
      put: "/v1/interest-parameters/notification-status",
      body: "*",
    };
  }

  // GetInterestParametersByProductVariant fetches interest parameters by product variant
  rpc GetInterestParametersByProductVariant (GetInterestParametersByProductVariantRequest) returns (GetInterestParametersByProductVariantResponse) {
    option (google.api.http) = {
      post: "/v1/get-interest-parameters",
      body: "*",
    };
  }

  // CreateProductVariantQuestions : creates new questions based on productVariantCode
  rpc CreateProductVariantQuestions (CreateProductVariantQuestionRequest) returns (CreateProductVariantQuestionResponse) {
    option (google.api.http) = {
      post: "/v1/product-variant-question",
      body: "*",
    };
  }

  // ListProductVariantQuestions : list the array of  questions and respective answers based on productVariantCode
  rpc ListProductVariantQuestions (ListProductVariantQuestionsRequest) returns (ListProductVariantQuestionsResponse) {
    option (google.api.http) = {
      get: "/v1/product-variant-question"
    };
  }

  // CreateLoanInterest creates a new loan interest
  rpc CreateLoanInterest(CreateLoanInterestRequest) returns (CreateLoanInterestResponse) {
    option (google.api.http) = {
      post: "/v1/loan-interest",
      body: "*",
    };
  }

  // GetLoanInterest fetches a loan interest by id
  rpc GetLoanInterest (GetLoanInterestByIDRequest) returns (GetLoanInterestByIDResponse) {
    option (google.api.http) = {
      get: "/v1/loan-interest/{id}"
    };
  }

  // GetLoanInterestByCode fetches a loan interest by code
  rpc GetLoanInterestByCode (GetLoanInterestByCodeRequest) returns (GetLoanInterestByCodeResponse) {
    option (google.api.http) = {
      get: "/v1/loan-interest"
    };
  }

  // GetLoanPastDueParametersByProductCode fetches a loan past due parameters by product code
  rpc GetLoanPastDueParametersByProductCode (GetLoanPastDueParametersByProductCodeRequest) returns (GetLoanPastDueParametersByProductCodeResponse) {
    option (google.api.http) = {
      get: "/v1/loan-past-due-parameters"
    };
  }

  // CreateLoanInstructionVersion creates a new version of loan instructions
  rpc CreateLoanInstructionVersion(CreateLoanInstructionVersionRequest) returns (CreateLoanInstructionVersionResponse) {
    option (google.api.http) = {
      post: "/v1/loan-instruction-versions",
      body: "*",
    };
  }

  // CreateLoanInstructionRequest creates a new loan instruction
  rpc CreateLoanInstruction(CreateLoanInstructionRequest) returns (CreateLoanInstructionResponse) {
    option (google.api.http) = {
      post: "/v1/loan-instructions",
      body: "*",
    };
  }

  // GetLoanInstructionsByCode fetches a loan instruction parameters by product variant code and instruction type
  rpc GetLoanInstructionsByCode (GetLoanInstructionsByCodeRequest) returns (GetLoanInstructionsByCodeResponse) {
    option (google.api.http) = {
      get: "/v1/loan-instructions"
    };
  }
  // ListLoanDocumentOptionsByProductVariant returns list of loan document options for a given product variant code
  rpc ListLoanDocumentOptionsByProductVariant(ListLoanDocumentOptionsByProductVariantRequest) returns (ListLoanDocumentOptionsByProductVariantResponse) {
    option (google.api.http) = {
      get: "/v1/loan-document-submission-options",
    };
    option (gxs.api.auth) = {
      client_identities: ["servicename.LoanApp"]
    };
  }
}


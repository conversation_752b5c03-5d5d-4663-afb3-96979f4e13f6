syntax = "proto3";

package productMaster;

option go_package = "gitlab.com/gx-regional/dbmy/core-banking/product-master/api";

import "gxs/api/annotations.proto";


// ListLoanDocumentOptionsByProductVariantRequest request to get document types api
message ListLoanDocumentOptionsByProductVariantRequest {
  string productVariantCode = 1[(gxs.api.validate) = "string,max=64,required"];
}

// ListLoanDocumentOptionsByProductVariantResponse response for get document types api
message ListLoanDocumentOptionsByProductVariantResponse {
  repeated LoanDocumentSubmissionOption loanDocumentSubmissionOptions = 1;
}

// FileConfig configuration for each submission
message FileConfig {
  int64 maxFileSizeInBytes = 1;
  int64 maxUploadLimit = 2;
  repeated string allowedFileExtensions = 3;
}

// LoanDocumentSubmissionOption information about document submission option
message LoanDocumentSubmissionOption {
  string documentType = 1;
  string applicationType = 2;
  string salaryType = 3;
  string employerType = 4;
  bool isEnable = 5;
  int64 priority = 6;
  int64 requiredDocNumber = 7;
  string requiredDocUnit = 8;
  int64 excludedMonths = 9;
  string cutOffDate = 10;
  FileConfig fileConfig = 11;
}

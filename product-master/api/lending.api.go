// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: lending.proto
package api

// ListLoanDocumentOptionsByProductVariantRequest request to get document types api
type ListLoanDocumentOptionsByProductVariantRequest struct {
	ProductVariantCode string `json:"productVariantCode,omitempty" validate:"string,max=64,required"`
}

// ListLoanDocumentOptionsByProductVariantResponse response for get document types api
type ListLoanDocumentOptionsByProductVariantResponse struct {
	LoanDocumentSubmissionOptions []LoanDocumentSubmissionOption `json:"loanDocumentSubmissionOptions,omitempty"`
}

// FileConfig configuration for each submission
type FileConfig struct {
	MaxFileSizeInBytes    int64    `json:"maxFileSizeInBytes,omitempty"`
	MaxUploadLimit        int64    `json:"maxUploadLimit,omitempty"`
	AllowedFileExtensions []string `json:"allowedFileExtensions,omitempty"`
}

// LoanDocumentSubmissionOption information about document submission option
type LoanDocumentSubmissionOption struct {
	DocumentType      string      `json:"documentType,omitempty"`
	ApplicationType   string      `json:"applicationType,omitempty"`
	SalaryType        string      `json:"salaryType,omitempty"`
	EmployerType      string      `json:"employerType,omitempty"`
	IsEnable          bool        `json:"isEnable,omitempty"`
	Priority          int64       `json:"priority,omitempty"`
	RequiredDocNumber int64       `json:"requiredDocNumber,omitempty"`
	RequiredDocUnit   string      `json:"requiredDocUnit,omitempty"`
	ExcludedMonths    int64       `json:"excludedMonths,omitempty"`
	CutOffDate        string      `json:"cutOffDate,omitempty"`
	FileConfig        *FileConfig `json:"fileConfig,omitempty"`
}

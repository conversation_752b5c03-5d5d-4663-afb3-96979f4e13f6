package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetLoanInterestByCode fetches a loan interest by code
func (p *ProductMasterService) GetLoanInterestByCode(ctx context.Context, req *api.GetLoanInterestByCodeRequest) (*api.GetLoanInterestByCodeResponse, error) {
	slog.FromContext(ctx).Info(constants.GetLoanInterestByCodeLogTag, fmt.Sprintf("Received Request: %+v", utils.ToJSON(req)), apiCommon.GetTraceID(ctx))

	if err := validateGetLoanInterestByCodeRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetLoanInterestByCode(ctx, req, p.Store)
}

func validateGetLoanInterestByCodeRequest(ctx context.Context, req *api.GetLoanInterestByCodeRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	return nil
}

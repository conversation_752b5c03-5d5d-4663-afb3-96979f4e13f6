package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetBaseInterest fetches a base interest by id
func (p *ProductMasterService) GetBaseInterest(ctx context.Context, req *api.GetBaseInterestRequest) (*api.GetBaseInterestResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetBaseInterest"))
	slog.FromContext(ctx).Info(constants.GetBaseInterestLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetBaseInterestRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetBaseInterest(ctx, req, p.Store)
}

func validateGetBaseInterestRequest(ctx context.Context, req *api.GetBaseInterestRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductTemplateParameter fetches a product template parameter by id
func (p *ProductMasterService) GetProductTemplateParameter(ctx context.Context, req *api.GetProductTemplateParameterRequest) (*api.GetProductTemplateParameterResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductTemplate"))
	slog.FromContext(ctx).Info(constants.GetProductTemplateParameterLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductTemplateParameterRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.GetProductTemplateParameter(ctx, req, p.Store)
}

func validateGetProductTemplateParameterRequest(ctx context.Context, req *api.GetProductTemplateParameterRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

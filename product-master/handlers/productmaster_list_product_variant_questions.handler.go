package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// ListProductVariantQuestions : list the array of  questions and respective answers based on productVariantCode
func (p *ProductMasterService) ListProductVariantQuestions(ctx context.Context, req *api.ListProductVariantQuestionsRequest) (*api.ListProductVariantQuestionsResponse, error) {
	slog.FromContext(ctx).Info(constants.ListProductVariantQuestionsLogTag, fmt.Sprintf("Request received: %s", utils.ToJSON(req)), utils.GetTraceID(ctx))

	// validate request parameter
	if err := productconfig.ValidateListProductVariantQuestionsRequest(ctx, req); err != nil {
		return nil, err
	}

	// list question answer pair
	impl := productconfig.QuestionsAnswersImpl{Store: p.Store}
	response, err := impl.ListProductVariantQuestions(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.ListProductVariantQuestionsLogTag, fmt.Sprintf("Successfully completed request: %s", utils.ToJSON(req)), utils.GetTraceID(ctx))
	return response, nil
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// UpdateProductTemplateParameterValue updates the value of Product Template Parameter
func (p *ProductMasterService) UpdateProductTemplateParameterValue(ctx context.Context, req *api.UpdateProductTemplateParameterValueRequest) (*api.UpdateProductTemplateParameterValueResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("UpdateProductTemplateParameterValue"))
	slog.FromContext(ctx).Info(constants.UpdateProductTemplateParameterValueLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateUpdateProductTemplateParameterValueRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.UpdateProductTemplateParameterValue(ctx, req, p.Store)
}

func validateUpdateProductTemplateParameterValueRequest(ctx context.Context, req *api.UpdateProductTemplateParameterValueRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterValue, "parameter-value", apiErr.ErrMissingParameterValue); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.UpdatedBy, "updated-by", apiErr.ErrMissingUpdatedBy); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/dto"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetInterestParameters fetches all product params whose values are updated
func (p *ProductMasterService) GetInterestParameters(ctx context.Context, req *api.GetInterestParametersRequest) (*api.GetInterestParametersResponse, error) {
	ctx = slog.AddTagsToContext(ctx,
		common.Service(constants.ServiceName),
		common.Endpoint("GetInterestParameters"),
		tags.T(constants.ParameterKey, req.ParameterKey),
	)

	err := validateGetInterestParameterRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	latestInterestParameterDetail, err := productconfig.GetInterestRateParams(ctx, p.Store)

	if err != nil {
		return nil, err
	}

	interestParameterFilter := []data.Condition{
		data.EqualTo("ParameterKey", req.ParameterKey),
		data.EqualTo("ParameterVersion", latestInterestParameterDetail.LatestDepositInterestVersion),
	}

	if req.IsScheduled == api.Status_N || req.IsScheduled == api.Status_Y {
		interestParameterFilter = append(interestParameterFilter, data.EqualTo("IsScheduled", constants.ParameterStatus[req.IsScheduled]))
		unNotifiedParameterDetail, err := getLatestParameterDetail(ctx, latestInterestParameterDetail, p.Store, interestParameterFilter)
		if err != nil {
			return nil, err
		}
		return unNotifiedParameterDetail, nil
	}

	if req.IsNotified == api.Status_N || req.IsNotified == api.Status_Y {
		interestParameterFilter = append(interestParameterFilter, data.EqualTo("IsNotificationSent", constants.ParameterStatus[req.IsNotified]))
		unScheduledParameterDetail, err := getLatestParameterDetail(ctx, latestInterestParameterDetail, p.Store, interestParameterFilter)
		if err != nil {
			return nil, err
		}
		return unScheduledParameterDetail, nil
	}

	return nil, nil
}

// getLatestParameterDetail ...
func getLatestParameterDetail(ctx context.Context, latestVersion *dto.InterestRateVersionDetail, store *storage.DBStore, conditions []data.Condition) (*api.GetInterestParametersResponse, error) {
	return productconfig.GetLatestParameterDetail(ctx, latestVersion, store, conditions)
}

// ValidateGetInterestParameterRequest ...
func validateGetInterestParameterRequest(ctx context.Context, req *api.GetInterestParametersRequest) error {
	return validations.ValidateGetInterestParameters(ctx, req.IsScheduled, req.IsNotified)
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetBaseInterestVersion fetches a base-interest-version by id
func (p *ProductMasterService) GetBaseInterestVersion(ctx context.Context, req *api.GetBaseInterestVersionRequest) (*api.GetBaseInterestVersionResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetBaseInterestVersion"))
	slog.FromContext(ctx).Info(constants.GetBaseInterestVersionLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetBaseInterestVersionRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetBaseInterestVersion(ctx, req, p.Store)
}

func validateGetBaseInterestVersionRequest(ctx context.Context, req *api.GetBaseInterestVersionRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

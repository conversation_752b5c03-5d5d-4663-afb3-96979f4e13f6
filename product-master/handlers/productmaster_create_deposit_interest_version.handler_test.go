package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateDepositInterestVersion(t *testing.T) {
	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateDepositInterestVersionRequest
		storeLoadResponse []*storage.DepositInterestVersion
		expectedResponse  *api.CreateDepositInterestVersionResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - deposit interest version created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "test-deposit",
				Version:           "1",
				EffectiveDate:     now,
				Description:       "create deposit interest version",
				CreatedBy:         "unit-test",
			},
			storeLoadResponse: []*storage.DepositInterestVersion{{
				PublicID:          "test-id",
				DepositInterestID: 1,
				Version:           "1",
				EffectiveDate:     now,
				Description: sql.NullString{
					String: "create deposit interest version",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.CreateDepositInterestVersionResponse{
				DepositInterestVersion: &api.DepositInterestVersion{
					Id:                "test-id",
					DepositInterestID: "test-deposit",
					Version:           "1",
					EffectiveDate:     now,
					Description:       "create deposit interest version",
					CreatedBy:         "unit-test",
					CreatedAt:         now,
					UpdatedBy:         "unit-test",
					UpdatedAt:         now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "test-deposit",
				Version:           "1",
				EffectiveDate:     now,
				Description:       "create deposit interest version",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - deposit interest id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "",
				Version:           "1",
				EffectiveDate:     now,
				Description:       "create deposit interest version",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingDepositInterestID.Code), 10), apiErr.ErrMissingDepositInterestID.Message),
		},
		{
			testDesc:       "error path - version is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "test-deposit",
				Version:           "",
				EffectiveDate:     now,
				Description:       "create deposit interest version",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingVersion.Code), 10), apiErr.ErrMissingVersion.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "test-deposit",
				Version:           "1",
				EffectiveDate:     now,
				Description:       "create deposit interest version",
				CreatedBy:         "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - effective date is less than current date",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "test-deposit",
				Version:           "1",
				EffectiveDate:     nowMinus10,
				Description:       "create deposit interest version",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrEffectiveDateLessThanCurrent.Code), 10), apiErr.ErrEffectiveDateLessThanCurrent.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "test-deposit",
				Version:           "1",
				EffectiveDate:     now,
				Description:       "create deposit interest version",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestVersionRequest{
				DepositInterestID: "test-deposit",
				Version:           "1",
				EffectiveDate:     now,
				Description:       "create deposit interest version",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockDepositInterest := &storage.MockIDepositInterestDAO{}
			mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{{}}, nil)
			mockDepositInterestVersion := &storage.MockIDepositInterestVersionDAO{}
			mockDepositInterestVersion.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				DepositInterestVersionDAO: mockDepositInterestVersion,
				DepositInterestDAO:        mockDepositInterest,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			depositInterestVersion, err := service.CreateDepositInterestVersion(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, depositInterestVersion)
			}
		})
	}
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
)

func Test_GetDepositInterestVersion(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetDepositInterestVersionRequest
		storeLoadResponse []*storage.DepositInterestVersion
		expectedResponse  *api.GetDepositInterestVersionResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get deposit interest version",
			requestParams: &api.GetDepositInterestVersionRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.DepositInterestVersion{{
				PublicID:          "test-id",
				DepositInterestID: 1,
				Version:           "1",
				EffectiveDate:     now,
				Description: sql.NullString{
					String: "get test deposit interest version",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.GetDepositInterestVersionResponse{
				DepositInterestVersion: &api.DepositInterestVersion{
					Id:                "test-id",
					DepositInterestID: "test-deposit-interest-id",
					Version:           "1",
					EffectiveDate:     now,
					Description:       "get test deposit interest version",
					CreatedBy:         "unit-test",
					CreatedAt:         now,
					UpdatedBy:         "unit-test",
					UpdatedAt:         now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetDepositInterestVersionRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - deposit interest version id not found in database",
			requestParams: &api.GetDepositInterestVersionRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetDepositInterestVersionRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockDepositInterest := &storage.MockIDepositInterestDAO{}
			mockDepositInterest.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.DepositInterest{PublicID: "test-deposit-interest-id"}, nil)

			mockDepositInterestVersion := &storage.MockIDepositInterestVersionDAO{}
			mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				DepositInterestVersionDAO: mockDepositInterestVersion,
				DepositInterestDAO:        mockDepositInterest,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			depositInterestVersion, err := service.GetDepositInterestVersion(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, depositInterestVersion)
			}
		})
	}
}

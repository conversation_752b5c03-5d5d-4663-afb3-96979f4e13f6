package handlers

import (
	"context"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateProductVariantQuestions(t *testing.T) {
	now := time.Now()
	scenarios := []struct {
		desc                       string
		request                    *api.CreateProductVariantQuestionRequest
		expectedResponse           *api.CreateProductVariantQuestionResponse
		isErrorExpected            bool
		loadProductVariantResponse []*storage.ProductVariant
		loadProductVariantError    error
		saveQuestionQueryError     error
		loadQuestionsResponse      []*storage.ProductVariantQuestion
		loadQuestionsError         error
		saveAnswersQueryError      error
		loadAnswerResponse         []*storage.ProductVariantAnswerSuggestion
		loadAnswerError            error
		expectedError              error
	}{
		{
			desc: "happy path",
			request: &api.CreateProductVariantQuestionRequest{
				ProductVariantCode:    "product1",
				ProductVariantVersion: "1",
				Locale:                "EN",
				CreatedBy:             "test-case",
				QuestionAnswerPairs:   []api.ProductVariantQuestionAnswerPairsRequest{{Code: "test-question-code", QuestionText: "How are you?", AnswerSuggestions: []api.ProductVariantAnswerSuggestion{{Code: "I am fine"}, {Code: "I am very well"}}}},
			},
			isErrorExpected:            false,
			loadProductVariantResponse: []*storage.ProductVariant{{ID: 10}},
			loadQuestionsResponse:      responses.SampleProductVariantQuestions(now),
			loadAnswerResponse:         responses.SampleProductVariantAnswerSuggestions(now),
			expectedResponse:           responses.CreateProductVariantQuestionResponse(now),
		},
		{
			desc:            "Error path - productVariantCode missing",
			request:         &api.CreateProductVariantQuestionRequest{ProductVariantCode: ""},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductVariantCode.Code), 10), apiErr.ErrMissingProductVariantCode.Message),
		},
		{
			desc:            "Error path - productVariantVersion missing",
			request:         &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1"},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductVariantVersion.Code), 10), apiErr.ErrMissingProductVariantVersion.Message),
		},
		{
			desc:            "Error path - locale missing",
			request:         &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1", ProductVariantVersion: "1"},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingLocale.Code), 10), apiErr.ErrMissingLocale.Message),
		},
		{
			desc:            "Error path - createdBy missing",
			request:         &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1", ProductVariantVersion: "1", Locale: "EN"},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			desc:            "Error path - QuestionAnswerPairs is missing",
			request:         &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1", ProductVariantVersion: "1", Locale: "EN", CreatedBy: "test-case"},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionAnswerPairs.Code), 10), apiErr.ErrMissingQuestionAnswerPairs.Message),
		},
		{
			desc: "Error path - Code in QuestionAnswerPairs is missing",
			request: &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1", ProductVariantVersion: "1",
				Locale: "EN", CreatedBy: "test-case", QuestionAnswerPairs: []api.ProductVariantQuestionAnswerPairsRequest{{AnswerSuggestions: []api.ProductVariantAnswerSuggestion{{Code: "A", Text: "A"}}}}},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionCode.Code), 10), apiErr.ErrMissingQuestionCode.Message),
		},
		{
			desc: "Error path - QuestionText in QuestionAnswerPairs is missing",
			request: &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1", ProductVariantVersion: "1",
				Locale: "EN", CreatedBy: "test-case", QuestionAnswerPairs: []api.ProductVariantQuestionAnswerPairsRequest{{Code: "test-question-code", AnswerSuggestions: []api.ProductVariantAnswerSuggestion{{Code: "A", Text: "A"}}}}},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message),
		},
		{
			desc: "Error path - AnswerSuggestions in QuestionAnswerPairs is missing",
			request: &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1", ProductVariantVersion: "1",
				Locale: "EN", CreatedBy: "test-case", QuestionAnswerPairs: []api.ProductVariantQuestionAnswerPairsRequest{{Code: "test-question-code", QuestionText: "..."}}},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message),
		},
		{
			desc: "Error path - QuestionText and AnswerSuggestions in QuestionAnswerPairs is missing",
			request: &api.CreateProductVariantQuestionRequest{ProductVariantCode: "product1", ProductVariantVersion: "1",
				Locale: "EN", CreatedBy: "test-case", QuestionAnswerPairs: []api.ProductVariantQuestionAnswerPairsRequest{{Code: "test-question-code", QuestionText: "", AnswerSuggestions: []api.ProductVariantAnswerSuggestion{}}}},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message),
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariantQuestion := &storage.MockIProductVariantQuestionDAO{}
			mockProductVariantAnswerSuggestion := &storage.MockIProductVariantAnswerSuggestionDAO{}

			mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadProductVariantResponse, scenario.loadProductVariantError)
			mockProductVariantQuestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(scenario.saveQuestionQueryError)
			mockProductVariantQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadQuestionsResponse, scenario.loadQuestionsError)
			mockProductVariantAnswerSuggestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(scenario.saveAnswersQueryError)
			mockProductVariantAnswerSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadAnswerResponse, scenario.loadAnswerError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO:                 mockProductVariant,
				ProductVariantQuestionDAO:         mockProductVariantQuestion,
				ProductVariantAnswerSuggestionDAO: mockProductVariantAnswerSuggestion,
			}
			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			response, err := service.CreateProductVariantQuestions(context.Background(), scenario.request)
			if scenario.isErrorExpected {
				assert.Equal(t, scenario.expectedError, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, scenario.expectedResponse, response)
		})
	}
}

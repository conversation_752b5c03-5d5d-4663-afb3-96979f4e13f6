package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetInterestParametersByProductVariant(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                                       string
		enableFESpotTenor                              bool
		requestParams                                  *api.GetInterestParametersByProductVariantRequest
		expectedResponse                               *api.GetInterestParametersByProductVariantResponse
		productVariantDAOResponse                      []*storage.ProductVariant
		depositInterestDAOResponse                     []*storage.DepositInterest
		depositInterestVersionDAOResponse              []*storage.DepositInterestVersion
		depositInterestSlabRateDAOResponseDAOResponse  []*storage.DepositInterestAmountSlabRate
		boostPocketSlabRateForBonusDAOResponseResponse []*storage.DepositInterestAmountSlabRate
		productVariantParameterDAOResponse             []*storage.ProductVariantParameter
		loanInterestDAOResponse                        []*storage.LoanInterest
		loanInterestVersionDAOResponse                 []*storage.LoanInterestVersion
		bizLoanInterestSlabRateDAOResponse             []*storage.LoanInterestSlabRate
		isErrorExpected                                bool
		expectedError                                  error
		productVariantDAOError                         error
		depositInterestDAOError                        error
		depositInterestVersionDAOError                 error
		depositInterestSlabRateDAOResponseDAOError     error
		loanInterestDAOResponseError                   error
		loanInterestVersionDAOResponseError            error
		bizLoanInterestSlabRateDAOResponseError        error
		productVariantParameterDAOError                error
		boostPocketSlabRateForBonusDAOResponseError    error
	}{
		{
			testDesc: "happy case - with product variant version & interest version",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - with product variant version only",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - with interest version only",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_DEPOSITS_ACCOUNT,
						InterestVersion:    "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - without product variant version & interest version",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_DEPOSITS_ACCOUNT,
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - with boost pocket code and versions",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BOOST_POCKET,
						ProductVariantVersion: "1",
					},
				},
			},
			expectedResponse:                               responses.GetBoostPocketInterestParametersResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketBonusSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - with boost pocket code and without versions",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
				},
			},
			expectedResponse:                               responses.GetBoostPocketInterestParametersResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketBonusSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - with boost pocket and savings account code",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
					{
						ProductVariantCode: api.ProductVariantCode_SAVINGS_POCKET,
					},
				},
			},
			expectedResponse:                               responses.GetSavingsAndBoostPocketInterestParametersResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketBonusSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - with boost pocket multiple tenors ranges",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
					{
						ProductVariantCode: api.ProductVariantCode_SAVINGS_POCKET,
					},
				},
			},
			expectedResponse:                               responses.GetSavingsAndBoostPocketInterestParametersMultipleTenorsResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketMultipleBonusSlabRatesResponse(now),
		},
		{
			testDesc: "happy case - with boost pocket max tenor ranges",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
					{
						ProductVariantCode: api.ProductVariantCode_SAVINGS_POCKET,
					},
				},
			},
			expectedResponse:                               responses.GetSavingsAndBoostPocketInterestParametersMaxTenorResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketMaxBonusSlabRatesResponse(now),
		}, {
			testDesc: "happy case - with boost pocket spot tenor",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
					{
						ProductVariantCode: api.ProductVariantCode_SAVINGS_POCKET,
					},
				},
			},
			enableFESpotTenor:                              true,
			expectedResponse:                               responses.GetSavingsAndBoostPocketInterestParametersSpotTenorResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketMaxBonusSlabRatesResponse(now),
		},
		{
			testDesc: "happy case - with boost pocket spot tenor with different units",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
					{
						ProductVariantCode: api.ProductVariantCode_SAVINGS_POCKET,
					},
				},
			},
			enableFESpotTenor:                              true,
			expectedResponse:                               responses.GetSavingsAndBoostPocketInterestParametersDiffSpotTenorsResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketDifferentSlabResponse(now),
		},
		{
			testDesc: "happy case - with boost pocket tenors having different units",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
					{
						ProductVariantCode: api.ProductVariantCode_SAVINGS_POCKET,
					},
				},
			},
			expectedResponse:                               responses.GetSavingsAndBoostPocketInterestParametersDiffTenorsResponse(),
			productVariantDAOResponse:                      responses.SampleProductVariantDAOResponse(),
			productVariantParameterDAOResponse:             responses.SampleBoostPocketParameterDAOResponse(),
			depositInterestDAOResponse:                     responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:              responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse:  responses.SampleDepositInterestSlabRateDAOResponse(now),
			boostPocketSlabRateForBonusDAOResponseResponse: responses.SampleBoostPocketDifferentSlabResponse(now),
		},
		{
			testDesc: "error case - missing product variant code",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode(""),
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
			isErrorExpected:                               true,
			expectedError:                                 apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCode.Code), 10), apiErr.ErrInvalidProductVariantCode.Message),
		},
		{
			testDesc: "error case - invalid product variant code",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode("INVALID_PRODUCT_VARIANT_CODE"),
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
			isErrorExpected:                               true,
			expectedError:                                 apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCode.Code), 10), apiErr.ErrInvalidProductVariantCode.Message),
		},

		{
			testDesc: "error case - product variant code not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			isErrorExpected:        true,
			productVariantDAOError: data.ErrNoData,
			expectedError:          apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error case - deposits interest not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse: responses.SampleProductVariantDAOResponse(),
			isErrorExpected:           true,
			depositInterestDAOError:   data.ErrNoData,
			expectedError:             apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error case - deposits interest version not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:      responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:     responses.SampleDepositInterestDAOResponse(),
			isErrorExpected:                true,
			depositInterestVersionDAOError: data.ErrNoData,
			expectedError:                  apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error case - deposits interest amount slab rate not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:                  responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                 responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:          responses.SampleDepositInterestVersionDAOResponse(now),
			isErrorExpected:                            true,
			depositInterestSlabRateDAOResponseDAOError: data.ErrNoData,
			expectedError:                              apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},

		{
			testDesc: "error case - error from product variant",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			isErrorExpected:        true,
			productVariantDAOError: errors.New("error from product variant"),
			expectedError:          apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error case - error from deposits interest",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse: responses.SampleProductVariantDAOResponse(),
			isErrorExpected:           true,
			depositInterestDAOError:   errors.New("error from deposits interest"),
			expectedError:             apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error case - error from deposits interest version",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:      responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:     responses.SampleDepositInterestDAOResponse(),
			isErrorExpected:                true,
			depositInterestVersionDAOError: errors.New("error from deposits interest version"),
			expectedError:                  apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error case - deposits interest amount slab rate not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEPOSITS_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:                  responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                 responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:          responses.SampleDepositInterestVersionDAOResponse(now),
			isErrorExpected:                            true,
			depositInterestSlabRateDAOResponseDAOError: errors.New("error from deposits interest amount slab rate"),
			expectedError:                              apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "happy case - biz with product variant version & interest version",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - biz with product variant version only",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - with biz interest version only",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						InterestVersion:    "1",
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "happy case - biz without product variant version & interest version",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
					},
				},
			},
			expectedResponse:                              responses.GetInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:                     responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                    responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:             responses.SampleDepositInterestVersionDAOResponse(now),
			depositInterestSlabRateDAOResponseDAOResponse: responses.SampleDepositInterestSlabRateDAOResponse(now),
		},
		{
			testDesc: "error case - biz deposits interest not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse: responses.SampleProductVariantDAOResponse(),
			isErrorExpected:           true,
			depositInterestDAOError:   data.ErrNoData,
			expectedError:             apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error case - biz deposits interest version not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:      responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:     responses.SampleDepositInterestDAOResponse(),
			isErrorExpected:                true,
			depositInterestVersionDAOError: data.ErrNoData,
			expectedError:                  apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error case - biz deposits interest amount slab rate not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:                  responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                 responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:          responses.SampleDepositInterestVersionDAOResponse(now),
			isErrorExpected:                            true,
			depositInterestSlabRateDAOResponseDAOError: data.ErrNoData,
			expectedError:                              apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error case - error from biz deposits interest",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse: responses.SampleProductVariantDAOResponse(),
			isErrorExpected:           true,
			depositInterestDAOError:   errors.New("error from biz deposits interest"),
			expectedError:             apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error case - error from biz deposits interest version",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:      responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:     responses.SampleDepositInterestDAOResponse(),
			isErrorExpected:                true,
			depositInterestVersionDAOError: errors.New("error from biz deposits interest version"),
			expectedError:                  apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error case - biz deposits interest amount slab rate not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:                  responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                 responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:          responses.SampleDepositInterestVersionDAOResponse(now),
			isErrorExpected:                            true,
			depositInterestSlabRateDAOResponseDAOError: errors.New("error from deposits interest amount slab rate"),
			expectedError:                              apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},

		{
			testDesc: "happy case - with biz product variant",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN,
						ProductVariantVersion: "",
						InterestVersion:       "",
					},
				},
			},
			expectedResponse:                   responses.GetBizLoanInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:          responses.SampleProductVariantDAOResponse(),
			loanInterestDAOResponse:            responses.SampleLoanInterestDAOResponse(),
			loanInterestVersionDAOResponse:     responses.SampleLoanInterestVersionDAOResponse(),
			bizLoanInterestSlabRateDAOResponse: responses.SampleBizLoanInterestSlabRateDAOResponse(),
		},
		{
			testDesc: "happy case - with biz product variant with product variant version and interest version",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			expectedResponse:                   responses.GetBizLoanInterestParametersByProductVariantResponse(),
			productVariantDAOResponse:          responses.SampleProductVariantDAOResponse(),
			loanInterestDAOResponse:            responses.SampleLoanInterestDAOResponse(),
			loanInterestVersionDAOResponse:     responses.SampleLoanInterestVersionDAOResponse(),
			bizLoanInterestSlabRateDAOResponse: responses.SampleBizLoanInterestSlabRateDAOResponse(),
		},
		{
			testDesc: "error case - with biz product variant loan interest not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN,
						ProductVariantVersion: "1",
					},
				},
			},
			isErrorExpected:                    true,
			expectedError:                      apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
			productVariantDAOResponse:          responses.SampleProductVariantDAOResponse(),
			loanInterestDAOResponseError:       data.ErrNoData,
			loanInterestVersionDAOResponse:     responses.SampleLoanInterestVersionDAOResponse(),
			bizLoanInterestSlabRateDAOResponse: responses.SampleBizLoanInterestSlabRateDAOResponse(),
		},
		{
			testDesc: "error case - with biz product variant loan interest version not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN,
						ProductVariantVersion: "1",
					},
				},
			},
			isErrorExpected:                     true,
			expectedError:                       apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
			productVariantDAOResponse:           responses.SampleProductVariantDAOResponse(),
			loanInterestDAOResponse:             responses.SampleLoanInterestDAOResponse(),
			loanInterestVersionDAOResponseError: data.ErrNoData,
			bizLoanInterestSlabRateDAOResponse:  responses.SampleBizLoanInterestSlabRateDAOResponse(),
		},
		{
			testDesc: "error case - with biz product variant loan interest slab not found in database",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN,
						ProductVariantVersion: "1",
					},
				},
			},
			isErrorExpected:                         true,
			expectedError:                           apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
			productVariantDAOResponse:               responses.SampleProductVariantDAOResponse(),
			loanInterestDAOResponse:                 responses.SampleLoanInterestDAOResponse(),
			loanInterestVersionDAOResponse:          responses.SampleLoanInterestVersionDAOResponse(),
			bizLoanInterestSlabRateDAOResponseError: data.ErrNoData,
		},
		{
			testDesc: "error case - error from deposits interest for boost pocket",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BOOST_POCKET,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse: responses.SampleProductVariantDAOResponse(),
			isErrorExpected:           true,
			depositInterestDAOError:   errors.New("error from deposits interest"),
			expectedError:             apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error case - deposits interest amount slab rate not found in database for boost pocket",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BOOST_POCKET,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:                  responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:                 responses.SampleDepositInterestDAOResponse(),
			depositInterestVersionDAOResponse:          responses.SampleDepositInterestVersionDAOResponse(now),
			isErrorExpected:                            true,
			depositInterestSlabRateDAOResponseDAOError: errors.New("error from deposits interest amount slab rate"),
			expectedError:                              apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error case - deposits interest not found in database for boost pocket",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode: api.ProductVariantCode_BOOST_POCKET,
					},
				},
			},
			productVariantDAOResponse: responses.SampleProductVariantDAOResponse(),
			isErrorExpected:           true,
			depositInterestDAOError:   data.ErrNoData,
			expectedError:             apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error case - deposits interest version not found in database for boost pocket",
			requestParams: &api.GetInterestParametersByProductVariantRequest{
				InterestParamRequest: []api.InterestParametersByProductVariantRequest{
					{
						ProductVariantCode:    api.ProductVariantCode_BOOST_POCKET,
						ProductVariantVersion: "1",
						InterestVersion:       "1",
					},
				},
			},
			productVariantDAOResponse:      responses.SampleProductVariantDAOResponse(),
			depositInterestDAOResponse:     responses.SampleDepositInterestDAOResponse(),
			isErrorExpected:                true,
			depositInterestVersionDAOError: data.ErrNoData,
			expectedError:                  apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockDepositInterest := &storage.MockIDepositInterestDAO{}
			mockDepositInterestVersion := &storage.MockIDepositInterestVersionDAO{}
			mockDepositInterestSlabRate := &storage.MockIDepositInterestAmountSlabRateDAO{}
			mockProductVariantParameter := &storage.MockIProductVariantParameterDAO{}
			mockLoanInterest := &storage.MockILoanInterestDAO{}
			mockLoanInterestVersion := &storage.MockILoanInterestVersionDAO{}
			mockLoanInterestSlabRate := &storage.MockILoanInterestSlabRateDAO{}

			mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.productVariantDAOResponse, s.productVariantDAOError)
			mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.depositInterestDAOResponse, s.depositInterestDAOError)
			mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.depositInterestVersionDAOResponse, s.depositInterestVersionDAOError)
			mockDepositInterestSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.depositInterestSlabRateDAOResponseDAOResponse, s.depositInterestSlabRateDAOResponseDAOError).Once()
			mockProductVariantParameter.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.productVariantParameterDAOResponse, s.productVariantParameterDAOError)

			mockLoanInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loanInterestDAOResponse, s.loanInterestDAOResponseError)
			mockLoanInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loanInterestVersionDAOResponse, s.loanInterestVersionDAOResponseError)
			mockLoanInterestSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.bizLoanInterestSlabRateDAOResponse, s.bizLoanInterestSlabRateDAOResponseError)

			if s.requestParams.InterestParamRequest[0].ProductVariantCode == api.ProductVariantCode_BOOST_POCKET {
				mockDepositInterestSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.boostPocketSlabRateForBonusDAOResponseResponse, s.boostPocketSlabRateForBonusDAOResponseError)
			}

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
				Locale: config.Locale{
					Currency: "MYR",
				},
				FeatureFlags: config.FeatureFlags{
					EnableBoostPocketFESpotRate: s.enableFESpotTenor,
				},
			}

			mockStore := &storage.DBStore{
				ProductVariantDAO:                mockProductVariant,
				DepositInterestDAO:               mockDepositInterest,
				DepositInterestVersionDAO:        mockDepositInterestVersion,
				DepositInterestAmountSlabRateDAO: mockDepositInterestSlabRate,
				ProductVariantParameterDAO:       mockProductVariantParameter,
				LoanInterestDAO:                  mockLoanInterest,
				LoanInterestVersionDAO:           mockLoanInterestVersion,
				LoanInterestSlabRateDAO:          mockLoanInterestSlabRate,
			}
			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			response, err := service.GetInterestParametersByProductVariant(context.Background(), s.requestParams)
			if s.isErrorExpected {
				assert.Equal(t, s.expectedError, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, response)
			}
		})
	}
}

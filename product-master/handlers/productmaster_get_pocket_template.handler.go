package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/pocket"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetPocketTemplate fetches pocket template details
func (p *ProductMasterService) GetPocketTemplate(ctx context.Context, req *api.GetPocketTemplateRequest) (*api.GetPocketTemplateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetPocketTemplate"))
	slog.FromContext(ctx).Info(constants.GetPocketTemplateLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetPocketTemplateRequest(ctx, req); err != nil {
		return nil, err
	}

	return pocket.GetProductTemplate(ctx, p.HermesClient, req, p.Store)
}

func validateGetPocketTemplateRequest(ctx context.Context, req *api.GetPocketTemplateRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "pocket-template-id", apiErr.ErrMissingPocketTemplateID); err != nil {
		return err
	}
	return nil
}

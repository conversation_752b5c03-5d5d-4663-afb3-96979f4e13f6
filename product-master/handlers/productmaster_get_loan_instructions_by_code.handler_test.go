package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestProductMasterService_GetLoanInstruction(t *testing.T) {
	getLoanInstructionRequest := &api.GetLoanInstructionsByCodeRequest{
		ProductVariantCode: "DEFAULT_FLEXI_LOAN_TERM_LOAN",
		Version:            "1",
		InstructionType:    "WRITE_OFF",
	}
	defaultVersion := "1"
	tests := []struct {
		name                               string
		request                            *api.GetLoanInstructionsByCodeRequest
		expectedResponse                   *api.GetLoanInstructionsByCodeResponse
		expectedError                      error
		isErrorExpected                    bool
		findProductVariantResponse         []*storage.ProductVariant
		findProductVariantError            error
		findLoanInstructionVersionResponse []*storage.LoanInstructionVersion
		findLoanInstructionVersionError    error
		findLoanInstructionResponse        []*storage.LoanInstruction
		findLoanInstructionError           error
	}{
		{
			name:                               "happy-path create instruction successfully",
			request:                            getLoanInstructionRequest,
			findProductVariantResponse:         []*storage.ProductVariant{{ID: 1}},
			findLoanInstructionVersionResponse: []*storage.LoanInstructionVersion{{ID: 10, Version: "1"}},
			findLoanInstructionResponse:        responses.SampleLoanInstructionDBResponse(),
			expectedResponse:                   responses.GetLoanInstructionResponse(),
		},
		{
			name:            "error - incorrect instruction code for Line of Credit",
			request:         newLoanInstructionRequest("DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT", "WAIVE_OFF", defaultVersion),
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponseWithErrorDetail(http.StatusBadRequest,
				fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code), apiErr.ErrInvalidInstructionRequest.Message,
				[]servus.ErrorDetail{{
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Code), 10),
					Message:   apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Message,
				}}),
		},
		{
			name:            "error - incorrect instruction code for Term Loan",
			request:         newLoanInstructionRequest("DEFAULT_FLEXI_LOAN_TERM_LOAN", "BLOCK_UNBLOCK", defaultVersion),
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponseWithErrorDetail(http.StatusBadRequest,
				fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code), apiErr.ErrInvalidInstructionRequest.Message,
				[]servus.ErrorDetail{{
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Code), 10),
					Message:   apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Message,
				}}),
		},
		{
			name:            "error - incorrect instruction code for biz line of credit",
			request:         newLoanInstructionRequest("DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT", "ACCELERATE", defaultVersion),
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponseWithErrorDetail(http.StatusBadRequest,
				fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code), apiErr.ErrInvalidInstructionRequest.Message,
				[]servus.ErrorDetail{{
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Code), 10),
					Message:   apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Message,
				}}),
		},
		{
			name:            "error - incorrect instruction code for biz term loan",
			request:         newLoanInstructionRequest("DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN", "DEACTIVATION", defaultVersion),
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponseWithErrorDetail(http.StatusBadRequest,
				fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code), apiErr.ErrInvalidInstructionRequest.Message,
				[]servus.ErrorDetail{{
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Code), 10),
					Message:   apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Message,
				}}),
		},
		{
			name:            "error-path validation failures",
			request:         &api.GetLoanInstructionsByCodeRequest{},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponseWithErrorDetail(http.StatusBadRequest,
				fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code), apiErr.ErrInvalidInstructionRequest.Message,
				[]servus.ErrorDetail{{
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingProductVariantCode.Code), 10),
					Message:   apiErr.ErrMissingProductVariantCode.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingInstructionType.Code), 10),
					Message:   apiErr.ErrMissingInstructionType.Message,
				}}),
		},
		{
			name:                    "error-path find product variant failed",
			request:                 getLoanInstructionRequest,
			isErrorExpected:         true,
			expectedError:           apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
			findProductVariantError: data.ErrTimedOut,
		},
		{
			name:                            "error-path missing loan instruction version",
			request:                         getLoanInstructionRequest,
			findProductVariantResponse:      []*storage.ProductVariant{{ID: 1}},
			isErrorExpected:                 true,
			expectedError:                   apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
			findLoanInstructionVersionError: data.ErrNoData,
		},
		{
			name:                            "error-path find loan instruction version failed",
			request:                         getLoanInstructionRequest,
			findProductVariantResponse:      []*storage.ProductVariant{{ID: 1}},
			isErrorExpected:                 true,
			expectedError:                   apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
			findLoanInstructionVersionError: data.ErrTimedOut,
		},
		{
			name:                               "error-path missing loan instruction",
			findProductVariantResponse:         []*storage.ProductVariant{{ID: 1}},
			findLoanInstructionVersionResponse: []*storage.LoanInstructionVersion{{ID: 10}},
			request:                            getLoanInstructionRequest,
			isErrorExpected:                    true,
			expectedError:                      apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
			findLoanInstructionError:           data.ErrNoData,
		},
		{
			name:                               "error-path find loan instruction failed",
			findProductVariantResponse:         []*storage.ProductVariant{{ID: 1}},
			findLoanInstructionVersionResponse: []*storage.LoanInstructionVersion{{ID: 10}},
			request:                            getLoanInstructionRequest,
			isErrorExpected:                    true,
			expectedError:                      apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
			findLoanInstructionError:           data.ErrTimedOut,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockLoanInstructionVersion := &storage.MockILoanInstructionVersionDAO{}
			mockLoanInstruction := &storage.MockILoanInstructionDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(test.findProductVariantResponse, test.findProductVariantError)
			mockLoanInstructionVersion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return(test.findLoanInstructionVersionResponse, test.findLoanInstructionVersionError)
			mockLoanInstruction.On("Find", mock.Anything, mock.Anything).Return(test.findLoanInstructionResponse, test.findLoanInstructionError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO:         mockProductVariant,
				LoanInstructionVersionDAO: mockLoanInstructionVersion,
				LoanInstructionDAO:        mockLoanInstruction,
			}
			p := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			response, err := p.GetLoanInstructionsByCode(context.Background(), test.request)
			fmt.Println(response)
			if test.isErrorExpected {
				assert.Error(t, err)
				assert.Equal(t, test.expectedError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedResponse, response)
			}
		})
	}
}

// nolint: unparam
func newLoanInstructionRequest(code, instruction string, version string) *api.GetLoanInstructionsByCodeRequest {
	return &api.GetLoanInstructionsByCodeRequest{
		ProductVariantCode: code,
		Version:            version,
		InstructionType:    instruction,
	}
}

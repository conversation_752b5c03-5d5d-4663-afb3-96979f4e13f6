package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateDepositInterestAmountSlabRate creates a new deposit interest amount slab rate
func (p *ProductMasterService) CreateDepositInterestAmountSlabRate(ctx context.Context, req *api.CreateDepositInterestAmountSlabRateRequest) (*api.CreateDepositInterestAmountSlabRateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateDepositInterestAmountSlabRate"))
	slog.FromContext(ctx).Info(constants.CreateDepositInterestAmountSlabRateLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateDepositInterestAmountSlabRateRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.CreateDepositInterestAmountSlabRate(ctx, req, p.Store)
}

func validateCreateDepositInterestAmountSlabRateRequest(ctx context.Context, req *api.CreateDepositInterestAmountSlabRateRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.DepositInterestVersionID, "deposit-interest-version-id", apiErr.ErrMissingDepositInterestVersionID); err != nil {
		return err
	}
	if err := validations.ValidateAmount(ctx, req.FromAmount, "from-amount", apiErr.ErrInvalidFromAmount); err != nil {
		return err
	}
	if err := validations.ValidateAmount(ctx, req.ToAmount, "to-amount", apiErr.ErrInvalidToAmount); err != nil {
		return err
	}
	if err := validations.ValidatePercentage(ctx, req.BaseRateInterestSpreadPercentage, "base-rate-interest-spread-percentage", apiErr.ErrInvalidBaseRateInterestSpreadPercentage); err != nil {
		return err
	}
	if err := validations.ValidatePercentage(ctx, req.AbsoluteInterestRatePercentage, "absolute-interest-rate-percentage", apiErr.ErrInvalidAbsoluteInterestRatePercentage); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}

	fromAmount, _ := strconv.ParseFloat(req.FromAmount, 32)
	toAmount, _ := strconv.ParseFloat(req.ToAmount, 32)
	if fromAmount > toAmount {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, fmt.Sprintf("fromAmount %s cannot be greater than toAmount %s", req.FromAmount, req.ToAmount), apiCommon.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrFromAmountGreaterThanToAmount.Code), 10),
			apiErr.ErrFromAmountGreaterThanToAmount.Message)
	}
	return nil
}

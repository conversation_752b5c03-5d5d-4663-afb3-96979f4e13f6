package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetGeneralLedgerByCode fetches a general-ledger by code
func (p *ProductMasterService) GetGeneralLedgerByCode(ctx context.Context, req *api.GetGeneralLedgerByCodeRequest) (*api.GetGeneralLedgerByCodeResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetGeneralLedgerByCode"))
	slog.FromContext(ctx).Info(constants.GetGeneralLedgerByCodeLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetGeneralLedgerByCodeRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.GetGeneralLedgerByCode(ctx, req, p.Store)
}

func validateGetGeneralLedgerByCodeRequest(ctx context.Context, req *api.GetGeneralLedgerByCodeRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	return nil
}

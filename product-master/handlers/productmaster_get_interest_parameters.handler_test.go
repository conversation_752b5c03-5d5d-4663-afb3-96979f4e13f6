package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetInterestParameters(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                            string
		requestParams                       *api.GetInterestParametersRequest
		responseParams                      *api.GetInterestParametersResponse
		productVariantDAOResponse           []*storage.ProductVariant
		productVariantDAOError              error
		productVariantParameterDAOResponse  []*storage.ProductVariantParameter
		productVariantParameterDAOError     error
		productDAOResponse                  *storage.Product
		productDAOError                     error
		productTemplateParameterDAOResponse []*storage.ProductTemplateParameter
		productTemplateParameterDAOError    error
		parameterScheduleDAOResponse        []*storage.ParameterChangeSchedule
		depositInterestSlabRateDAOResponse  []*storage.DepositInterestAmountSlabRate
		depositInterestVersion              []*storage.DepositInterestVersion
		depositInterest                     []*storage.DepositInterest
		parameterScheduleDAOError           error
		isErrorExpected                     bool
		storeLoadError                      error
		expectedError                       error
		responseError                       error
	}{
		{
			testDesc: "happy path - get notified interest params",
			productVariantDAOResponse: []*storage.ProductVariant{{
				ID:        1,
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
				IsNotified:   api.Status_N,
			},
			responseParams: &api.GetInterestParametersResponse{
				ParameterKey:              "test_key",
				ParameterValue:            "{\"tier1\":{\"min\":\"100\",\"max\":\"200\",\"rate\":\"2\",\"minAmount\":\"100\",\"maxAmount\":\"200\"}}",
				ParameterVersion:          "1",
				SmartContractVersionID:    "108",
				EffectiveScheduleDate:     now,
				EffectiveNotificationDate: now,
			},
			depositInterestSlabRateDAOResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			depositInterestVersion: []*storage.DepositInterestVersion{{
				ID:            1,
				Version:       "1",
				EffectiveDate: now,
			}},

			isErrorExpected: false,
			storeLoadError:  nil,
			expectedError:   nil,
		},
		{
			testDesc: "happy path - get Scheduled interest params",
			productVariantDAOResponse: []*storage.ProductVariant{{
				ID:        1,
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
				IsScheduled:  api.Status_N,
			},
			responseParams: &api.GetInterestParametersResponse{
				ParameterKey:              "test_key",
				ParameterValue:            "{\"tier1\":{\"min\":\"100\",\"max\":\"200\",\"rate\":\"2\",\"minAmount\":\"100\",\"maxAmount\":\"200\"}}",
				ParameterVersion:          "1",
				SmartContractVersionID:    "108",
				EffectiveScheduleDate:     now,
				EffectiveNotificationDate: now,
			},
			depositInterestSlabRateDAOResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			depositInterestVersion: []*storage.DepositInterestVersion{{
				ID:            1,
				Version:       "1",
				EffectiveDate: now,
			}},

			isErrorExpected: false,
			storeLoadError:  nil,
			expectedError:   nil,
		},
		{
			testDesc: "Error Path - validation failed",
			productVariantDAOResponse: []*storage.ProductVariant{{
				ID:        1,
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
			},
			responseParams:  nil,
			isErrorExpected: true,
			responseError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10),
				apiErr.ErrInvalidRequestParameter.Message),
			storeLoadError: nil,
			expectedError:  nil,
		},
		{
			testDesc:                  "Error path - product master store load error",
			productVariantDAOResponse: nil,
			productVariantDAOError:    data.ErrTimedOut,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
				IsScheduled:  api.Status_N,
			},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			responseParams:  nil,
			isErrorExpected: true,
			responseError: apiCommon.BuildLoadDataFromDBErrResponse(context.Background(),
				"unable to load product-variant from database for request with code: test-param-key", data.ErrTimedOut),
			storeLoadError: nil,
			expectedError:  nil,
		},
		{
			testDesc:                  "Error path - product master store load error",
			productVariantDAOResponse: nil,
			productVariantDAOError:    data.ErrTimedOut,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
				IsNotified:   api.Status_N,
			},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			responseParams:  nil,
			isErrorExpected: true,
			responseError: apiCommon.BuildLoadDataFromDBErrResponse(context.Background(),
				"unable to load product-variant from database for request with code: test-param-key", data.ErrTimedOut),
			storeLoadError: nil,
			expectedError:  nil,
		},
		{
			testDesc: "Error path - deposits-core DB error - UnScheduled Params",
			productVariantDAOResponse: []*storage.ProductVariant{{
				ID:        1,
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
				IsScheduled:  api.Status_N,
			},
			responseParams: nil,
			depositInterestSlabRateDAOResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			depositInterestVersion: []*storage.DepositInterestVersion{{
				ID:            1,
				Version:       "1",
				EffectiveDate: now,
			}},

			isErrorExpected:           true,
			parameterScheduleDAOError: errors.New("database load error"),
			storeLoadError:            nil,
			expectedError:             nil,
		},
		{
			testDesc: "Error path - deposits-core DB error - Not notified params",
			productVariantDAOResponse: []*storage.ProductVariant{{
				ID:        1,
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
				IsNotified:   api.Status_N,
			},
			responseParams: nil,
			depositInterestSlabRateDAOResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			depositInterestVersion: []*storage.DepositInterestVersion{{
				ID:            1,
				Version:       "1",
				EffectiveDate: now,
			}},

			isErrorExpected:           true,
			parameterScheduleDAOError: errors.New("database load error"),
			storeLoadError:            nil,
			expectedError:             nil,
		},
		{
			testDesc: "Error path - Invalid Input",
			productVariantDAOResponse: []*storage.ProductVariant{{
				ID:        1,
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			requestParams: &api.GetInterestParametersRequest{
				ParameterKey: "test-param-key",
				IsNotified:   "yes",
			},
			responseParams: nil,
			depositInterestSlabRateDAOResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			depositInterestVersion: []*storage.DepositInterestVersion{{
				ID:            1,
				Version:       "1",
				EffectiveDate: now,
			}},
			responseError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10),
				apiErr.ErrInvalidRequestParameter.Message),
			isErrorExpected: true,
			storeLoadError:  nil,
			expectedError:   nil,
		},
	}
	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.Anything).Return(s.productVariantDAOResponse, s.productVariantDAOError)

			depositInterest := &storage.MockIDepositInterestDAO{}
			depositInterest.On("Find", mock.Anything).Return(s.depositInterest, nil)

			depositInterestVersion := &storage.MockIDepositInterestVersionDAO{}
			depositInterestVersion.On("Find", mock.Anything, mock.Anything).Return(s.depositInterestVersion, s.storeLoadError)

			depositInterestSlabRate := &storage.MockIDepositInterestAmountSlabRateDAO{}
			depositInterestSlabRate.On("Find", mock.Anything, mock.Anything).Return(s.depositInterestSlabRateDAOResponse, nil)

			parameterScheduleDAOResponse := &storage.MockIParameterChangeScheduleDAO{}
			parameterScheduleDAOResponse.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(s.parameterScheduleDAOResponse, s.parameterScheduleDAOError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO:                mockProductVariant,
				DepositInterestDAO:               depositInterest,
				DepositInterestVersionDAO:        depositInterestVersion,
				DepositInterestAmountSlabRateDAO: depositInterestSlabRate,
				ParameterChangeScheduleDAO:       parameterScheduleDAOResponse,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			interestParameters, err := service.GetInterestParameters(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.responseParams, interestParameters)
			}
		})
	}
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/txncatalogue"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// UpdateTransactionCatalogueStatus updates the status of Transaction Catalogue
func (p *ProductMasterService) UpdateTransactionCatalogueStatus(ctx context.Context, req *api.UpdateTransactionCatalogueStatusRequest) (*api.UpdateTransactionCatalogueStatusResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("UpdateTransactionCatalogueStatus"))
	slog.FromContext(ctx).Info(constants.UpdateTransactionCatalogueStatusLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateUpdateTransactionCatalogueStatusRequest(ctx, req); err != nil {
		return nil, err
	}

	return txncatalogue.UpdateTransactionCatalogueStatus(ctx, req, p.Store)
}

func validateUpdateTransactionCatalogueStatusRequest(ctx context.Context, req *api.UpdateTransactionCatalogueStatusRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	if err := validations.ValidateEntityStatus(ctx, string(req.Status)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.UpdatedBy, "updated-by", apiErr.ErrMissingUpdatedBy); err != nil {
		return err
	}
	return nil
}

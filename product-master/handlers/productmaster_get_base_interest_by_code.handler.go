package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetBaseInterestByCode fetches a base interest by code
func (p *ProductMasterService) GetBaseInterestByCode(ctx context.Context, req *api.GetBaseInterestByCodeRequest) (*api.GetBaseInterestByCodeResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetBaseInterestByCode"))
	slog.FromContext(ctx).Info(constants.GetBaseInterestByCodeLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetBaseInterestByCodeRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetBaseInterestByCode(ctx, req, p.Store)
}

func validateGetBaseInterestByCodeRequest(ctx context.Context, req *api.GetBaseInterestByCodeRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	return nil
}

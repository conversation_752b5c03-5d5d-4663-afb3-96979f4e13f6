package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateLoanInterest(t *testing.T) {
	now := time.Now().UTC()
	dummyCode := "test-loan-interest-code"
	dummyInterestName := "test-loan-interest-name"
	dummyProductVariant := "test-product-variant"
	dummyIdemKey := "test-idempotency-key"
	dummyCreatedBy := "unit-test"
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateLoanInterestRequest
		storeLoadResponse []*storage.LoanInterest
		expectedResponse  *api.CreateLoanInterestResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - loan-interest linked to base interest created",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-base-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			storeLoadResponse: []*storage.LoanInterest{{
				PublicID:           "test-id",
				ProductVariantID:   1,
				IsLinkedToBaseRate: true,
				BaseInterestID: sql.NullInt64{
					Int64: 2,
					Valid: true,
				},
				Code: dummyCode,
				Name: dummyInterestName,
				Description: sql.NullString{
					String: "get test loan-interest",
					Valid:  true,
				},
				Currency:              "SGD",
				RoundOffType:          "FLOOR",
				InterestSlabUnitType:  "AMOUNT",
				InterestSlabStructure: "ABSOLUTE",
				InterestType:          "NORMAL",
				CreatedBy:             dummyCreatedBy,
				CreatedAt:             now,
				UpdatedBy:             dummyCreatedBy,
				UpdatedAt:             now,
			}},
			expectedResponse: &api.CreateLoanInterestResponse{
				LoanInterest: &api.LoanInterest{
					Id:                    "test-id",
					ProductVariantID:      dummyProductVariant,
					IsLinkedToBaseRate:    true,
					BaseInterestID:        "test-base-interest-id",
					Code:                  dummyCode,
					Name:                  dummyInterestName,
					Description:           "get test loan-interest",
					Currency:              api.Currency_SGD,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestType:          api.InterestType_NORMAL,
					InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             dummyCreatedBy,
					CreatedAt:             now,
					UpdatedBy:             dummyCreatedBy,
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "happy path - loan-interest not linked to base interest created",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    false,
				BaseInterestID:        "",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			storeLoadResponse: []*storage.LoanInterest{{
				PublicID:           "test-id",
				ProductVariantID:   1,
				IsLinkedToBaseRate: false,
				BaseInterestID:     sql.NullInt64{},
				Code:               dummyCode,
				Name:               dummyInterestName,
				Description: sql.NullString{
					String: "get test loan-interest",
					Valid:  true,
				},
				Currency:              "SGD",
				RoundOffType:          "FLOOR",
				InterestSlabUnitType:  "AMOUNT",
				InterestSlabStructure: "ABSOLUTE",
				InterestType:          "NORMAL",
				CreatedBy:             dummyCreatedBy,
				CreatedAt:             now,
				UpdatedBy:             dummyCreatedBy,
				UpdatedAt:             now,
			}},
			expectedResponse: &api.CreateLoanInterestResponse{
				LoanInterest: &api.LoanInterest{
					Id:                    "test-id",
					ProductVariantID:      dummyProductVariant,
					IsLinkedToBaseRate:    false,
					BaseInterestID:        "",
					Code:                  dummyCode,
					Name:                  dummyInterestName,
					Description:           "get test loan-interest",
					Currency:              api.Currency_SGD,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
					InterestType:          api.InterestType_NORMAL,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             dummyCreatedBy,
					CreatedAt:             now,
					UpdatedBy:             dummyCreatedBy,
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - product variant id is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      "",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - base interest id is missing when IsLinkedToBaseRate is true",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingLinkedBaseInterestID.Code), 10), apiErr.ErrMissingLinkedBaseInterestID.Message),
		},
		{
			testDesc:       "error path - code is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  "",
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc:       "error path - name is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  "",
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingName.Code), 10), apiErr.ErrMissingName.Message),
		},
		{
			testDesc:       "error path - currency is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidCurrency.Code), 10), apiErr.ErrInvalidCurrency.Message),
		},
		{
			testDesc:       "error path - invalid currency",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency("INVALID_CURRENCY"),
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidCurrency.Code), 10), apiErr.ErrInvalidCurrency.Message),
		},
		{
			testDesc:       "error path - round off type is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRoundOffType.Code), 10), apiErr.ErrInvalidRoundOffType.Message),
		},
		{
			testDesc:       "error path - invalid round off type",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency("INVALID_CURRENCY"),
				RoundOffType:          api.RoundOffType("INVALID_TYPE"),
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRoundOffType.Code), 10), apiErr.ErrInvalidRoundOffType.Message),
		},
		{
			testDesc:       "error path - interest slab type is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabType.Code), 10), apiErr.ErrInvalidInterestSlabType.Message),
		},
		{
			testDesc:       "error path - invalid interest slab type",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType("INVALID_TYPE"),
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabType.Code), 10), apiErr.ErrInvalidInterestSlabType.Message),
		},
		{
			testDesc:       "error path - interest slab structure is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:     dummyProductVariant,
				IsLinkedToBaseRate:   true,
				BaseInterestID:       "test-loan-interest-id",
				Code:                 dummyCode,
				Name:                 dummyInterestName,
				Description:          "create loan interest",
				Currency:             api.Currency_SGD,
				RoundOffType:         api.RoundOffType_FLOOR,
				InterestType:         api.InterestType_NORMAL,
				InterestSlabUnitType: api.InterestSlabType_AMOUNT,
				CreatedBy:            dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabStructure.Code), 10), apiErr.ErrInvalidInterestSlabStructure.Message),
		},
		{
			testDesc:       "error path - invalid interest slab structure",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure("INVALID_STRUCTURE"),
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabStructure.Code), 10), apiErr.ErrInvalidInterestSlabStructure.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: dummyIdemKey,
			requestParams: &api.CreateLoanInterestRequest{
				ProductVariantID:      dummyProductVariant,
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-loan-interest-id",
				Code:                  dummyCode,
				Name:                  dummyInterestName,
				Description:           "create loan interest",
				Currency:              api.Currency_SGD,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
				InterestType:          api.InterestType_NORMAL,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             dummyCreatedBy,
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterestDao := &storage.MockIBaseInterestDAO{}
			mockBaseInterestDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{{}}, nil)
			mockProductVariantDao := &storage.MockIProductVariantDAO{}
			mockProductVariantDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{}}, nil)
			mockLoanInterest := &storage.MockILoanInterestDAO{}
			mockLoanInterest.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockLoanInterest.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				LoanInterestDAO:   mockLoanInterest,
				BaseInterestDAO:   mockBaseInterestDao,
				ProductVariantDAO: mockProductVariantDao,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			loanInterest, err := service.CreateLoanInterest(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, loanInterest)
			}
		})
	}
}

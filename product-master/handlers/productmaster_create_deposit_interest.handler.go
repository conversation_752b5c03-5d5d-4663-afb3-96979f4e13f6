package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateDepositInterest creates a new deposit interest
func (p *ProductMasterService) CreateDepositInterest(ctx context.Context, req *api.CreateDepositInterestRequest) (*api.CreateDepositInterestResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateDepositInterest"))
	slog.FromContext(ctx).Info(constants.CreateDepositInterestLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateCreateDepositInterestRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.CreateDepositInterest(ctx, req, p.Store)
}

func validateCreateDepositInterestRequest(ctx context.Context, req *api.CreateDepositInterestRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantID, "product-variant-id", apiErr.ErrMissingProductVariantID); err != nil {
		return err
	}
	if req.IsLinkedToBaseRate {
		if err := validations.ValidateFieldNotEmpty(ctx, req.BaseInterestID, "base-interest-id", apiErr.ErrMissingLinkedBaseInterestID); err != nil {
			return err
		}
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Name, "name", apiErr.ErrMissingName); err != nil {
		return err
	}
	if err := validations.ValidateCurrency(ctx, string(req.Currency)); err != nil {
		return err
	}
	if err := validations.ValidateRoundOffType(ctx, string(req.RoundOffType)); err != nil {
		return err
	}
	if err := validations.ValidateInterestSlabType(ctx, string(req.InterestSlabType)); err != nil {
		return err
	}
	if err := validations.ValidateInterestSlabStructure(ctx, string(req.InterestSlabStructure)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

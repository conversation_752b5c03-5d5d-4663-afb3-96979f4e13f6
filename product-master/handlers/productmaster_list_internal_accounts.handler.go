package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// ListInternalAccounts fetches a list of internal account by code and generalLedgerID
func (p *ProductMasterService) ListInternalAccounts(ctx context.Context, req *api.ListInternalAccountsRequest) (*api.ListInternalAccountsResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListInternalAccounts"))
	slog.FromContext(ctx).Info(constants.ListInternalAccountsLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateListInternalAccountsRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.ListInternalAccounts(ctx, req, p.Store)
}

func validateListInternalAccountsRequest(ctx context.Context, req *api.ListInternalAccountsRequest) error {
	if req.Code == "" && req.GeneralLedgerID == "" {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "At least one field is necessary (code, generalLedgerID)")
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingAtLeastOneField.Code), 10),
			apiErr.ErrMissingAtLeastOneField.Message)
	}
	return nil
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_UpdateGeneralLedgerStatus(t *testing.T) {
	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	now := time.Now().UTC()
	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := appConfig.Locale.Currency
	scenarios := []struct {
		testDesc           string
		idempotencyKey     string
		requestParams      *api.UpdateGeneralLedgerStatusRequest
		storeLoadResponse1 []*storage.GeneralLedger
		storeLoadResponse2 []*storage.GeneralLedger
		expectedResponse   *api.UpdateGeneralLedgerStatusResponse
		isErrorExpected    bool
		storeUpdateError   error
		storeLoadError     error
		expectedError      error
	}{
		{
			testDesc:       "happy path - general ledger updated - ACTIVE to INACTIVE",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			storeLoadResponse1: []*storage.GeneralLedger{{
				PublicID: "test-id",
				Code:     "test-general-ledger-code",
				Name:     "test-general-ledger-name",
				Description: sql.NullString{
					String: "create test general-ledger",
					Valid:  true,
				},
				Currency:  currency,
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			storeLoadResponse2: []*storage.GeneralLedger{{
				PublicID: "test-id",
				Code:     "test-general-ledger-code",
				Name:     "test-general-ledger-name",
				Description: sql.NullString{
					String: "create test general-ledger",
					Valid:  true,
				},
				Currency:  currency,
				Status:    "INACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.UpdateGeneralLedgerStatusResponse{
				GeneralLedger: &api.GeneralLedger{
					Id:          "test-id",
					Code:        "test-general-ledger-code",
					Name:        "test-general-ledger-name",
					Description: "create test general-ledger",
					Status:      api.EntityStatus_INACTIVE,
					Currency:    api.Currency(currency),
					CreatedBy:   "unit-test",
					CreatedAt:   nowMinus10,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "happy path - general ledger updated - INACTIVE to ACTIVE",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_ACTIVE,
				UpdatedBy: "unit-test",
			},
			storeLoadResponse1: []*storage.GeneralLedger{{
				PublicID: "test-id",
				Code:     "test-general-ledger-code",
				Name:     "test-general-ledger-name",
				Description: sql.NullString{
					String: "create test general-ledger",
					Valid:  true,
				},
				Currency:  currency,
				Status:    "INACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			storeLoadResponse2: []*storage.GeneralLedger{{
				PublicID: "test-id",
				Code:     "test-general-ledger-code",
				Name:     "test-general-ledger-name",
				Description: sql.NullString{
					String: "create test general-ledger",
					Valid:  true,
				},
				Currency:  currency,
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.UpdateGeneralLedgerStatusResponse{
				GeneralLedger: &api.GeneralLedger{
					Id:          "test-id",
					Code:        "test-general-ledger-code",
					Name:        "test-general-ledger-name",
					Description: "create test general-ledger",
					Status:      api.EntityStatus_ACTIVE,
					Currency:    api.Currency(currency),
					CreatedBy:   "unit-test",
					CreatedAt:   nowMinus10,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc:       "error path - status is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidEntityStatus.Code), 10), apiErr.ErrInvalidEntityStatus.Message),
		},
		{
			testDesc:       "error path - invalid status",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    "INVALID_STATUS",
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidEntityStatus.Code), 10), apiErr.ErrInvalidEntityStatus.Message),
		},
		{
			testDesc:       "error path - updated by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingUpdatedBy.Code), 10), apiErr.ErrMissingUpdatedBy.Message),
		},
		{
			testDesc: "error path - general-ledger not found in database",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc:       "error path - database update error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateGeneralLedgerStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			storeLoadResponse1: []*storage.GeneralLedger{{
				PublicID: "test-id",
				Code:     "test-general-ledger-code",
				Name:     "test-general-ledger-name",
				Description: sql.NullString{
					String: "create test general-ledger",
					Valid:  true,
				},
				Status:    "ACTIVE",
				Currency:  currency,
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			isErrorExpected:  true,
			storeUpdateError: errors.New("database update error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseUpdate.Code), 10), apiErr.ErrDatabaseUpdate.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
			mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse1, s.storeLoadError).Once()
			mockGeneralLedger.On("Update", mock.Anything, mock.Anything).Return(s.storeUpdateError)
			mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse2, s.storeLoadError).Once()

			mockStore := &storage.DBStore{
				GeneralLedgerDAO: mockGeneralLedger,
			}
			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			generalLedger, err := service.UpdateGeneralLedgerStatus(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, generalLedger)
			}
		})
	}
}

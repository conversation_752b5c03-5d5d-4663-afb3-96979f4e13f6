package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductByCode fetches a product by code
func (p *ProductMasterService) GetProductByCode(ctx context.Context, req *api.GetProductByCodeRequest) (*api.GetProductByCodeResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductByCode"))
	slog.FromContext(ctx).Info(constants.GetProductByCodeLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductByCodeRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.GetProductByCode(ctx, req, p.Store)
}

func validateGetProductByCodeRequest(ctx context.Context, req *api.GetProductByCodeRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	return nil
}

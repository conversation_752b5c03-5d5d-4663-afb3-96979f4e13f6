package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateTransactionCatalogue(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateTransactionCatalogueRequest
		storeLoadResponse []*storage.TransactionCatalogue
		expectedResponse  *api.CreateTransactionCatalogueResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - product created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			storeLoadResponse: []*storage.TransactionCatalogue{{
				PublicID:       "test-id",
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description: sql.NullString{
					String: "create transaction catalogue",
					Valid:  true,
				},
				Status:    string(api.EntityStatus_ACTIVE),
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.CreateTransactionCatalogueResponse{
				TransactionCatalogue: &api.TransactionCatalogue{
					Id:             "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description:    "create transaction catalogue",
					Status:         api.EntityStatus_ACTIVE,
					CreatedBy:      "unit-test",
					CreatedAt:      now,
					UpdatedBy:      "unit-test",
					UpdatedAt:      now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - domain is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingDomain.Code), 10), apiErr.ErrMissingDomain.Message),
		},
		{
			testDesc:       "error path - txn type is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingTxnType.Code), 10), apiErr.ErrMissingTxnType.Message),
		},
		{
			testDesc:       "error path - txn subtype is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingTxnSubType.Code), 10), apiErr.ErrMissingTxnSubType.Message),
		},
		{
			testDesc:       "error path - display name is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingDisplayName.Code), 10), apiErr.ErrMissingDisplayName.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateTransactionCatalogueRequest{
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description:    "create transaction catalogue",
				CreatedBy:      "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockTransactionCatalogue := &storage.MockITransactionCatalogueDAO{}
			mockTransactionCatalogue.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				TransactionCatalogueDAO: mockTransactionCatalogue,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			transactionCatalogue, err := service.CreateTransactionCatalogue(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, transactionCatalogue)
			}
		})
	}
}

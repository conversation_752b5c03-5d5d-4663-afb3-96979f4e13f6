package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestProductMasterService_CreateLoanInstruction(t *testing.T) {
	createLoanVersionRequest := &api.CreateLoanInstructionRequest{
		LoanInstructionVersionID: "1c3a0ca4-9530-48fa-897d-ab2a960a33e0",
		Code:                     "DECEASED",
		Name:                     "Deceased",
		CreatedBy:                "MANUAL",
	}
	tests := []struct {
		name                               string
		request                            *api.CreateLoanInstructionRequest
		expectedResponse                   *api.CreateLoanInstructionResponse
		expectedError                      error
		isErrorExpected                    bool
		findLoanInstructionVersionResponse []*storage.LoanInstructionVersion
		findLoanInstructionVersionError    error
		saveLoanInstructionError           error
		findLoanInstructionResponse        []*storage.LoanInstruction
		findLoanInstructionError           error
	}{
		{
			name:                               "happy-path create instruction successfully",
			request:                            createLoanVersionRequest,
			findLoanInstructionVersionResponse: []*storage.LoanInstructionVersion{{ID: 10}},
			findLoanInstructionResponse:        responses.SampleLoanInstructionDBResponse(),
			expectedResponse:                   responses.CreateLoanInstructionResponse(),
		},
		{
			name:            "error-path validation failures",
			request:         &api.CreateLoanInstructionRequest{},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponseWithErrorDetail(http.StatusBadRequest,
				fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code), apiErr.ErrInvalidInstructionRequest.Message,
				[]servus.ErrorDetail{{
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingLoanInstructionVersionID.Code), 10),
					Message:   apiErr.ErrMissingLoanInstructionVersionID.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10),
					Message:   apiErr.ErrMissingCode.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingName.Code), 10),
					Message:   apiErr.ErrMissingName.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10),
					Message:   apiErr.ErrMissingCreatedBy.Message,
				}}),
		},
		{
			name:                            "error-path find product variant failed",
			request:                         createLoanVersionRequest,
			isErrorExpected:                 true,
			expectedError:                   apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
			findLoanInstructionVersionError: data.ErrTimedOut,
		},
		{
			name:                               "error-path save loan instruction failed",
			request:                            createLoanVersionRequest,
			findLoanInstructionVersionResponse: []*storage.LoanInstructionVersion{{ID: 10}},
			isErrorExpected:                    true,
			expectedError:                      apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
			saveLoanInstructionError:           data.ErrTimedOut,
		},
		{
			name:                               "error-path find loan instruction failed",
			findLoanInstructionVersionResponse: []*storage.LoanInstructionVersion{{ID: 10}},
			request:                            createLoanVersionRequest,
			isErrorExpected:                    true,
			expectedError:                      apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
			findLoanInstructionError:           data.ErrTimedOut,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockLoanInstructionVersion := &storage.MockILoanInstructionVersionDAO{}
			mockLoanInstruction := &storage.MockILoanInstructionDAO{}
			mockLoanInstructionVersion.On("Find", mock.Anything, mock.Anything).Return(test.findLoanInstructionVersionResponse, test.findLoanInstructionVersionError)
			mockLoanInstruction.On("Save", mock.Anything, mock.Anything).Return(test.saveLoanInstructionError)
			mockLoanInstruction.On("Find", mock.Anything, mock.Anything).Return(test.findLoanInstructionResponse, test.findLoanInstructionError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				LoanInstructionVersionDAO: mockLoanInstructionVersion,
				LoanInstructionDAO:        mockLoanInstruction,
			}
			p := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			response, err := p.CreateLoanInstruction(context.Background(), test.request)
			if test.isErrorExpected {
				assert.Error(t, err)
				assert.Equal(t, test.expectedError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedResponse, response)
			}
		})
	}
}

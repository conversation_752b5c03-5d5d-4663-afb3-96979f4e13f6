package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductTemplate fetches a product template by id
func (p *ProductMasterService) GetProductTemplate(ctx context.Context, req *api.GetProductTemplateRequest) (*api.GetProductTemplateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductTemplate"))
	slog.FromContext(ctx).Info(constants.GetProductTemplateLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductTemplateRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.GetProductTemplate(ctx, req, p.Store)
}

func validateGetProductTemplateRequest(ctx context.Context, req *api.GetProductTemplateRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_UpdateProductTemplateStatus(t *testing.T) {
	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc           string
		idempotencyKey     string
		requestParams      *api.UpdateProductTemplateStatusRequest
		storeLoadResponse1 []*storage.ProductTemplate
		storeLoadResponse2 []*storage.ProductTemplate
		expectedResponse   *api.UpdateProductTemplateStatusResponse
		isErrorExpected    bool
		storeUpdateError   error
		storeLoadError     error
		expectedError      error
	}{
		{
			testDesc:       "happy path - product template updated - ACTIVE to INACTIVE",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			storeLoadResponse1: []*storage.ProductTemplate{{
				PublicID: "test-id",
				Name:     "test-product-template-name",
				Description: sql.NullString{
					String: "create test product template",
					Valid:  true,
				},
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: nowMinus10,
			}},
			storeLoadResponse2: []*storage.ProductTemplate{{
				PublicID: "test-id",
				Name:     "test-product-template-name",
				Description: sql.NullString{
					String: "create test product template",
					Valid:  true,
				},
				Status:    "INACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.UpdateProductTemplateStatusResponse{
				ProductTemplate: &api.ProductTemplate{
					Id:          "test-id",
					Name:        "test-product-template-name",
					Description: "create test product template",
					Status:      "INACTIVE",
					CreatedBy:   "unit-test",
					CreatedAt:   nowMinus10,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "happy path - product template updated - INACTIVE to ACTIVE",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_ACTIVE,
				UpdatedBy: "unit-test",
			},
			storeLoadResponse1: []*storage.ProductTemplate{{
				PublicID: "test-id",
				Name:     "test-product-template-name",
				Description: sql.NullString{
					String: "create test product template",
					Valid:  true,
				},
				Status:    "INACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: nowMinus10,
			}},
			storeLoadResponse2: []*storage.ProductTemplate{{
				PublicID: "test-id",
				Name:     "test-product-template-name",
				Description: sql.NullString{
					String: "create test product template",
					Valid:  true,
				},
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.UpdateProductTemplateStatusResponse{
				ProductTemplate: &api.ProductTemplate{
					Id:          "test-id",
					Name:        "test-product-template-name",
					Description: "create test product template",
					Status:      "ACTIVE",
					CreatedBy:   "unit-test",
					CreatedAt:   nowMinus10,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc:       "error path - status is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidEntityStatus.Code), 10), apiErr.ErrInvalidEntityStatus.Message),
		},
		{
			testDesc:       "error path - invalid status",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    "INVALID_STATUS",
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidEntityStatus.Code), 10), apiErr.ErrInvalidEntityStatus.Message),
		},
		{
			testDesc:       "error path - updated by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingUpdatedBy.Code), 10), apiErr.ErrMissingUpdatedBy.Message),
		},
		{
			testDesc: "error path - product template not found in database",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc:       "error path - database update error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateStatusRequest{
				Id:        "test-id",
				Status:    api.EntityStatus_INACTIVE,
				UpdatedBy: "unit-test",
			},
			storeLoadResponse1: []*storage.ProductTemplate{{
				PublicID: "test-id",
				Name:     "test-product-template-name",
				Description: sql.NullString{
					String: "create test product template",
					Valid:  true,
				},
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			isErrorExpected:  true,
			storeUpdateError: errors.New("database update error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseUpdate.Code), 10), apiErr.ErrDatabaseUpdate.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductTemplate := &storage.MockIProductTemplateDAO{}
			mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse1, s.storeLoadError).Once()
			mockProductTemplate.On("Update", mock.Anything, mock.Anything).Return(s.storeUpdateError)
			mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse2, nil).Once()

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductTemplateDAO: mockProductTemplate,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			product, err := service.UpdateProductTemplateStatus(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, product)
			}
		})
	}
}

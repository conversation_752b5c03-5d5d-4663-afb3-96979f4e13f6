package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateProductVariantTransactionCatalogueInternalAccountMapping(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest
		storeLoadResponse []*storage.ProductVariantTransactionCatalogueInternalAccountMapping
		expectedResponse  *api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - product created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{
				InternalAccountID: "test-internal-account-id",
				ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
				IdentifierKey: "test-identifier",
				CreatedBy:     "unit-test",
			},
			storeLoadResponse: []*storage.ProductVariantTransactionCatalogueInternalAccountMapping{{
				PublicID:          "test-id",
				InternalAccountID: 1,
				ProductVariantTransactionCatalogueMappingID: 2,
				IdentifierKey: "test-identifier",
				Status:        "ACTIVE",
				CreatedBy:     "unit-test",
				CreatedAt:     now,
				UpdatedBy:     "unit-test",
				UpdatedAt:     now,
			}},
			expectedResponse: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse{
				ProductVariantTransactionCatalogueInternalAccountMapping: &api.ProductVariantTransactionCatalogueInternalAccountMapping{
					Id:                "test-id",
					InternalAccountID: "test-internal-account-id",
					ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
					IdentifierKey: "test-identifier",
					Status:        api.EntityStatus_ACTIVE,
					CreatedBy:     "unit-test",
					CreatedAt:     now,
					UpdatedBy:     "unit-test",
					UpdatedAt:     now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{
				InternalAccountID: "test-internal-account-id",
				ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
				IdentifierKey: "test-identifier",
				CreatedBy:     "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - internal account id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{
				InternalAccountID: "",
				ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
				IdentifierKey: "test-identifier",
				CreatedBy:     "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingInternalAccountID.Code), 10), apiErr.ErrMissingInternalAccountID.Message),
		},
		{
			testDesc:       "error path - product variant transaction catalogue mapping id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{
				InternalAccountID: "test-internal-account-id",
				ProductVariantTransactionCatalogueMappingID: "",
				IdentifierKey: "test-identifier",
				CreatedBy:     "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductVariantTransactionCatalogueMappingID.Code), 10), apiErr.ErrMissingProductVariantTransactionCatalogueMappingID.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{
				InternalAccountID: "test-internal-account-id",
				ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
				IdentifierKey: "test-identifier",
				CreatedBy:     "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{
				InternalAccountID: "test-internal-account-id",
				ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
				IdentifierKey: "test-identifier",
				CreatedBy:     "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{
				InternalAccountID: "test-internal-account-id",
				ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
				IdentifierKey: "test-identifier",
				CreatedBy:     "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockInternalAccountDAO := &storage.MockIInternalAccountDAO{}
			mockInternalAccountDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{{}}, nil)
			mappingDAO2 := &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
			mappingDAO2.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueMapping{{}}, nil)
			mappingDAO := &storage.MockIProductVariantTransactionCatalogueInternalAccountMappingDAO{}
			mappingDAO.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mappingDAO.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantTransactionCatalogueInternalAccountMappingDAO: mappingDAO,
				InternalAccountDAO:                           mockInternalAccountDAO,
				ProductVariantTransactionCatalogueMappingDAO: mappingDAO2,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			productVariantTransactionCatalogueMapping, err := service.CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, productVariantTransactionCatalogueMapping)
			}
		})
	}
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductVariantByCode fetches a product variant by code
func (p *ProductMasterService) GetProductVariantByCode(ctx context.Context, req *api.GetProductVariantByCodeRequest) (*api.GetProductVariantByCodeResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductVariantByCode"))
	slog.FromContext(ctx).Info(constants.GetProductVariantByCodeLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductVariantByCodeRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.GetProductVariantByCode(ctx, req, p.Store)
}

func validateGetProductVariantByCodeRequest(ctx context.Context, req *api.GetProductVariantByCodeRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductVariantTransactionCatalogueInternalAccountMappingByKey fetches a product-variant-transaction-catalogue-internal-account-mapping by identifer key
func (p *ProductMasterService) GetProductVariantTransactionCatalogueInternalAccountMappingByKey(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductVariantTransactionCatalogueInternalAccountMappingByKey"))
	slog.FromContext(ctx).Info(constants.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.GetProductVariantTransactionCatalogueInternalAccountMappingByKey(ctx, req, p.Store)
}

func validateGetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.IdentifierKey, "identifier-key", apiErr.ErrMissingIdentifierKey); err != nil {
		return err
	}
	return nil
}

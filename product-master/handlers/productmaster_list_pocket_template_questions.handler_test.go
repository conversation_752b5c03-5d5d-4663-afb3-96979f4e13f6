package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

func TestListPocketTemplateQuestions(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                            string
		requestParams                       *api.ListPocketTemplateQuestionsRequest
		expectedResp                        *api.ListPocketTemplateQuestionsResponse
		pocketTemplateDBResp                []*storage.PocketTemplate
		pocketTemplateQuestionDBResp        []*storage.PocketTemplateQuestion
		answerSuggestionsDBResp             []*storage.PocketTemplateAnswerSuggestion
		loadPocketTemplateImageSuggResponse []*storage.PocketTemplateImageSuggestion
		hermesListImageDetailsResponse      *hermes.GetDocumentsResponse
		isErrorExpected                     bool
		storeLoadError                      error
		loadPocketTemplateImageSuggError    error
		hermesListImageDetailsError         error
		expectedError                       error
	}{
		{
			testDesc: "happyPath - List pocket template questions",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
			},
			expectedResp:                        responses.ListPocketTemplateQuestionResponse(),
			pocketTemplateDBResp:                responses.PocketTemplateMockDBResponse(now),
			pocketTemplateQuestionDBResp:        responses.PocketTemplateQuestionsMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			hermesListImageDetailsResponse:      responses.ListImageDetailsResponseFromHermes(),
			answerSuggestionsDBResp:             responses.PocketTemplateAnswerSuggestionsMockDBResponse(now),
			isErrorExpected:                     false,
		},
		{
			testDesc: "error path - pocket id is missing",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingPocketTemplateID.Code), 10), apiErr.ErrMissingPocketTemplateID.Message),
		},
		{
			testDesc: "error path - pocket template not found in database",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - pocket template question not found in database",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - pocket template answer suggestions not found in database",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "happyPath - pocket template image suggestions not found in database",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
			},
			pocketTemplateDBResp:             responses.PocketTemplateMockDBResponse(now),
			pocketTemplateQuestionDBResp:     responses.PocketTemplateQuestionsMockDBResponse(now),
			answerSuggestionsDBResp:          responses.PocketTemplateAnswerSuggestionsMockDBResponse(now),
			isErrorExpected:                  true,
			loadPocketTemplateImageSuggError: data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error path - list image details error from hermes",
			requestParams: &api.ListPocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
			},
			pocketTemplateDBResp:                responses.PocketTemplateMockDBResponse(now),
			pocketTemplateQuestionDBResp:        responses.PocketTemplateQuestionsMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			answerSuggestionsDBResp:             responses.PocketTemplateAnswerSuggestionsMockDBResponse(now),
			isErrorExpected:                     true,
			hermesListImageDetailsError:         errors.New("error from hermes"),
			expectedError:                       errors.New("error from hermes"),
		},
	}
	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockPocketTemplate := &storage.MockIPocketTemplateDAO{}
			mockPocketTemplateQuestion := &storage.MockIPocketTemplateQuestionDAO{}
			mockPocketTemplateAnswerSuggestions := &storage.MockIPocketTemplateAnswerSuggestionDAO{}
			mockPocketTemplateImageSuggestion := &storage.MockIPocketTemplateImageSuggestionDAO{}
			mockHermes := &hermesMock.Hermes{}

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.pocketTemplateDBResp, s.storeLoadError)
			mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(s.pocketTemplateQuestionDBResp, s.storeLoadError)
			mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.answerSuggestionsDBResp, s.storeLoadError)
			mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loadPocketTemplateImageSuggResponse, s.loadPocketTemplateImageSuggError)
			mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(s.hermesListImageDetailsResponse, s.hermesListImageDetailsError)

			mockStore := &storage.DBStore{
				PocketTemplateDAO:                 mockPocketTemplate,
				PocketTemplateQuestionDAO:         mockPocketTemplateQuestion,
				PocketTemplateAnswerSuggestionDAO: mockPocketTemplateAnswerSuggestions,
				PocketTemplateImageSuggestionDAO:  mockPocketTemplateImageSuggestion,
			}
			service := &ProductMasterService{
				AppConfig:    appConfig,
				HermesClient: mockHermes,
				Store:        mockStore,
			}
			pocketTemplateQuestions, err := service.ListPocketTemplateQuestions(context.Background(), s.requestParams)
			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
				assert.Equal(t, s.expectedError, err)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResp, pocketTemplateQuestions)
			}
		})
	}
}

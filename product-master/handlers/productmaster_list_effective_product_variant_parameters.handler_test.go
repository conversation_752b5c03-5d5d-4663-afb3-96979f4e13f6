package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"

	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_ListEffectiveProductVariantParameters(t *testing.T) {
	dbError := errors.New("database update error")
	errNoData := data.ErrNoData
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                            string
		requestParams                       *api.ListEffectiveProductVariantParametersRequest
		productVariantDAOResponse           []*storage.ProductVariant
		productVariantDAOError              error
		productVariantParameterDAOResponse  []*storage.ProductVariantParameter
		productVariantParameterDAOError     error
		productDAOResponse                  *storage.Product
		productDAOError                     error
		productTemplateParameterDAOResponse []*storage.ProductTemplateParameter
		depositInterest                     []*storage.DepositInterest
		depositInterestSlabRateDAOResponse  []*storage.DepositInterestAmountSlabRate
		depositInterestVersion              []*storage.DepositInterestVersion
		productTemplateParameterDAOError    error
		expectedResponse                    *api.ListEffectiveProductVariantParametersResponse
		isErrorExpected                     bool
		expectedError                       error
	}{
		{
			testDesc: "happy case - variant and template params present",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{
				ProductVariantCode:            "test-product-code",
				IncludeInterestRateParameters: true,
			},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			expectedResponse: &api.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []api.ProductVariantParameter{{
					Id:               "test-product-variant-parameter-id-1",
					ProductVariantID: "test-product-variant-id",
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-template-parameter-2",
					ParameterValue:   "test-product-template-parameter-value-2",
					DataType:         "INT",
					OverrideLevel:    "none",
				},
					{
						Id:               "test-product-variant-parameter-id-1",
						ProductVariantID: "test-product-variant-id",
						Namespace:        "test-namespace",
						ParameterKey:     "test-product-variant-parameter-1",
						ParameterValue:   "test-product-variant-parameter-value-1",
						DataType:         "INT",
						OverrideLevel:    "none",
					}},
				InterestRateParameters: nil,
			},
			depositInterestSlabRateDAOResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			depositInterest: []*storage.DepositInterest{{
				ID: 1,
			}},
			depositInterestVersion: []*storage.DepositInterestVersion{{
				ID:            1,
				Version:       "1",
				EffectiveDate: now,
			}},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc:      "happy case - variant and template params present - overriding same parameter key",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-parameter-common",
				ParameterValue:   "test-product-parameter-variant-value",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-parameter-common",
				ParameterValue:    "test-product-parameter-template-value",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			expectedResponse: &api.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []api.ProductVariantParameter{{
					Id:               "test-product-variant-parameter-id-1",
					ProductVariantID: "test-product-variant-id",
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-parameter-common",
					ParameterValue:   "test-product-parameter-variant-value",
					DataType:         "INT",
					OverrideLevel:    "none",
				}},
				InterestRateParameters: nil,
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc:      "happy case - no variant params",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError:             nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{},
			productVariantParameterDAOError:    nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			expectedResponse: &api.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []api.ProductVariantParameter{{
					Id:               "test-product-variant-parameter-id-1",
					ProductVariantID: "test-product-variant-id",
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-template-parameter-2",
					ParameterValue:   "test-product-template-parameter-value-2",
					DataType:         "INT",
					OverrideLevel:    "none",
				},
				},
				InterestRateParameters: nil,
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc:      "happy case - no template parameters",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError:                     nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{},
			productTemplateParameterDAOError:    nil,
			expectedResponse: &api.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []api.ProductVariantParameter{
					{
						Id:               "test-product-variant-parameter-id-1",
						ProductVariantID: "test-product-variant-id",
						Namespace:        "test-namespace",
						ParameterKey:     "test-product-variant-parameter-1",
						ParameterValue:   "test-product-variant-parameter-value-1",
						DataType:         "INT",
						OverrideLevel:    "none",
					}},
				InterestRateParameters: nil,
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc:                            "validation failure - no product-variant code in request",
			requestParams:                       &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: ""},
			productVariantDAOResponse:           []*storage.ProductVariant{},
			productVariantDAOError:              nil,
			productVariantParameterDAOResponse:  []*storage.ProductVariantParameter{},
			productVariantParameterDAOError:     nil,
			productDAOResponse:                  nil,
			productDAOError:                     nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{},
			productTemplateParameterDAOError:    nil,
			expectedResponse:                    &api.ListEffectiveProductVariantParametersResponse{},
			isErrorExpected:                     true,
			expectedError:                       apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc:                            "service failure - error while fetching product-variant",
			requestParams:                       &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse:           []*storage.ProductVariant{},
			productVariantDAOError:              dbError,
			productVariantParameterDAOResponse:  []*storage.ProductVariantParameter{},
			productVariantParameterDAOError:     nil,
			productDAOResponse:                  nil,
			productDAOError:                     nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{},
			productTemplateParameterDAOError:    nil,
			expectedResponse:                    &api.ListEffectiveProductVariantParametersResponse{},
			isErrorExpected:                     true,
			expectedError:                       apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc:                            "service failure - error while fetching product-variant - not found",
			requestParams:                       &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse:           []*storage.ProductVariant{},
			productVariantDAOError:              errNoData,
			productVariantParameterDAOResponse:  []*storage.ProductVariantParameter{},
			productVariantParameterDAOError:     nil,
			productDAOResponse:                  nil,
			productDAOError:                     nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{},
			productTemplateParameterDAOError:    nil,
			expectedResponse:                    &api.ListEffectiveProductVariantParametersResponse{},
			isErrorExpected:                     true,
			expectedError:                       apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc:      "service failure - error while fetching product-variant-parameters",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError:              nil,
			productVariantParameterDAOResponse:  []*storage.ProductVariantParameter{},
			productVariantParameterDAOError:     dbError,
			productDAOResponse:                  nil,
			productDAOError:                     nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{},
			productTemplateParameterDAOError:    nil,
			expectedResponse:                    &api.ListEffectiveProductVariantParametersResponse{},
			isErrorExpected:                     true,
			expectedError:                       apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc:      "happy case - error while fetching product-variant-parameters - not found",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError:             nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{},
			productVariantParameterDAOError:    errNoData,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError: nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-product-variant-parameter-id-1",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-product-template-parameter-2",
				ParameterValue:    "test-product-template-parameter-value-2",
				DataType:          "INT",
				OverrideLevel:     "none",
			}},
			productTemplateParameterDAOError: nil,
			expectedResponse: &api.ListEffectiveProductVariantParametersResponse{
				ProductVariantParameters: []api.ProductVariantParameter{{
					Id:               "test-product-variant-parameter-id-1",
					ProductVariantID: "test-product-variant-id",
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-template-parameter-2",
					ParameterValue:   "test-product-template-parameter-value-2",
					DataType:         "INT",
					OverrideLevel:    "none",
				},
				},
				InterestRateParameters: nil,
			},
			isErrorExpected: false,
			expectedError:   nil,
		},
		{
			testDesc:      "service failure - error while fetching product",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError:     nil,
			productDAOResponse:                  nil,
			productDAOError:                     dbError,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{},
			productTemplateParameterDAOError:    nil,
			expectedResponse:                    &api.ListEffectiveProductVariantParametersResponse{},
			isErrorExpected:                     true,
			expectedError:                       apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc:      "service failure - error while fetching product-template-parameters",
			requestParams: &api.ListEffectiveProductVariantParametersRequest{ProductVariantCode: "test-product-code"},
			productVariantDAOResponse: []*storage.ProductVariant{{
				PublicID:  "test-product-variant-id",
				ProductID: 1,
				Code:      "test-product-code",
				Version:   "1",
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
			productVariantDAOError: nil,
			productVariantParameterDAOResponse: []*storage.ProductVariantParameter{{
				PublicID:         "test-product-variant-parameter-id-1",
				ProductVariantID: 1,
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			}},
			productVariantParameterDAOError: nil,
			productDAOResponse: &storage.Product{
				PublicID:          "test-product-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Status:            "ACTIVE",
			},
			productDAOError:                     nil,
			productTemplateParameterDAOResponse: []*storage.ProductTemplateParameter{},
			productTemplateParameterDAOError:    dbError,
			expectedResponse:                    &api.ListEffectiveProductVariantParametersResponse{},
			isErrorExpected:                     true,
			expectedError:                       apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.Anything).Return(s.productVariantDAOResponse, s.productVariantDAOError)

			mockProductVariantParameters := &storage.MockIProductVariantParameterDAO{}
			mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return(s.productVariantParameterDAOResponse, s.productVariantParameterDAOError)

			mockProduct := &storage.MockIProductDAO{}
			mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(s.productDAOResponse, s.productDAOError)

			mockProductTemplateParameter := &storage.MockIProductTemplateParameterDAO{}
			mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return(s.productTemplateParameterDAOResponse, s.productTemplateParameterDAOError)

			mockProductTemplate := &storage.MockIProductTemplateDAO{}
			mockProductTemplate.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductTemplate{PublicID: "test-product-template-id-1"}, nil)

			depositInterest := &storage.MockIDepositInterestDAO{}
			depositInterest.On("Find", mock.Anything, mock.Anything).Return(s.depositInterest, nil)

			depositInterestVersion := &storage.MockIDepositInterestVersionDAO{}
			depositInterestVersion.On("Find", mock.Anything, mock.Anything).Return(s.depositInterestVersion, nil)

			depositInterestSlabRate := &storage.MockIDepositInterestAmountSlabRateDAO{}
			depositInterestSlabRate.On("Find", mock.Anything, mock.Anything).Return(s.depositInterestSlabRateDAOResponse, nil)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO:                mockProductVariant,
				ProductDAO:                       mockProduct,
				ProductVariantParameterDAO:       mockProductVariantParameters,
				ProductTemplateParameterDAO:      mockProductTemplateParameter,
				ProductTemplateDAO:               mockProductTemplate,
				DepositInterestDAO:               depositInterest,
				DepositInterestVersionDAO:        depositInterestVersion,
				DepositInterestAmountSlabRateDAO: depositInterestSlabRate,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			effectiveProductVariantParameters, err := service.ListEffectiveProductVariantParameters(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Equal(t, s.expectedError, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.True(t, common.UnorderedEqual(s.expectedResponse.ProductVariantParameters, effectiveProductVariantParameters.ProductVariantParameters), s.testDesc)
			}
		})
	}
}

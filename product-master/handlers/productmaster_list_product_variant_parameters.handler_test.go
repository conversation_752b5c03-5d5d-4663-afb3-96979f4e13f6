package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_ListProductVariantParameters(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.ListProductVariantParametersRequest
		storeLoadResponse []*storage.ProductVariantParameter
		expectedResponse  *api.ListProductVariantParametersResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - list product variant parameter",
			requestParams: &api.ListProductVariantParametersRequest{
				ProductVariantID: "test-product-variant-id-1",
			},
			storeLoadResponse: []*storage.ProductVariantParameter{
				{
					PublicID:         "test-id",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-parameter-key",
					ParameterValue:   "test-parameter-value",
					DataType:         "STRING",
					OverrideLevel:    "NO_OVERRIDE",
					ExceptionLevel: sql.NullString{
						String: "",
						Valid:  false,
					},
					Description: sql.NullString{
						String: "create test product variant parameter",
						Valid:  true,
					},
					CreatedBy: "unit-test",
					CreatedAt: now,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				},
			},
			expectedResponse: &api.ListProductVariantParametersResponse{
				ProductVariantParameters: []api.ProductVariantParameter{
					{
						Id:               "test-id",
						ProductVariantID: "test-product-variant-id-1",
						Namespace:        "test-namespace",
						ParameterKey:     "test-parameter-key",
						ParameterValue:   "test-parameter-value",
						DataType:         "STRING",
						OverrideLevel:    "NO_OVERRIDE",
						ExceptionLevel:   "",
						Description:      "create test product variant parameter",
						CreatedBy:        "unit-test",
						CreatedAt:        now,
						UpdatedBy:        "unit-test",
						UpdatedAt:        now,
					},
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - product variant id is missing",
			requestParams: &api.ListProductVariantParametersRequest{
				ProductVariantID: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductVariantID.Code), 10), apiErr.ErrMissingProductVariantID.Message),
		},
		{
			testDesc: "error path - product variant parameter not found in database",
			requestParams: &api.ListProductVariantParametersRequest{
				ProductVariantID: "test-product-variant-id-1",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.ListProductVariantParametersRequest{
				ProductVariantID: "test-product-variant-id-1",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{PublicID: "test-product-variant-id-1"}}, nil)

			mockProductVariantParameter := &storage.MockIProductVariantParameterDAO{}
			mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantParameterDAO: mockProductVariantParameter,
				ProductVariantDAO:          mockProductVariant,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ProductVariantParameters, err := service.ListProductVariantParameters(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, ProductVariantParameters)
			}
		})
	}
}

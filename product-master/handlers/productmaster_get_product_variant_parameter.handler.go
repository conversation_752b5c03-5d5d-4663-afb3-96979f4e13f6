package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductVariantParameter fetches a product variant parameter by id
func (p *ProductMasterService) GetProductVariantParameter(ctx context.Context, req *api.GetProductVariantParameterRequest) (*api.GetProductVariantParameterResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductVariantParameter"))
	slog.FromContext(ctx).Info(constants.GetProductVariantParameterLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductVariantParameterRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.GetProductVariantParameter(ctx, req, p.Store)
}

func validateGetProductVariantParameterRequest(ctx context.Context, req *api.GetProductVariantParameterRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

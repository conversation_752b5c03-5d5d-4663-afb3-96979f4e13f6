package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetDepositInterestByCode fetches a deposit interest by code
func (p *ProductMasterService) GetDepositInterestByCode(ctx context.Context, req *api.GetDepositInterestByCodeRequest) (*api.GetDepositInterestByCodeResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetDepositInterestByCode"))
	slog.FromContext(ctx).Info(constants.GetDepositInterestByCodeLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetDepositInterestByCodeRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetDepositInterestByCode(ctx, req, p.Store)
}

func validateGetDepositInterestByCodeRequest(ctx context.Context, req *api.GetDepositInterestByCodeRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	return nil
}

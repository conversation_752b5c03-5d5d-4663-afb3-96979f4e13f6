package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateProductVariantQuestions : creates new questions based on productVariantCode
func (p *ProductMasterService) CreateProductVariantQuestions(ctx context.Context, req *api.CreateProductVariantQuestionRequest) (*api.CreateProductVariantQuestionResponse, error) {
	slog.FromContext(ctx).Info(constants.CreateProductVariantQuestionsLogTag, fmt.Sprintf("Request received: %s", utils.ToJSON(req)), utils.GetTraceID(ctx))

	// validate request parameter
	if err := productconfig.ValidateCreateProductVariantQuestionRequest(ctx, req); err != nil {
		return nil, err
	}
	// create question answer pair
	impl := productconfig.QuestionsAnswersImpl{Store: p.Store}
	response, err := impl.CreateProductVariantQuestions(ctx, req)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.CreateProductVariantQuestionsLogTag, fmt.Sprintf("Successfully completed request: %s", utils.ToJSON(req)), utils.GetTraceID(ctx))
	return response, nil
}

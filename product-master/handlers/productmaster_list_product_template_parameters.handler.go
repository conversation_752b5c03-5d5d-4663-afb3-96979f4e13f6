package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// ListProductTemplateParameters fetches all product template parameters for a product template
func (p *ProductMasterService) ListProductTemplateParameters(ctx context.Context, req *api.ListProductTemplateParametersRequest) (*api.ListProductTemplateParametersResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListProductTemplateParameters"))
	slog.FromContext(ctx).Info(constants.ListProductTemplateParametersLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateListProductTemplateParameterRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.ListProductTemplateParameter(ctx, req, p.Store)
}

func validateListProductTemplateParameterRequest(ctx context.Context, req *api.ListProductTemplateParametersRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductTemplateID, "product-template-id", apiErr.ErrMissingProductTemplateID); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.myteksi.net/dbmy/common/tenants"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

func Test_ListPocketTemplates(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                            string
		requestParams                       *api.ListPocketTemplatesRequest
		loadPocketTemplateResponse          []*storage.PocketTemplate
		loadPocketTemplateImageSuggResponse []*storage.PocketTemplateImageSuggestion
		hermesListImageDetailsResponse      *hermes.GetDocumentsResponse
		expectedResponse                    *api.ListPocketTemplatesResponse
		isErrorExpected                     bool
		loadPocketTemplateError             error
		loadPocketTemplateImageSuggError    error
		hermesListImageDetailsError         error
		expectedError                       error
	}{
		{
			testDesc: "happy path - list SAVINGS pocket templates",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS,
			},
			loadPocketTemplateResponse:          responses.PocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			hermesListImageDetailsResponse:      responses.ListImageDetailsResponseFromHermes(),
			expectedResponse:                    responses.ListPocketTemplatesResponse(),
			isErrorExpected:                     false,
		},
		{
			testDesc: "happy path - list SAVINGS_POCKET templates",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS_POCKET,
			},
			loadPocketTemplateResponse:          responses.PocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			hermesListImageDetailsResponse:      responses.ListImageDetailsResponseFromHermes(),
			expectedResponse:                    responses.ListPocketTemplatesResponse(),
			isErrorExpected:                     false,
		},
		{
			testDesc: "happy path - list BOOST_POCKET templates",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_BOOST_POCKET,
			},
			loadPocketTemplateResponse:          responses.PocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			hermesListImageDetailsResponse:      responses.ListImageDetailsResponseFromHermes(),
			expectedResponse:                    responses.ListPocketTemplatesResponse(),
			isErrorExpected:                     false,
		},
		{
			testDesc: "error path - pocket type is missing",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType(""),
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidPocketType.Code), 10), apiErr.ErrInvalidPocketType.Message),
		},
		{
			testDesc: "error path - invalid pocket type",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType("INVALID_POCKET_TYPE"),
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidPocketType.Code), 10), apiErr.ErrInvalidPocketType.Message),
		},
		{
			testDesc: "error path - pocket template not found in database",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS,
			},
			isErrorExpected:         true,
			loadPocketTemplateError: data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - pocket template database load error",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS,
			},
			isErrorExpected:         true,
			loadPocketTemplateError: errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "happy path - pocket template image suggestion not found in database",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS,
			},
			loadPocketTemplateResponse:       responses.PocketTemplateMockDBResponse(now),
			isErrorExpected:                  true,
			loadPocketTemplateImageSuggError: data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "happy path - pocket template image suggestion database load error",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS,
			},
			loadPocketTemplateResponse:       responses.PocketTemplateMockDBResponse(now),
			isErrorExpected:                  true,
			loadPocketTemplateImageSuggError: errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error path - list image details error from hermes",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS,
			},
			loadPocketTemplateResponse:          responses.PocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			isErrorExpected:                     true,
			hermesListImageDetailsError:         errors.New("error from hermes"),
			expectedError:                       errors.New("error from hermes"),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockPocketTemplate := &storage.MockIPocketTemplateDAO{}
			mockPocketTemplateImageSuggestion := &storage.MockIPocketTemplateImageSuggestionDAO{}
			mockHermes := &hermesMock.Hermes{}

			mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loadPocketTemplateResponse, s.loadPocketTemplateError)
			mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loadPocketTemplateImageSuggResponse, s.loadPocketTemplateImageSuggError)
			mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(s.hermesListImageDetailsResponse, s.hermesListImageDetailsError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				PocketTemplateDAO:                mockPocketTemplate,
				PocketTemplateImageSuggestionDAO: mockPocketTemplateImageSuggestion,
			}

			service := &ProductMasterService{
				AppConfig:    appConfig,
				HermesClient: mockHermes,
				Store:        mockStore,
			}
			pocketTemplates, err := service.ListPocketTemplates(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, pocketTemplates)
			}
		})
	}
}

func Test_DBMYListPocketTemplates(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                            string
		requestParams                       *api.ListPocketTemplatesRequest
		loadPocketTemplateResponse          []*storage.PocketTemplate
		loadPocketTemplateImageSuggResponse []*storage.PocketTemplateImageSuggestion
		hermesListImageDetailsResponse      *hermes.GetDocumentsResponse
		expectedResponse                    *api.ListPocketTemplatesResponse
		isErrorExpected                     bool
		loadPocketTemplateError             error
		loadPocketTemplateImageSuggError    error
		hermesListImageDetailsError         error
		expectedError                       error
	}{
		{
			testDesc: "happy path - list pocket templates",
			requestParams: &api.ListPocketTemplatesRequest{
				Type: api.PocketType_SAVINGS,
			},
			loadPocketTemplateResponse:          responses.DBMYPocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			hermesListImageDetailsResponse:      responses.ListImageDetailsResponseFromHermes(),
			expectedResponse:                    responses.DBMYListPocketTemplatesResponse(),
			isErrorExpected:                     false,
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockPocketTemplate := &storage.MockIPocketTemplateDAO{}
			mockPocketTemplateImageSuggestion := &storage.MockIPocketTemplateImageSuggestionDAO{}
			mockHermes := &hermesMock.Hermes{}

			mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loadPocketTemplateResponse, s.loadPocketTemplateError)
			mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loadPocketTemplateImageSuggResponse, s.loadPocketTemplateImageSuggError)
			mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(s.hermesListImageDetailsResponse, s.hermesListImageDetailsError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				PocketTemplateDAO:                mockPocketTemplate,
				PocketTemplateImageSuggestionDAO: mockPocketTemplateImageSuggestion,
			}

			service := &ProductMasterService{
				AppConfig:    appConfig,
				HermesClient: mockHermes,
				Store:        mockStore,
			}
			ctx := context.Background()
			tenant := tenants.NewTenant(tenants.TenantMY)
			ctx = tenant.MustInjectTenant(ctx, tenant.Name)
			pocketTemplates, err := service.ListPocketTemplates(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, pocketTemplates)
			}
		})
	}
}

package handlers

import (
	"context"
	"net/http"
	"strconv"
	"testing"
	"time"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetLoanPastDueParametersByProductCode(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                      string
		requestParams                 *api.GetLoanPastDueParametersByProductCodeRequest
		expectedResponse              *api.GetLoanPastDueParametersByProductCodeResponse
		productDAOResponse            []*storage.Product
		loanPastDueVersionDAOResponse []*storage.LoanPastDueVersion
		loanPastDueSlabDAOResponse    []*storage.LoanPastDueSlab
		isErrorExpected               bool
		expectedError                 error
		productDAOError               error
		loanPastDueVersionDAOError    error
		loanPastDueSlabDAOError       error
	}{
		{
			testDesc: "happy case - with product code & loan past due version",
			requestParams: &api.GetLoanPastDueParametersByProductCodeRequest{
				ProductCode:        api.ProductCode_FLEXI_LOAN_TERM_LOAN,
				LoanPastDueVersion: "1",
			},
			expectedResponse:              responses.GetLoanPastDueParametersByProductCodeResponse(now),
			productDAOResponse:            responses.SampleProductDBResponse(),
			loanPastDueVersionDAOResponse: responses.SampleLoanPastDueVersionDAOResponse(now),
			loanPastDueSlabDAOResponse:    responses.SampleGetLoanPastDueSlabDAOResponse(now),
		},
		{
			testDesc: "happy case - with product code only",
			requestParams: &api.GetLoanPastDueParametersByProductCodeRequest{
				ProductCode:        api.ProductCode_FLEXI_LOAN_TERM_LOAN,
				LoanPastDueVersion: "1",
			},
			expectedResponse:              responses.GetLoanPastDueParametersByProductCodeResponse(now),
			productDAOResponse:            responses.SampleProductDBResponse(),
			loanPastDueVersionDAOResponse: responses.SampleLoanPastDueVersionDAOResponse(now),
			loanPastDueSlabDAOResponse:    responses.SampleGetLoanPastDueSlabDAOResponse(now),
		},
		{
			testDesc:                      "error case - missing product code",
			requestParams:                 &api.GetLoanPastDueParametersByProductCodeRequest{},
			expectedResponse:              responses.GetLoanPastDueParametersByProductCodeResponse(now),
			productDAOResponse:            responses.SampleProductDBResponse(),
			loanPastDueVersionDAOResponse: responses.SampleLoanPastDueVersionDAOResponse(now),
			loanPastDueSlabDAOResponse:    responses.SampleGetLoanPastDueSlabDAOResponse(now),
			isErrorExpected:               true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingProductCode.Code), 10),
				apiErr.ErrMissingProductCode.Message),
		},
		{
			testDesc:                      "error case - invalid product code",
			requestParams:                 &api.GetLoanPastDueParametersByProductCodeRequest{ProductCode: "INVALID_PRODUCT_CODE"},
			expectedResponse:              responses.GetLoanPastDueParametersByProductCodeResponse(now),
			productDAOResponse:            responses.SampleProductDBResponse(),
			loanPastDueVersionDAOResponse: responses.SampleLoanPastDueVersionDAOResponse(now),
			loanPastDueSlabDAOResponse:    responses.SampleGetLoanPastDueSlabDAOResponse(now),
			isErrorExpected:               true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidProductCode.Code), 10),
				apiErr.ErrInvalidProductCode.Message),
		},
		{
			testDesc:                      "error case - product code not found in database",
			requestParams:                 &api.GetLoanPastDueParametersByProductCodeRequest{ProductCode: api.ProductCode_FLEXI_LOAN_TERM_LOAN},
			expectedResponse:              responses.GetLoanPastDueParametersByProductCodeResponse(now),
			productDAOResponse:            responses.SampleProductDBResponse(),
			loanPastDueVersionDAOResponse: responses.SampleLoanPastDueVersionDAOResponse(now),
			loanPastDueSlabDAOResponse:    responses.SampleGetLoanPastDueSlabDAOResponse(now),
			isErrorExpected:               true,
			productDAOError:               data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10),
				apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc:                      "error case - loan past due version not found in database",
			requestParams:                 &api.GetLoanPastDueParametersByProductCodeRequest{ProductCode: api.ProductCode_FLEXI_LOAN_TERM_LOAN},
			expectedResponse:              responses.GetLoanPastDueParametersByProductCodeResponse(now),
			productDAOResponse:            responses.SampleProductDBResponse(),
			loanPastDueVersionDAOResponse: responses.SampleLoanPastDueVersionDAOResponse(now),
			loanPastDueSlabDAOResponse:    responses.SampleGetLoanPastDueSlabDAOResponse(now),
			isErrorExpected:               true,
			loanPastDueVersionDAOError:    data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10),
				apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc:                      "error case - loan past due slab not found in database",
			requestParams:                 &api.GetLoanPastDueParametersByProductCodeRequest{ProductCode: api.ProductCode_FLEXI_LOAN_TERM_LOAN},
			expectedResponse:              responses.GetLoanPastDueParametersByProductCodeResponse(now),
			productDAOResponse:            responses.SampleProductDBResponse(),
			loanPastDueVersionDAOResponse: responses.SampleLoanPastDueVersionDAOResponse(now),
			loanPastDueSlabDAOResponse:    responses.SampleGetLoanPastDueSlabDAOResponse(now),
			isErrorExpected:               true,
			loanPastDueSlabDAOError:       data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10),
				apiErr.ErrRecordNotFound.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProduct := &storage.MockIProductDAO{}
			mockLoanPastDueVersion := &storage.MockILoanPastDueVersionDAO{}
			mockLoanPastDueSlab := &storage.MockILoanPastDueSlabDAO{}

			mockProduct.On("Find", mock.Anything, mock.Anything).Return(s.productDAOResponse, s.productDAOError)
			mockLoanPastDueVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loanPastDueVersionDAOResponse, s.loanPastDueVersionDAOError)
			mockLoanPastDueSlab.On("Find", mock.Anything, mock.Anything).Return(s.loanPastDueSlabDAOResponse, s.loanPastDueSlabDAOError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}

			mockStore := &storage.DBStore{
				ProductDAO:            mockProduct,
				LoanPastDueVersionDAO: mockLoanPastDueVersion,
				LoanPastDueSlabDAO:    mockLoanPastDueSlab,
			}
			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			response, err := service.GetLoanPastDueParametersByProductCode(context.Background(), s.requestParams)
			if s.isErrorExpected {
				assert.Equal(t, s.expectedError, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, response)
			}
		})
	}
}

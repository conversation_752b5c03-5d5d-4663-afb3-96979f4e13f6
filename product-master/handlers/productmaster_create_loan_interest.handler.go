package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateLoanInterest creates a new loan interest
func (p *ProductMasterService) CreateLoanInterest(ctx context.Context, req *api.CreateLoanInterestRequest) (*api.CreateLoanInterestResponse, error) {
	slog.FromContext(ctx).Info(constants.CreateLoanInterestLogTag, fmt.Sprintf("Received Request: %+v", utils.ToJSON(req)), apiCommon.GetTraceID(ctx))
	if err := validateCreateLoanInterestRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.CreateLoanInterest(ctx, req, p.Store)
}

func validateCreateLoanInterestRequest(ctx context.Context, req *api.CreateLoanInterestRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantID, "product-variant-id", apiErr.ErrMissingProductVariantID); err != nil {
		return err
	}
	if req.IsLinkedToBaseRate {
		if err := validations.ValidateFieldNotEmpty(ctx, req.BaseInterestID, "base-interest-id", apiErr.ErrMissingLinkedBaseInterestID); err != nil {
			return err
		}
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Name, "name", apiErr.ErrMissingName); err != nil {
		return err
	}
	if err := validations.ValidateCurrency(ctx, string(req.Currency)); err != nil {
		return err
	}
	if err := validations.ValidateRoundOffType(ctx, string(req.RoundOffType)); err != nil {
		return err
	}
	if err := validations.ValidateInterestSlabType(ctx, string(req.InterestSlabUnitType)); err != nil {
		return err
	}
	if err := validations.ValidateInterestSlabStructure(ctx, string(req.InterestSlabStructure)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	if err := validations.ValidateInterestType(ctx, string(req.InterestType)); err != nil {
		return err
	}
	return nil
}

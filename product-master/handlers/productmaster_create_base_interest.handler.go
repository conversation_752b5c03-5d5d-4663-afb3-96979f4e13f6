package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateBaseInterest creates a new base-interest
func (p *ProductMasterService) CreateBaseInterest(ctx context.Context, req *api.CreateBaseInterestRequest) (*api.CreateBaseInterestResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateBaseInterest"))
	slog.FromContext(ctx).Info(constants.CreateBaseInterestLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateBaseInterestRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.CreateBaseInterest(ctx, req, p.Store)
}

func validateCreateBaseInterestRequest(ctx context.Context, req *api.CreateBaseInterestRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Name, "name", apiErr.ErrMissingName); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	if err := validations.ValidateCurrency(ctx, string(req.Currency)); err != nil {
		return err
	}
	return nil
}

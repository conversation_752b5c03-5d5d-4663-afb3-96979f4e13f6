package handlers

import (
	"context"

	"golang.org/x/text/language"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/localisation"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// WithLocale is a middleware that sets the locale for the request
func WithLocale() servus.MiddlewareFunc {
	return func(h servus.HandlerFunc) servus.HandlerFunc {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			locale := localisation.GetLocale(ctx, language.English)
			newCtx := localisation.NewContextWithLocale(ctx, locale)
			return h(newCtx, request)
		}
	}
}

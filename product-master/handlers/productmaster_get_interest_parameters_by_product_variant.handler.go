package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetInterestParametersByProductVariant fetches interest parameters by product variant
func (p *ProductMasterService) GetInterestParametersByProductVariant(ctx context.Context, req *api.GetInterestParametersByProductVariantRequest) (*api.GetInterestParametersByProductVariantResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetInterestParametersByProductVariant"))
	slog.FromContext(ctx).Info(constants.GetInterestParametersByProductVariantLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetInterestParametersByProductVariantRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetInterestParametersByProductVariant(ctx, req, p.Store, p.AppConfig)
}

func validateGetInterestParametersByProductVariantRequest(ctx context.Context, req *api.GetInterestParametersByProductVariantRequest) error {
	for _, requestParam := range req.InterestParamRequest {
		if err := validations.ValidateProductVariantCode(ctx, string(requestParam.ProductVariantCode)); err != nil {
			return err
		}
	}
	return nil
}

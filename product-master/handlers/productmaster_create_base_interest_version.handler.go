package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateBaseInterestVersion creates a new base-interest-version
func (p *ProductMasterService) CreateBaseInterestVersion(ctx context.Context, req *api.CreateBaseInterestVersionRequest) (*api.CreateBaseInterestVersionResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateBaseInterestVersion"))
	slog.FromContext(ctx).Info(constants.CreateBaseInterestVersionLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateBaseInterestVersionRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.CreateBaseInterestVersion(ctx, req, p.Store)
}

func validateCreateBaseInterestVersionRequest(ctx context.Context, req *api.CreateBaseInterestVersionRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.BaseInterestID, "base-interest-id", apiErr.ErrMissingBaseInterestID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Version, "version", apiErr.ErrMissingVersion); err != nil {
		return err
	}
	if err := validations.ValidateIntegerVersion(ctx, req.Version, apiErr.ErrInvalidBaseInterestVersion); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	if err := validations.ValidateDateFieldWithCurrentDate(ctx, req.EffectiveDate, "effective-date", apiErr.ErrEffectiveDateLessThanCurrent); err != nil {
		return err
	}
	return nil
}

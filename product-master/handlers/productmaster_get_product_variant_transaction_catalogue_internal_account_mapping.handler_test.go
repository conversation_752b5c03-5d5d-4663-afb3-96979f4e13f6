package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetProductVariantTransactionCatalogueInternalAccountMapping(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest
		storeLoadResponse []*storage.ProductVariantTransactionCatalogueInternalAccountMapping
		expectedResponse  *api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get product variant transaction catalogue mapping",
			requestParams: &api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.ProductVariantTransactionCatalogueInternalAccountMapping{{

				PublicID:          "test-id",
				InternalAccountID: 1,
				ProductVariantTransactionCatalogueMappingID: 2,
				IdentifierKey: "test-key",
				Status:        "ACTIVE",
				CreatedBy:     "unit-test",
				CreatedAt:     now,
				UpdatedBy:     "unit-test",
				UpdatedAt:     now,
			}},
			expectedResponse: &api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse{
				ProductVariantTransactionCatalogueInternalAccountMapping: &api.ProductVariantTransactionCatalogueInternalAccountMapping{
					Id:                "test-id",
					InternalAccountID: "test-internal-account-id",
					ProductVariantTransactionCatalogueMappingID: "test-product-variant-transaction-catalogue-mapping-id",
					IdentifierKey: "test-key",
					Status:        api.EntityStatus_ACTIVE,
					CreatedBy:     "unit-test",
					CreatedAt:     now,
					UpdatedBy:     "unit-test",
					UpdatedAt:     now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - productVariantTransactionCatalogueInternalAccountMapping not found in database",
			requestParams: &api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mappingDAO := &storage.MockIProductVariantTransactionCatalogueInternalAccountMappingDAO{}
			mappingDAO.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockMapping1 := &storage.MockIInternalAccountDAO{}
			mockMapping1.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.InternalAccount{PublicID: "test-internal-account-id"}, nil)

			mockMapping2 := &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
			mockMapping2.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariantTransactionCatalogueMapping{PublicID: "test-product-variant-transaction-catalogue-mapping-id"}, nil)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantTransactionCatalogueInternalAccountMappingDAO: mappingDAO,
				InternalAccountDAO:                           mockMapping1,
				ProductVariantTransactionCatalogueMappingDAO: mockMapping2,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			mapping, err := service.GetProductVariantTransactionCatalogueInternalAccountMapping(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, mapping)
			}
		})
	}
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_ListInternalAccounts(t *testing.T) {
	now := time.Now().UTC()
	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := api.Currency(appConfig.Locale.Currency)
	scenarios := []struct {
		testDesc          string
		requestParams     *api.ListInternalAccountsRequest
		storeLoadResponse []*storage.InternalAccount
		expectedResponse  *api.ListInternalAccountsResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - list internal accounts by combination of code and general ledger id",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "test-internal-account-code",
				GeneralLedgerID: "test-general-ledger-id",
			},
			storeLoadResponse: []*storage.InternalAccount{
				{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  string(currency),
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "unit-test",
					CreatedAt: now,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				},
			},
			expectedResponse: &api.ListInternalAccountsResponse{
				InternalAccounts: []api.InternalAccount{
					{
						Id:              "test-id",
						GeneralLedgerID: "test-general-ledger-id",
						Code:            "test-internal-account-code",
						Name:            "test-internal-account-name",
						Description:     "create test internal account",
						Currency:        currency,
						Status:          api.EntityStatus_ACTIVE,
						CreatedBy:       "unit-test",
						CreatedAt:       now,
						UpdatedBy:       "unit-test",
						UpdatedAt:       now,
					},
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "happy path - list internal accounts by general ledger id only",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "",
				GeneralLedgerID: "test-general-ledger-id",
			},
			storeLoadResponse: []*storage.InternalAccount{
				{
					PublicID:        "test-id-1",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code-1",
					Name:            "test-internal-account-name-1",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  string(currency),
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "unit-test",
					CreatedAt: now,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				},
				{
					PublicID:        "test-id-2",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code-2",
					Name:            "test-internal-account-name-2",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  string(currency),
					Status:    string(api.EntityStatus_INACTIVE),
					CreatedBy: "unit-test",
					CreatedAt: now,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				},
			},
			expectedResponse: &api.ListInternalAccountsResponse{
				InternalAccounts: []api.InternalAccount{
					{
						Id:              "test-id-1",
						GeneralLedgerID: "test-general-ledger-id",
						Code:            "test-internal-account-code-1",
						Name:            "test-internal-account-name-1",
						Description:     "create test internal account",
						Currency:        currency,
						Status:          api.EntityStatus_ACTIVE,
						CreatedBy:       "unit-test",
						CreatedAt:       now,
						UpdatedBy:       "unit-test",
						UpdatedAt:       now,
					},
					{
						Id:              "test-id-2",
						GeneralLedgerID: "test-general-ledger-id",
						Code:            "test-internal-account-code-2",
						Name:            "test-internal-account-name-2",
						Description:     "create test internal account",
						Currency:        currency,
						Status:          api.EntityStatus_INACTIVE,
						CreatedBy:       "unit-test",
						CreatedAt:       now,
						UpdatedBy:       "unit-test",
						UpdatedAt:       now,
					},
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "happy path - list internal accounts by code only",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "test-internal-account-code-1",
				GeneralLedgerID: "",
			},
			storeLoadResponse: []*storage.InternalAccount{
				{
					PublicID:        "test-id-1",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code-1",
					Name:            "test-internal-account-name-1",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  string(currency),
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "unit-test",
					CreatedAt: now,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				},
			},
			expectedResponse: &api.ListInternalAccountsResponse{
				InternalAccounts: []api.InternalAccount{
					{
						Id:              "test-id-1",
						GeneralLedgerID: "test-general-ledger-id",
						Code:            "test-internal-account-code-1",
						Name:            "test-internal-account-name-1",
						Description:     "create test internal account",
						Currency:        currency,
						Status:          api.EntityStatus_ACTIVE,
						CreatedBy:       "unit-test",
						CreatedAt:       now,
						UpdatedBy:       "unit-test",
						UpdatedAt:       now,
					},
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - code and general ledger id both are missing",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "",
				GeneralLedgerID: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingAtLeastOneField.Code), 10), apiErr.ErrMissingAtLeastOneField.Message),
		},
		{
			testDesc: "error path - code not found in database",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "test-internal-account-code",
				GeneralLedgerID: "",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - general ledger id not found in database",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "",
				GeneralLedgerID: "test-general-ledger-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - combination of code and general ledger id not found in database",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "test-internal-account-code",
				GeneralLedgerID: "test-general-ledger-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.ListInternalAccountsRequest{
				Code:            "test-internal-account-code",
				GeneralLedgerID: "test-general-ledger-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
			mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{PublicID: "test-general-ledger-id"}}, nil)

			mockInternalAccount := &storage.MockIInternalAccountDAO{}
			mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockGeneralLedger.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.GeneralLedger{PublicID: "test-general-ledger-id"}, nil)

			mockStore := &storage.DBStore{
				InternalAccountDAO: mockInternalAccount,
				GeneralLedgerDAO:   mockGeneralLedger,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			internalAccounts, err := service.ListInternalAccounts(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, internalAccounts)
			}
		})
	}
}

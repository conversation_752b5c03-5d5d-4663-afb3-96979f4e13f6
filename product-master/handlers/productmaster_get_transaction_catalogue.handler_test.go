package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetTransactionCatalogue(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetTransactionCatalogueRequest
		storeLoadResponse []*storage.TransactionCatalogue
		expectedResponse  *api.GetTransactionCatalogueResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get transaction catalogue",
			requestParams: &api.GetTransactionCatalogueRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.TransactionCatalogue{{
				PublicID:       "test-id",
				Domain:         "test-domain",
				IsFinancialTxn: true,
				TxnType:        "test-txn-type",
				TxnSubType:     "test-txn-sub-type",
				DisplayName:    "test-display-name",
				Description: sql.NullString{
					String: "create transaction catalogue",
					Valid:  true,
				},
				Status:    string(api.EntityStatus_ACTIVE),
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.GetTransactionCatalogueResponse{
				TransactionCatalogue: &api.TransactionCatalogue{
					Id:             "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description:    "create transaction catalogue",
					Status:         api.EntityStatus_ACTIVE,
					CreatedBy:      "unit-test",
					CreatedAt:      now,
					UpdatedBy:      "unit-test",
					UpdatedAt:      now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetTransactionCatalogueRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - transaction catalogue not found in database",
			requestParams: &api.GetTransactionCatalogueRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetTransactionCatalogueRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockTransactionCatalogue := &storage.MockITransactionCatalogueDAO{}
			mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				TransactionCatalogueDAO: mockTransactionCatalogue,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			transactionCatalogue, err := service.GetTransactionCatalogue(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, transactionCatalogue)
			}
		})
	}
}

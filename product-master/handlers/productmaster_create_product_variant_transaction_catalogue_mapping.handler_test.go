package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateProductVariantTransactionCatalogueMapping(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateProductVariantTransactionCatalogueMappingRequest
		storeLoadResponse []*storage.ProductVariantTransactionCatalogueMapping
		expectedResponse  *api.CreateProductVariantTransactionCatalogueMappingResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - product created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueMappingRequest{
				ProductVariantID:       "test-product-variant-id",
				TransactionCatalogueID: "test-transaction-catalogue-id",
				CreatedBy:              "unit-test",
			},
			storeLoadResponse: []*storage.ProductVariantTransactionCatalogueMapping{{
				PublicID:               "test-id",
				ProductVariantID:       1,
				TransactionCatalogueID: 2,
				Status:                 "ACTIVE",
				CreatedBy:              "unit-test",
				CreatedAt:              now,
				UpdatedBy:              "unit-test",
				UpdatedAt:              now,
			}},
			expectedResponse: &api.CreateProductVariantTransactionCatalogueMappingResponse{
				ProductVariantTransactionCatalogueMapping: &api.ProductVariantTransactionCatalogueMapping{
					Id:                     "test-id",
					ProductVariantID:       "test-product-variant-id",
					TransactionCatalogueID: "test-transaction-catalogue-id",
					Status:                 api.EntityStatus_ACTIVE,
					CreatedBy:              "unit-test",
					CreatedAt:              now,
					UpdatedBy:              "unit-test",
					UpdatedAt:              now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateProductVariantTransactionCatalogueMappingRequest{
				ProductVariantID:       "test-product-variant-id",
				TransactionCatalogueID: "test-transaction-catalogue-id",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - product variant id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueMappingRequest{
				ProductVariantID:       "",
				TransactionCatalogueID: "test-transaction-catalogue-id",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductVariantID.Code), 10), apiErr.ErrMissingProductVariantID.Message),
		},
		{
			testDesc:       "error path - transaction catalogue id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueMappingRequest{
				ProductVariantID:       "test-product-variant-id",
				TransactionCatalogueID: "",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingTransactionCatalogueID.Code), 10), apiErr.ErrMissingTransactionCatalogueID.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueMappingRequest{
				ProductVariantID:       "test-product-variant-id",
				TransactionCatalogueID: "test-transaction-catalogue-id",
				CreatedBy:              "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueMappingRequest{
				ProductVariantID:       "test-product-variant-id",
				TransactionCatalogueID: "test-transaction-catalogue-id",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantTransactionCatalogueMappingRequest{
				ProductVariantID:       "test-product-variant-id",
				TransactionCatalogueID: "test-transaction-catalogue-id",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mappingDAO1 := &storage.MockITransactionCatalogueDAO{}
			mappingDAO1.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{}}, nil)
			mappingDAO2 := &storage.MockIProductVariantDAO{}
			mappingDAO2.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{}}, nil)
			mockProductVariantTransactionCatalogueMapping := &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
			mockProductVariantTransactionCatalogueMapping.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockProductVariantTransactionCatalogueMapping.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantTransactionCatalogueMappingDAO: mockProductVariantTransactionCatalogueMapping,
				TransactionCatalogueDAO:                      mappingDAO1,
				ProductVariantDAO:                            mappingDAO2,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			productVariantTransactionCatalogueMapping, err := service.CreateProductVariantTransactionCatalogueMapping(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, productVariantTransactionCatalogueMapping)
			}
		})
	}
}

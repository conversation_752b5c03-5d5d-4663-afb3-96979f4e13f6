package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateProduct(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateProductRequest
		storeLoadResponse []*storage.Product
		expectedResponse  *api.CreateProductResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - product created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "test-product-template-id-1",
				Code:              "test-product-code",
				Name:              "test-product-name",
				Description:       "create test product",
				CreatedBy:         "unit-test",
			},
			storeLoadResponse: []*storage.Product{{
				PublicID:          "test-id",
				ProductTemplateID: 1,
				Code:              "test-product-code",
				Name:              "test-product-name",
				Description: sql.NullString{
					String: "create test product",
					Valid:  true,
				},
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.CreateProductResponse{
				Product: &api.Product{
					Id:                "test-id",
					ProductTemplateID: "test-product-template-id-1",
					Code:              "test-product-code",
					Name:              "test-product-name",
					Description:       "create test product",
					Status:            api.EntityStatus_ACTIVE,
					CreatedBy:         "unit-test",
					CreatedAt:         now,
					UpdatedBy:         "unit-test",
					UpdatedAt:         now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "test-product-template-id-1",
				Code:              "test-product-code",
				Name:              "test-product-name",
				Description:       "create test product",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - product template id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "",
				Code:              "test-product-code",
				Name:              "test-product-name",
				Description:       "create test product",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductTemplateID.Code), 10), apiErr.ErrMissingProductTemplateID.Message),
		},
		{
			testDesc:       "error path - code is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "test-product-template-id-1",
				Code:              "",
				Name:              "test-product-name",
				Description:       "create test product",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc:       "error path - name is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "test-product-template-id-1",
				Code:              "test-product-code",
				Name:              "",
				Description:       "create test product",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingName.Code), 10), apiErr.ErrMissingName.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "test-product-template-id-1",
				Code:              "test-product-code",
				Name:              "test-product-name",
				Description:       "create test product",
				CreatedBy:         "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "test-product-template-id-1",
				Code:              "test-product-code",
				Name:              "test-product-name",
				Description:       "create test product",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductRequest{
				ProductTemplateID: "test-product-template-id-1",
				Code:              "test-product-code",
				Name:              "test-product-name",
				Description:       "create test product",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProduct := &storage.MockIProductDAO{}
			mockProductTemplate := &storage.MockIProductTemplateDAO{}
			mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{}}, nil)
			mockProduct.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockProduct.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductDAO:         mockProduct,
				ProductTemplateDAO: mockProductTemplate,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			product, err := service.CreateProduct(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, product)
			}
		})
	}
}

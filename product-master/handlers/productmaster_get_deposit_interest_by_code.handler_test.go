package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetDepositInterestByCode(t *testing.T) {
	now := time.Now().UTC()
	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := api.Currency(appConfig.Locale.Currency)
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetDepositInterestByCodeRequest
		storeLoadResponse []*storage.DepositInterest
		expectedResponse  *api.GetDepositInterestByCodeResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get deposit interest linked to base interest",
			requestParams: &api.GetDepositInterestByCodeRequest{
				Code: "test-deposit-interest-code",
			},
			storeLoadResponse: []*storage.DepositInterest{
				{
					PublicID:           "test-id",
					ProductVariantID:   1,
					IsLinkedToBaseRate: true,
					BaseInterestID: sql.NullInt64{
						Int64: 1,
						Valid: true,
					},
					Code: "test-deposit-interest-code",
					Name: "test-deposit-interest-name",
					Description: sql.NullString{
						String: "get test deposit-interest",
						Valid:  true,
					},
					Currency:              string(currency),
					RoundOffType:          "FLOOR",
					InterestSlabType:      "AMOUNT",
					InterestSlabStructure: "ABSOLUTE",
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			expectedResponse: &api.GetDepositInterestByCodeResponse{
				DepositInterest: &api.DepositInterest{
					Id:                    "test-id",
					ProductVariantID:      "test-product-variant-id",
					IsLinkedToBaseRate:    true,
					BaseInterestID:        "test-base-interest-id",
					Code:                  "test-deposit-interest-code",
					Name:                  "test-deposit-interest-name",
					Description:           "get test deposit-interest",
					Currency:              currency,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestSlabType:      api.InterestSlabType_AMOUNT,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "happy path - get deposit interest not linked to base interest",
			requestParams: &api.GetDepositInterestByCodeRequest{
				Code: "test-deposit-interest-code",
			},
			storeLoadResponse: []*storage.DepositInterest{
				{
					PublicID:           "test-id",
					ProductVariantID:   1,
					IsLinkedToBaseRate: false,
					BaseInterestID:     sql.NullInt64{},
					Code:               "test-deposit-interest-code",
					Name:               "test-deposit-interest-name",
					Description: sql.NullString{
						String: "get test deposit-interest",
						Valid:  true,
					},
					Currency:              string(currency),
					RoundOffType:          "FLOOR",
					InterestSlabType:      "AMOUNT",
					InterestSlabStructure: "ABSOLUTE",
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			expectedResponse: &api.GetDepositInterestByCodeResponse{
				DepositInterest: &api.DepositInterest{
					Id:                    "test-id",
					ProductVariantID:      "test-product-variant-id",
					IsLinkedToBaseRate:    false,
					BaseInterestID:        "",
					Code:                  "test-deposit-interest-code",
					Name:                  "test-deposit-interest-name",
					Description:           "get test deposit-interest",
					Currency:              currency,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestSlabType:      api.InterestSlabType_AMOUNT,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - code is missing",
			requestParams: &api.GetDepositInterestByCodeRequest{
				Code: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc: "error path - deposit interest not found in database",
			requestParams: &api.GetDepositInterestByCodeRequest{
				Code: "test-deposit-interest-code",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetDepositInterestByCodeRequest{
				Code: "test-deposit-interest-code",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterest := &storage.MockIBaseInterestDAO{}
			mockBaseInterest.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.BaseInterest{PublicID: "test-base-interest-id"}, nil)

			mockProductVariantDAO := &storage.MockIProductVariantDAO{}
			mockProductVariantDAO.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariant{PublicID: "test-product-variant-id"}, nil)

			mockDepositInterest := &storage.MockIDepositInterestDAO{}
			mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockStore := &storage.DBStore{
				DepositInterestDAO: mockDepositInterest,
				BaseInterestDAO:    mockBaseInterest,
				ProductVariantDAO:  mockProductVariantDAO,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			depositInterest, err := service.GetDepositInterestByCode(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, depositInterest)
			}
		})
	}
}

package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/pocket"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// ListPocketTemplates fetches list of pocket templates
func (p *ProductMasterService) ListPocketTemplates(ctx context.Context, req *api.ListPocketTemplatesRequest) (*api.ListPocketTemplatesResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListPocketTemplates"))
	slog.FromContext(ctx).Info(constants.ListPocketTemplatesLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateListPocketTemplatesRequest(ctx, req); err != nil {
		return nil, err
	}

	return pocket.ListPocketTemplates(ctx, p.HermesClient, req, p.Store)
}

func validateListPocketTemplatesRequest(ctx context.Context, req *api.ListPocketTemplatesRequest) error {
	if err := validations.ValidatePocketType(ctx, string(req.Type)); err != nil {
		return err
	}
	return nil
}

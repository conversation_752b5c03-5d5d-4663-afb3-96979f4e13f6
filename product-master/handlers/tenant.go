package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/featureflag"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func withTenant(a *ProductMasterService) servus.MiddlewareFunc {
	return func(h servus.HandlerFunc) servus.HandlerFunc {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			if a.Tenant != nil {
				return h(a.Tenant.MustInjectTenant(ctx, a.Tenant.Name), request)
			}
			return h(ctx, request)
		}
	}
}

func withFeatureFlag(a *ProductMasterService) servus.MiddlewareFunc {
	return func(h servus.HandlerFunc) servus.HandlerFunc {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			ctx = featureflag.NewContextWithFeatureFlags(ctx, &a.AppConfig.FeatureFlags)
			return h(ctx, request)
		}
	}
}

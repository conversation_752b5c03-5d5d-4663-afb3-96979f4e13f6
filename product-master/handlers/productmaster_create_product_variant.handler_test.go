package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateProductVariant(t *testing.T) {
	now := time.Now().UTC()
	nowPlus10 := time.Now().UTC().Add(10 * time.Minute)
	nowMinus10 := time.Now().UTC().Add(-10 * time.Minute)
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateProductVariantRequest
		storeLoadResponse []*storage.ProductVariant
		expectedResponse  *api.CreateProductVariantResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - product variant created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			storeLoadResponse: []*storage.ProductVariant{{
				PublicID:  "test-id",
				ProductID: 1,
				Code:      "test-product-variant-code",
				Name:      "test-product-variant-name",
				Version:   "1.0.0",
				Description: sql.NullString{
					String: "create test product variant",
					Valid:  true,
				},
				ValidFrom: now,
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.CreateProductVariantResponse{
				ProductVariant: &api.ProductVariant{
					Id:          "test-id",
					Code:        "test-product-variant-code",
					Version:     "1.0.0",
					Name:        "test-product-variant-name",
					ProductID:   "test-product",
					Description: "create test product variant",
					Status:      api.EntityStatus_ACTIVE,
					ValidFrom:   now,
					ValidTo:     time.Time{},
					CreatedBy:   "unit-test",
					CreatedAt:   now,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - product id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductID.Code), 10), apiErr.ErrMissingProductID.Message),
		},
		{
			testDesc:       "error path - code is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc:       "error path - name is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingName.Code), 10), apiErr.ErrMissingName.Message),
		},
		{
			testDesc:       "error path - valid from is less than current date",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   nowMinus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrValidFromLessThanCurrent.Code), 10), apiErr.ErrValidFromLessThanCurrent.Message),
		},
		{
			testDesc:       "error path - valid to is less than current date",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidTo:     nowMinus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrValidToLessThanCurrent.Code), 10), apiErr.ErrValidToLessThanCurrent.Message),
		},
		{
			testDesc:       "error path - valid to is less than valid from",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   nowPlus10,
				ValidTo:     now,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrValidFromGreaterThanValidTo.Code), 10), apiErr.ErrValidFromGreaterThanValidTo.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - version is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingVersion.Code), 10), apiErr.ErrMissingVersion.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductVariantRequest{
				Code:        "test-product-variant-code",
				Version:     "1.0.0",
				Name:        "test-product-variant-name",
				ProductID:   "test-product",
				Description: "create test product variant",
				ValidFrom:   now,
				ValidTo:     nowPlus10,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductDAO := &storage.MockIProductDAO{}
			mockProductDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Product{{}}, nil)
			mockProductVariantDAO := &storage.MockIProductVariantDAO{}
			mockProductVariantDAO.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockProductVariantDAO.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO: mockProductVariantDAO,
				ProductDAO:        mockProductDAO,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			product, err := service.CreateProductVariant(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, product)
			}
		})
	}
}

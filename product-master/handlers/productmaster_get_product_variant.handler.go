package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductVariant fetches a product variant by id
func (p *ProductMasterService) GetProductVariant(ctx context.Context, req *api.GetProductVariantRequest) (*api.GetProductVariantResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductVariant"))
	slog.FromContext(ctx).Info(constants.GetProductVariantLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductVariantRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.GetProductVariant(ctx, req, p.Store)
}

func validateGetProductVariantRequest(ctx context.Context, req *api.GetProductVariantRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

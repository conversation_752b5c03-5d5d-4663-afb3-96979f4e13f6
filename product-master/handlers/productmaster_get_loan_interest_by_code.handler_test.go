package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetLoanInterestByCode(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetLoanInterestByCodeRequest
		storeLoadResponse []*storage.LoanInterest
		expectedResponse  *api.GetLoanInterestByCodeResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get loan interest linked to base interest",
			requestParams: &api.GetLoanInterestByCodeRequest{
				Code: "test-loan-interest-code",
			},
			storeLoadResponse: []*storage.LoanInterest{
				{
					PublicID:           "test-id",
					ProductVariantID:   1,
					IsLinkedToBaseRate: true,
					BaseInterestID: sql.NullInt64{
						Int64: 1,
						Valid: true,
					},
					Code: "test-loan-interest-code",
					Name: "test-loan-interest-name",
					Description: sql.NullString{
						String: "get test loan-interest",
						Valid:  true,
					},
					Currency:              "SGD",
					RoundOffType:          "FLOOR",
					InterestSlabUnitType:  "AMOUNT",
					InterestSlabStructure: "ABSOLUTE",
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			expectedResponse: &api.GetLoanInterestByCodeResponse{
				LoanInterest: &api.LoanInterest{
					Id:                    "test-id",
					ProductVariantID:      "test-product-variant-id",
					IsLinkedToBaseRate:    true,
					BaseInterestID:        "test-base-interest-id",
					Code:                  "test-loan-interest-code",
					Name:                  "test-loan-interest-name",
					Description:           "get test loan-interest",
					Currency:              api.Currency_SGD,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "happy path - get loan interest not linked to base interest",
			requestParams: &api.GetLoanInterestByCodeRequest{
				Code: "test-loan-interest-code",
			},
			storeLoadResponse: []*storage.LoanInterest{
				{
					PublicID:           "test-id",
					ProductVariantID:   1,
					IsLinkedToBaseRate: false,
					BaseInterestID:     sql.NullInt64{},
					Code:               "test-loan-interest-code",
					Name:               "test-loan-interest-name",
					Description: sql.NullString{
						String: "get test loan-interest",
						Valid:  true,
					},
					Currency:              "SGD",
					RoundOffType:          "FLOOR",
					InterestSlabUnitType:  "AMOUNT",
					InterestSlabStructure: "ABSOLUTE",
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			expectedResponse: &api.GetLoanInterestByCodeResponse{
				LoanInterest: &api.LoanInterest{
					Id:                    "test-id",
					ProductVariantID:      "test-product-variant-id",
					IsLinkedToBaseRate:    false,
					BaseInterestID:        "",
					Code:                  "test-loan-interest-code",
					Name:                  "test-loan-interest-name",
					Description:           "get test loan-interest",
					Currency:              api.Currency_SGD,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestSlabUnitType:  api.InterestSlabType_AMOUNT,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - code is missing",
			requestParams: &api.GetLoanInterestByCodeRequest{
				Code: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc: "error path - loan interest not found in database",
			requestParams: &api.GetLoanInterestByCodeRequest{
				Code: "test-loan-interest-code",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetLoanInterestByCodeRequest{
				Code: "test-loan-interest-code",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterest := &storage.MockIBaseInterestDAO{}
			mockBaseInterest.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.BaseInterest{PublicID: "test-base-interest-id"}, nil)

			mockProductVariantDAO := &storage.MockIProductVariantDAO{}
			mockProductVariantDAO.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariant{PublicID: "test-product-variant-id"}, nil)

			mockLoanInterest := &storage.MockILoanInterestDAO{}
			mockLoanInterest.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				LoanInterestDAO:   mockLoanInterest,
				BaseInterestDAO:   mockBaseInterest,
				ProductVariantDAO: mockProductVariantDAO,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			loanInterest, err := service.GetLoanInterestByCode(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, loanInterest)
			}
		})
	}
}

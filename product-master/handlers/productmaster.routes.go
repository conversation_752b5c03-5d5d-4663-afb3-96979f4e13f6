// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/mockservices"
	"gitlab.myteksi.net/dakota/common/servicename"

	servus "gitlab.myteksi.net/dakota/servus/v2"
)

func withClientIdentities(middlewareFunc servus.MiddlewareFunc) servus.MiddlewareFunc {
	if mockservices.UseMockAuth {
		return mockservices.WithAuth()
	} else {
		return middlewareFunc
	}
}

func passThroughMiddleware(h servus.HandlerFunc) servus.HandlerFunc {
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		return h(ctx, req)
	}
}

// RegisterRoutes registers handlers with the Servus library.
func (p *ProductMasterService) RegisterRoutes(app *servus.Application) {
	app.POST(
		"/v1/product-templates",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProductTemplate(ctx, req.(*api.CreateProductTemplateRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductTemplateRequest{}),
		servus.WithResponse(&api.CreateProductTemplateResponse{}),
		servus.WithDescription("CreateProductTemplate creates a new Product Template"),
		withTenant(p),
	)
	app.GET(
		"/v1/product-templates/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductTemplate(ctx, req.(*api.GetProductTemplateRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductTemplateRequest{}),
		servus.WithResponse(&api.GetProductTemplateResponse{}),
		servus.WithDescription("GetProductTemplate fetches a product template by id"),
		withTenant(p),
	)
	app.PUT(
		"/v1/product-templates/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateProductTemplateStatus(ctx, req.(*api.UpdateProductTemplateStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateProductTemplateStatusRequest{}),
		servus.WithResponse(&api.UpdateProductTemplateStatusResponse{}),
		servus.WithDescription("UpdateProductTemplateStatus updates the status of Product Template"),
		withTenant(p),
	)
	app.POST(
		"/v1/product-variants",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProductVariant(ctx, req.(*api.CreateProductVariantRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductVariantRequest{}),
		servus.WithResponse(&api.CreateProductVariantResponse{}),
		servus.WithDescription("CreateProductVariant creates a new Product Variant"),
		withTenant(p),
	)
	app.GET(
		"/v2/product-variants",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListProductVariants(ctx, req.(*api.ListProductVariantsRequest))
			return res, err
		},
		servus.WithRequest(&api.ListProductVariantsRequest{}),
		servus.WithResponse(&api.ListProductVariantsResponse{}),
		servus.WithDescription("ListProductVariants fetches a product variant by id"),
		withClientIdentities(servus.WithClientIdentities(servicename.DepositsExp)),
		withTenant(p),
	)
	app.GET(
		"/v1/product-variants/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductVariant(ctx, req.(*api.GetProductVariantRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductVariantRequest{}),
		servus.WithResponse(&api.GetProductVariantResponse{}),
		servus.WithDescription("GetProductVariant fetches a product variant by id"),
		withTenant(p),
	)
	app.GET(
		"/v1/product-variants",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductVariantByCode(ctx, req.(*api.GetProductVariantByCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductVariantByCodeRequest{}),
		servus.WithResponse(&api.GetProductVariantByCodeResponse{}),
		servus.WithDescription("GetProductVariantByCode fetches a product variant by code"),
		withClientIdentities(servus.WithClientIdentities(servicename.DepositsCore, servicename.LoanCore)),
		withTenant(p),
	)
	app.PUT(
		"/v1/product-variants/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateProductVariantStatus(ctx, req.(*api.UpdateProductVariantStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateProductVariantStatusRequest{}),
		servus.WithResponse(&api.UpdateProductVariantStatusResponse{}),
		servus.WithDescription("UpdateProductVariantStatus updates the status of Product Variant"),
		withTenant(p),
	)
	app.POST(
		"/v1/products",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProduct(ctx, req.(*api.CreateProductRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductRequest{}),
		servus.WithResponse(&api.CreateProductResponse{}),
		servus.WithDescription("CreateProduct creates a new Product"),
		withTenant(p),
	)
	app.GET(
		"/v1/products/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProduct(ctx, req.(*api.GetProductRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductRequest{}),
		servus.WithResponse(&api.GetProductResponse{}),
		servus.WithDescription("GetProduct fetches a product by id"),
		withClientIdentities(servus.WithClientIdentities(servicename.DepositsCore, servicename.LoanCore)),
		withTenant(p),
	)
	app.GET(
		"/v1/products",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductByCode(ctx, req.(*api.GetProductByCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductByCodeRequest{}),
		servus.WithResponse(&api.GetProductByCodeResponse{}),
		servus.WithDescription("GetProduct fetches a product by code"),
		withTenant(p),
	)
	app.PUT(
		"/v1/products/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateProductStatus(ctx, req.(*api.UpdateProductStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateProductStatusRequest{}),
		servus.WithResponse(&api.UpdateProductStatusResponse{}),
		servus.WithDescription("UpdateProductStatus updates the status of Product Template"),
		withTenant(p),
	)
	app.POST(
		"/v1/product-template-parameters",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProductTemplateParameter(ctx, req.(*api.CreateProductTemplateParameterRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductTemplateParameterRequest{}),
		servus.WithResponse(&api.CreateProductTemplateParameterResponse{}),
		servus.WithDescription("CreateProductTemplateParameter creates a new Product Template Parameter"),
		withTenant(p),
	)
	app.GET(
		"/v1/product-template-parameters/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductTemplateParameter(ctx, req.(*api.GetProductTemplateParameterRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductTemplateParameterRequest{}),
		servus.WithResponse(&api.GetProductTemplateParameterResponse{}),
		servus.WithDescription("GetProductTemplateParameter fetches a product template parameter by id"),
		withTenant(p),
	)
	app.GET(
		"/v1/product-template-parameters",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListProductTemplateParameters(ctx, req.(*api.ListProductTemplateParametersRequest))
			return res, err
		},
		servus.WithRequest(&api.ListProductTemplateParametersRequest{}),
		servus.WithResponse(&api.ListProductTemplateParametersResponse{}),
		servus.WithDescription("ListProductTemplateParameters fetches all product template parameters for a product variant"),
		withTenant(p),
	)
	app.PUT(
		"/v1/product-template-parameters/:id/value",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateProductTemplateParameterValue(ctx, req.(*api.UpdateProductTemplateParameterValueRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateProductTemplateParameterValueRequest{}),
		servus.WithResponse(&api.UpdateProductTemplateParameterValueResponse{}),
		servus.WithDescription("UpdateProductTemplateParameterValue updates the value of Product Template Parameter Value"),
		withTenant(p),
	)
	app.POST(
		"/v1/product-variant-parameters",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProductVariantParameter(ctx, req.(*api.CreateProductVariantParameterRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductVariantParameterRequest{}),
		servus.WithResponse(&api.CreateProductVariantParameterResponse{}),
		servus.WithDescription("CreateProductVariantParameter creates a new Product Variant Parameter"),
		withTenant(p),
	)
	app.GET(
		"/v1/product-variant-parameters/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductVariantParameter(ctx, req.(*api.GetProductVariantParameterRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductVariantParameterRequest{}),
		servus.WithResponse(&api.GetProductVariantParameterResponse{}),
		servus.WithDescription("GetProductVariantParameter fetches a product variant parameter by id"),
		withTenant(p),
	)
	app.GET(
		"/v1/product-variant-parameters",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListProductVariantParameters(ctx, req.(*api.ListProductVariantParametersRequest))
			return res, err
		},
		servus.WithRequest(&api.ListProductVariantParametersRequest{}),
		servus.WithResponse(&api.ListProductVariantParametersResponse{}),
		servus.WithDescription("ListProductVariantParameters fetches all product variant parameters for a product variant"),
		withTenant(p),
	)
	app.POST(
		"/v2/effective-product-variant-parameters/get",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListEffectiveProductVariantParameters(ctx, req.(*api.ListEffectiveProductVariantParametersRequest))
			return res, err
		},
		servus.WithRequest(&api.ListEffectiveProductVariantParametersRequest{}),
		servus.WithResponse(&api.ListEffectiveProductVariantParametersResponse{}),
		servus.WithDescription("ListEffectiveProductVariantParameters fetches all product variant parameters for a product variant (includes the overrides from product-template-parameters"),
		withClientIdentities(servus.WithClientIdentities(servicename.DepositsCore, servicename.CustomerPortal, servicename.PaymentOpsTrf, servicename.SentryPartnerT6, servicename.LoanExp, servicename.LoanCore, servicename.LoanApp, servicename.AccountService, servicename.DepositsExp)),
		withFeatureFlag(p),
		withTenant(p),
	)
	app.PUT(
		"/v1/product-variant-parameters/:id/value",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateProductVariantParameterValue(ctx, req.(*api.UpdateProductVariantParameterValueRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateProductVariantParameterValueRequest{}),
		servus.WithResponse(&api.UpdateProductVariantParameterValueResponse{}),
		servus.WithDescription("UpdateProductVariantParameterValue updates the value of Product Variant Parameter Value"),
		withTenant(p),
	)
	app.POST(
		"/v1/transaction-catalogue",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateTransactionCatalogue(ctx, req.(*api.CreateTransactionCatalogueRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateTransactionCatalogueRequest{}),
		servus.WithResponse(&api.CreateTransactionCatalogueResponse{}),
		servus.WithDescription("CreateTransactionCatalogue creates a new Transaction catalogue"),
		withTenant(p),
	)
	app.GET(
		"/v1/transaction-catalogue/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetTransactionCatalogue(ctx, req.(*api.GetTransactionCatalogueRequest))
			return res, err
		},
		servus.WithRequest(&api.GetTransactionCatalogueRequest{}),
		servus.WithResponse(&api.GetTransactionCatalogueResponse{}),
		servus.WithDescription("GetTransactionCatalogue fetches a transaction catalogue by id"),
		withTenant(p),
	)
	app.PUT(
		"/v1/transaction-catalogue/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateTransactionCatalogueStatus(ctx, req.(*api.UpdateTransactionCatalogueStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateTransactionCatalogueStatusRequest{}),
		servus.WithResponse(&api.UpdateTransactionCatalogueStatusResponse{}),
		servus.WithDescription("UpdateTransactionCatalogueStatus updates the status of Transaction catalogue"),
		withTenant(p),
	)
	app.POST(
		"/v1/product-variant-transaction-catalogue-mapping",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProductVariantTransactionCatalogueMapping(ctx, req.(*api.CreateProductVariantTransactionCatalogueMappingRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductVariantTransactionCatalogueMappingRequest{}),
		servus.WithResponse(&api.CreateProductVariantTransactionCatalogueMappingResponse{}),
		servus.WithDescription("CreateProductVariantTransactionCatalogueMapping creates a new Product Variant Transaction catalogue mapping"),
		withTenant(p),
	)
	app.GET(
		"/v1/product-variant-transaction-catalogue-mapping/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductVariantTransactionCatalogueMapping(ctx, req.(*api.GetProductVariantTransactionCatalogueMappingRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductVariantTransactionCatalogueMappingRequest{}),
		servus.WithResponse(&api.GetProductVariantTransactionCatalogueMappingResponse{}),
		servus.WithDescription("GetProductVariantTransactionCatalogueMapping fetches a Product Variant Transaction catalogue mapping by id"),
		withTenant(p),
	)
	app.PUT(
		"/v1/product-variant-transaction-catalogue-mapping/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateProductVariantTransactionCatalogueMappingStatus(ctx, req.(*api.UpdateProductVariantTransactionCatalogueMappingStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateProductVariantTransactionCatalogueMappingStatusRequest{}),
		servus.WithResponse(&api.UpdateProductVariantTransactionCatalogueMappingStatusResponse{}),
		servus.WithDescription("UpdateProductVariantTransactionCatalogueMappingStatus updates the status of Product Variant Transaction catalogue mapping"),
		withTenant(p),
	)
	app.POST(
		"/v1/general-ledgers",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateGeneralLedger(ctx, req.(*api.CreateGeneralLedgerRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateGeneralLedgerRequest{}),
		servus.WithResponse(&api.CreateGeneralLedgerResponse{}),
		servus.WithDescription("CreateGeneralLedger creates a new GeneralLedger"),
		withTenant(p),
	)
	app.GET(
		"/v1/general-ledgers/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetGeneralLedger(ctx, req.(*api.GetGeneralLedgerRequest))
			return res, err
		},
		servus.WithRequest(&api.GetGeneralLedgerRequest{}),
		servus.WithResponse(&api.GetGeneralLedgerResponse{}),
		servus.WithDescription("GetGeneralLedger fetches a general-ledger by id"),
		withTenant(p),
	)
	app.GET(
		"/v1/general-ledgers",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetGeneralLedgerByCode(ctx, req.(*api.GetGeneralLedgerByCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetGeneralLedgerByCodeRequest{}),
		servus.WithResponse(&api.GetGeneralLedgerByCodeResponse{}),
		servus.WithDescription("GetGeneralLedgerByCode fetches a general-ledger by code"),
		withTenant(p),
	)
	app.PUT(
		"/v1/general-ledgers/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateGeneralLedgerStatus(ctx, req.(*api.UpdateGeneralLedgerStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateGeneralLedgerStatusRequest{}),
		servus.WithResponse(&api.UpdateGeneralLedgerStatusResponse{}),
		servus.WithDescription("UpdateGeneralLedgerStatus updates the status of general-ledger"),
		withTenant(p),
	)
	app.POST(
		"/v1/internal-accounts",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateInternalAccount(ctx, req.(*api.CreateInternalAccountRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateInternalAccountRequest{}),
		servus.WithResponse(&api.CreateInternalAccountResponse{}),
		servus.WithDescription("CreateInternalAccount creates a new Internal Account"),
		withTenant(p),
	)
	app.GET(
		"/v1/internal-accounts/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetInternalAccount(ctx, req.(*api.GetInternalAccountRequest))
			return res, err
		},
		servus.WithRequest(&api.GetInternalAccountRequest{}),
		servus.WithResponse(&api.GetInternalAccountResponse{}),
		servus.WithDescription("GetInternalAccount fetches a internal account by id"),
		withTenant(p),
	)
	app.GET(
		"/v1/internal-accounts",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListInternalAccounts(ctx, req.(*api.ListInternalAccountsRequest))
			return res, err
		},
		servus.WithRequest(&api.ListInternalAccountsRequest{}),
		servus.WithResponse(&api.ListInternalAccountsResponse{}),
		servus.WithDescription("ListInternalAccounts fetches a list of internal account by code and generalLedgerID"),
		withTenant(p),
	)
	app.PUT(
		"/v1/internal-accounts/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateInternalAccountStatus(ctx, req.(*api.UpdateInternalAccountStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateInternalAccountStatusRequest{}),
		servus.WithResponse(&api.UpdateInternalAccountStatusResponse{}),
		servus.WithDescription("UpdateInternalAccountStatus updates the status of internal account"),
	)
	app.POST(
		"/v1/product-variant-transaction-catalogue-internal-account-mapping",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx, req.(*api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest{}),
		servus.WithResponse(&api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse{}),
		servus.WithDescription("CreateProductVariantTransactionCatalogueInternalAccountMapping creates a new product-variant-transaction-catalogue-internal-account-mapping"),
	)
	app.GET(
		"/v1/product-variant-transaction-catalogue-internal-account-mapping/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductVariantTransactionCatalogueInternalAccountMapping(ctx, req.(*api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest{}),
		servus.WithResponse(&api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse{}),
		servus.WithDescription("GetProductVariantTransactionCatalogueInternalAccountMapping fetches a product-variant-transaction-catalogue-internal-account-mapping by id"),
	)
	app.GET(
		"/v1/product-variant-transaction-catalogue-internal-account-mapping",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetProductVariantTransactionCatalogueInternalAccountMappingByKey(ctx, req.(*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest))
			return res, err
		},
		servus.WithRequest(&api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest{}),
		servus.WithResponse(&api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse{}),
		servus.WithDescription("GetProductVariantTransactionCatalogueInternalAccountMappingByKey fetches a product-variant-transaction-catalogue-internal-account-mapping by identifer key"),
	)
	app.PUT(
		"/v1/product-variant-transaction-catalogue-internal-account-mapping/:id/status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(ctx, req.(*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest{}),
		servus.WithResponse(&api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse{}),
		servus.WithDescription("UpdateProductVariantTransactionCatalogueInternalAccountMapping updates the status of product-variant-transaction-catalogue-internal-account-mapping"),
	)
	app.POST(
		"/v1/base-interest",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateBaseInterest(ctx, req.(*api.CreateBaseInterestRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateBaseInterestRequest{}),
		servus.WithResponse(&api.CreateBaseInterestResponse{}),
		servus.WithDescription("CreateBaseInterest creates a new base-interest"),
	)
	app.GET(
		"/v1/base-interest/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetBaseInterest(ctx, req.(*api.GetBaseInterestRequest))
			return res, err
		},
		servus.WithRequest(&api.GetBaseInterestRequest{}),
		servus.WithResponse(&api.GetBaseInterestResponse{}),
		servus.WithDescription("GetBaseInterest fetches a base interest by id"),
	)
	app.GET(
		"/v1/base-interest",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetBaseInterestByCode(ctx, req.(*api.GetBaseInterestByCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetBaseInterestByCodeRequest{}),
		servus.WithResponse(&api.GetBaseInterestByCodeResponse{}),
		servus.WithDescription("GetBaseInterestByCode fetches a base interest by code"),
	)
	app.POST(
		"/v1/base-interest-version",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateBaseInterestVersion(ctx, req.(*api.CreateBaseInterestVersionRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateBaseInterestVersionRequest{}),
		servus.WithResponse(&api.CreateBaseInterestVersionResponse{}),
		servus.WithDescription("CreateBaseInterestVersion creates a new base interest version"),
	)
	app.GET(
		"/v1/base-interest-version/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetBaseInterestVersion(ctx, req.(*api.GetBaseInterestVersionRequest))
			return res, err
		},
		servus.WithRequest(&api.GetBaseInterestVersionRequest{}),
		servus.WithResponse(&api.GetBaseInterestVersionResponse{}),
		servus.WithDescription("GetBaseInterestVersion fetches a base interest version by id"),
	)
	app.POST(
		"/v1/base-interest-time-slab-rate",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateBaseInterestTimeSlabRate(ctx, req.(*api.CreateBaseInterestTimeSlabRateRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateBaseInterestTimeSlabRateRequest{}),
		servus.WithResponse(&api.CreateBaseInterestTimeSlabRateResponse{}),
		servus.WithDescription("CreateBaseInterestTimeSlabRate creates a new base interest time slab rate"),
	)
	app.GET(
		"/v1/base-interest-time-slab-rate/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetBaseInterestTimeSlabRate(ctx, req.(*api.GetBaseInterestTimeSlabRateRequest))
			return res, err
		},
		servus.WithRequest(&api.GetBaseInterestTimeSlabRateRequest{}),
		servus.WithResponse(&api.GetBaseInterestTimeSlabRateResponse{}),
		servus.WithDescription("GetBaseInterestTimeSlabRate fetches a base interest time slab rate by id"),
	)
	app.POST(
		"/v1/deposit-interest",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateDepositInterest(ctx, req.(*api.CreateDepositInterestRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateDepositInterestRequest{}),
		servus.WithResponse(&api.CreateDepositInterestResponse{}),
		servus.WithDescription("CreateDepositInterest creates a new deposit interest"),
	)
	app.GET(
		"/v1/deposit-interest/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetDepositInterest(ctx, req.(*api.GetDepositInterestRequest))
			return res, err
		},
		servus.WithRequest(&api.GetDepositInterestRequest{}),
		servus.WithResponse(&api.GetDepositInterestResponse{}),
		servus.WithDescription("GetDepositInterest fetches a deposit interest by id"),
	)
	app.GET(
		"/v1/deposit-interest",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetDepositInterestByCode(ctx, req.(*api.GetDepositInterestByCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetDepositInterestByCodeRequest{}),
		servus.WithResponse(&api.GetDepositInterestByCodeResponse{}),
		servus.WithDescription("GetDepositInterestByCode fetches a deposit interest by code"),
	)
	app.POST(
		"/v1/deposit-interest-version",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateDepositInterestVersion(ctx, req.(*api.CreateDepositInterestVersionRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateDepositInterestVersionRequest{}),
		servus.WithResponse(&api.CreateDepositInterestVersionRequest{}),
		servus.WithDescription("CreateDepositInterestVersion creates a new deposit interest version"),
		withTenant(p),
	)
	app.GET(
		"/v1/deposit-interest-version/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetDepositInterestVersion(ctx, req.(*api.GetDepositInterestVersionRequest))
			return res, err
		},
		servus.WithRequest(&api.GetDepositInterestVersionRequest{}),
		servus.WithResponse(&api.GetDepositInterestVersionResponse{}),
		servus.WithDescription("GetDepositInterestVersion fetches a deposit interest version by id"),
	)
	app.POST(
		"/v1/deposit-interest-amount-slab-rate",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateDepositInterestAmountSlabRate(ctx, req.(*api.CreateDepositInterestAmountSlabRateRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateDepositInterestAmountSlabRateRequest{}),
		servus.WithResponse(&api.CreateDepositInterestAmountSlabRateResponse{}),
		servus.WithDescription("CreateDepositInterestAmountSlabRate creates a new deposit interest amount slab rate"),
	)
	app.GET(
		"/v1/deposit-interest-amount-slab-rate/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetDepositInterestAmountSlabRate(ctx, req.(*api.GetDepositInterestAmountSlabRateRequest))
			return res, err
		},
		servus.WithRequest(&api.GetDepositInterestAmountSlabRateRequest{}),
		servus.WithResponse(&api.GetDepositInterestAmountSlabRateResponse{}),
		servus.WithDescription("GetDepositInterestAmountSlabRate fetches a deposit interest amount slab rate by id"),
	)
	app.POST(
		"/v1/pocket-templates",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreatePocketTemplate(ctx, req.(*api.CreatePocketTemplateRequest))
			return res, err
		},
		servus.WithRequest(&api.CreatePocketTemplateRequest{}),
		servus.WithResponse(&api.CreatePocketTemplateResponse{}),
		servus.WithDescription("CreatePocketTemplate creates a new pocket template"),
	)
	app.GET(
		"/v1/pocket-templates",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListPocketTemplates(ctx, req.(*api.ListPocketTemplatesRequest))
			return res, err
		},
		servus.WithRequest(&api.ListPocketTemplatesRequest{}),
		servus.WithResponse(&api.ListPocketTemplatesResponse{}),
		servus.WithDescription("ListPocketTemplates fetches list of pocket templates"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6)),
		withTenant(p),
	)
	app.GET(
		"/v1/pocket-templates/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetPocketTemplate(ctx, req.(*api.GetPocketTemplateRequest))
			return res, err
		},
		servus.WithRequest(&api.GetPocketTemplateRequest{}),
		servus.WithResponse(&api.GetPocketTemplateResponse{}),
		servus.WithDescription("GetPocketTemplate fetches pocket template details"),
		withClientIdentities(servus.WithClientIdentities(servicename.AccountService)),
		withTenant(p),
	)
	app.POST(
		"/v1/pocket-template-questions",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreatePocketTemplateQuestions(ctx, req.(*api.CreatePocketTemplateQuestionsRequest))
			return res, err
		},
		servus.WithRequest(&api.CreatePocketTemplateQuestionsRequest{}),
		servus.WithResponse(&api.CreatePocketTemplateQuestionsResponse{}),
		servus.WithDescription("CreatePocketTemplateQuestions creates new pocket template questions"),
	)
	app.GET(
		"/v1/pocket-template-questions",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListPocketTemplateQuestions(ctx, req.(*api.ListPocketTemplateQuestionsRequest))
			return res, err
		},
		servus.WithRequest(&api.ListPocketTemplateQuestionsRequest{}),
		servus.WithResponse(&api.ListPocketTemplateQuestionsResponse{}),
		servus.WithDescription("ListPocketTemplateQuestions fetches list of pocket template questions"),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6)),
		WithLocale(),
		withTenant(p),
	)
	app.GET(
		"/v1/interest-parameters",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetInterestParameters(ctx, req.(*api.GetInterestParametersRequest))
			return res, err
		},
		servus.WithRequest(&api.GetInterestParametersRequest{}),
		servus.WithResponse(&api.GetInterestParametersResponse{}),
		servus.WithDescription("GetInterestParameters fetches all product params whose values are updated"),
		withClientIdentities(servus.WithClientIdentities(servicename.DepositsCore)),
	)
	app.PUT(
		"/v1/interest-parameters/schedule-status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateInterestParameterScheduleStatus(ctx, req.(*api.UpdateInterestParameterScheduleStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateInterestParameterScheduleStatusRequest{}),
		servus.WithResponse(&api.UpdateInterestParameterScheduleStatusResponse{}),
		servus.WithDescription("UpdateInterestParameterScheduleStatus keep the status whether parameter is scheduled in TM or not"),
		withClientIdentities(servus.WithClientIdentities(servicename.DepositsCore)),
	)
	app.PUT(
		"/v1/interest-parameters/notification-status",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.UpdateInterestParameterNotificationStatus(ctx, req.(*api.UpdateInterestParameterNotificationStatusRequest))
			return res, err
		},
		servus.WithRequest(&api.UpdateInterestParameterNotificationStatusRequest{}),
		servus.WithResponse(&api.UpdateInterestParameterNotificationStatusResponse{}),
		servus.WithDescription("UpdateInterestParameterNotificationStatus keep the status whether noti is sent to the customer"),
		withClientIdentities(servus.WithClientIdentities(servicename.DepositsCore)),
	)
	app.POST(
		"/v1/get-interest-parameters",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetInterestParametersByProductVariant(ctx, req.(*api.GetInterestParametersByProductVariantRequest))
			return res, err
		},
		servus.WithRequest(&api.GetInterestParametersByProductVariantRequest{}),
		servus.WithResponse(&api.GetInterestParametersByProductVariantResponse{}),
		servus.WithDescription("GetInterestParametersByProductVariant fetches interest parameters by product variant"),
		withClientIdentities(servus.WithClientIdentities(servicename.AccountService, servicename.DepositsCore, servicename.DepositsExp, servicename.SentryT6, servicename.LoanExp, servicename.LokiService, servicename.LoanCore, servicename.DepositsExp)),
		withFeatureFlag(p),
	)

	app.POST(
		"/v1/product-variant-question",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateProductVariantQuestions(ctx, req.(*api.CreateProductVariantQuestionRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateProductVariantQuestionRequest{}),
		servus.WithResponse(&api.CreateProductVariantQuestionResponse{}),
		servus.WithDescription("CreateProductVariantQuestions : creates new questions specific to lending based on productVariantCode"),
	)
	app.GET(
		"/v1/product-variant-question",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListProductVariantQuestions(ctx, req.(*api.ListProductVariantQuestionsRequest))
			return res, err
		},
		servus.WithRequest(&api.ListProductVariantQuestionsRequest{}),
		servus.WithResponse(&api.ListProductVariantQuestionsResponse{}),
		servus.WithDescription("ListProductVariantQuestions : list the array of  questions and respective answers based on productVariantCode"),
		WithLocale(),
		withClientIdentities(servus.WithClientIdentities(servicename.SentryT6)),
		withTenant(p),
	)
	app.POST(
		"/v1/loan-interest",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateLoanInterest(ctx, req.(*api.CreateLoanInterestRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateLoanInterestRequest{}),
		servus.WithResponse(&api.CreateLoanInterestResponse{}),
		servus.WithDescription("CreateLoanInterest creates a new loan interest"),
	)
	app.GET(
		"/v1/loan-interest/:id",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetLoanInterest(ctx, req.(*api.GetLoanInterestByIDRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLoanInterestByIDRequest{}),
		servus.WithResponse(&api.GetLoanInterestByIDResponse{}),
		servus.WithDescription("GetLoanInterest fetches a loan interest by id"),
	)
	app.GET(
		"/v1/loan-interest",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetLoanInterestByCode(ctx, req.(*api.GetLoanInterestByCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLoanInterestByCodeRequest{}),
		servus.WithResponse(&api.GetLoanInterestByCodeResponse{}),
		servus.WithDescription("GetLoanInterestByCode fetches a loan interest by code"),
	)
	app.GET(
		"/v1/loan-past-due-parameters",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetLoanPastDueParametersByProductCode(ctx, req.(*api.GetLoanPastDueParametersByProductCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLoanPastDueParametersByProductCodeRequest{}),
		servus.WithResponse(&api.GetLoanPastDueParametersByProductCodeResponse{}),
		servus.WithDescription("GetLoanPastDueParametersByProductCode fetches a loan past due parameters by product code"),
		withClientIdentities(servus.WithClientIdentities(servicename.LoanCore)),
	)
	app.POST(
		"/v1/loan-instruction-versions",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateLoanInstructionVersion(ctx, req.(*api.CreateLoanInstructionVersionRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateLoanInstructionVersionRequest{}),
		servus.WithResponse(&api.CreateLoanInstructionVersionResponse{}),
		servus.WithDescription("CreateLoanInstructionVersion creates a new version of loan instructions"),
	)
	app.POST(
		"/v1/loan-instructions",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.CreateLoanInstruction(ctx, req.(*api.CreateLoanInstructionRequest))
			return res, err
		},
		servus.WithRequest(&api.CreateLoanInstructionRequest{}),
		servus.WithResponse(&api.CreateLoanInstructionResponse{}),
		servus.WithDescription("CreateLoanInstructionRequest creates a new loan instruction"),
	)
	app.GET(
		"/v1/loan-instructions",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.GetLoanInstructionsByCode(ctx, req.(*api.GetLoanInstructionsByCodeRequest))
			return res, err
		},
		servus.WithRequest(&api.GetLoanInstructionsByCodeRequest{}),
		servus.WithResponse(&api.GetLoanInstructionsByCodeResponse{}),
		servus.WithDescription("GetLoanInstructionsByCode fetches a loan instruction parameters by product variant code and instruction type"),
		withClientIdentities(servus.WithClientIdentities(servicename.LoanExp, servicename.LoanCore, servicename.SentryPartnerT6, servicename.CustomerPortal)),
	)
	app.GET(
		"/v1/loan-document-submission-options",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := p.ListLoanDocumentOptionsByProductVariant(ctx, req.(*api.ListLoanDocumentOptionsByProductVariantRequest))
			return res, err
		},
		servus.WithRequest(&api.ListLoanDocumentOptionsByProductVariantRequest{}),
		servus.WithResponse(&api.ListLoanDocumentOptionsByProductVariantResponse{}),
		servus.WithDescription("ListLoanDocumentOptionsByProductVariant returns list of loan document options for a given product variant code"),
		servus.WithClientIdentities(servicename.LoanApp),
	)
}

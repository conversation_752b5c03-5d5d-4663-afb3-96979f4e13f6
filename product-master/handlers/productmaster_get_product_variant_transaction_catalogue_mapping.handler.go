package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/txncatalogue"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductVariantTransactionCatalogueMapping fetches a Product Variant Transaction catalogue mapping by id
func (p *ProductMasterService) GetProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueMappingRequest) (*api.GetProductVariantTransactionCatalogueMappingResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductVariantTransactionCatalogueMapping"))
	slog.FromContext(ctx).Info(constants.GetProductVariantTransactionCatalogueMappingLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductVariantTransactionCatalogueMappingRequest(ctx, req); err != nil {
		return nil, err
	}

	return txncatalogue.GetProductVariantTransactionCatalogueMapping(ctx, req, p.Store)
}

func validateGetProductVariantTransactionCatalogueMappingRequest(ctx context.Context, req *api.GetProductVariantTransactionCatalogueMappingRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// UpdateProductVariantParameterValue updates the value of Product Variant Parameter
func (p *ProductMasterService) UpdateProductVariantParameterValue(ctx context.Context, req *api.UpdateProductVariantParameterValueRequest) (*api.UpdateProductVariantParameterValueResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("UpdateProductVariantParameterValue"))
	slog.FromContext(ctx).Info(constants.UpdateProductVariantParameterValueLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateUpdateProductVariantParameterValueRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.UpdateProductVariantParameterValue(ctx, req, p.Store)
}

func validateUpdateProductVariantParameterValueRequest(ctx context.Context, req *api.UpdateProductVariantParameterValueRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterValue, "parameter-value", apiErr.ErrMissingParameterValue); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.UpdatedBy, "updated-by", apiErr.ErrMissingUpdatedBy); err != nil {
		return err
	}
	return nil
}

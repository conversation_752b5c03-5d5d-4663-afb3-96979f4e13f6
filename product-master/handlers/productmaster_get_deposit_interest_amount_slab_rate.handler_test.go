package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetDepositInterestAmountSlabRate(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetDepositInterestAmountSlabRateRequest
		storeLoadResponse []*storage.DepositInterestAmountSlabRate
		expectedResponse  *api.GetDepositInterestAmountSlabRateResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get base interest time slab rate",
			requestParams: &api.GetDepositInterestAmountSlabRateRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			expectedResponse: &api.GetDepositInterestAmountSlabRateResponse{
				DepositInterestAmountSlabRate: &api.DepositInterestAmountSlabRate{
					Id:                               "test-id",
					DepositInterestVersionID:         "test-deposit-interest-version-id",
					FromAmount:                       "100",
					ToAmount:                         "200",
					BaseRateInterestSpreadPercentage: "0.1",
					AbsoluteInterestRatePercentage:   "2",
					CreatedBy:                        "unit-test",
					CreatedAt:                        now,
					UpdatedBy:                        "unit-test",
					UpdatedAt:                        now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetDepositInterestAmountSlabRateRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - deposit interest amount slab rate id not found in database",
			requestParams: &api.GetDepositInterestAmountSlabRateRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetDepositInterestAmountSlabRateRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockDepositInterestAmountSlabRate := &storage.MockIDepositInterestAmountSlabRateDAO{}
			mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockDepositInterestVersion := &storage.MockIDepositInterestVersionDAO{}
			mockDepositInterestVersion.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.DepositInterestVersion{PublicID: "test-deposit-interest-version-id"}, nil)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				DepositInterestAmountSlabRateDAO: mockDepositInterestAmountSlabRate,
				DepositInterestVersionDAO:        mockDepositInterestVersion,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			depositInterestAmountSlabRate, err := service.GetDepositInterestAmountSlabRate(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, depositInterestAmountSlabRate)
			}
		})
	}
}

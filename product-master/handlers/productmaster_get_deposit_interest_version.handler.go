package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetDepositInterestVersion fetches a deposit interest version by id
func (p *ProductMasterService) GetDepositInterestVersion(ctx context.Context, req *api.GetDepositInterestVersionRequest) (*api.GetDepositInterestVersionResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetDepositInterestVersion"))
	slog.FromContext(ctx).Info(constants.GetDepositInterestVersionLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetDepositInterestVersionRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetDepositInterestVersion(ctx, req, p.Store)
}

func validateGetDepositInterestVersionRequest(ctx context.Context, req *api.GetDepositInterestVersionRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

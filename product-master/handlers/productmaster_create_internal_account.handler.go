package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateInternalAccount creates a new Internal Account
func (p *ProductMasterService) CreateInternalAccount(ctx context.Context, req *api.CreateInternalAccountRequest) (*api.CreateInternalAccountResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateInternalAccount"))
	slog.FromContext(ctx).Info(constants.CreateInternalAccountLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateCreateInternalAccountRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.CreateInternalAccount(ctx, req, p.Store)
}

func validateCreateInternalAccountRequest(ctx context.Context, req *api.CreateInternalAccountRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.GeneralLedgerID, "general-ledger-id", apiErr.ErrMissingGeneralLedgerID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Name, "name", apiErr.ErrMissingName); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	if err := validations.ValidateCurrency(ctx, string(req.Currency)); err != nil {
		return err
	}
	return nil
}

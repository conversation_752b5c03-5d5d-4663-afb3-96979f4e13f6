package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
)

func Test_GetBaseInterestTimeSlabRate(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetBaseInterestTimeSlabRateRequest
		storeLoadResponse []*storage.BaseInterestTimeSlabRate
		expectedResponse  *api.GetBaseInterestTimeSlabRateResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get base interest time slab rate",
			requestParams: &api.GetBaseInterestTimeSlabRateRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.BaseInterestTimeSlabRate{{
				PublicID:              "test-id",
				BaseInterestVersionID: 1,
				TermUnit:              string(api.TermUnit_MONTH),
				TermValue:             2,
				BaseRatePercentage:    "5.00",
				CreatedBy:             "unit-test",
				CreatedAt:             now,
				UpdatedBy:             "unit-test",
				UpdatedAt:             now,
			}},
			expectedResponse: &api.GetBaseInterestTimeSlabRateResponse{
				BaseInterestTimeSlabRate: &api.BaseInterestTimeSlabRate{
					Id:                     "test-id",
					BaseInterestVersionID:  "test-base-version-id",
					TermUnit:               api.TermUnit_MONTH,
					TermValue:              2,
					BaseInterestPercentage: "5.00",
					CreatedBy:              "unit-test",
					CreatedAt:              now,
					UpdatedBy:              "unit-test",
					UpdatedAt:              now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetBaseInterestTimeSlabRateRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - base interest time slab rate id not found in database",
			requestParams: &api.GetBaseInterestTimeSlabRateRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetBaseInterestTimeSlabRateRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterestVersion := &storage.MockIBaseInterestVersionDAO{}
			mockBaseInterestVersion.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.BaseInterestVersion{PublicID: "test-base-version-id"}, nil)

			mockBaseInterestTimeSlabRate := &storage.MockIBaseInterestTimeSlabRateDAO{}
			mockBaseInterestTimeSlabRate.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				BaseInterestTimeSlabRateDAO: mockBaseInterestTimeSlabRate,
				BaseInterestVersionDAO:      mockBaseInterestVersion,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			baseInterestTimeSlabRate, err := service.GetBaseInterestTimeSlabRate(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, baseInterestTimeSlabRate)
			}
		})
	}
}

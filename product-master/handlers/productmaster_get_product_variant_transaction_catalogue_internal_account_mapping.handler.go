package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProductVariantTransactionCatalogueInternalAccountMapping fetches a product-variant-transaction-catalogue-internal-account-mapping by id
func (p *ProductMasterService) GetProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProductVariantTransactionCatalogueInternalAccountMapping"))
	slog.FromContext(ctx).Info(constants.GetProductVariantTransactionCatalogueInternalAccountMappingLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductVariantTransactionCatalogueInternalAccountMappingRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.GetProductVariantTransactionCatalogueInternalAccountMapping(ctx, req, p.Store)
}

func validateGetProductVariantTransactionCatalogueInternalAccountMappingRequest(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

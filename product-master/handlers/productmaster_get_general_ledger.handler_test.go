package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetGeneralLedger(t *testing.T) {
	now := time.Now().UTC()

	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := api.Currency(appConfig.Locale.Currency)
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetGeneralLedgerRequest
		storeLoadResponse []*storage.GeneralLedger
		expectedResponse  *api.GetGeneralLedgerResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get general ledger",
			requestParams: &api.GetGeneralLedgerRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.GeneralLedger{{
				PublicID: "test-id",
				Code:     "test-general-ledger-code",
				Name:     "test-general-ledger-name",
				Description: sql.NullString{
					String: "get test general ledger",
					Valid:  true,
				},
				Currency:  string(currency),
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.GetGeneralLedgerResponse{
				GeneralLedger: &api.GeneralLedger{
					Id:          "test-id",
					Code:        "test-general-ledger-code",
					Name:        "test-general-ledger-name",
					Description: "get test general ledger",
					Currency:    currency,
					Status:      api.EntityStatus_ACTIVE,
					CreatedBy:   "unit-test",
					CreatedAt:   now,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetGeneralLedgerRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - general ledger not found in database",
			requestParams: &api.GetGeneralLedgerRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetGeneralLedgerRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
			mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockStore := &storage.DBStore{
				GeneralLedgerDAO: mockGeneralLedger,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			generalLedger, err := service.GetGeneralLedger(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, generalLedger)
			}
		})
	}
}

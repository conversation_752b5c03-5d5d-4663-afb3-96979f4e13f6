package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestProductMasterService_CreateLoanInstructionVersion(t *testing.T) {
	createLoanVersionRequest := &api.CreateLoanInstructionVersionRequest{
		ProductVariantCode:    "DEFAULT_FLEXI_LOAN_TERM_LOAN",
		ProductVariantVersion: "1",
		Version:               "1",
		InstructionType:       "WRITE_OFF",
		Description:           "Default Flexi Loan - Term Loan WriteOff Instruction v1.0",
		EffectiveDate:         time.Time{},
		CreatedBy:             "MANUAL",
	}
	tests := []struct {
		name                            string
		request                         *api.CreateLoanInstructionVersionRequest
		expectedResponse                *api.CreateLoanInstructionVersionResponse
		expectedError                   error
		isErrorExpected                 bool
		findProductVariantResponse      []*storage.ProductVariant
		findProductVariantError         error
		saveLoanInstructionVersionError error
		findLoanInstructionResponse     []*storage.LoanInstructionVersion
		findLoanInstructionVersionError error
	}{
		{
			name:                        "happy-path create instruction successfully",
			request:                     createLoanVersionRequest,
			findProductVariantResponse:  []*storage.ProductVariant{{ID: 10}},
			findLoanInstructionResponse: responses.SampleLoanInstructionVersionDBResponse(),
			expectedResponse:            responses.CreateLoanInstructionVersionResponse(),
		},
		{
			name:            "error-path validation failures",
			request:         &api.CreateLoanInstructionVersionRequest{},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponseWithErrorDetail(http.StatusBadRequest,
				fmt.Sprint(apiErr.ErrInvalidInstructionVersionRequest.Code), apiErr.ErrInvalidInstructionVersionRequest.Message,
				[]servus.ErrorDetail{{
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingVersion.Code), 10),
					Message:   apiErr.ErrMissingVersion.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingProductVariantCode.Code), 10),
					Message:   apiErr.ErrMissingProductVariantCode.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingProductVariantVersion.Code), 10),
					Message:   apiErr.ErrMissingProductVariantVersion.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingInstructionType.Code), 10),
					Message:   apiErr.ErrMissingInstructionType.Message,
				}, {
					ErrorCode: strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10),
					Message:   apiErr.ErrMissingCreatedBy.Message,
				}}),
		},
		{
			name:                    "error-path find product variant failed",
			request:                 createLoanVersionRequest,
			isErrorExpected:         true,
			expectedError:           apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
			findProductVariantError: data.ErrTimedOut,
		},
		{
			name:                            "error-path save loan instruction failed",
			request:                         createLoanVersionRequest,
			findProductVariantResponse:      []*storage.ProductVariant{{ID: 10}},
			isErrorExpected:                 true,
			expectedError:                   apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
			saveLoanInstructionVersionError: data.ErrTimedOut,
		},
		{
			name:                            "error-path find loan instruction failed",
			findProductVariantResponse:      []*storage.ProductVariant{{ID: 10}},
			request:                         createLoanVersionRequest,
			isErrorExpected:                 true,
			expectedError:                   apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
			findLoanInstructionVersionError: data.ErrTimedOut,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockLoanInstructionVersion := &storage.MockILoanInstructionVersionDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(test.findProductVariantResponse, test.findProductVariantError)
			mockLoanInstructionVersion.On("Save", mock.Anything, mock.Anything).Return(test.saveLoanInstructionVersionError)
			mockLoanInstructionVersion.On("Find", mock.Anything, mock.Anything).Return(test.findLoanInstructionResponse, test.findLoanInstructionVersionError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO:         mockProductVariant,
				LoanInstructionVersionDAO: mockLoanInstructionVersion,
			}
			p := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			response, err := p.CreateLoanInstructionVersion(context.Background(), test.request)
			if test.isErrorExpected {
				assert.Error(t, err)
				assert.Equal(t, test.expectedError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expectedResponse, response)
			}
		})
	}
}

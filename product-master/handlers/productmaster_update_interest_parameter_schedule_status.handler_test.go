package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func Test_UpdateInterestParameterScheduleStatus(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                           string
		requestParams                      *api.UpdateInterestParameterScheduleStatusRequest
		responseParams                     *api.UpdateInterestParameterScheduleStatusResponse
		parameterScheduleDAOResponse       []*storage.ParameterChangeSchedule
		updateParameterScheduleDAOResponse error
		parameterScheduleDAOError          error
		isErrorExpected                    bool
		expectedError                      error
		responseError                      error
	}{
		{
			testDesc: "happy path - update notified interest params",
			requestParams: &api.UpdateInterestParameterScheduleStatusRequest{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				IsScheduled:            "Y",
				UpdatedBy:              "dev",
			},
			responseParams: &api.UpdateInterestParameterScheduleStatusResponse{
				IsScheduled: api.Status_Y,
			},
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			updateParameterScheduleDAOResponse: nil,
			parameterScheduleDAOError:          nil,
			isErrorExpected:                    false,
			expectedError:                      nil,
		},
		{
			testDesc: "error path - Invalid Input",
			requestParams: &api.UpdateInterestParameterScheduleStatusRequest{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				IsScheduled:            "Y",
			},
			responseParams: nil,
			parameterScheduleDAOResponse: []*storage.ParameterChangeSchedule{{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				NotificationDate:       now,
			}},
			updateParameterScheduleDAOResponse: nil,
			parameterScheduleDAOError:          nil,
			isErrorExpected:                    true,
			expectedError:                      nil,
			responseError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10),
				apiErr.ErrInvalidRequestParameter.Message),
		},
		{
			testDesc: "error path - DB error",
			requestParams: &api.UpdateInterestParameterScheduleStatusRequest{
				ParameterKey:           "test_key",
				ParameterVersion:       "1",
				SmartContractVersionID: "108",
				IsScheduled:            "Y",
				UpdatedBy:              "test",
			},
			responseParams:                     nil,
			parameterScheduleDAOResponse:       nil,
			updateParameterScheduleDAOResponse: nil,
			parameterScheduleDAOError:          errors.New("DB Load error"),
			isErrorExpected:                    true,
			expectedError:                      nil,
			responseError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10),
				apiErr.ErrInvalidRequestParameter.Message),
		},
	}
	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			parameterScheduleDAOResponse := &storage.MockIParameterChangeScheduleDAO{}
			parameterScheduleDAOResponse.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(s.parameterScheduleDAOResponse, s.parameterScheduleDAOError)
			parameterScheduleDAOResponse.On("Update", mock.Anything, mock.Anything).Return(s.updateParameterScheduleDAOResponse)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ParameterChangeScheduleDAO: parameterScheduleDAOResponse,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			updateResponse, err := service.UpdateInterestParameterScheduleStatus(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.responseParams, updateResponse)
			}
		})
	}
}

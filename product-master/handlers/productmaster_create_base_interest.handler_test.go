package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateBaseInterest(t *testing.T) {
	now := time.Now().UTC()
	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := api.Currency(appConfig.Locale.Currency)
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateBaseInterestRequest
		storeLoadResponse []*storage.BaseInterest
		expectedResponse  *api.CreateBaseInterestResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - base-interest created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "test-base-interest-name",
				Description: "create test base interest",
				Currency:    currency,
				CreatedBy:   "unit-test",
			},
			storeLoadResponse: []*storage.BaseInterest{{
				PublicID: "test-id",
				Code:     "test-base-interest-code",
				Name:     "test-base-interest-name",
				Description: sql.NullString{
					String: "create test base interest",
					Valid:  true,
				},
				Currency:  string(currency),
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.CreateBaseInterestResponse{
				BaseInterest: &api.BaseInterest{
					Id:          "test-id",
					Code:        "test-base-interest-code",
					Name:        "test-base-interest-name",
					Description: "create test base interest",
					Currency:    currency,
					CreatedBy:   "unit-test",
					CreatedAt:   now,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "test-base-interest-name",
				Currency:    currency,
				Description: "create test base interest",
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - code is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "",
				Name:        "test-base-interest-name",
				Currency:    currency,
				Description: "create test base interest",
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc:       "error path - name is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "",
				Description: "create test base interest",
				Currency:    currency,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingName.Code), 10), apiErr.ErrMissingName.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "test-base-interest-name",
				Currency:    currency,
				Description: "create test base interest",
				CreatedBy:   "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - currency is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "test-base-interest-name",
				Description: "create test base interest",
				Currency:    "INVALID_X",
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidCurrency.Code), 10), apiErr.ErrInvalidCurrency.Message),
		},
		{
			testDesc:       "error path - invalid currency",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "test-base-interest-name",
				Description: "create test base interest",
				Currency:    "INVALID_X",
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidCurrency.Code), 10), apiErr.ErrInvalidCurrency.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "test-base-interest-name",
				Description: "create test base interest",
				Currency:    currency,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestRequest{
				Code:        "test-base-interest-code",
				Name:        "test-base-interest-name",
				Description: "create test base interest",
				Currency:    currency,
				CreatedBy:   "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterest := &storage.MockIBaseInterestDAO{}
			mockBaseInterest.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockBaseInterest.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockStore := &storage.DBStore{
				BaseInterestDAO: mockBaseInterest,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			baseInterest, err := service.CreateBaseInterest(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, baseInterest)
			}
		})
	}
}

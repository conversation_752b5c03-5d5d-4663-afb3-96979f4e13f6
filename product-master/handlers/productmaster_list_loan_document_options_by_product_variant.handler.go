package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/loandocumentsubmissionoptions"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// ListLoanDocumentOptionsByProductVariant fetches loan document options for a specific product variant. Validates input and supports caching based on feature flags.
func (p *ProductMasterService) ListLoanDocumentOptionsByProductVariant(ctx context.Context, req *api.ListLoanDocumentOptionsByProductVariantRequest) (*api.ListLoanDocumentOptionsByProductVariantResponse, error) {
	slog.FromContext(ctx).Info(constants.ListLoanDocumentTypesByProductVariantLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if errs := validations.ValidateGetDocumentTypesRequest(ctx, req); errs != nil {
		slog.FromContext(ctx).Warn(constants.ListLoanDocumentTypesByProductVariantLogTag, fmt.Sprintf("Error while validating request for ListDocumentTypesByProductVariant: %v", errs), apiCommon.GetTraceID(ctx))
		return nil, errs
	}
	return loandocumentsubmissionoptions.GetLoanDocumentTypesByProductVariantCode(loandocumentsubmissionoptions.FetchLoanDocumentSubmissionOptionsParamsDTO{
		LoanDocumentOptionsRequest: req,
		LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
		Store:                      p.Store,
	}).FetchAndBuildLoanDocumentSubmissionOptionsByProductVariantCodeResponse(ctx)
}

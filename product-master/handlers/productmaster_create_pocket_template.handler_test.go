package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

func Test_CreatePocketTemplate(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc                         string
		idempotencyKey                   string
		requestParams                    *api.CreatePocketTemplateRequest
		expectedResponse                 *api.CreatePocketTemplateResponse
		loadPocketTemplateResponse       []*storage.PocketTemplate
		hermesListImageDetailsResponse   *hermes.GetDocumentsResponse
		isErrorExpected                  bool
		savePocketTemplateError          error
		loadPocketTemplateError          error
		savePocketTemplateImageSuggError error
		hermesListImageDetailsError      error
		expectedError                    error
	}{
		{
			testDesc:       "happy path - pocket template created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			loadPocketTemplateResponse:     responses.PocketTemplateMockDBResponse(now),
			hermesListImageDetailsResponse: responses.ListImageDetailsResponseFromHermes(),
			expectedResponse:               responses.CreatePocketTemplateResponse(),
			isErrorExpected:                false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - invalid pocket type",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType("INVALID_POCKET_TYPE"),
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidPocketType.Code), 10), apiErr.ErrInvalidPocketType.Message),
		},
		{
			testDesc:       "error path - missing pocket type",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType(""),
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidPocketType.Code), 10), apiErr.ErrInvalidPocketType.Message),
		},
		{
			testDesc:       "error path - name is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingName.Code), 10), apiErr.ErrMissingName.Message),
		},
		{
			testDesc:       "error path - image id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingImageID.Code), 10), apiErr.ErrMissingImageID.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - pocket template database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			isErrorExpected:         true,
			savePocketTemplateError: errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - pocket template database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			isErrorExpected:         true,
			loadPocketTemplateError: errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc:       "error path - pocket template image suggestion database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			loadPocketTemplateResponse:       responses.PocketTemplateMockDBResponse(now),
			isErrorExpected:                  true,
			savePocketTemplateImageSuggError: errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path -  list image details error from hermes",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateRequest{
				Type:      api.PocketType_SAVINGS,
				Name:      "test-template-name",
				ImageIDs:  []string{"test-image-id"},
				CreatedBy: "unit-test",
			},
			loadPocketTemplateResponse:  responses.PocketTemplateMockDBResponse(now),
			isErrorExpected:             true,
			hermesListImageDetailsError: errors.New("error from hermes"),
			expectedError:               errors.New("error from hermes"),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockPocketTemplateDAO := &storage.MockIPocketTemplateDAO{}
			mockPocketTemplateImageSuggestion := &storage.MockIPocketTemplateImageSuggestionDAO{}
			mockHermes := &hermesMock.Hermes{}

			mockPocketTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(s.savePocketTemplateError)
			mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything).Return(s.loadPocketTemplateResponse, s.loadPocketTemplateError)
			mockPocketTemplateImageSuggestion.On("SaveBatch", mock.Anything, mock.Anything).Return(s.savePocketTemplateImageSuggError)
			mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(s.hermesListImageDetailsResponse, s.hermesListImageDetailsError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				PocketTemplateDAO:                mockPocketTemplateDAO,
				PocketTemplateImageSuggestionDAO: mockPocketTemplateImageSuggestion,
			}

			service := &ProductMasterService{
				AppConfig:    appConfig,
				HermesClient: mockHermes,
				Store:        mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			pocketTemplate, err := service.CreatePocketTemplate(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, pocketTemplate)
			}
		})
	}
}

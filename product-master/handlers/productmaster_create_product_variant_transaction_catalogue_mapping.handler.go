package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/txncatalogue"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateProductVariantTransactionCatalogueMapping creates a new Product Variant Transaction Catalogue mapping
func (p *ProductMasterService) CreateProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueMappingRequest) (*api.CreateProductVariantTransactionCatalogueMappingResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateProductVariantTransactionCatalogueMapping"))
	slog.FromContext(ctx).Info(constants.CreateProductVariantTransactionCatalogueMappingLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateProductVariantTransactionCatalogueMappingRequest(ctx, req); err != nil {
		return nil, err
	}

	return txncatalogue.CreateProductVariantTransactionCatalogueMapping(ctx, req, p.Store)
}

func validateCreateProductVariantTransactionCatalogueMappingRequest(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueMappingRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantID, "product-variant-id", apiErr.ErrMissingProductVariantID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.TransactionCatalogueID, "transaction-catalogue-id", apiErr.ErrMissingTransactionCatalogueID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

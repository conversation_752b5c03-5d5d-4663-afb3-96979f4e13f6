package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetProductVariantTransactionCatalogueMapping(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetProductVariantTransactionCatalogueMappingRequest
		storeLoadResponse []*storage.ProductVariantTransactionCatalogueMapping
		expectedResponse  *api.GetProductVariantTransactionCatalogueMappingResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get product variant transaction catalogue mapping",
			requestParams: &api.GetProductVariantTransactionCatalogueMappingRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.ProductVariantTransactionCatalogueMapping{{
				PublicID:               "test-id",
				ProductVariantID:       1,
				TransactionCatalogueID: 2,
				Status:                 "ACTIVE",
				CreatedBy:              "unit-test",
				CreatedAt:              now,
				UpdatedBy:              "unit-test",
				UpdatedAt:              now,
			}},
			expectedResponse: &api.GetProductVariantTransactionCatalogueMappingResponse{
				ProductVariantTransactionCatalogueMapping: &api.ProductVariantTransactionCatalogueMapping{
					Id:                     "test-id",
					ProductVariantID:       "test-product-variant-id",
					TransactionCatalogueID: "test-transaction-catalogue-id",
					Status:                 api.EntityStatus_ACTIVE,
					CreatedBy:              "unit-test",
					CreatedAt:              now,
					UpdatedBy:              "unit-test",
					UpdatedAt:              now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetProductVariantTransactionCatalogueMappingRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - product variant transaction catalogue mapping not found in database",
			requestParams: &api.GetProductVariantTransactionCatalogueMappingRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetProductVariantTransactionCatalogueMappingRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockTransactionCatalogue := &storage.MockITransactionCatalogueDAO{}
			mockTransactionCatalogue.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.TransactionCatalogue{PublicID: "test-transaction-catalogue-id"}, nil)

			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariant.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariant{PublicID: "test-product-variant-id"}, nil)

			mockProductVariantTransactionCatalogueMapping := &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
			mockProductVariantTransactionCatalogueMapping.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantTransactionCatalogueMappingDAO: mockProductVariantTransactionCatalogueMapping,
				ProductVariantDAO:       mockProductVariant,
				TransactionCatalogueDAO: mockTransactionCatalogue,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			productVariantTransactionCatalogueMapping, err := service.GetProductVariantTransactionCatalogueMapping(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, productVariantTransactionCatalogueMapping)
			}
		})
	}
}

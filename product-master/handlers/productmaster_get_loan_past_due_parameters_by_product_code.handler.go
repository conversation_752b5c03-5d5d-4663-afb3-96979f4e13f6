package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/loanpastdueconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetLoanPastDueParametersByProductCode fetches a loan past due parameters by product code
func (p *ProductMasterService) GetLoanPastDueParametersByProductCode(ctx context.Context, req *api.GetLoanPastDueParametersByProductCodeRequest) (*api.GetLoanPastDueParametersByProductCodeResponse, error) {
	slog.FromContext(ctx).Info(constants.GetLoanPastDueParametersByProductCodeLogTag, fmt.Sprintf("Received Request: %+v", utils.ToJSON(req)), apiCommon.GetTraceID(ctx))
	if err := validateGetLoanPastDueParametersByProductCodeRequest(ctx, req); err != nil {
		return nil, err
	}

	response, err := loanpastdueconfig.GetLoanPastDueParametersByProductCode(ctx, req, p.Store)
	if err != nil {
		return nil, err
	}

	slog.FromContext(ctx).Info(constants.GetLoanPastDueParametersByProductCodeLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return response, nil
}

func validateGetLoanPastDueParametersByProductCodeRequest(ctx context.Context, req *api.GetLoanPastDueParametersByProductCodeRequest) error {
	if err := validations.ValidateProductCode(ctx, string(req.ProductCode)); err != nil {
		return err
	}
	return nil
}

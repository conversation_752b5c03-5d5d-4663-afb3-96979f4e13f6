package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/txncatalogue"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetTransactionCatalogue fetches a transaction catalogue by id
func (p *ProductMasterService) GetTransactionCatalogue(ctx context.Context, req *api.GetTransactionCatalogueRequest) (*api.GetTransactionCatalogueResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetTransactionCatalogue"))
	slog.FromContext(ctx).Info(constants.GetTransactionCatalogueLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetTransactionCatalogueRequest(ctx, req); err != nil {
		return nil, err
	}

	return txncatalogue.GetTransactionCatalogue(ctx, req, p.Store)
}

func validateGetTransactionCatalogueRequest(ctx context.Context, req *api.GetTransactionCatalogueRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

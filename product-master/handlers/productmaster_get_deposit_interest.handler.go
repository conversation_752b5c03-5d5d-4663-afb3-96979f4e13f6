package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetDepositInterest fetches a deposit interest by id
func (p *ProductMasterService) GetDepositInterest(ctx context.Context, req *api.GetDepositInterestRequest) (*api.GetDepositInterestResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetDepositInterest"))
	slog.FromContext(ctx).Info(constants.GetDepositInterestLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetDepositInterestRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetDepositInterest(ctx, req, p.Store)
}

func validateGetDepositInterestRequest(ctx context.Context, req *api.GetDepositInterestRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

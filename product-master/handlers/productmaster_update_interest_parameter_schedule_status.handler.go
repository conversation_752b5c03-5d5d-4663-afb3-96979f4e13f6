package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// UpdateInterestParameterScheduleStatus keep the status whether parameter is scheduled in TM or not
func (p *ProductMasterService) UpdateInterestParameterScheduleStatus(ctx context.Context, req *api.UpdateInterestParameterScheduleStatusRequest) (*api.UpdateInterestParameterScheduleStatusResponse, error) {
	ctx = slog.AddTagsToContext(ctx,
		common.Service(constants.ServiceName),
		common.Endpoint("UpdateInterestParameterNotificationStatus"),
		tags.T(constants.ParameterKey, req.ParameterKey),
	)

	err := validateUpdateScheduleParametersRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	return productconfig.UpdateParameterScheduleStatus(ctx, p.Store, req, constants.ParameterStatus[req.IsScheduled])
}

// validateUpdateScheduleParametersRequest ...
func validateUpdateScheduleParametersRequest(ctx context.Context, req *api.UpdateInterestParameterScheduleStatusRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterKey, "parameterKey", apiErr.ErrMissingParameterKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterVersion, "parameterVersion", apiErr.ErrMissingParameterKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, string(req.IsScheduled), "isScheduled", apiErr.ErrMissingIsScheduledKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.SmartContractVersionID, "SmartContractVersionID", apiErr.ErrSmartContractVersionID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.UpdatedBy, "UpdatedBy", apiErr.ErrMissingUpdatedBy); err != nil {
		return err
	}
	if err := validations.ValidateIsScheduledValue(ctx, req.IsScheduled); err != nil {
		return err
	}
	return nil
}

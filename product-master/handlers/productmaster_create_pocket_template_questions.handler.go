package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/pocket"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreatePocketTemplateQuestions creates new pocket template questions
func (p *ProductMasterService) CreatePocketTemplateQuestions(ctx context.Context, req *api.CreatePocketTemplateQuestionsRequest) (*api.CreatePocketTemplateQuestionsResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreatePocketTemplateQuestion"))
	slog.FromContext(ctx).Info(constants.CreatePocketTemplateQuestionLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreatePocketTemplateQuestionRequest(ctx, req); err != nil {
		return nil, err
	}
	return pocket.CreatePocketTemplateQuestion(ctx, req, p.Store)
}
func validateCreatePocketTemplateQuestionRequest(ctx context.Context, req *api.CreatePocketTemplateQuestionsRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.PocketTemplateID, "pocket-template-if", apiErr.ErrMissingPocketTemplateID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, string(req.Locale), "locale", apiErr.ErrMissingLocale); err != nil {
		return err
	}
	if len(req.QuestionAnswerPairs) == 0 {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "QuestionAnswerPairs field is missing")
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingQuestionAnswerPairs.Code), 10), apiErr.ErrMissingQuestionAnswerPairs.Message)
	}
	for _, questionAnswerPair := range req.QuestionAnswerPairs {
		if questionAnswerPair.QuestionText == "" || len(questionAnswerPair.AnswerSuggestions) == 0 {
			slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "QuestionText or AnswerSuggestions field is missing")
			return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message)
		}
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

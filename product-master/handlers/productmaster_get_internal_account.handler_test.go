package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetInternalAccount(t *testing.T) {
	now := time.Now().UTC()
	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := appConfig.Locale.Currency
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetInternalAccountRequest
		storeLoadResponse []*storage.InternalAccount
		expectedResponse  *api.GetInternalAccountResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get general ledger",
			requestParams: &api.GetInternalAccountRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.InternalAccount{{
				PublicID:        "test-id",
				GeneralLedgerID: 1,
				Code:            "test-internal-account-code",
				Name:            "test-internal-account-name",
				Description: sql.NullString{
					String: "create test internal account",
					Valid:  true,
				},
				Currency:  currency,
				Status:    string(api.EntityStatus_ACTIVE),
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.GetInternalAccountResponse{
				InternalAccount: &api.InternalAccount{
					Id:              "test-id",
					GeneralLedgerID: "test-general-ledger-id",
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description:     "create test internal account",
					Currency:        api.Currency(currency),
					Status:          api.EntityStatus_ACTIVE,
					CreatedBy:       "unit-test",
					CreatedAt:       now,
					UpdatedBy:       "unit-test",
					UpdatedAt:       now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetInternalAccountRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - general ledger not found in database",
			requestParams: &api.GetInternalAccountRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetInternalAccountRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
			mockGeneralLedger.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.GeneralLedger{PublicID: "test-general-ledger-id"}, nil)

			mockInternalAccount := &storage.MockIInternalAccountDAO{}
			mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				InternalAccountDAO: mockInternalAccount,
				GeneralLedgerDAO:   mockGeneralLedger,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			internalAccount, err := service.GetInternalAccount(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, internalAccount)
			}
		})
	}
}

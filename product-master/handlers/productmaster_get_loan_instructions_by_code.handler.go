package handlers

import (
	context "context"
	"fmt"
	"net/http"
	"strings"

	"github.com/samber/lo"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/loaninstruction"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetLoanInstructionsByCode fetches a loan instruction parameters by product variant code and instruction type
func (p *ProductMasterService) GetLoanInstructionsByCode(ctx context.Context, req *api.GetLoanInstructionsByCodeRequest) (*api.GetLoanInstructionsByCodeResponse, error) {
	slog.FromContext(ctx).Info(constants.GetLoanInstructionByCodeLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if errs := validateGetLoanInstructionByCodeRequest(req); len(errs) != 0 {
		return nil, apiErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code),
			apiErr.ErrInvalidInstructionRequest.Message, errs)
	}

	return loaninstruction.GetLoanInstructionByCode(ctx, req, p.Store)
}

func validateGetLoanInstructionByCodeRequest(req *api.GetLoanInstructionsByCodeRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	req.ProductVariantCode = strings.TrimSpace(req.ProductVariantCode)
	req.InstructionType = strings.TrimSpace(req.InstructionType)

	// validate product variant code
	if req.ProductVariantCode == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingProductVariantCode.Code), Message: apiErr.ErrMissingProductVariantCode.Message})
	} else if !lo.Contains(constants.ValidProductVariantCodes, req.ProductVariantCode) {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrInvalidProductVariantCode.Code), Message: apiErr.ErrInvalidProductVariantCode.Message})
	}

	// validate instruction type
	if req.InstructionType == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingInstructionType.Code), Message: apiErr.ErrMissingInstructionType.Message})
	} else if !lo.Contains(constants.ValidInstructionTypes, req.InstructionType) {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrInvalidInstructionType.Code), Message: apiErr.ErrInvalidInstructionType.Message})
	}

	// validate instruction type to variant code mapping
	invalidInstructionTypes := constants.ProductVariantInvalidInstructionsMap
	if invalidTypes, exists := invalidInstructionTypes[req.ProductVariantCode]; exists {
		for _, invalidType := range invalidTypes {
			if req.InstructionType == invalidType {
				errs = append(errs, servus.ErrorDetail{
					ErrorCode: fmt.Sprint(apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Code),
					Message:   apiErr.ErrInvalidProductVariantCodeInstructionTypeMap.Message,
				})
				break
			}
		}
	}
	return errs
}

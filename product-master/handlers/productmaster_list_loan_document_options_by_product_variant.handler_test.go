package handlers

import (
	"context"
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func Test_ListLoanDocumentOptionsByProductVariantNoCache(t *testing.T) {
	scenerios := []struct {
		description                                      string
		expectedErr                                      error
		request                                          *api.ListLoanDocumentOptionsByProductVariantRequest
		expectedResponse                                 *api.ListLoanDocumentOptionsByProductVariantResponse
		productVariantDBResponse                         []*storage.ProductVariant
		loanDocumentSubmissionOptionDBResponse           []*storage.LoanDocumentSubmissionOption
		loanDocumentSubmissionOptionVersionDBResponse    []*storage.LoanDocumentSubmissionOptionVersion
		loanDocumentSubmissionOptionParametersDBResponse []*storage.LoanDocumentSubmissionOptionParameters
		productVariantDBError                            error
		loanDocumentSubmissionOptionDBError              error
		loanDocumentSubmissionOptionVersionDBError       error
		loanDocumentSubmissionOptionParametersDBError    error
		validationErr                                    error
	}{
		{
			description:                                      "happy-path no redis",
			productVariantDBResponse:                         responses.SampleProductVariantDAOResponse(),
			loanDocumentSubmissionOptionDBResponse:           responses.SampleDocumentSubmissionOptionDAOResponse(),
			loanDocumentSubmissionOptionVersionDBResponse:    responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
			loanDocumentSubmissionOptionParametersDBResponse: responses.SampleDocumentSubmissionOptionParametersDAOResponse(),
			expectedResponse:                                 responses.ListLoanDocumentTypesSampleResponse(),
			request: &api.ListLoanDocumentOptionsByProductVariantRequest{
				ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT),
			},
		},
		{
			description: "validation failure",
			request: &api.ListLoanDocumentOptionsByProductVariantRequest{
				ProductVariantCode: "DEFAULT_FLEXI",
			},
			expectedErr: apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCode.Code), 10), apiErr.ErrInvalidProductVariantCode.Message),
		},
		{
			description: "error while fetching product variant",
			request: &api.ListLoanDocumentOptionsByProductVariantRequest{
				ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			productVariantDBError: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
		{
			description:              "error while fetching loan document options",
			productVariantDBResponse: responses.SampleProductVariantDAOResponse(),
			request: &api.ListLoanDocumentOptionsByProductVariantRequest{
				ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			loanDocumentSubmissionOptionDBError: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
		{
			description:              "error while fetching loan document options version",
			productVariantDBResponse: responses.SampleProductVariantDAOResponse(),
			request: &api.ListLoanDocumentOptionsByProductVariantRequest{
				ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			loanDocumentSubmissionOptionDBResponse: responses.SampleDocumentSubmissionOptionDAOResponse(),
			loanDocumentSubmissionOptionVersionDBError: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
		{
			description:              "error while fetching loan document options options",
			productVariantDBResponse: responses.SampleProductVariantDAOResponse(),
			request: &api.ListLoanDocumentOptionsByProductVariantRequest{
				ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT),
			},
			loanDocumentSubmissionOptionDBResponse:        responses.SampleDocumentSubmissionOptionDAOResponse(),
			loanDocumentSubmissionOptionVersionDBResponse: responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
			loanDocumentSubmissionOptionParametersDBError: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
	}
	for _, scenario := range scenerios {
		t.Run(scenario.description, func(t *testing.T) {
			mockDBStore := &storage.DBStore{}
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(scenario.productVariantDBResponse, scenario.productVariantDBError)
			mockDBStore.ProductVariantDAO = mockProductVariant
			mockLoanDocumentSubmissionOption := &storage.MockILoanDocumentSubmissionOptionDAO{}
			mockLoanDocumentSubmissionOption.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(scenario.loanDocumentSubmissionOptionDBResponse, scenario.loanDocumentSubmissionOptionDBError)
			mockDBStore.LoanDocumentSubmissionOptionDAO = mockLoanDocumentSubmissionOption
			mockLoanDocumentSubmissionOptionVersion := &storage.MockILoanDocumentSubmissionOptionVersionDAO{}
			mockLoanDocumentSubmissionOptionVersion.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(scenario.loanDocumentSubmissionOptionVersionDBResponse, scenario.loanDocumentSubmissionOptionVersionDBError)
			mockDBStore.LoanDocumentSubmissionOptionVersionDAO = mockLoanDocumentSubmissionOptionVersion
			mockLoanDocumentSubmissionOptionParameters := &storage.MockILoanDocumentSubmissionOptionParametersDAO{}
			mockLoanDocumentSubmissionOptionParameters.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(scenario.loanDocumentSubmissionOptionParametersDBResponse, scenario.loanDocumentSubmissionOptionParametersDBError)
			mockDBStore.LoanDocumentSubmissionOptionParametersDAO = mockLoanDocumentSubmissionOptionParameters
			service := &ProductMasterService{
				Store:     mockDBStore,
				AppConfig: &config.AppConfig{},
			}
			actualResponse, actualErr := service.ListLoanDocumentOptionsByProductVariant(context.Background(), scenario.request)
			assert.Equal(t, scenario.expectedResponse, actualResponse)
			assert.Equal(t, scenario.expectedErr, actualErr)
		})
	}
}

package handlers

import (
	"context"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_ListProductVariantQuestions(t *testing.T) {
	now := time.Now()
	scenarios := []struct {
		desc                       string
		request                    *api.ListProductVariantQuestionsRequest
		expectedResponse           *api.ListProductVariantQuestionsResponse
		expectedError              error
		isErrorExpected            bool
		loadProductVariantResponse []*storage.ProductVariant
		loadProductVariantError    error
		loadQuestionsResponse      []*storage.ProductVariantQuestion
		loadQuestionsError         error
		loadAnswerResponse         []*storage.ProductVariantAnswerSuggestion
		loadAnswerError            error
	}{{
		desc:                       "happy-path",
		request:                    &api.ListProductVariantQuestionsRequest{ProductVariantCode: "product1", ProductVariantVersion: "1"},
		isErrorExpected:            false,
		loadProductVariantResponse: []*storage.ProductVariant{{ID: 10}},
		loadQuestionsResponse:      responses.SampleProductVariantQuestions(now),
		loadAnswerResponse:         responses.SampleProductVariantAnswerSuggestions(now),
		expectedResponse:           responses.ListProductVariantQuestionsResponse(now),
	}, {
		desc:            "Error path - productVariantCode missing",
		request:         &api.ListProductVariantQuestionsRequest{ProductVariantCode: "", ProductVariantVersion: "1"},
		isErrorExpected: true,
		expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
			strconv.FormatInt(int64(apiErr.ErrMissingProductVariantCode.Code), 10), apiErr.ErrMissingProductVariantCode.Message),
	}}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariantQuestion := &storage.MockIProductVariantQuestionDAO{}
			mockProductVariantAnswerSuggestion := &storage.MockIProductVariantAnswerSuggestionDAO{}

			mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadProductVariantResponse, scenario.loadProductVariantError)
			mockProductVariantQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadQuestionsResponse, scenario.loadQuestionsError)
			mockProductVariantAnswerSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadAnswerResponse, scenario.loadAnswerError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO:                 mockProductVariant,
				ProductVariantQuestionDAO:         mockProductVariantQuestion,
				ProductVariantAnswerSuggestionDAO: mockProductVariantAnswerSuggestion,
			}
			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			response, err := service.ListProductVariantQuestions(context.Background(), scenario.request)
			if scenario.isErrorExpected {
				assert.Equal(t, scenario.expectedError, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, scenario.expectedResponse, response)
		})
	}
}

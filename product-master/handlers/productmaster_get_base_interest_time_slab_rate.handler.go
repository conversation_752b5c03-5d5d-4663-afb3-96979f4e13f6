package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetBaseInterestTimeSlabRate fetches a base interest time slab rate by id
func (p *ProductMasterService) GetBaseInterestTimeSlabRate(ctx context.Context, req *api.GetBaseInterestTimeSlabRateRequest) (*api.GetBaseInterestTimeSlabRateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetBaseInterestTimeSlabRate"))
	slog.FromContext(ctx).Info(constants.GetBaseInterestTimeSlabRateLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetBaseInterestTimeSlabRateRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetBaseInterestTimeSlabRate(ctx, req, p.Store)
}

func validateGetBaseInterestTimeSlabRateRequest(ctx context.Context, req *api.GetBaseInterestTimeSlabRateRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

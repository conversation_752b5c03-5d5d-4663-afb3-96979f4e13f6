package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dbmy/common/tenants"
)

func Test_withTenant(t *testing.T) {
	tests := []struct {
		name       string
		tenant     *tenants.Tenant
		ctx        context.Context
		request    interface{}
		wantTenant string
		wantErr    bool
	}{
		{
			name: "should return tenant",
			tenant: &tenants.Tenant{
				Name: tenants.TenantMY,
			},
			ctx:        context.Background(),
			request:    "test-request",
			wantTenant: tenants.TenantMY,
			wantErr:    false,
		},
		{
			name:       "should return empty string when tenant is nil",
			tenant:     nil,
			ctx:        context.Background(),
			request:    "test-request",
			wantTenant: "",
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &ProductMasterService{
				Tenant: tt.tenant,
			}
			middleware := withTenant(a)
			handler := middleware(func(ctx context.Context, request interface{}) (interface{}, error) {
				return ctx, nil
			})
			tenantCtx, err := handler(tt.ctx, tt.request)
			if !tt.wantErr {
				assert.NoError(t, err)
			}
			tenant := tenants.FromContext(tenantCtx.(context.Context))
			assert.Equal(t, tt.wantTenant, tenant)
		})
	}
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetProductTemplateParameter(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetProductTemplateParameterRequest
		storeLoadResponse []*storage.ProductTemplateParameter
		expectedResponse  *api.GetProductTemplateParameterResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get product template parameter",
			requestParams: &api.GetProductTemplateParameterRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-id",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel: sql.NullString{
					String: "",
					Valid:  false,
				},
				Description: sql.NullString{
					String: "create test product template parameter",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.GetProductTemplateParameterResponse{
				ProductTemplateParameter: &api.ProductTemplateParameter{
					Id:                "test-id",
					ProductTemplateID: "test-product-template-id-1",
					Namespace:         "test-namespace",
					ParameterKey:      "test-parameter-key",
					ParameterValue:    "test-parameter-value",
					DataType:          "STRING",
					OverrideLevel:     "NO_OVERRIDE",
					ExceptionLevel:    "",
					Description:       "create test product template parameter",
					CreatedBy:         "unit-test",
					CreatedAt:         now,
					UpdatedBy:         "unit-test",
					UpdatedAt:         now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetProductTemplateParameterRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - product template parameter not found in database",
			requestParams: &api.GetProductTemplateParameterRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetProductTemplateParameterRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductTemplate := &storage.MockIProductTemplateDAO{}
			mockProductTemplate.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductTemplate{PublicID: "test-product-template-id-1"}, nil)

			mockProductTemplateParameter := &storage.MockIProductTemplateParameterDAO{}
			mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductTemplateParameterDAO: mockProductTemplateParameter,
				ProductTemplateDAO:          mockProductTemplate,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			productTemplateParameter, err := service.GetProductTemplateParameter(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, productTemplateParameter)
			}
		})
	}
}

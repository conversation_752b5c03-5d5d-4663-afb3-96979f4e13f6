package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateBaseInterestTimeSlabRate(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateBaseInterestTimeSlabRateRequest
		storeLoadResponse []*storage.BaseInterestTimeSlabRate
		expectedResponse  *api.CreateBaseInterestTimeSlabRateResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - base-interest-time-slab-rate created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			storeLoadResponse: []*storage.BaseInterestTimeSlabRate{{
				PublicID:              "test-id",
				BaseInterestVersionID: 1,
				TermUnit:              string(api.TermUnit_MONTH),
				TermValue:             2,
				BaseRatePercentage:    "5.00",
				CreatedBy:             "unit-test",
				CreatedAt:             now,
				UpdatedBy:             "unit-test",
				UpdatedAt:             now,
			}},
			expectedResponse: &api.CreateBaseInterestTimeSlabRateResponse{
				BaseInterestTimeSlabRate: &api.BaseInterestTimeSlabRate{
					Id:                     "test-id",
					BaseInterestVersionID:  "test-base-version-id",
					TermUnit:               api.TermUnit_MONTH,
					TermValue:              2,
					BaseInterestPercentage: "5.00",
					CreatedBy:              "unit-test",
					CreatedAt:              now,
					UpdatedBy:              "unit-test",
					UpdatedAt:              now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - base interest version id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingBaseInterestVersionID.Code), 10), apiErr.ErrMissingBaseInterestVersionID.Message),
		},
		{
			testDesc:       "error path - invalid term unit",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               "INVALID_TERM_UNIT",
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidTermUnit.Code), 10), apiErr.ErrInvalidTermUnit.Message),
		},
		{
			testDesc:       "error path - term unit is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               "",
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidTermUnit.Code), 10), apiErr.ErrInvalidTermUnit.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - base interest percentage is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestPercentage.Code), 10), apiErr.ErrInvalidBaseInterestPercentage.Message),
		},
		{
			testDesc:       "error path - base interest percentage is non numeric",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "XXX",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestPercentage.Code), 10), apiErr.ErrInvalidBaseInterestPercentage.Message),
		},
		{
			testDesc:       "error path - term value is zero",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              0,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidTermValue.Code), 10), apiErr.ErrInvalidTermValue.Message),
		},
		{
			testDesc:       "error path - term value is negative",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              -10,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidTermValue.Code), 10), apiErr.ErrInvalidTermValue.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateBaseInterestTimeSlabRateRequest{
				BaseInterestVersionID:  "test-base-version-id",
				TermUnit:               api.TermUnit_MONTH,
				TermValue:              2,
				BaseInterestPercentage: "5.00",
				CreatedBy:              "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseVersionDao := &storage.MockIBaseInterestVersionDAO{}
			mockBaseVersionDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestVersion{{}}, nil)
			mockBaseInterestTimeSlabRate := &storage.MockIBaseInterestTimeSlabRateDAO{}
			mockBaseInterestTimeSlabRate.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockBaseInterestTimeSlabRate.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				BaseInterestTimeSlabRateDAO: mockBaseInterestTimeSlabRate,
				BaseInterestVersionDAO:      mockBaseVersionDao,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			baseInterestTimeSlabRate, err := service.CreateBaseInterestTimeSlabRate(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, baseInterestTimeSlabRate)
			}
		})
	}
}

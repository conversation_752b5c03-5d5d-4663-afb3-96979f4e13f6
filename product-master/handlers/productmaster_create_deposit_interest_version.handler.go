package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateDepositInterestVersion creates a new deposit interest version
func (p *ProductMasterService) CreateDepositInterestVersion(ctx context.Context, req *api.CreateDepositInterestVersionRequest) (*api.CreateDepositInterestVersionResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateDepositInterestVersion"))
	slog.FromContext(ctx).Info(constants.CreateDepositInterestVersionLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateCreateDepositInterestVersionRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.CreateDepositInterestVersion(ctx, req, p.Store)
}

func validateCreateDepositInterestVersionRequest(ctx context.Context, req *api.CreateDepositInterestVersionRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.DepositInterestID, "deposit-interest-id", apiErr.ErrMissingDepositInterestID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Version, "version", apiErr.ErrMissingVersion); err != nil {
		return err
	}

	if err := validations.ValidateIntegerVersion(ctx, req.Version, apiErr.ErrInvalidDepositInterestVersion); err != nil {
		return err
	}

	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	if err := validations.ValidateDateFieldWithCurrentDate(ctx, req.EffectiveDate, "effective-date", apiErr.ErrEffectiveDateLessThanCurrent); err != nil {
		return err
	}
	return nil
}

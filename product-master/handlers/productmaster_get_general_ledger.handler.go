package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetGeneralLedger fetches a general-ledger by id
func (p *ProductMasterService) GetGeneralLedger(ctx context.Context, req *api.GetGeneralLedgerRequest) (*api.GetGeneralLedgerResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetGeneralLedger"))
	slog.FromContext(ctx).Info(constants.GetGeneralLedgerLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetGeneralLedgerRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.GetGeneralLedger(ctx, req, p.Store)
}

func validateGetGeneralLedgerRequest(ctx context.Context, req *api.GetGeneralLedgerRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateBaseInterestTimeSlabRate creates a new base interest time slab rate
func (p *ProductMasterService) CreateBaseInterestTimeSlabRate(ctx context.Context, req *api.CreateBaseInterestTimeSlabRateRequest) (*api.CreateBaseInterestTimeSlabRateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateBaseInterestTimeSlabRate"))
	slog.FromContext(ctx).Info(constants.CreateBaseInterestTimeSlabRateLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateBaseInterestTimeSlabRateRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.CreateBaseInterestTimeSlabRate(ctx, req, p.Store)
}

func validateCreateBaseInterestTimeSlabRateRequest(ctx context.Context, req *api.CreateBaseInterestTimeSlabRateRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.BaseInterestVersionID, "base-interest-version-id", apiErr.ErrMissingBaseInterestVersionID); err != nil {
		return err
	}
	if err := validations.ValidateTermUnit(ctx, string(req.TermUnit)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNonNegative(ctx, int(req.TermValue), "term-value", apiErr.ErrInvalidTermValue); err != nil {
		return err
	}

	if err := validations.ValidatePercentage(ctx, req.BaseInterestPercentage, "base-interest-percentage", apiErr.ErrInvalidBaseInterestPercentage); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

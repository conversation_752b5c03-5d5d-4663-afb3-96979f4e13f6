package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.myteksi.net/dbmy/common/tenants"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

func Test_GetPocketTemplate(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		tenant                              string
		testDesc                            string
		requestParams                       *api.GetPocketTemplateRequest
		expectedResponse                    *api.GetPocketTemplateResponse
		loadPocketTemplateResponse          []*storage.PocketTemplate
		loadPocketTemplateImageSuggResponse []*storage.PocketTemplateImageSuggestion
		hermesListImageDetailsResponse      *hermes.GetDocumentsResponse
		isErrorExpected                     bool
		loadPocketTemplateError             error
		loadPocketTemplateImageSuggError    error
		hermesListImageDetailsError         error
		expectedError                       error
	}{
		{
			testDesc: "happy path - list pocket templates",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			loadPocketTemplateResponse:          responses.PocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			hermesListImageDetailsResponse:      responses.ListImageDetailsResponseFromHermes(),
			expectedResponse:                    responses.GetPocketTemplateResponse(),
			isErrorExpected:                     false,
		},
		{
			tenant:   tenants.TenantMY,
			testDesc: "DBMY - happy path - list pocket templates",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			loadPocketTemplateResponse:          responses.PocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			hermesListImageDetailsResponse:      responses.ListImageDetailsResponseFromHermes(),
			expectedResponse:                    responses.GetPocketTemplateResponse(),
			isErrorExpected:                     false,
		},
		{
			tenant:   tenants.TenantMY,
			testDesc: "DBMY - happy path - list INACTIVE pocket templates",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			loadPocketTemplateResponse:          responses.InactivePocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: []*storage.PocketTemplateImageSuggestion{},
			hermesListImageDetailsResponse:      &hermes.GetDocumentsResponse{},
			expectedResponse:                    responses.GetInactivePocketTemplateResponse(),
			isErrorExpected:                     false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError:   apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingPocketTemplateID.Code), 10), apiErr.ErrMissingPocketTemplateID.Message),
		},
		{
			testDesc: "error path - pocket template not found in database",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			isErrorExpected:         true,
			loadPocketTemplateError: data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - pocket template database load error",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			isErrorExpected:         true,
			loadPocketTemplateError: errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error path - pocket template image suggestion not found in database",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			loadPocketTemplateResponse:       responses.PocketTemplateMockDBResponse(now),
			isErrorExpected:                  true,
			loadPocketTemplateImageSuggError: data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - pocket template image suggestion database load error",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			loadPocketTemplateResponse:       responses.PocketTemplateMockDBResponse(now),
			isErrorExpected:                  true,
			loadPocketTemplateImageSuggError: errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc: "error path - list image details error from hermes",
			requestParams: &api.GetPocketTemplateRequest{
				Id: "test-id",
			},
			loadPocketTemplateResponse:          responses.PocketTemplateMockDBResponse(now),
			loadPocketTemplateImageSuggResponse: responses.PocketTemplateImageSuggestionMockDBResponse(),
			isErrorExpected:                     true,
			hermesListImageDetailsError:         errors.New("error from hermes"),
			expectedError:                       errors.New("error from hermes"),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockPocketTemplate := &storage.MockIPocketTemplateDAO{}
			mockPocketTemplateImageSuggestion := &storage.MockIPocketTemplateImageSuggestionDAO{}
			mockHermes := &hermesMock.Hermes{}

			mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loadPocketTemplateResponse, s.loadPocketTemplateError)
			mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.loadPocketTemplateImageSuggResponse, s.loadPocketTemplateImageSuggError)
			mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(s.hermesListImageDetailsResponse, s.hermesListImageDetailsError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				PocketTemplateDAO:                mockPocketTemplate,
				PocketTemplateImageSuggestionDAO: mockPocketTemplateImageSuggestion,
			}
			service := &ProductMasterService{
				AppConfig:    appConfig,
				HermesClient: mockHermes,
				Store:        mockStore,
			}
			ctx := context.Background()
			if s.tenant == tenants.TenantMY {
				tenant := tenants.NewTenant(tenants.TenantMY)
				ctx = tenant.MustInjectTenant(ctx, tenant.Name)
			}
			pocketTemplates, err := service.GetPocketTemplate(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, pocketTemplates)
			}
		})
	}
}

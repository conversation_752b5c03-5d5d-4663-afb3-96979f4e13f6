package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateDepositInterestAmountSlabRate(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateDepositInterestAmountSlabRateRequest
		storeLoadResponse []*storage.DepositInterestAmountSlabRate
		expectedResponse  *api.CreateDepositInterestAmountSlabRateResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - base-interest-time-slab-rate created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			storeLoadResponse: []*storage.DepositInterestAmountSlabRate{{
				PublicID:                         "test-id",
				DepositInterestVersionID:         1,
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
				CreatedAt:                        now,
				UpdatedBy:                        "unit-test",
				UpdatedAt:                        now,
			}},
			expectedResponse: &api.CreateDepositInterestAmountSlabRateResponse{
				DepositInterestAmountSlabRate: &api.DepositInterestAmountSlabRate{
					Id:                               "test-id",
					DepositInterestVersionID:         "test-deposit-interest-version-id",
					FromAmount:                       "100",
					ToAmount:                         "200",
					BaseRateInterestSpreadPercentage: "0.1",
					AbsoluteInterestRatePercentage:   "2",
					CreatedBy:                        "unit-test",
					CreatedAt:                        now,
					UpdatedBy:                        "unit-test",
					UpdatedAt:                        now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - deposit interest version id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingDepositInterestVersionID.Code), 10), apiErr.ErrMissingDepositInterestVersionID.Message),
		},
		{
			testDesc:       "error path - from amount field is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidFromAmount.Code), 10), apiErr.ErrInvalidFromAmount.Message),
		},
		{
			testDesc:       "error path - invalid from amount field",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "xxx",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidFromAmount.Code), 10), apiErr.ErrInvalidFromAmount.Message),
		},
		{
			testDesc:       "error path - to amount field is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidToAmount.Code), 10), apiErr.ErrInvalidToAmount.Message),
		},
		{
			testDesc:       "error path - invalid to amount field",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "xxx",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidToAmount.Code), 10), apiErr.ErrInvalidToAmount.Message),
		},
		{
			testDesc:       "error path - base rate interest spread percentage is non numeric",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "xxx",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Code), 10), apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Message),
		},
		{
			testDesc:       "error path - base rate interest spread percentage is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Code), 10), apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Message),
		},
		{
			testDesc:       "error path - absolute interest rate percentage is non numeric",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "xxx",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidAbsoluteInterestRatePercentage.Code), 10), apiErr.ErrInvalidAbsoluteInterestRatePercentage.Message),
		},
		{
			testDesc:       "error path - absolute interest rate percentage is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidAbsoluteInterestRatePercentage.Code), 10), apiErr.ErrInvalidAbsoluteInterestRatePercentage.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - fromAmount greater than toAmount",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "200",
				ToAmount:                         "100",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrFromAmountGreaterThanToAmount.Code), 10), apiErr.ErrFromAmountGreaterThanToAmount.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestAmountSlabRateRequest{
				DepositInterestVersionID:         "test-deposit-interest-version-id",
				FromAmount:                       "100",
				ToAmount:                         "200",
				BaseRateInterestSpreadPercentage: "0.1",
				AbsoluteInterestRatePercentage:   "2",
				CreatedBy:                        "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockInterestVersion := &storage.MockIDepositInterestVersionDAO{}
			mockInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestVersion{{}}, nil)
			mockDepositInterestAmountSlabRate := &storage.MockIDepositInterestAmountSlabRateDAO{}
			mockDepositInterestAmountSlabRate.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				DepositInterestAmountSlabRateDAO: mockDepositInterestAmountSlabRate,
				DepositInterestVersionDAO:        mockInterestVersion,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			depositInterestAmountSlabRate, err := service.CreateDepositInterestAmountSlabRate(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, depositInterestAmountSlabRate)
			}
		})
	}
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetDepositInterestAmountSlabRate fetches a deposit interest amount slab rate by id
func (p *ProductMasterService) GetDepositInterestAmountSlabRate(ctx context.Context, req *api.GetDepositInterestAmountSlabRateRequest) (*api.GetDepositInterestAmountSlabRateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetDepositInterestAmountSlabRate"))
	slog.FromContext(ctx).Info(constants.GetDepositInterestAmountSlabRateLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetGetDepositInterestAmountSlabRateRequest(ctx, req); err != nil {
		return nil, err
	}

	return interestconfig.GetDepositInterestAmountSlabRate(ctx, req, p.Store)
}

func validateGetGetDepositInterestAmountSlabRateRequest(ctx context.Context, req *api.GetDepositInterestAmountSlabRateRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

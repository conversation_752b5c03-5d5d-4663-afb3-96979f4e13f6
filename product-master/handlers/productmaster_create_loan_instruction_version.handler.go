package handlers

import (
	context "context"
	"fmt"
	"net/http"
	"strings"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/loaninstruction"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateLoanInstructionVersion creates a new version of loan instructions
func (p *ProductMasterService) CreateLoanInstructionVersion(ctx context.Context, req *api.CreateLoanInstructionVersionRequest) (*api.CreateLoanInstructionVersionResponse, error) {
	slog.FromContext(ctx).Info(constants.CreateLoanInstructionVersionLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if errs := validateCreateLoanInstructionVersionRequest(req); len(errs) != 0 {
		return nil, apiErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, fmt.Sprint(apiErr.ErrInvalidInstructionVersionRequest.Code),
			apiErr.ErrInvalidInstructionVersionRequest.Message, errs)
	}

	return loaninstruction.CreateLoanInstructionVersion(ctx, req, p.Store)
}

func validateCreateLoanInstructionVersionRequest(req *api.CreateLoanInstructionVersionRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if strings.TrimSpace(req.Version) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingVersion.Code), Message: apiErr.ErrMissingVersion.Message})
	}
	if strings.TrimSpace(req.ProductVariantCode) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingProductVariantCode.Code), Message: apiErr.ErrMissingProductVariantCode.Message})
	}
	if strings.TrimSpace(req.ProductVariantVersion) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingProductVariantVersion.Code), Message: apiErr.ErrMissingProductVariantVersion.Message})
	}
	if strings.TrimSpace(req.InstructionType) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingInstructionType.Code), Message: apiErr.ErrMissingInstructionType.Message})
	}
	if strings.TrimSpace(req.CreatedBy) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingCreatedBy.Code), Message: apiErr.ErrMissingCreatedBy.Message})
	}
	return errs
}

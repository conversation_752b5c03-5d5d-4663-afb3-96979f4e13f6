package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetLoanInterest fetches a loan interest by id
func (p *ProductMasterService) GetLoanInterest(ctx context.Context, req *api.GetLoanInterestByIDRequest) (*api.GetLoanInterestByIDResponse, error) {
	slog.FromContext(ctx).Info(constants.GetLoanInterestByIDLogTag, fmt.Sprintf("Received Request: %+v", utils.ToJSON(req)), apiCommon.GetTraceID(ctx))
	if err := validateGetLoanInterestRequest(ctx, req); err != nil {
		return nil, err
	}
	return interestconfig.GetLoanInterestByID(ctx, req, p.Store)
}

func validateGetLoanInterestRequest(ctx context.Context, req *api.GetLoanInterestByIDRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

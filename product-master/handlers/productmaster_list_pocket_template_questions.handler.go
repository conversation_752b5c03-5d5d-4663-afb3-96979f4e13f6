package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/pocket"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// ListPocketTemplateQuestions fetches list of pocket template questions
func (p *ProductMasterService) ListPocketTemplateQuestions(ctx context.Context, req *api.ListPocketTemplateQuestionsRequest) (*api.ListPocketTemplateQuestionsResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListPocketTemplateQuestions"))
	slog.FromContext(ctx).Info(constants.ListPocketTemplateQuestionsLogTag, fmt.Sprintf("Received Request: %+v", req))

	if err := validateListPocketTemplateQuestionsRequest(ctx, req); err != nil {
		return nil, err
	}
	return pocket.ListPocketTemplateQuestions(ctx, p.HermesClient, req, p.Store)
}

func validateListPocketTemplateQuestionsRequest(ctx context.Context, req *api.ListPocketTemplateQuestionsRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.PocketTemplateID, "pocket-template-id", apiErr.ErrMissingPocketTemplateID); err != nil {
		return err
	}
	return nil
}

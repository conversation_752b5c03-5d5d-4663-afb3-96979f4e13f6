package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetProductByCode(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetProductByCodeRequest
		storeLoadResponse []*storage.Product
		expectedResponse  *api.GetProductByCodeResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get product",
			requestParams: &api.GetProductByCodeRequest{
				Code: "test-product-code",
			},
			storeLoadResponse: []*storage.Product{
				{
					PublicID:          "test-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Description: sql.NullString{
						String: "create test product",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "unit-test",
					CreatedAt: now,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				},
			},
			expectedResponse: &api.GetProductByCodeResponse{
				Product: &api.Product{
					Id:                "test-id",
					ProductTemplateID: "test-product-template-id-1",
					Code:              "test-product-code",
					Name:              "test-product-name",
					Description:       "create test product",
					Status:            api.EntityStatus_ACTIVE,
					CreatedBy:         "unit-test",
					CreatedAt:         now,
					UpdatedBy:         "unit-test",
					UpdatedAt:         now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetProductByCodeRequest{
				Code: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc: "error path - product not found in database",
			requestParams: &api.GetProductByCodeRequest{
				Code: "test-product-code",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetProductByCodeRequest{
				Code: "test-product-code",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductTemplate := &storage.MockIProductTemplateDAO{}
			mockProductTemplate.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductTemplate{PublicID: "test-product-template-id-1"}, nil)

			mockProduct := &storage.MockIProductDAO{}
			mockProduct.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductDAO:         mockProduct,
				ProductTemplateDAO: mockProductTemplate,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			product, err := service.GetProductByCode(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, product)
			}
		})
	}
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// ListProductVariantParameters fetches all product variant parameters for a product variant
func (p *ProductMasterService) ListProductVariantParameters(ctx context.Context, req *api.ListProductVariantParametersRequest) (*api.ListProductVariantParametersResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListProductVariantParameters"))
	slog.FromContext(ctx).Info(constants.ListProductVariantParametersLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateListProductVariantParametersRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.ListProductVariantParameters(ctx, req, p.Store)
}

func validateListProductVariantParametersRequest(ctx context.Context, req *api.ListProductVariantParametersRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantID, "product-variant-id", apiErr.ErrMissingProductVariantID); err != nil {
		return err
	}
	return nil
}

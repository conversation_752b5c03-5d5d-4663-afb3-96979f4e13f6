package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetProductVariant(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetProductVariantRequest
		storeLoadResponse []*storage.ProductVariant
		expectedResponse  *api.GetProductVariantResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get product variant",
			requestParams: &api.GetProductVariantRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.ProductVariant{{
				PublicID:  "test-id",
				ProductID: 1,
				Code:      "test-product-variant-code",
				Name:      "test-product-variant-name",
				Version:   "1.0.0",
				Description: sql.NullString{
					String: "create test product variant",
					Valid:  true,
				},
				ValidFrom: now,
				Status:    "ACTIVE",
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.GetProductVariantResponse{
				ProductVariant: &api.ProductVariant{
					Id:          "test-id",
					Code:        "test-product-variant-code",
					Version:     "1.0.0",
					Name:        "test-product-variant-name",
					ProductID:   "test-product",
					Description: "create test product variant",
					Status:      api.EntityStatus_ACTIVE,
					ValidFrom:   now,
					ValidTo:     time.Time{},
					CreatedBy:   "unit-test",
					CreatedAt:   now,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetProductVariantRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - product variant not found in database",
			requestParams: &api.GetProductVariantRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetProductVariantRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProduct := &storage.MockIProductDAO{}
			mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{PublicID: "test-product"}, nil)

			mockProductVariantDAO := &storage.MockIProductVariantDAO{}
			mockProductVariantDAO.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO: mockProductVariantDAO,
				ProductDAO:        mockProduct,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			product, err := service.GetProductVariant(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, product)
			}
		})
	}
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateDepositInterest(t *testing.T) {
	now := time.Now().UTC()
	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := api.Currency(appConfig.Locale.Currency)
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateDepositInterestRequest
		storeLoadResponse []*storage.DepositInterest
		expectedResponse  *api.CreateDepositInterestResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - deposit-interest linked to base interest created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-base-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			storeLoadResponse: []*storage.DepositInterest{{
				PublicID:           "test-id",
				ProductVariantID:   1,
				IsLinkedToBaseRate: true,
				BaseInterestID: sql.NullInt64{
					Int64: 2,
					Valid: true,
				},
				Code: "test-deposit-interest-code",
				Name: "test-deposit-interest-name",
				Description: sql.NullString{
					String: "get test deposit-interest",
					Valid:  true,
				},
				Currency:              string(currency),
				RoundOffType:          "FLOOR",
				InterestSlabType:      "AMOUNT",
				InterestSlabStructure: "ABSOLUTE",
				CreatedBy:             "unit-test",
				CreatedAt:             now,
				UpdatedBy:             "unit-test",
				UpdatedAt:             now,
			}},
			expectedResponse: &api.CreateDepositInterestResponse{
				DepositInterest: &api.DepositInterest{
					Id:                    "test-id",
					ProductVariantID:      "test-product-variant-id",
					IsLinkedToBaseRate:    true,
					BaseInterestID:        "test-base-interest-id",
					Code:                  "test-deposit-interest-code",
					Name:                  "test-deposit-interest-name",
					Description:           "get test deposit-interest",
					Currency:              currency,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestSlabType:      api.InterestSlabType_AMOUNT,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "happy path - deposit-interest not linked to base interest created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    false,
				BaseInterestID:        "",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			storeLoadResponse: []*storage.DepositInterest{{
				PublicID:           "test-id",
				ProductVariantID:   1,
				IsLinkedToBaseRate: false,
				BaseInterestID:     sql.NullInt64{},
				Code:               "test-deposit-interest-code",
				Name:               "test-deposit-interest-name",
				Description: sql.NullString{
					String: "get test deposit-interest",
					Valid:  true,
				},
				Currency:              string(currency),
				RoundOffType:          "FLOOR",
				InterestSlabType:      "AMOUNT",
				InterestSlabStructure: "ABSOLUTE",
				CreatedBy:             "unit-test",
				CreatedAt:             now,
				UpdatedBy:             "unit-test",
				UpdatedAt:             now,
			}},
			expectedResponse: &api.CreateDepositInterestResponse{
				DepositInterest: &api.DepositInterest{
					Id:                    "test-id",
					ProductVariantID:      "test-product-variant-id",
					IsLinkedToBaseRate:    false,
					BaseInterestID:        "",
					Code:                  "test-deposit-interest-code",
					Name:                  "test-deposit-interest-name",
					Description:           "get test deposit-interest",
					Currency:              currency,
					RoundOffType:          api.RoundOffType_FLOOR,
					InterestSlabType:      api.InterestSlabType_AMOUNT,
					InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
					CreatedBy:             "unit-test",
					CreatedAt:             now,
					UpdatedBy:             "unit-test",
					UpdatedAt:             now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - product variant id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - base interest id is missing when IsLinkedToBaseRate is true",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingLinkedBaseInterestID.Code), 10), apiErr.ErrMissingLinkedBaseInterestID.Message),
		},
		{
			testDesc:       "error path - code is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc:       "error path - name is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingName.Code), 10), apiErr.ErrMissingName.Message),
		},
		{
			testDesc:       "error path - currency is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidCurrency.Code), 10), apiErr.ErrInvalidCurrency.Message),
		},
		{
			testDesc:       "error path - invalid currency",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              api.Currency("INVALID_CURRENCY"),
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidCurrency.Code), 10), apiErr.ErrInvalidCurrency.Message),
		},
		{
			testDesc:       "error path - round off type is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRoundOffType.Code), 10), apiErr.ErrInvalidRoundOffType.Message),
		},
		{
			testDesc:       "error path - invalid round off type",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              api.Currency("INVALID_CURRENCY"),
				RoundOffType:          api.RoundOffType("INVALID_TYPE"),
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRoundOffType.Code), 10), apiErr.ErrInvalidRoundOffType.Message),
		},
		{
			testDesc:       "error path - interest slab type is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabType.Code), 10), apiErr.ErrInvalidInterestSlabType.Message),
		},
		{
			testDesc:       "error path - invalid interest slab type",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType("INVALID_TYPE"),
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabType.Code), 10), apiErr.ErrInvalidInterestSlabType.Message),
		},
		{
			testDesc:       "error path - interest slab structure is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:   "test-product-variant-id",
				IsLinkedToBaseRate: true,
				BaseInterestID:     "test-deposit-interest-id",
				Code:               "test-deposit-interest-code",
				Name:               "test-deposit-interest-name",
				Description:        "create deposit interest",
				Currency:           currency,
				RoundOffType:       api.RoundOffType_FLOOR,
				InterestSlabType:   api.InterestSlabType_AMOUNT,
				CreatedBy:          "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabStructure.Code), 10), apiErr.ErrInvalidInterestSlabStructure.Message),
		},
		{
			testDesc:       "error path - invalid interest slab structure",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure("INVALID_STRUCTURE"),
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabStructure.Code), 10), apiErr.ErrInvalidInterestSlabStructure.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateDepositInterestRequest{
				ProductVariantID:      "test-product-variant-id",
				IsLinkedToBaseRate:    true,
				BaseInterestID:        "test-deposit-interest-id",
				Code:                  "test-deposit-interest-code",
				Name:                  "test-deposit-interest-name",
				Description:           "create deposit interest",
				Currency:              currency,
				RoundOffType:          api.RoundOffType_FLOOR,
				InterestSlabType:      api.InterestSlabType_AMOUNT,
				InterestSlabStructure: api.InterestSlabStructure_ABSOLUTE,
				CreatedBy:             "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterestDao := &storage.MockIBaseInterestDAO{}
			mockBaseInterestDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{{}}, nil)
			mockProductVariantDao := &storage.MockIProductVariantDAO{}
			mockProductVariantDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{}}, nil)
			mockDepositInterest := &storage.MockIDepositInterestDAO{}
			mockDepositInterest.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockStore := &storage.DBStore{
				DepositInterestDAO: mockDepositInterest,
				BaseInterestDAO:    mockBaseInterestDao,
				ProductVariantDAO:  mockProductVariantDao,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			depositInterest, err := service.CreateDepositInterest(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, depositInterest)
			}
		})
	}
}

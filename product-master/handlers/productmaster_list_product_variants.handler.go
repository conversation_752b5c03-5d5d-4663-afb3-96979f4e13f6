package handlers

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	logic "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// ListProductVariants fetches a product variant by id
func (p *ProductMasterService) ListProductVariants(ctx context.Context, req *api.ListProductVariantsRequest) (*api.ListProductVariantsResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListProductVariants"))
	slog.FromContext(ctx).Info(constants.ListProductVariantsLogTag, fmt.Sprintf("Received Request: %+v", req),
		apiCommon.GetTraceID(ctx))

	if err := validateListProductVariantsRequest(ctx, req); err != nil {
		return nil, err
	}

	return logic.ListProductVariants(ctx, req, p.Store)
}

// validateListProductVariantsRequest is used to validate ListProductVariantsRequest
func validateListProductVariantsRequest(ctx context.Context, req *api.ListProductVariantsRequest) error {
	if req.ProductTemplateCode == "" && req.ProductCode == "" && req.Code == "" {
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag,
			"productTemplateCode, productCode and code fields are empty", apiCommon.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, utils.SafeIntToString(apiErr.ErrMissingAtLeastOneField.Code), apiErr.ErrMissingAtLeastOneField.Message)
	}
	if req.Code != "" && (req.ProductCode != "" || req.ProductTemplateCode != "") {
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag,
			"code field should be empty if productCode or productTemplateCode is present", apiCommon.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, utils.SafeIntToString(apiErr.ErrInvalidRequestParameter.Code), apiErr.ErrInvalidRequestParameter.Message)
	}
	return nil
}

package handlers

import (
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dbmy/common/tenants"

	"gitlab.myteksi.net/dakota/common/tracing"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
)

// ProductMasterService serves as the context for handlers.
type ProductMasterService struct {
	AppConfig    *config.AppConfig `inject:"config"`
	HermesClient hermes.Hermes     `inject:"client.hermes"`
	Statsd       statsd.Client     `inject:"statsD"`
	Store        *storage.DBStore  `inject:"DBStore"`
	Tracer       tracing.Tracer    `inject:"tracer"`
	Tenant       *tenants.Tenant   `inject:"tenant"`
}

package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateProductVariantParameter creates a new Product Variant Parameter
func (p *ProductMasterService) CreateProductVariantParameter(ctx context.Context, req *api.CreateProductVariantParameterRequest) (*api.CreateProductVariantParameterResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateProductVariantParameter"))
	slog.FromContext(ctx).Info(constants.CreateProductVariantParameterLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateProductVariantParameterRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.CreateProductVariantParameter(ctx, req, p.Store)
}

func validateCreateProductVariantParameterRequest(ctx context.Context, req *api.CreateProductVariantParameterRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantID, "product-variant-id", apiErr.ErrMissingProductVariantID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Namespace, "namespace", apiErr.ErrMissingNamespace); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterKey, "parameter-key", apiErr.ErrMissingParameterKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterValue, "parameter-value", apiErr.ErrMissingParameterValue); err != nil {
		return err
	}
	if err := validations.ValidateParameterDataType(ctx, string(req.DataType)); err != nil {
		return err
	}
	if err := validations.ValidateParameterOverrideLevel(ctx, string(req.OverrideLevel)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

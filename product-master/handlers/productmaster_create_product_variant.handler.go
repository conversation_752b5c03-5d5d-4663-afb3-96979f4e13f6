package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateProductVariant creates a new Product Variant
func (p *ProductMasterService) CreateProductVariant(ctx context.Context, req *api.CreateProductVariantRequest) (*api.CreateProductVariantResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateProductVariant"))
	slog.FromContext(ctx).Info(constants.CreateProductVariantLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateCreateProductVariantRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.CreateProductVariant(ctx, req, p.Store)
}

func validateCreateProductVariantRequest(ctx context.Context, req *api.CreateProductVariantRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Name, "name", apiErr.ErrMissingName); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Code, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Version, "version", apiErr.ErrMissingVersion); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductID, "product-id", apiErr.ErrMissingProductID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	if err := validations.ValidateDateFieldWithCurrentDate(ctx, req.ValidFrom, "valid-from", apiErr.ErrValidFromLessThanCurrent); err != nil {
		return err
	}
	if err := validations.ValidateDateFieldWithCurrentDate(ctx, req.ValidTo, "valid-to", apiErr.ErrValidToLessThanCurrent); err != nil {
		return err
	}
	if req.ValidTo.Before(req.ValidFrom) {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "validFrom cannot be greater than validTo")
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrValidFromGreaterThanValidTo.Code), 10),
			apiErr.ErrValidFromGreaterThanValidTo.Message)
	}
	return nil
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_CreateProductTemplateParameter(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		idempotencyKey    string
		requestParams     *api.CreateProductTemplateParameterRequest
		storeLoadResponse []*storage.ProductTemplateParameter
		expectedResponse  *api.CreateProductTemplateParameterResponse
		isErrorExpected   bool
		storeSaveError    error
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc:       "happy path - product template parameter created",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			storeLoadResponse: []*storage.ProductTemplateParameter{{
				PublicID:          "test-id",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel: sql.NullString{
					String: "",
					Valid:  false,
				},
				Description: sql.NullString{
					String: "create test product template parameter",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.CreateProductTemplateParameterResponse{
				ProductTemplateParameter: &api.ProductTemplateParameter{
					Id:                "test-id",
					ProductTemplateID: "test-product-template-id-1",
					Namespace:         "test-namespace",
					ParameterKey:      "test-parameter-key",
					ParameterValue:    "test-parameter-value",
					DataType:          "STRING",
					OverrideLevel:     "NO_OVERRIDE",
					ExceptionLevel:    "",
					Description:       "create test product template parameter",
					CreatedBy:         "unit-test",
					CreatedAt:         now,
					UpdatedBy:         "unit-test",
					UpdatedAt:         now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - product template id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingProductTemplateID.Code), 10), apiErr.ErrMissingProductTemplateID.Message),
		},
		{
			testDesc:       "error path - namespace is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingNamespace.Code), 10), apiErr.ErrMissingNamespace.Message),
		},
		{
			testDesc:       "error path - parameter key is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingParameterKey.Code), 10), apiErr.ErrMissingParameterKey.Message),
		},
		{
			testDesc:       "error path - parameter value is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-value",
				ParameterValue:    "",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingParameterValue.Code), 10), apiErr.ErrMissingParameterValue.Message),
		},
		{
			testDesc:       "error path - data type is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidParameterDatatype.Code), 10), apiErr.ErrInvalidParameterDatatype.Message),
		},
		{
			testDesc:       "error path - invalid data type",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "INVALID_DATATYPE",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidParameterDatatype.Code), 10), apiErr.ErrInvalidParameterDatatype.Message),
		},
		{
			testDesc:       "error path - override level is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidParameterOverrideLevel.Code), 10), apiErr.ErrInvalidParameterOverrideLevel.Message),
		},
		{
			testDesc:       "error path - invalid override level",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "INVALID_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidParameterOverrideLevel.Code), 10), apiErr.ErrInvalidParameterOverrideLevel.Message),
		},
		{
			testDesc:       "error path - created by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
		{
			testDesc:       "error path - database save error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			storeSaveError:  errors.New("database save error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseSave.Code), 10), apiErr.ErrDatabaseSave.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreateProductTemplateParameterRequest{
				ProductTemplateID: "test-product-template-id-1",
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel:    "",
				Description:       "create test product template parameter",
				CreatedBy:         "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductTemplate := &storage.MockIProductTemplateDAO{}
			mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{}}, nil)

			mockProductTemplateParameter := &storage.MockIProductTemplateParameterDAO{}
			mockProductTemplateParameter.On("Save", mock.Anything, mock.Anything).Return(s.storeSaveError)
			mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductTemplateParameterDAO: mockProductTemplateParameter,
				ProductTemplateDAO:          mockProductTemplate,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			productTemplateParameter, err := service.CreateProductTemplateParameter(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, productTemplateParameter)
			}
		})
	}
}

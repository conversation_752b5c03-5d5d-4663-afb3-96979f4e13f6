package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetInternalAccount fetches a internal account by id
func (p *ProductMasterService) GetInternalAccount(ctx context.Context, req *api.GetInternalAccountRequest) (*api.GetInternalAccountResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetInternalAccount"))
	slog.FromContext(ctx).Info(constants.GetInternalAccountLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateGetInternalAccountRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.GetInternalAccount(ctx, req, p.Store)
}

func validateGetInternalAccountRequest(ctx context.Context, req *api.GetInternalAccountRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_UpdateProductTemplateParameterValue(t *testing.T) {
	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc           string
		idempotencyKey     string
		requestParams      *api.UpdateProductTemplateParameterValueRequest
		storeLoadResponse1 []*storage.ProductTemplateParameter
		storeLoadResponse2 []*storage.ProductTemplateParameter
		expectedResponse   *api.UpdateProductTemplateParameterValueResponse
		isErrorExpected    bool
		storeUpdateError   error
		storeLoadError     error
		expectedError      error
	}{
		{
			testDesc:       "happy path - product template parameter updated",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "test-id",
				ParameterValue: "test-parameter-value-2",
				UpdatedBy:      "unit-test",
			},
			storeLoadResponse1: []*storage.ProductTemplateParameter{{
				PublicID:          "test-id",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel: sql.NullString{
					String: "",
					Valid:  false,
				},
				Description: sql.NullString{
					String: "create test product template parameter",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			storeLoadResponse2: []*storage.ProductTemplateParameter{{
				PublicID:          "test-id",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value-2",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel: sql.NullString{
					String: "",
					Valid:  false,
				},
				Description: sql.NullString{
					String: "create test product template parameter",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.UpdateProductTemplateParameterValueResponse{
				ProductTemplateParameter: &api.ProductTemplateParameter{
					Id:                "test-id",
					ProductTemplateID: "test-product-template-id-1",
					Namespace:         "test-namespace",
					ParameterKey:      "test-parameter-key",
					ParameterValue:    "test-parameter-value-2",
					DataType:          "STRING",
					OverrideLevel:     "NO_OVERRIDE",
					ExceptionLevel:    "",
					Description:       "create test product template parameter",
					CreatedBy:         "unit-test",
					CreatedAt:         nowMinus10,
					UpdatedBy:         "unit-test",
					UpdatedAt:         now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc:       "error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "test-id",
				ParameterValue: "test-parameter-value-2",
				UpdatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "error path - id is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "",
				ParameterValue: "test-parameter-value-2",
				UpdatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc:       "error path - parameter value is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "test-id",
				ParameterValue: "",
				UpdatedBy:      "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingParameterValue.Code), 10), apiErr.ErrMissingParameterValue.Message),
		},
		{
			testDesc:       "error path - updated by is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "test-id",
				ParameterValue: "test-parameter-value-2",
				UpdatedBy:      "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingUpdatedBy.Code), 10), apiErr.ErrMissingUpdatedBy.Message),
		},
		{
			testDesc: "error path - product template parameter not found in database",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "test-id",
				ParameterValue: "test-parameter-value-2",
				UpdatedBy:      "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc:       "error path - database load error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "test-id",
				ParameterValue: "test-parameter-value-2",
				UpdatedBy:      "unit-test",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			testDesc:       "error path - database update error",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.UpdateProductTemplateParameterValueRequest{
				Id:             "test-id",
				ParameterValue: "test-parameter-value-2",
				UpdatedBy:      "unit-test",
			},
			storeLoadResponse1: []*storage.ProductTemplateParameter{{
				PublicID:          "test-id",
				ProductTemplateID: 1,
				Namespace:         "test-namespace",
				ParameterKey:      "test-parameter-key",
				ParameterValue:    "test-parameter-value",
				DataType:          "STRING",
				OverrideLevel:     "NO_OVERRIDE",
				ExceptionLevel: sql.NullString{
					String: "",
					Valid:  false,
				},
				Description: sql.NullString{
					String: "create test product template parameter",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: nowMinus10,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			isErrorExpected:  true,
			storeUpdateError: errors.New("database update error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseUpdate.Code), 10), apiErr.ErrDatabaseUpdate.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockProductTemplateParameter := &storage.MockIProductTemplateParameterDAO{}
			mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse1, s.storeLoadError).Once()
			mockProductTemplateParameter.On("Update", mock.Anything, mock.Anything).Return(s.storeUpdateError)
			mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse2, s.storeLoadError).Once()

			mockProductTemplate := &storage.MockIProductTemplateDAO{}
			mockProductTemplate.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductTemplate{PublicID: "test-product-template-id-1"}, nil)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductTemplateParameterDAO: mockProductTemplateParameter,
				ProductTemplateDAO:          mockProductTemplate,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}
			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			productTemplateParameter, err := service.UpdateProductTemplateParameterValue(ctx, s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, productTemplateParameter)
			}
		})
	}
}

package handlers

import (
	"context"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func TestCreatePocketTemplateQuestions(t *testing.T) {
	now := time.Now().UTC()

	scenarios := []struct {
		testDesc                     string
		idempotencyKey               string
		requestParams                *api.CreatePocketTemplateQuestionsRequest
		expectedResp                 *api.CreatePocketTemplateQuestionsResponse
		pocketTemplateDBResp         []*storage.PocketTemplate
		pocketTemplateQuestionDBResp []*storage.PocketTemplateQuestion
		answerSuggestionsDBResp      []*storage.PocketTemplateAnswerSuggestion
		isErrorExpected              bool
		storeSaveError               error
		storeLoadError               error
		expectedError                error
	}{
		{
			testDesc:       "Happy path - Create pocket template questions",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
				Locale:           api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "Where are you heading to?",
						AnswerSuggestions: []string{"US", "Paris", "UK"},
					},
				},
				CreatedBy: "unit-test",
			},
			expectedResp:                 responses.CreatePocketTemplateQuestionResponse(),
			pocketTemplateDBResp:         responses.PocketTemplateMockDBResponse(now),
			pocketTemplateQuestionDBResp: responses.PocketTemplateQuestionsMockDBResponse(now),
			answerSuggestionsDBResp:      responses.PocketTemplateAnswerSuggestionsMockDBResponse(now),
			isErrorExpected:              false,
		},
		{
			testDesc:       "Error path - idempotency key is missing",
			idempotencyKey: "",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
				Locale:           api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "Where are you heading to?",
						AnswerSuggestions: []string{"US", "Paris", "UK"},
					},
				},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingIdempotencyKey.Code), 10), apiErr.ErrMissingIdempotencyKey.Message),
		},
		{
			testDesc:       "Error path - PocketTemplateID is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "",
				Locale:           api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "Where are you heading to?",
						AnswerSuggestions: []string{"US", "Paris", "UK"},
					},
				},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingPocketTemplateID.Code), 10), apiErr.ErrMissingPocketTemplateID.Message),
		},
		{
			testDesc:       "Error path - Locale is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
				Locale:           "",
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "Where are you heading to?",
						AnswerSuggestions: []string{"US", "Paris", "UK"},
					},
				},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingLocale.Code), 10), apiErr.ErrMissingLocale.Message),
		},
		{
			testDesc:       "Error path - QuestionAnswerPairs is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID:    "test-id",
				Locale:              api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{},
				CreatedBy:           "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionAnswerPairs.Code), 10), apiErr.ErrMissingQuestionAnswerPairs.Message),
		},
		{
			testDesc:       "Error path - QuestionText in QuestionAnswerPairs is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
				Locale:           api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "",
						AnswerSuggestions: []string{"US", "Paris", "UK"},
					},
				},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message),
		},
		{
			testDesc:       "Error path - AnswerSuggestions in QuestionAnswerPairs is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
				Locale:           api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "Where are you heading to?",
						AnswerSuggestions: []string{},
					},
				},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message),
		},
		{
			testDesc:       "Error path - QuestionText and AnswerSuggestions in QuestionAnswerPairs is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
				Locale:           api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "",
						AnswerSuggestions: []string{},
					},
				},
				CreatedBy: "unit-test",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message),
		},
		{
			testDesc:       "Error path - CreatedBy is missing",
			idempotencyKey: "test-idempotency-key",
			requestParams: &api.CreatePocketTemplateQuestionsRequest{
				PocketTemplateID: "test-id",
				Locale:           api.Locale_EN,
				QuestionAnswerPairs: []api.QuestionAnswerPairsRequest{
					{
						QuestionText:      "Where are you heading to?",
						AnswerSuggestions: []string{"US", "Paris", "UK"},
					},
				},
				CreatedBy: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCreatedBy.Code), 10), apiErr.ErrMissingCreatedBy.Message),
		},
	}
	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockPocketTemplate := &storage.MockIPocketTemplateDAO{}
			mockPocketTemplateQuestion := &storage.MockIPocketTemplateQuestionDAO{}
			mockPocketTemplateAnswerSuggestions := &storage.MockIPocketTemplateAnswerSuggestionDAO{}

			mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.pocketTemplateDBResp, s.storeLoadError)
			mockPocketTemplateQuestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(s.storeLoadError)
			mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.pocketTemplateQuestionDBResp, s.storeLoadError)
			mockPocketTemplateAnswerSuggestions.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(s.storeLoadError)
			mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(s.answerSuggestionsDBResp, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				PocketTemplateDAO:                 mockPocketTemplate,
				PocketTemplateQuestionDAO:         mockPocketTemplateQuestion,
				PocketTemplateAnswerSuggestionDAO: mockPocketTemplateAnswerSuggestions,
			}
			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			ctx := apiCommon.AddIdempotencyKeyToHeader(context.Background(), s.idempotencyKey)
			pocketTemplateQuestions, err := service.CreatePocketTemplateQuestions(ctx, s.requestParams)
			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
				assert.Equal(t, s.expectedError, err)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResp, pocketTemplateQuestions)
			}
		})
	}
}

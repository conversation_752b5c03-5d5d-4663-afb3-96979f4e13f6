package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/txncatalogue"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateTransactionCatalogue creates a new Transaction catalogue
func (p *ProductMasterService) CreateTransactionCatalogue(ctx context.Context, req *api.CreateTransactionCatalogueRequest) (*api.CreateTransactionCatalogueResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateTransactionCatalogue"))
	slog.FromContext(ctx).Info(constants.CreateTransactionCatalogueLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateTransactionCatalogueRequest(ctx, req); err != nil {
		return nil, err
	}

	return txncatalogue.CreateTransactionCatalogue(ctx, req, p.Store)
}

func validateCreateTransactionCatalogueRequest(ctx context.Context, req *api.CreateTransactionCatalogueRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Domain, "domain", apiErr.ErrMissingDomain); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.TxnType, "txn-type", apiErr.ErrMissingTxnType); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.TxnSubType, "txn-sub-type", apiErr.ErrMissingTxnSubType); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.DisplayName, "display-name", apiErr.ErrMissingDisplayName); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

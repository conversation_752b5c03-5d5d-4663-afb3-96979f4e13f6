package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateProductTemplateParameter creates a new Product Template Parameter
func (p *ProductMasterService) CreateProductTemplateParameter(ctx context.Context, req *api.CreateProductTemplateParameterRequest) (*api.CreateProductTemplateParameterResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateProductTemplateParameter"))

	slog.FromContext(ctx).Info(constants.CreateProductTemplateParameterLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateCreateProductTemplateParameterRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.CreateProductTemplateParameter(ctx, req, p.Store)
}

func validateCreateProductTemplateParameterRequest(ctx context.Context, req *api.CreateProductTemplateParameterRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductTemplateID, "product-template-id", apiErr.ErrMissingProductTemplateID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Namespace, "namespace", apiErr.ErrMissingNamespace); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterKey, "parameter-key", apiErr.ErrMissingParameterKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterValue, "parameter-value", apiErr.ErrMissingParameterValue); err != nil {
		return err
	}
	if err := validations.ValidateParameterDataType(ctx, string(req.DataType)); err != nil {
		return err
	}
	if err := validations.ValidateParameterOverrideLevel(ctx, string(req.OverrideLevel)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

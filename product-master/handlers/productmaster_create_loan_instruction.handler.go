package handlers

import (
	context "context"
	"fmt"
	"net/http"
	"strings"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/loaninstruction"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateLoanInstruction creates a new loan instruction
func (p *ProductMasterService) CreateLoanInstruction(ctx context.Context, req *api.CreateLoanInstructionRequest) (*api.CreateLoanInstructionResponse, error) {
	slog.FromContext(ctx).Info(constants.CreateLoanInstructionVersionLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if errs := validateCreateLoanInstructionRequest(req); len(errs) != 0 {
		return nil, apiErr.BuildErrorResponseWithErrorDetail(
			http.StatusBadRequest, fmt.Sprint(apiErr.ErrInvalidInstructionRequest.Code),
			apiErr.ErrInvalidInstructionRequest.Message, errs)
	}

	return loaninstruction.CreateLoanInstruction(ctx, req, p.Store)
}

func validateCreateLoanInstructionRequest(req *api.CreateLoanInstructionRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	if strings.TrimSpace(req.LoanInstructionVersionID) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingLoanInstructionVersionID.Code), Message: apiErr.ErrMissingLoanInstructionVersionID.Message})
	}
	if strings.TrimSpace(req.Code) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingCode.Code), Message: apiErr.ErrMissingCode.Message})
	}
	if strings.TrimSpace(req.Name) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingName.Code), Message: apiErr.ErrMissingName.Message})
	}
	if strings.TrimSpace(req.CreatedBy) == "" {
		errs = append(errs, servus.ErrorDetail{ErrorCode: fmt.Sprint(apiErr.ErrMissingCreatedBy.Code), Message: apiErr.ErrMissingCreatedBy.Message})
	}
	return errs
}

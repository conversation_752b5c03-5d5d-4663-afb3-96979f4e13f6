package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreateProductVariantTransactionCatalogueInternalAccountMapping creates a new product-variant-transaction-catalogue-internal-account-mapping
func (p *ProductMasterService) CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) (*api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreateProductVariantTransactionCatalogueInternalAccountMapping"))
	slog.FromContext(ctx).Info(constants.CreateProductVariantTransactionCatalogueInternalAccountMappingLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreateProductVariantTransactionCatalogueInternalAccountMappingRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx, req, p.Store)
}

func validateCreateProductVariantTransactionCatalogueInternalAccountMappingRequest(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.InternalAccountID, "internal-account-id", apiErr.ErrMissingInternalAccountID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantTransactionCatalogueMappingID, "product-variant-transaction-catalogue-mapping-id", apiErr.ErrMissingProductVariantTransactionCatalogueMappingID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.IdentifierKey, "identifier-key", apiErr.ErrMissingIdentifierKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetBaseInterestByCode(t *testing.T) {
	now := time.Now().UTC()

	appConfig := &config.AppConfig{
		DefaultAppConfig: servus.DefaultAppConfig{
			Data: &servus.DataConfig{
				MySQL: &data.MysqlConfig{},
			},
		},
		Locale: utils.GetLocale(),
	}
	currency := api.Currency(appConfig.Locale.Currency)
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetBaseInterestByCodeRequest
		storeLoadResponse []*storage.BaseInterest
		expectedResponse  *api.GetBaseInterestByCodeResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get base interest",
			requestParams: &api.GetBaseInterestByCodeRequest{
				Code: "test-base-interest-code",
			},
			storeLoadResponse: []*storage.BaseInterest{
				{
					PublicID: "test-id",
					Code:     "test-base-interest-code",
					Name:     "test-base-interest-name",
					Description: sql.NullString{
						String: "get test base interest",
						Valid:  true,
					},
					Currency:  string(currency),
					CreatedBy: "unit-test",
					CreatedAt: now,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				},
			},
			expectedResponse: &api.GetBaseInterestByCodeResponse{
				BaseInterest: &api.BaseInterest{
					Id:          "test-id",
					Code:        "test-base-interest-code",
					Name:        "test-base-interest-name",
					Description: "get test base interest",
					Currency:    currency,
					CreatedBy:   "unit-test",
					CreatedAt:   now,
					UpdatedBy:   "unit-test",
					UpdatedAt:   now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - code is missing",
			requestParams: &api.GetBaseInterestByCodeRequest{
				Code: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingCode.Code), 10), apiErr.ErrMissingCode.Message),
		},
		{
			testDesc: "error path - base interest not found in database",
			requestParams: &api.GetBaseInterestByCodeRequest{
				Code: "test-base-interest-code",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetBaseInterestByCodeRequest{
				Code: "test-base-interest-code",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterest := &storage.MockIBaseInterestDAO{}
			mockBaseInterest.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			mockStore := &storage.DBStore{
				BaseInterestDAO: mockBaseInterest,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			baseInterest, err := service.GetBaseInterestByCode(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, baseInterest)
			}
		})
	}
}

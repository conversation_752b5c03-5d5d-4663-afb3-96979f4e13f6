package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// ListEffectiveProductVariantParameters fetches all product variant parameters for a product variant (includes the overrides from product-template-parameters
func (p *ProductMasterService) ListEffectiveProductVariantParameters(ctx context.Context, req *api.ListEffectiveProductVariantParametersRequest) (*api.ListEffectiveProductVariantParametersResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListEffectiveProductVariantParameters"))
	slog.FromContext(ctx).Info(constants.ListEffectiveProductVariantParametersLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))
	if err := validateListEffectiveProductVariantParametersRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.ListEffectiveProductVariantParameters(ctx, req, p.Store)
}

func validateListEffectiveProductVariantParametersRequest(ctx context.Context, req *api.ListEffectiveProductVariantParametersRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantCode, "code", apiErr.ErrMissingCode); err != nil {
		return err
	}
	return nil
}

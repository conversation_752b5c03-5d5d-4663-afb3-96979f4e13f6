package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_GetBaseInterestVersion(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		testDesc          string
		requestParams     *api.GetBaseInterestVersionRequest
		storeLoadResponse []*storage.BaseInterestVersion
		expectedResponse  *api.GetBaseInterestVersionResponse
		isErrorExpected   bool
		storeLoadError    error
		expectedError     error
	}{
		{
			testDesc: "happy path - get base interest version",
			requestParams: &api.GetBaseInterestVersionRequest{
				Id: "test-id",
			},
			storeLoadResponse: []*storage.BaseInterestVersion{{
				PublicID:       "test-id",
				BaseInterestID: 1,
				Version:        "1",
				EffectiveDate:  now,
				Description: sql.NullString{
					String: "get test base interest version",
					Valid:  true,
				},
				CreatedBy: "unit-test",
				CreatedAt: now,
				UpdatedBy: "unit-test",
				UpdatedAt: now,
			}},
			expectedResponse: &api.GetBaseInterestVersionResponse{
				BaseInterestVersion: &api.BaseInterestVersion{
					Id:             "test-id",
					BaseInterestID: "test-base-interest-id",
					Version:        "1",
					EffectiveDate:  now,
					Description:    "get test base interest version",
					CreatedBy:      "unit-test",
					CreatedAt:      now,
					UpdatedBy:      "unit-test",
					UpdatedAt:      now,
				},
			},
			isErrorExpected: false,
		},
		{
			testDesc: "error path - id is missing",
			requestParams: &api.GetBaseInterestVersionRequest{
				Id: "",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingID.Code), 10), apiErr.ErrMissingID.Message),
		},
		{
			testDesc: "error path - base interest version id not found in database",
			requestParams: &api.GetBaseInterestVersionRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  data.ErrNoData,
			expectedError: apiErr.BuildErrorResponse(http.StatusNotFound,
				strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc: "error path - database load error",
			requestParams: &api.GetBaseInterestVersionRequest{
				Id: "test-id",
			},
			isErrorExpected: true,
			storeLoadError:  errors.New("database load error"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockBaseInterest := &storage.MockIBaseInterestDAO{}
			mockBaseInterest.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.BaseInterest{PublicID: "test-base-interest-id"}, nil)

			mockBaseInterestVersion := &storage.MockIBaseInterestVersionDAO{}
			mockBaseInterestVersion.On("Find", mock.Anything, mock.Anything).Return(s.storeLoadResponse, s.storeLoadError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				BaseInterestVersionDAO: mockBaseInterestVersion,
				BaseInterestDAO:        mockBaseInterest,
			}

			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			baseInterestVersion, err := service.GetBaseInterestVersion(context.Background(), s.requestParams)

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedResponse, baseInterestVersion)
			}
		})
	}
}

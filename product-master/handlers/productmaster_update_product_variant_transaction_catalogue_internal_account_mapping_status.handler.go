package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/financialconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus updates the status of product-variant-transaction-catalogue-internal-account-mapping
func (p *ProductMasterService) UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) (*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus"))
	slog.FromContext(ctx).Info(constants.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateUpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest(ctx, req); err != nil {
		return nil, err
	}

	return financialconfig.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(ctx, req, p.Store)
}

func validateUpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	if err := validations.ValidateEntityStatus(ctx, string(req.Status)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.UpdatedBy, "updated-by", apiErr.ErrMissingUpdatedBy); err != nil {
		return err
	}
	return nil
}

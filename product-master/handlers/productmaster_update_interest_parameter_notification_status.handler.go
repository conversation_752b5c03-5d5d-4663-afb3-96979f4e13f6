package handlers

import (
	"context"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// UpdateInterestParameterNotificationStatus keep the status whether noti is sent to the customer
func (p *ProductMasterService) UpdateInterestParameterNotificationStatus(ctx context.Context, req *api.UpdateInterestParameterNotificationStatusRequest) (*api.UpdateInterestParameterNotificationStatusResponse, error) {
	ctx = slog.AddTagsToContext(ctx,
		common.Service(constants.ServiceName),
		common.Endpoint("UpdateInterestParameterNotificationStatus"),
		tags.T(constants.ParameterKey, req.ParameterKey),
	)
	err := validateUpdateNotifyParametersRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	return productconfig.UpdateNotificationParameterStatus(ctx, p.Store, req, constants.ParameterStatus[req.IsNotified])
}

// ValidateUpdateNotifyParameters ...
func validateUpdateNotifyParametersRequest(ctx context.Context, req *api.UpdateInterestParameterNotificationStatusRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterKey, "parameterKey", apiErr.ErrMissingParameterKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ParameterVersion, "parameterVersion", apiErr.ErrMissingParameterVersionKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, string(req.IsNotified), "isNotified", apiErr.ErrMissingIsNotifiedKey); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.SmartContractVersionID, "SmartContractVersionID", apiErr.ErrSmartContractVersionID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.UpdatedBy, "UpdatedBy", apiErr.ErrMissingUpdatedBy); err != nil {
		return err
	}
	if err := validations.ValidateIsNotifiedValue(ctx, req.IsNotified); err != nil {
		return err
	}
	return nil
}

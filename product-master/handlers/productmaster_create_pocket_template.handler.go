package handlers

import (
	context "context"
	"fmt"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/pocket"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// CreatePocketTemplate creates a new pocket template
func (p *ProductMasterService) CreatePocketTemplate(ctx context.Context, req *api.CreatePocketTemplateRequest) (*api.CreatePocketTemplateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("CreatePocketTemplate"))
	slog.FromContext(ctx).Info(constants.CreatePocketTemplateLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateCreatePocketTemplateRequest(ctx, req); err != nil {
		return nil, err
	}

	return pocket.CreatePocketTemplate(ctx, p.HermesClient, req, p.Store)
}

func validateCreatePocketTemplateRequest(ctx context.Context, req *api.CreatePocketTemplateRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, apiCommon.GetIdempotencyKeyFromHeader(ctx), "idempotency-key", apiErr.ErrMissingIdempotencyKey); err != nil {
		return err
	}
	if err := validations.ValidatePocketType(ctx, string(req.Type)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.Name, "name", apiErr.ErrMissingName); err != nil {
		return err
	}
	if err := validations.ValidateListNotEmpty(ctx, req.ImageIDs, "image-ids", apiErr.ErrMissingImageID); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}
	return nil
}

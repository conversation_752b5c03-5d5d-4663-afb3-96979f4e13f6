package handlers

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/productconfig"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetProduct fetches a product by id
func (p *ProductMasterService) GetProduct(ctx context.Context, req *api.GetProductRequest) (*api.GetProductResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetProduct"))
	slog.FromContext(ctx).Info(constants.GetProductLogTag, fmt.Sprintf("Received Request: %+v", req), apiCommon.GetTraceID(ctx))

	if err := validateGetProductRequest(ctx, req); err != nil {
		return nil, err
	}

	return productconfig.GetProduct(ctx, req, p.Store)
}

func validateGetProductRequest(ctx context.Context, req *api.GetProductRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.Id, "id", apiErr.ErrMissingID); err != nil {
		return err
	}
	return nil
}

package handlers

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_ListProductVariants(t *testing.T) {
	now := time.Now().UTC()
	scenarios := []struct {
		desc                        string
		request                     *api.ListProductVariantsRequest
		expectedResponse            *api.ListProductVariantsResponse
		enableCache                 bool
		cacheGetResponse            interface{}
		expectedError               error
		isErrorExpected             bool
		loadProductTemplateResponse []*storage.ProductTemplate
		loadProductTemplateError    error
		loadProductResponse         []*storage.Product
		loadProductError            error
		loadProductVariantResponse  []*storage.ProductVariant
		loadProductVariantError     error
	}{
		{
			desc: "happy-path - both productCode and productTemplateCode is present",
			request: &api.ListProductVariantsRequest{
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			loadProductVariantResponse:  responses.SampleProductVariantDAOResponse(),
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			expectedResponse: responses.ListProductVariantsResponse(now, "1"),
		},
		{
			desc: "happy-path - both productCode and productTemplateCode is present and multiple versions",
			request: &api.ListProductVariantsRequest{
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			loadProductVariantResponse:  responses.SampleProductVariantVersionsDAOResponse(),
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			expectedResponse: responses.ListProductVariantsResponse(now, "2"),
		},
		{
			desc: "happy-path - both productCode and productTemplateCode are present and multiple versions",
			request: &api.ListProductVariantsRequest{
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			loadProductVariantResponse: []*storage.ProductVariant{
				{
					ID:        1,
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "3",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				},
				{
					ID:        1,
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "2",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				},
			},
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			expectedResponse: responses.ListProductVariantsResponse(now, "3"),
		},
		{
			desc: "happy-path - only code is present",
			request: &api.ListProductVariantsRequest{
				Code: "sampleCode",
			},
			loadProductVariantResponse:  responses.SampleProductVariantDAOResponse(),
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			expectedResponse: responses.ListProductVariantsResponse(now, "1"),
		},
		{
			desc: "happy-path - only productTemplateCode is present",
			request: &api.ListProductVariantsRequest{
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			loadProductVariantResponse:  responses.SampleProductVariantDAOResponse(),
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			expectedResponse: responses.ListProductVariantsResponse(now, "1"),
		},
		{
			desc: "happy-path - only productCode present",
			request: &api.ListProductVariantsRequest{
				ProductCode: "sampleProductCode",
			},
			loadProductVariantResponse: responses.SampleProductVariantDAOResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			expectedResponse: responses.ListProductVariantsResponse(now, "1"),
		},
		{
			desc: "error-path - code, productCode and productTemplateCode is present",
			request: &api.ListProductVariantsRequest{
				Code:                "sampleCode",
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10), apiErr.ErrInvalidRequestParameter.Message),
		},
		{
			desc:            "Error path - both productTemplateCode and productCode missing",
			request:         &api.ListProductVariantsRequest{},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusBadRequest,
				strconv.FormatInt(int64(apiErr.ErrMissingAtLeastOneField.Code), 10), apiErr.ErrMissingAtLeastOneField.Message),
		},
		{
			desc: "Error path - error while fetching records from product_template table",
			request: &api.ListProductVariantsRequest{
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			isErrorExpected:          true,
			loadProductTemplateError: errors.New("unexpected error from DB"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			desc: "Error path - error while fetching records from product table",
			request: &api.ListProductVariantsRequest{
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			isErrorExpected:             true,
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductError:            errors.New("unexpected error from DB"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			desc: "Error path - error while fetching records from product_variant table",
			request: &api.ListProductVariantsRequest{
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			isErrorExpected:             true,
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			loadProductVariantError: errors.New("unexpected error from DB"),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message),
		},
		{
			desc: "Error path - Invalid product variant versions",
			request: &api.ListProductVariantsRequest{
				ProductCode:         "sampleProductCode",
				ProductTemplateCode: "sampleProductTemplateCode",
			},
			isErrorExpected:             true,
			loadProductTemplateResponse: responses.SampleProductTemplateResponse(),
			loadProductResponse: []*storage.Product{
				responses.SampleProductDAOResponse(),
			},
			loadProductVariantResponse: responses.InvalidProductVariantVersions(),
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrNoProductVariantVersionForVersionID.Code), 10), apiErr.ErrNoProductVariantVersionForVersionID.Message),
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProduct := &storage.MockIProductDAO{}
			mockProductTemplate := &storage.MockIProductTemplateDAO{}

			productVariantFilters := []any{mock.Anything, mock.Anything, mock.Anything}
			if lo.IsNotEmpty(scenario.request.Code) {
				productVariantFilters = append(productVariantFilters, mock.Anything)
			}

			mockProductVariant.On("Find", productVariantFilters...).Return(scenario.loadProductVariantResponse, scenario.loadProductVariantError)
			mockProduct.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadProductResponse, scenario.loadProductError)
			mockProductTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(scenario.loadProductTemplateResponse, scenario.loadProductTemplateError)

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
			}
			mockStore := &storage.DBStore{
				ProductVariantDAO:  mockProductVariant,
				ProductDAO:         mockProduct,
				ProductTemplateDAO: mockProductTemplate,
			}
			service := &ProductMasterService{
				AppConfig: appConfig,
				Store:     mockStore,
			}

			response, err := service.ListProductVariants(context.Background(), scenario.request)
			if scenario.isErrorExpected {
				assert.Equal(t, scenario.expectedError, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, scenario.expectedResponse, response)
		})
	}
}

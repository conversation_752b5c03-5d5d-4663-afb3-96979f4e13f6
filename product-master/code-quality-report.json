[{"description": "errcheck: Error return value of `os.Getwd` is not checked", "check_name": "<PERSON><PERSON><PERSON>", "severity": "critical", "fingerprint": "E422892FCEBF4C9A5973ECA4DEA82BBD", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 9}}}, {"description": "errcheck: Error return value of `os.Setenv` is not checked", "check_name": "<PERSON><PERSON><PERSON>", "severity": "critical", "fingerprint": "1C5536A18E514FFA199340264ED0A6FC", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 12}}}, {"description": "errcheck: Error return value of `os.Setenv` is not checked", "check_name": "<PERSON><PERSON><PERSON>", "severity": "critical", "fingerprint": "AB76778CEDC64A0CE659172335E37B00", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 13}}}, {"description": "errcheck: Error return value of `os.Setenv` is not checked", "check_name": "<PERSON><PERSON><PERSON>", "severity": "critical", "fingerprint": "5F8A619702E6389AD1E5E8C8423D20C9", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 15}}}, {"description": "errcheck: Error return value of `os.Setenv` is not checked", "check_name": "<PERSON><PERSON><PERSON>", "severity": "critical", "fingerprint": "F9D58138BDE9B4A0FAEA988F8C08A602", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 16}}}, {"description": "errcheck: Error return value of `os.Setenv` is not checked", "check_name": "<PERSON><PERSON><PERSON>", "severity": "critical", "fingerprint": "369716E1CA4F86B4B54C495EDF92F071", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 17}}}, {"description": "errcheck: Error return value of `os.Setenv` is not checked", "check_name": "<PERSON><PERSON><PERSON>", "severity": "critical", "fingerprint": "AB69018BFB5A267ABC6CA5D8275ECF4D", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 18}}}, {"description": "stylecheck: ST1000: at least one file in a package should have a package comment", "check_name": "stylecheck", "severity": "critical", "fingerprint": "81E1AE9B4F8A822DD3922EE3BAA75E18", "location": {"path": "temp/go/api/env/env.go", "lines": {"begin": 1}}}]
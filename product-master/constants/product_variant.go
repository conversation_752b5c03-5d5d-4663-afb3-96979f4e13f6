package constants

import "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

// ProductVariantCode ...
type ProductVariantCode string

// Constants ...
const (
	// DepositAccount the product variant code is now following GXS naming, BERSAMA has a different one which is casa_account_default, and it has hard reference when calling in API
	// TODO: double check on the pv code before move to production
	DepositAccount    ProductVariantCode = "DEPOSITS_ACCOUNT"
	SavingsPocket     ProductVariantCode = "SAVINGS_POCKET"
	BoostPocket       ProductVariantCode = "BOOST_POCKET"
	LoanAccount       ProductVariantCode = "DEFAULT_FLEXI_LOAN_TERM_LOAN"
	BizDepositAccount ProductVariantCode = "BIZ_DEPOSIT_ACCOUNT"
	BizLocAccount     ProductVariantCode = "DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT"
	BizLoanAccount    ProductVariantCode = "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN"
)

// ProductVariantMap ...
var ProductVariantMap = map[api.ProductVariantCode]ProductVariantCode{
	api.ProductVariantCode_DEPOSITS_ACCOUNT:                        DepositAccount,
	api.ProductVariantCode_SAVINGS_POCKET:                          SavingsPocket,
	api.ProductVariantCode_DEFAULT_FLEXI_LOAN_TERM_LOAN:            LoanAccount,
	api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT:                     BizDepositAccount,
	api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT: BizLocAccount,
	api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN:      BizLoanAccount,
	api.ProductVariantCode_BOOST_POCKET:                            BoostPocket,
}

// ProductVariantInvalidInstructionsMap is a static map of invalid instructions for product variants.
var ProductVariantInvalidInstructionsMap = map[string][]string{
	string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_TERM_LOAN):            {BlockUnblock, Deactivation},
	string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT):       {Accelerate, WaiveOff, WriteOff},
	string(api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT): {Accelerate, WaiveOff, WriteOff},
	string(api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN):      {BlockUnblock, Deactivation},
}

// ValidProductVariantCodes is a static var of valid product variant codes
var ValidProductVariantCodes = []string{
	string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT),
	string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_TERM_LOAN),
	string(api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT),
	string(api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN),
}

// ValidInstructionTypes is a static var of valid instruction types
var ValidInstructionTypes = []string{Accelerate, WaiveOff, WriteOff, BlockUnblock, Deactivation}

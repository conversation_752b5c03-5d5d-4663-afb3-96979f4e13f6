// Package constants ...
package constants

import (
	"time"

	"github.com/shopspring/decimal"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
)

// Constants ...
const (
	// Slog Constants ...
	ServiceName                                                                = "product-master"
	CreateBaseInterestLogTag                                                   = "CreateBaseInterestLog"
	CreateBaseInterestTimeSlabRateLogTag                                       = "CreateBaseInterestTimeSlabRateLog"
	CreateBaseInterestVersionLogTag                                            = "CreateBaseInterestVersionLog"
	CreateDepositInterestLogTag                                                = "CreateDepositInterestLog"
	CreateDepositInterestAmountSlabRateLogTag                                  = "CreateDepositInterestAmountSlabRateLog"
	CreateDepositInterestVersionLogTag                                         = "CreateDepositInterestVersionLog"
	CreateGeneralLedgerLogTag                                                  = "CreateGeneralLedgerLog"
	CreateInternalAccountLogTag                                                = "CreateInternalAccountLog"
	CreateProductLogTag                                                        = "CreateProductLog"
	ListProductVariantsLogTag                                                  = "ListProductVariantsLog"
	CreateProductTemplateLogTag                                                = "CreateProductTemplateLog"
	CreateProductTemplateParameterLogTag                                       = "CreateProductTemplateParameterLog"
	CreateProductVariantLogTag                                                 = "CreateProductVariantLog"
	CreateProductVariantParameterLogTag                                        = "CreateProductVariantParameterLog"
	CreateProductVariantTransactionCatalogueInternalAccountMappingLogTag       = "CreateProductVariantTransactionCatalogueInternalAccountMappingLog"
	CreateProductVariantTransactionCatalogueMappingLogTag                      = "CreateProductVariantTransactionCatalogueMappingLog"
	CreateTransactionCatalogueLogTag                                           = "CreateTransactionCatalogueLog"
	GetBaseInterestLogTag                                                      = "GetBaseInterestLog"
	GetBaseInterestVersionLogTag                                               = "GetBaseInterestVersionLog"
	GetDepositInterestLogTag                                                   = "GetDepositInterestLog"
	GetDepositInterestAmountSlabRateLogTag                                     = "GetDepositInterestAmountSlabRateLog"
	GetDepositInterestByCodeLogTag                                             = "GetDepositInterestByCodeLog"
	GetDepositInterestVersionLogTag                                            = "GetDepositInterestVersionLog"
	GetGeneralLedgerLogTag                                                     = "GetGeneralLedgerLog"
	GetGeneralLedgerByCodeLogTag                                               = "GetGeneralLedgerByCodeLogTag"
	GetInternalAccountLogTag                                                   = "GetInternalAccountLog"
	GetBaseInterestByCodeLogTag                                                = "GetBaseInterestByCodeLogTag"
	GetInterestRateParamsForProductVariantLogTag                               = "GetInterestRateParamsForProductVariantLog"
	GetBaseInterestTimeSlabRateLogTag                                          = "GetBaseInterestTimeSlabRateLog"
	GetLatestBaseInterestVersionLogTag                                         = "GetLatestBaseInterestVersionLog"
	GetInterestSlabLogTag                                                      = "GetInterestSlabLog"
	GetInterestParametersByProductVariantLogTag                                = "GetInterestParametersByProductVariantLogTag"
	GetProductLogTag                                                           = "GetProductLog"
	GetProductByCodeLogTag                                                     = "GetProductByCodeLog"
	GetProductTemplateLogTag                                                   = "GetProductTemplateLogTag"
	GetProductTemplateParameterLogTag                                          = "GetProductTemplateParameterLog"
	GetProductVariantLogTag                                                    = "GetProductVariantLog"
	GetProductVariantByCodeLogTag                                              = "GetProductVariantByCodeLog"
	GetProductVariantParameterLogTag                                           = "GetProductVariantParameterLog"
	GetProductVariantTransactionCatalogueInternalAccountMappingLogTag          = "GetProductVariantTransactionCatalogueInternalAccountMappingLog"
	GetProductVariantTransactionCatalogueInternalAccountMappingByKeyLogTag     = "GetProductVariantTransactionCatalogueInternalAccountMappingByKeyLog"
	GetProductVariantTransactionCatalogueMappingLogTag                         = "GetProductVariantTransactionCatalogueMappingLog"
	GetTransactionCatalogueLogTag                                              = "GetTransactionCatalogueLog"
	ListEffectiveProductVariantParametersLogTag                                = "ListEffectiveProductVariantParametersLog"
	ListInternalAccountsLogTag                                                 = "ListInternalAccountsLog"
	ListProductTemplateParametersLogTag                                        = "ListProductTemplateParametersLog"
	ListProductVariantParametersLogTag                                         = "ListProductVariantParametersLog"
	UpdateGeneralLedgerStatusLogTag                                            = "UpdateGeneralLedgerStatusLog"
	UpdateInternalAccountStatusLogTag                                          = "UpdateInternalAccountStatusLog"
	UpdateProductStatusLogTag                                                  = "UpdateProductStatusLog"
	UpdateProductTemplateParameterValueLogTag                                  = "UpdateProductTemplateParameterValueLog"
	UpdateProductTemplateStatusLogTag                                          = "UpdateProductTemplateStatusLog"
	UpdateProductVariantParameterValueLogTag                                   = "UpdateProductVariantParameterValueLog"
	UpdateProductVariantStatusLogTag                                           = "UpdateProductVariantStatusLog"
	UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusLogTag = "UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusLog"
	UpdateProductVariantTransactionCatalogueMappingStatusLogTag                = "UpdateProductVariantTransactionCatalogueMappingStatusLog"
	UpdateTransactionCatalogueStatusLogTag                                     = "UpdateTransactionCatalogueStatusLog"
	CreatePocketTemplateLogTag                                                 = "CreatePocketTemplateLog"
	ListPocketTemplatesLogTag                                                  = "ListPocketTemplatesLog"
	GetPocketTemplateLogTag                                                    = "GetPocketTemplateLogTag"
	CreatePocketTemplateQuestionLogTag                                         = "CreatePocketTemplateQuestionLog"
	ListPocketTemplateQuestionsLogTag                                          = "ListPocketTemplateQuestionsLog"
	CreateProductVariantQuestionsLogTag                                        = "CreateProductVariantQuestionsLog"
	ListProductVariantQuestionsLogTag                                          = "ListProductVariantQuestionsLog"
	CreateLoanInterestLogTag                                                   = "CreateLoanInterestLog"
	GetLoanInterestByIDLogTag                                                  = "GetLoanInterestByIDLog"
	GetLoanInterestByCodeLogTag                                                = "GetLoanInterestByCodeLog"
	GetLoanPastDueParametersByProductCodeLogTag                                = "GetLoanPastDueParametersByProductCodeLog"
	CreateLoanInstructionVersionLogTag                                         = "CreateLoanInstructionVersionLog"
	CreateLoanInstructionLogTag                                                = "CreateLoanInstructionLog"
	GetLoanInstructionByCodeLogTag                                             = "GetLoanInstructionByCodeLog"
	GetAdditionalLoanParametersLogTag                                          = "GetAdditionalLoanParametersLog"
	ListLoanDocumentTypesByProductVariantLogTag                                = "ListDocumentTypesByProductVariantLog"

	// Status Value
	ACTIVE   = "ACTIVE"
	INACTIVE = "INACTIVE"
	// Product Config Constants ...
	ProductVariantID = "ProductVariantID"

	// Interest Configuration Constants ...
	Version                     = "version"
	RequestValidationFailureTag = "RequestValidationFailure"
	DatabaseErrorTag            = "DatabaseError"
	ParameterKey                = "parameterKey"

	// DepositInterestMultiplier ...
	DepositInterestMultiplier = 100

	// DefaultBoostPocketInterestCode ...
	DefaultBoostPocketInterestCode = "DEFAULT_BOOST_POCKET_INTEREST"

	// BoostPocketBonusInterestCode ...
	BoostPocketBonusInterestCode = "BOOST_POCKET_BONUS_INTEREST"

	// MaxInterestRateOfferedParameter ...
	MaxInterestRateOfferedParameter = "maximum_interest_rate_offered"

	// IdempotencyKeyHeader is the idempotency key required for the request.
	IdempotencyKeyHeader = "Idempotency-Key"

	// Year Date and Time Constants ...
	Year           = (24 * time.Hour) * 365
	HundredYears   = 100 * Year
	DeltaTimeInSec = 10

	// DaysInMonth ...
	DaysInMonth = 30

	// DaysInYear ...
	DaysInYear = 365

	// Day ,Year and Month as TenorUnits
	DayTenorUnit   = "DAY"
	YearTenorUnit  = "YEAR"
	MonthTenorUnit = "MONTH"

	// Default Configs
	DefaultCurrency = "SGD"
)

// ParameterStatus ...
var ParameterStatus = map[api.Status]bool{
	api.Status_N: false,
	api.Status_Y: true,
}

// PocketTemplateSortingConfig is the config to hold the pocket template sorting sequence, need to make sure the latest pocket template name is being used here. Smaller number will rank higher.
var PocketTemplateSortingConfig = map[string]int{
	"Custom":            8,
	"Education":         7,
	"Emergency Savings": 1,
	"Holiday":           2,
	"Investment":        3,
	"Home":              4,
	"Vehicle":           5,
	"Health & Fitness":  6,
}

var (
	// DepositInterestMultiplierInDecimal ...
	DepositInterestMultiplierInDecimal = decimal.NewFromInt(DepositInterestMultiplier)
)

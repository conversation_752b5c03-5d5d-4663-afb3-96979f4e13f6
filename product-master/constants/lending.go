package constants

// lending constants ...
const (
	// Loan account interest params constants ...
	NormalInterest    = "DEFAULT_FLEXI_LOAN_TERM_LOAN_INTEREST_NORMAL"
	PenalInterest     = "DEFAULT_FLEXI_LOAN_TERM_LOAN_INTEREST_PENAL"
	DiscoveryInterest = "DEFAULT_FLEXI_LOAN_TERM_LOAN_INTEREST_DISCOVERY"

	// Accelerate Loan account off-boarding instruction types ...
	Accelerate   = "ACCELERATE"
	WriteOff     = "WRITE_OFF"
	WaiveOff     = "WAIVE_OFF"
	Deactivation = "DEACTIVATION"
	BlockUnblock = "BLOCK_UNBLOCK"

	// BizNormalInterest BIZ Loan account interest params constants ...
	BizNormalInterest = "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN_INTEREST_NORMAL"
	BizPenalInterest  = "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN_INTEREST_PENAL"

	// LoanDocumentSubmissionOptionVersionStatusActive loan document submission option version status active string
	LoanDocumentSubmissionOptionVersionStatusActive = "ACTIVE"
)

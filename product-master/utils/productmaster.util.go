// Package utils : Utilities packages
package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"

	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetTraceID ...
func GetTraceID(ctx context.Context) slog.Tag {
	span := commonCtx.GetSpan(ctx)
	return slog.TraceID(SafeIntToString(span.Context().TraceID()))
}

// ToJSON converts struct to JSON string
func ToJSON(i interface{}) string {
	s, _ := json.Marshal(i)
	return string(s)
}

// SafeIntToString converts int to string
func SafeIntToString[T uint64 | int](i T) string {
	return fmt.Sprintf("%d", i)
}

// MustConvertToInt64 converts uint to int
// nolint: gosec
func MustConvertToInt64(u uint64) int64 {
	if u > math.MaxInt64 {
		panic(fmt.Errorf("overflow: cannot convert %d to int64", u))
	}
	return int64(u)
}

// MustConvertToUint64 converts int to uint
// nolint: gosec
func MustConvertToUint64(u int64) uint64 {
	if u < 0 {
		panic(fmt.Errorf("overflow: cannot convert %d to uint64", u))
	}
	return uint64(u)
}

// ConvertToInterfaceSlice ....
func ConvertToInterfaceSlice[T any](items []T) []any {
	return lo.Map(items, func(item T, _ int) any {
		return item
	})
}

// ConvertTenorToDays converts given tenor in DAYS/MONTHS/YEARS to DAYS. If given tenor value
// cannot be parsed to an integer, an error is returned.
func ConvertTenorToDays(ctx context.Context, tenor, tenorUnit string) (int64, error) {
	tenorInInt, err := strconv.Atoi(tenor)
	if err != nil {
		return 0, err
	}

	switch tenorUnit {
	case constants.DayTenorUnit:
		return int64(tenorInInt), nil
	case constants.MonthTenorUnit:
		return int64(constants.DaysInMonth * tenorInInt), nil
	case constants.YearTenorUnit:
		return int64(constants.DaysInYear * tenorInInt), nil
	default:
		return 0, fmt.Errorf("cannot convert tenor. invalid tenor unit %s", tenorUnit)
	}
}

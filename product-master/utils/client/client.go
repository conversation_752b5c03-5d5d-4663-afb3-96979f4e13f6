package client

import (
	"net/http"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"

	"gitlab.myteksi.net/dakota/common/tracing"
	"gitlab.myteksi.net/dakota/klient"
	hermesClient "gitlab.myteksi.net/dbmy/hermes/api/client"
)

// HermesClient ...
func HermesClient(appCfg *config.AppConfig, tracer tracing.Tracer) (*hermesClient.HermesClient, error) {
	return hermesClient.NewHermesClient(
		appCfg.HermesConfig.BaseURL,
		klient.WithHTTPClient(makeHTTPClient(appCfg)),
		klient.WithTracing(tracer),
	)
}

func makeHTTPClient(appCfg *config.AppConfig) *http.Client {
	httpClient := &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: appCfg.ProductMasterConfig.ClientConfig.MaxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(appCfg.ProductMasterConfig.ClientConfig.IdleConnTimeoutInMillis) * time.Millisecond,
		},
		Timeout: time.Duration(appCfg.ProductMasterConfig.ClientConfig.TimeoutInMillis) * time.Millisecond,
	}
	return httpClient
}

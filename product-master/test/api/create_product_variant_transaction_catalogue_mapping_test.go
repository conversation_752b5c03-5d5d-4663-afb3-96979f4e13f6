package testapi

import (
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateProductVariantTransactionCatalogueMapping", func() {
	var mockProductVariantTransactionCatalogueMapping *storage.MockIProductVariantTransactionCatalogueMappingDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mappingDAO1 := &storage.MockITransactionCatalogueDAO{}
		mappingDAO1.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{}}, nil)
		mappingDAO2 := &storage.MockIProductVariantDAO{}
		mappingDAO2.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{}}, nil)

		mockProductVariantTransactionCatalogueMapping = &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
		service.Store = &storage.DBStore{
			ProductVariantTransactionCatalogueMappingDAO: mockProductVariantTransactionCatalogueMapping,
			TransactionCatalogueDAO:                      mappingDAO1,
			ProductVariantDAO:                            mappingDAO2,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariantTransactionCatalogueMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueMapping{{
					PublicID:               "test-id",
					ProductVariantID:       1,
					TransactionCatalogueID: 1,
					Status:                 "ACTIVE",
					CreatedBy:              "api-test",
					CreatedAt:              now,
					UpdatedBy:              "api-test",
					UpdatedAt:              now,
				}}, nil)
				mockProductVariantTransactionCatalogueMapping.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductVariantTransactionCatalogueMappingURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"transactionCatalogueID": "test-transaction-catalogue-id",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productVariantTransactionCatalogueMapping": {
											"id":  "test-id",
											"productVariantID": "test-product-variant-id",
											"transactionCatalogueID": "test-transaction-catalogue-id",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
										}
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueMappingURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"transactionCatalogueID": "test-transaction-catalogue-id",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Product Variant id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueMappingURL,
					hcl.JSON(`{
										"productVariantID": "",
										"transactionCatalogueID": "test-transaction-catalogue-id",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductVariantIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Transaction catalogue id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueMappingURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"transactionCatalogueID": "",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingTransactionCatalogueIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created by is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueMappingURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"transactionCatalogueID": "test-transaction-catalogue-id",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockProductVariantTransactionCatalogueMapping.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateProductVariantTransactionCatalogueMappingURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"transactionCatalogueID": "test-transaction-catalogue-id",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProductVariantTransactionCatalogueMapping.On("Find", mock.Anything, mock.Anything).Return(
					[]*storage.ProductVariantTransactionCatalogueMapping{}, errors.New("database load error"))
				mockProductVariantTransactionCatalogueMapping.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductVariantTransactionCatalogueMappingURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"transactionCatalogueID": "test-transaction-catalogue-id",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

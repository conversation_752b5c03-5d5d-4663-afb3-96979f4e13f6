package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetDepositInterestVersion", func() {
	var mockDepositInterestVersion *storage.MockIDepositInterestVersionDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockDepositInterest := &storage.MockIDepositInterestDAO{}
		mockDepositInterest.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.DepositInterest{PublicID: "test-deposit-interest-id"}, nil)

		mockDepositInterestVersion = &storage.MockIDepositInterestVersionDAO{}
		service.Store = &storage.DBStore{
			DepositInterestVersionDAO: mockDepositInterestVersion,
			DepositInterestDAO:        mockDepositInterest,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestVersion{{
					PublicID:          "test-id",
					DepositInterestID: 1,
					Version:           "1",
					EffectiveDate:     now,
					Description: sql.NullString{
						String: "get test deposit interest version",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetDepositInterestVersionURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterestVersion": {
											"id": "test-id",
											"depositInterestID": "test-deposit-interest-id",
											"version": "1",
											"effectiveDate":` + `"` + string(nowString) + `",` + `
											"description": "get test deposit interest version",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("deposit interest version not found in database", func() {
			It("return error", func() {
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestVersion{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetDepositInterestVersionURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestVersion{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetDepositInterestVersionURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

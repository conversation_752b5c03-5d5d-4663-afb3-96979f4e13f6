package testapi

import (
	"encoding/json"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

var _ = Describe("ListPocketTemplates", func() {
	var xfccHeader hcl.RequestModifier
	var mockPocketTemplateDAO *storage.MockIPocketTemplateDAO
	var mockPocketTemplateImageSuggestion *storage.MockIPocketTemplateImageSuggestionDAO
	now := time.Now().UTC()

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		mockPocketTemplateDAO = &storage.MockIPocketTemplateDAO{}
		mockPocketTemplateImageSuggestion = &storage.MockIPocketTemplateImageSuggestionDAO{}
		mockHermes = &hermesMock.Hermes{}

		service.HermesClient = mockHermes
		service.Store = &storage.DBStore{
			PocketTemplateDAO:                mockPocketTemplateDAO,
			PocketTemplateImageSuggestionDAO: mockPocketTemplateImageSuggestion,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateImageSuggestionMockDBResponse(), nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(responses.ListImageDetailsResponseFromHermes(), nil)

				resp, err := client.Get(strings.Replace(GetPocketTemplateURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				responseObj := &api.GetPocketTemplateResponse{}
				err = json.Unmarshal(resp.Body.Bytes, responseObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(responseObj).Should(Equal(responses.GetPocketTemplateResponse()))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Pocket template not found in database", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplate{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetPocketTemplateURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template image suggestion not found in database", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplateImageSuggestion{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetPocketTemplateURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Pocket template database load error", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplate{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetPocketTemplateURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template image suggestion database load error", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplateImageSuggestion{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetPocketTemplateURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("External service errors", func() {
		When("Pixie error", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateImageSuggestionMockDBResponse(), nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(&hermes.GetDocumentsResponse{}, errors.New("error from hermes"))

				resp, err := client.Get(strings.Replace(GetPocketTemplateURL, ":id", "test-id", -1), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
			})
		})
	})
})

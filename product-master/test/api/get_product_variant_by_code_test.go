package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetProductVariantByCode", func() {
	var xfccHeader hcl.RequestModifier
	var mockProductVariant *storage.MockIProductVariantDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockProduct := &storage.MockIProductDAO{}
		mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{PublicID: "test-product"}, nil)

		mockProductVariant = &storage.MockIProductVariantDAO{}
		service.Store = &storage.DBStore{
			ProductVariantDAO: mockProductVariant,
			ProductDAO:        mockProduct,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{
					{
						PublicID:  "test-id",
						ProductID: 1,
						Code:      "test-product-variant-code",
						Name:      "test-product-variant-name",
						Version:   "1.0.0",
						Description: sql.NullString{
							String: "create test product variant",
							Valid:  true,
						},
						ValidFrom: now,
						Status:    "ACTIVE",
						CreatedBy: "api-test",
						CreatedAt: now,
						UpdatedBy: "api-test",
						UpdatedAt: now,
					},
				}, nil)

				resp, err := client.Get(strings.Replace(GetProductVariantByCodeURL, ":code", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
											"productVariant": {
										  "id": "test-id",
										  "productID": "test-product",
										  "code": "test-product-variant-code",
										  "version": "1.0.0",
										  "name": "test-product-variant-name",
										  "description": "create test product variant",
										  "status": "ACTIVE",
										  "validFrom":` + `"` + string(nowString) + `",` + `
										  "validTo": "0001-01-01T00:00:00Z",
										  "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
            }
          }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Get(strings.Replace(GetProductVariantByCodeURL, ":code", "", -1), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetProductVariantByCodeURL, ":code", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetProductVariantByCodeURL, ":code", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateGeneralLedger", func() {
	var mockGeneralLedgerDAO *storage.MockIGeneralLedgerDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockGeneralLedgerDAO = &storage.MockIGeneralLedgerDAO{}
		service.Store = &storage.DBStore{
			GeneralLedgerDAO: mockGeneralLedgerDAO,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{
					PublicID: "test-id",
					Code:     "test-general-ledger-code",
					Name:     "test-general-ledger-name",
					Description: sql.NullString{
						String: "create test general ledger",
						Valid:  true,
					},
					Status:    "ACTIVE",
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockGeneralLedgerDAO.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"code": "test-general-ledger-code",
										"name": "test-general-ledger-name",
										"description": "create test general ledger",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"generalLedger": {
											"id": "test-id",
											"code": "test-general-ledger-code",
											"name": "test-general-ledger-name",
											"description": "create test general ledger",
											"status": "ACTIVE",
											"currency": ` + `"` + currency + `",` + `
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"code": "test-general-ledger-code",
										"name": "test-general-ledger-name",
										"description": "create test general-ledger",
										"createdBy": "api-test",
										"currency": `+`"`+currency+`"`+`
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid currency", func() {
			It("return error", func() {
				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"currency": "INVALID_X",
										"code": "test-general-ledger-code",
										"name": "test-general-ledger-name",
										"description": "create test general-ledger",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Currency is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"currency": "",
										"code": "test-general-ledger-code",
										"name": "test-general-ledger-name",
										"description": "create test general-ledger",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"code": "",
										"name": "test-general-ledger-name",
										"description": "create test general-ledger",
										"createdBy": "api-test",
										"currency": `+`"`+currency+`"`+`
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"code": "test-general-ledger-code",
										"name": "",
										"description": "create test general-ledger",
										"createdBy": "api-test",
										"currency": `+`"`+currency+`"`+`
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"code": "test-general-ledger-code",
										"name": "test-general-ledger-name",
										"description": "create test general-ledger",
										"currency": `+`"`+currency+`",`+`
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockGeneralLedgerDAO.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"code": "test-general-ledger-code",
										"name": "test-general-ledger-name",
										"description": "create test general-ledger",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{}, errors.New("database load error"))
				mockGeneralLedgerDAO.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateGeneralLedgerURL,
					hcl.JSON(`{
										"code": "test-general-ledger-code",
										"name": "test-general-ledger-name",
										"description": "create test general-ledger",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

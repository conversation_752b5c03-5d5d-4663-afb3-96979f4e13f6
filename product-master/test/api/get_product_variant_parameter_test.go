package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetProductVariantParameter", func() {
	var mockProductVariantParameter *storage.MockIProductVariantParameterDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductVariantDAO := &storage.MockIProductVariantDAO{}
		mockProductVariantDAO.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariant{PublicID: "test-product-variant-id-1"}, nil)

		mockProductVariantParameter = &storage.MockIProductVariantParameterDAO{}
		service.Store = &storage.DBStore{
			ProductVariantParameterDAO: mockProductVariantParameter,
			ProductVariantDAO:          mockProductVariantDAO,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-id",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-parameter-key",
					ParameterValue:   "test-parameter-value",
					DataType:         "STRING",
					OverrideLevel:    "NO_OVERRIDE",
					ExceptionLevel: sql.NullString{
						String: "",
						Valid:  false,
					},
					Description: sql.NullString{
						String: "create test product variant parameter",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetProductVariantParameterURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productVariantParameter": {
											"id": "test-id",
											"productVariantID": "test-product-variant-id-1",
											"namespace": "test-namespace",
											"parameterKey": "test-parameter-key",
											"parameterValue": "test-parameter-value",
											"dataType": "STRING",
											"overrideLevel": "NO_OVERRIDE",
											"description": "create test product variant parameter",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
										}
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product variant parameter not found in database", func() {
			It("return error", func() {
				mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetProductVariantParameterURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetProductVariantParameterURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

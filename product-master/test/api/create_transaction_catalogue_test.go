package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateTransactionCatalogue", func() {
	var mockTransactionCatalogue *storage.MockITransactionCatalogueDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockTransactionCatalogue = &storage.MockITransactionCatalogueDAO{}
		service.Store = &storage.DBStore{
			TransactionCatalogueDAO: mockTransactionCatalogue,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{
					PublicID:       "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description: sql.NullString{
						String: "create transaction catalogue",
						Valid:  true,
					},
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockTransactionCatalogue.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "test-txn-sub-type",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"transactionCatalogue": {
											"id": "test-id",
											"domain": "test-domain",
											"isFinancialTxn": true,
											"txnType": "test-txn-type",
											"txnSubType": "test-txn-sub-type",
											"displayName": "test-display-name",
											"description": "create transaction catalogue",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                      }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "test-txn-sub-type",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Domain is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "test-txn-sub-type",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingDomainResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Txn type is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "",
										"txnSubType": "test-txn-sub-type",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingTxnTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Txn SubType is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingTxnSubTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Display name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "test-txn-sub-type",
										"displayName": "",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingDisplayNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created by is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "test-txn-sub-type",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockTransactionCatalogue.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "test-txn-sub-type",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{}, errors.New("database load error"))
				mockTransactionCatalogue.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateTransactionCatalogueURL,
					hcl.JSON(`{
										"domain": "test-domain",
										"isFinancialTxn": true,
										"txnType": "test-txn-type",
										"txnSubType": "test-txn-sub-type",
										"displayName": "test-display-name",
										"description": "create transaction catalogue",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetBaseInterestTimeSlabRate", func() {
	var mockBaseInterestTimeSlabRate *storage.MockIBaseInterestTimeSlabRateDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockBaseInterestVersion := &storage.MockIBaseInterestVersionDAO{}
		mockBaseInterestVersion.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.BaseInterestVersion{PublicID: "test-base-version-id"}, nil)

		mockBaseInterestTimeSlabRate = &storage.MockIBaseInterestTimeSlabRateDAO{}
		service.Store = &storage.DBStore{
			BaseInterestTimeSlabRateDAO: mockBaseInterestTimeSlabRate,
			BaseInterestVersionDAO:      mockBaseInterestVersion,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockBaseInterestTimeSlabRate.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestTimeSlabRate{{
					PublicID:              "test-id",
					BaseInterestVersionID: 1,
					TermUnit:              string(api.TermUnit_MONTH),
					TermValue:             5,
					BaseRatePercentage:    "5.10",
					CreatedBy:             "api-test",
					CreatedAt:             now,
					UpdatedBy:             "api-test",
					UpdatedAt:             now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetBaseInterestTimeSlabRateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"baseInterestTimeSlabRate": {
											"id": "test-id",
											"baseInterestVersionID": "test-base-version-id",
											"termUnit": "MONTH",
										    "termValue": 5,
										    "baseInterestPercentage": "5.10",
										    "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("base interest time slab rate not found in database", func() {
			It("return error", func() {
				mockBaseInterestTimeSlabRate.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestTimeSlabRate{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetBaseInterestTimeSlabRateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockBaseInterestTimeSlabRate.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestTimeSlabRate{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetBaseInterestTimeSlabRateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

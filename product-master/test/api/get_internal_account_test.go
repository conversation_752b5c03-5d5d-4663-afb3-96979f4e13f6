package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetInternalAccount", func() {
	var mockInternalAccount *storage.MockIInternalAccountDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
		mockGeneralLedger.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.GeneralLedger{PublicID: "test-general-ledger-id"}, nil)

		mockInternalAccount = &storage.MockIInternalAccountDAO{}
		service.Store = &storage.DBStore{
			InternalAccountDAO: mockInternalAccount,
			GeneralLedgerDAO:   mockGeneralLedger,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  currency,
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetInternalAccountURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"internalAccount": {
											"id": "test-id",
											"generalLedgerID": "test-general-ledger-id",
											"code": "test-internal-account-code",
											"name": "test-internal-account-name",
											"description": "create test internal account",
											"currency":` + `"` + currency + `",` + `
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("internal account not found in database", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetInternalAccountURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetInternalAccountURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

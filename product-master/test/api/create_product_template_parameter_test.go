package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateProductTemplateParameter", func() {
	var mockProductTemplateParameter *storage.MockIProductTemplateParameterDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductTemplate := &storage.MockIProductTemplateDAO{}
		mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{}}, nil)

		mockProductTemplateParameter = &storage.MockIProductTemplateParameterDAO{}
		service.Store = &storage.DBStore{
			ProductTemplateParameterDAO: mockProductTemplateParameter,
			ProductTemplateDAO:          mockProductTemplate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{{
					PublicID:          "test-id",
					ProductTemplateID: 1,
					Namespace:         "test-namespace",
					ParameterKey:      "test-parameter-key",
					ParameterValue:    "test-parameter-value",
					DataType:          "STRING",
					OverrideLevel:     "NO_OVERRIDE",
					ExceptionLevel: sql.NullString{
						String: "",
						Valid:  false,
					},
					Description: sql.NullString{
						String: "create test product template parameter",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockProductTemplateParameter.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productTemplateParameter": {
											"id": "test-id",
											"productTemplateID": "test-product-template-id-1",
											"namespace": "test-namespace",
											"parameterKey": "test-parameter-key",
											"parameterValue": "test-parameter-value",
											"dataType": "STRING",
											"overrideLevel": "NO_OVERRIDE",
											"description": "create test product template parameter",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
										}
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Product template id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductTemplateIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Namespace is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNamespaceResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Parameter key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingParameterKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Parameter value is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingParameterValueResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Data type is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidParameterDatatypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Override level is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidParameterOverrideLevelResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid data type", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "INVALID_TYPE",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidParameterDatatypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid override level", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "INVALID_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidParameterOverrideLevelResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockProductTemplateParameter.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{}, errors.New("database load error"))
				mockProductTemplateParameter.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductTemplateParameterURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"namespace": "test-namespace",
										"parameterKey": "test-parameter-key",
										"parameterValue": "test-parameter-value",
										"dataType": "STRING",
										"overrideLevel": "NO_OVERRIDE",
										"description": "create test product template parameter",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("UpdateProductVariantStatus", func() {
	var mockProductVariant *storage.MockIProductVariantDAO

	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	nowPlus10 := time.Now().Add(10 * time.Minute).UTC()
	nowPlus10String, _ := nowPlus10.MarshalText()

	BeforeEach(func() {
		mockProduct := &storage.MockIProductDAO{}
		mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{PublicID: "test-product"}, nil)

		mockProductVariant = &storage.MockIProductVariantDAO{}
		service.Store = &storage.DBStore{
			ProductVariantDAO: mockProductVariant,
			ProductDAO:        mockProduct,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case - ACTIVE to INACTIVE", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: nowPlus10,
				}}, nil).Once()
				mockProductVariant.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "INACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: nowPlus10,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
											"productVariant": {
										  "id": "test-id",
										  "productID": "test-product",
										  "code": "test-product-variant-code",
										  "version": "1.0.0",
										  "name": "test-product-variant-name",
										  "description": "create test product variant",
										  "status": "INACTIVE",
										  "validFrom":` + `"` + string(nowString) + `",` + `
										  "validTo": "0001-01-01T00:00:00Z",
										  "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowPlus10String) + `"` + `
            }
          }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
			It("returns 200 happy case - INACTIVE to ACTIVE", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "INACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "INACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: nowPlus10,
				}}, nil).Once()
				mockProductVariant.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "INACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: nowPlus10,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "ACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
											"productVariant": {
										  "id": "test-id",
										  "productID": "test-product",
										  "code": "test-product-variant-code",
										  "version": "1.0.0",
										  "name": "test-product-variant-name",
										  "description": "create test product variant",
										  "status": "ACTIVE",
										  "validFrom":` + `"` + string(nowString) + `",` + `
										  "validTo": "0001-01-01T00:00:00Z",
										  "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowPlus10String) + `"` + `
            }
          }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Id is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Status is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid status", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INVALID_STATUS",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Updated by is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingUpdatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, data.ErrNoData)

				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database update error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockProductVariant.On("Update", mock.Anything, mock.Anything).Return(errors.New("database update error"))

				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseUpdateResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, errors.New("database load error"))
				mockProductVariant.On("Update", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Put(strings.Replace(UpdateProductVariantStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

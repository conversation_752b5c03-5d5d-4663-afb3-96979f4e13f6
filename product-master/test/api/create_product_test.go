package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateProduct", func() {
	var mockProduct *storage.MockIProductDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductTemplate := &storage.MockIProductTemplateDAO{}
		mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{}}, nil)

		mockProduct = &storage.MockIProductDAO{}
		service.Store = &storage.DBStore{
			ProductDAO:         mockProduct,
			ProductTemplateDAO: mockProductTemplate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProduct.On("Find", mock.Anything, mock.Anything).Return([]*storage.Product{{
					PublicID:          "test-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Description: sql.NullString{
						String: "create test product",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockProduct.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"code": "test-product-code",
										"name": "test-product-name",
										"description": "create test product",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"product": {
											"id": "test-id",
											"productTemplateID": "test-product-template-id-1",
											"code": "test-product-code",
											"name": "test-product-name",
											"description": "create test product",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"code": "test-product-code",
										"name": "test-product-name",
										"description": "create test product",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Product template id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "",
										"code": "test-product-code",
										"name": "test-product-name",
										"description": "create test product",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductTemplateIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"code": "",
										"name": "test-product-name",
										"description": "create test product",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"code": "test-product-code",
										"name": "",
										"description": "create test product",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"code": "test-product-code",
										"name": "test-product-name",
										"description": "create test product",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockProduct.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"code": "test-product-code",
										"name": "test-product-name",
										"description": "create test product",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProduct.On("Find", mock.Anything, mock.Anything).Return([]*storage.Product{}, errors.New("database load error"))
				mockProduct.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductURL,
					hcl.JSON(`{
										"productTemplateID": "test-product-template-id-1",
										"code": "test-product-code",
										"name": "test-product-name",
										"description": "create test product",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("UpdateGeneralLedgerStatus", func() {
	var mockGeneralLedgerDAO *storage.MockIGeneralLedgerDAO

	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	nowMinus10String, _ := nowMinus10.MarshalText()
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockGeneralLedgerDAO = &storage.MockIGeneralLedgerDAO{}
		service.Store = &storage.DBStore{
			GeneralLedgerDAO: mockGeneralLedgerDAO,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case - ACTIVE to INACTIVE", func() {
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{
					PublicID: "test-id",
					Code:     "test-general-ledger-code",
					Name:     "test-general-ledger-name",
					Description: sql.NullString{
						String: "create test general ledger",
						Valid:  true,
					},
					Status:    "ACTIVE",
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: nowMinus10,
				}}, nil).Once()
				mockGeneralLedgerDAO.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{
					PublicID: "test-id",
					Code:     "test-general-ledger-code",
					Name:     "test-general-ledger-name",
					Description: sql.NullString{
						String: "create test general ledger",
						Valid:  true,
					},
					Status:    "INACTIVE",
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"generalLedger": {
											"id": "test-id",
											"code": "test-general-ledger-code",
											"name": "test-general-ledger-name",
											"description": "create test general ledger",
											"status": "INACTIVE",
											"currency":` + `"` + currency + `",` + `
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
			It("returns 200 happy case - INACTIVE to ACTIVE", func() {
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{
					PublicID: "test-id",
					Code:     "test-general-ledger-code",
					Name:     "test-general-ledger-name",
					Description: sql.NullString{
						String: "create test general ledger",
						Valid:  true,
					},
					Status:    "INACTIVE",
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: nowMinus10,
				}}, nil).Once()
				mockGeneralLedgerDAO.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{
					PublicID: "test-id",
					Code:     "test-general-ledger-code",
					Name:     "test-general-ledger-name",
					Description: sql.NullString{
						String: "create test general ledger",
						Valid:  true,
					},
					Status:    "ACTIVE",
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "ACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"generalLedger": {
											"id": "test-id",
											"code": "test-general-ledger-code",
											"name": "test-general-ledger-name",
											"description": "create test general ledger",
											"status": "ACTIVE",
											"currency":` + `"` + currency + `",` + `
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Id is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Status is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid status", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INVALID_STATUS",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Updated by is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingUpdatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("General Ledger not found in database", func() {
			It("return error", func() {
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{}, data.ErrNoData)

				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database update error", func() {
			It("return error", func() {
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{
					PublicID: "test-id",
					Code:     "test-general-ledger-code",
					Name:     "test-general-ledger-name",
					Description: sql.NullString{
						String: "create test general ledger",
						Valid:  true,
					},
					Status:    "INACTIVE",
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockGeneralLedgerDAO.On("Update", mock.Anything, mock.Anything).Return(errors.New("database update error"))

				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseUpdateResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockGeneralLedgerDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{}, errors.New("database load error"))
				mockGeneralLedgerDAO.On("Update", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Put(strings.Replace(UpdateGeneralLedgerStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

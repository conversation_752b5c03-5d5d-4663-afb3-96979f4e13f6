package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("UpdateProductTemplateStatus", func() {
	var mockProductTemplate *storage.MockIProductTemplateDAO

	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	nowMinus10String, _ := nowMinus10.MarshalText()
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductTemplate = &storage.MockIProductTemplateDAO{}
		service.Store = &storage.DBStore{
			ProductTemplateDAO: mockProductTemplate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case - ACTIVE to INACTIVE", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{
					PublicID: "test-id",
					Name:     "test-product-template-name",
					Description: sql.NullString{
						String: "create test product template",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: nowMinus10,
				}}, nil).Once()
				mockProductTemplate.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{
					PublicID: "test-id",
					Name:     "test-product-template-name",
					Description: sql.NullString{
						String: "create test product template",
						Valid:  true,
					},
					Status:    "INACTIVE",
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productTemplate": {
											"id": "test-id",
											"name": "test-product-template-name",
											"description": "create test product template",
											"status": "INACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                       }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
			It("returns 200 happy case - INACTIVE to ACTIVE", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{
					PublicID: "test-id",
					Name:     "test-product-template-name",
					Description: sql.NullString{
						String: "create test product template",
						Valid:  true,
					},
					Status:    "INACTIVE",
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: nowMinus10,
				}}, nil).Once()
				mockProductTemplate.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{
					PublicID: "test-id",
					Name:     "test-product-template-name",
					Description: sql.NullString{
						String: "create test product template",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "ACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productTemplate": {
											"id": "test-id",
											"name": "test-product-template-name",
											"description": "create test product template",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                       }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Id is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Status is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid status", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INVALID_STATUS",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Updated by is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingUpdatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product not found in database", func() {
			It("return error", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{}, data.ErrNoData)

				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database update error", func() {
			It("return error", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{
					PublicID: "test-id",
					Name:     "test-product-template-name",
					Description: sql.NullString{
						String: "create test product template",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: nowMinus10,
				}}, nil).Once()
				mockProductTemplate.On("Update", mock.Anything, mock.Anything).Return(errors.New("database update error"))

				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseUpdateResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{}, errors.New("database load error"))
				mockProductTemplate.On("Update", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Put(strings.Replace(UpdateProductTemplateStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

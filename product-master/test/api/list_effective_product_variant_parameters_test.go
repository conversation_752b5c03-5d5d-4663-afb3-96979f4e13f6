package testapi

import (
	"encoding/json"
	"errors"
	"strings"

	api "gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("ListEffectiveProductVariantParameters", func() {
	var xfccHeader hcl.RequestModifier
	dbError := errors.New("database update error")
	errNoData := data.ErrNoData
	var mockProductTemplateParameter *storage.MockIProductTemplateParameterDAO
	var mockProductVariant *storage.MockIProductVariantDAO
	var mockProductVariantParameters *storage.MockIProductVariantParameterDAO
	var mockProduct *storage.MockIProductDAO

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockProductTemplate := &storage.MockIProductTemplateDAO{}
		mockProductTemplate.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductTemplate{PublicID: "test-product-template-id-1"}, nil)

		mockProductVariant = &storage.MockIProductVariantDAO{}
		mockProductVariantParameters = &storage.MockIProductVariantParameterDAO{}
		mockProduct = &storage.MockIProductDAO{}
		mockProductTemplateParameter = &storage.MockIProductTemplateParameterDAO{}
		service.Store = &storage.DBStore{
			ProductTemplateParameterDAO: mockProductTemplateParameter,
			ProductDAO:                  mockProduct,
			ProductVariantDAO:           mockProductVariant,
			ProductVariantParameterDAO:  mockProductVariantParameters,
			ProductTemplateDAO:          mockProductTemplate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-product-variant-parameter-id-1",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-variant-parameter-1",
					ParameterValue:   "test-product-variant-parameter-value-1",
					DataType:         "INT",
					OverrideLevel:    "none",
				}}, nil)

				mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{
					PublicID:          "test-product-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Status:            "ACTIVE",
				}, nil)

				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{{
					PublicID:          "test-product-variant-parameter-id-1",
					ProductTemplateID: 1,
					Namespace:         "test-namespace",
					ParameterKey:      "test-product-template-parameter-2",
					ParameterValue:    "test-product-template-parameter-value-2",
					DataType:          "INT",
					OverrideLevel:     "none",
				}}, nil)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
                                        "productVariantParameters": [
                                          {
                                            "id": "test-product-variant-parameter-id-1",
                                            "productVariantID": "test-product-variant-id",
                                            "namespace": "test-namespace",
                                            "parameterKey": "test-product-template-parameter-2",
                                            "parameterValue": "test-product-template-parameter-value-2",
                                            "dataType": "INT",
                                            "overrideLevel": "none",
                                            "createdAt": "0001-01-01T00:00:00Z",
                                            "updatedAt": "0001-01-01T00:00:00Z"
                                          },
                                          {
                                            "id": "test-product-variant-parameter-id-1",
                                            "productVariantID": "test-product-variant-id",
                                            "namespace": "test-namespace",
                                            "parameterKey": "test-product-variant-parameter-1",
                                            "parameterValue": "test-product-variant-parameter-value-1",
                                            "dataType": "INT",
                                            "overrideLevel": "none",
                                            "createdAt": "0001-01-01T00:00:00Z",
                                            "updatedAt": "0001-01-01T00:00:00Z"
                                          }
                                        ]
                                      }`

				actualParams := api.ListEffectiveProductVariantParametersResponse{}
				expectedParams := api.ListEffectiveProductVariantParametersResponse{}
				json.Unmarshal([]byte(expectedResponse), &expectedParams)
				json.Unmarshal([]byte(resp.Body.String), &actualParams)
				Expect(common.UnorderedEqual(actualParams.ProductVariantParameters, expectedParams.ProductVariantParameters)).Should(Equal(true))
			})
			It("returns 200 happy case - overriding parameter value", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-product-variant-parameter-id-1",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-common-parameter",
					ParameterValue:   "test-product-common-parameter-variant-value",
					DataType:         "INT",
					OverrideLevel:    "none",
				}}, nil)

				mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{
					PublicID:          "test-product-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Status:            "ACTIVE",
				}, nil)

				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{{
					PublicID:          "test-product-variant-parameter-id-1",
					ProductTemplateID: 1,
					Namespace:         "test-namespace",
					ParameterKey:      "test-product-common-parameter",
					ParameterValue:    "test-product-common-parameter-template-value",
					DataType:          "INT",
					OverrideLevel:     "none",
				}}, nil)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
                                        "productVariantParameters": [
                                          {
                                            "id": "test-product-variant-parameter-id-1",
                                            "productVariantID": "test-product-variant-id",
                                            "namespace": "test-namespace",
                                            "parameterKey": "test-product-common-parameter",
                                            "parameterValue": "test-product-common-parameter-variant-value",
                                            "dataType": "INT",
                                            "overrideLevel": "none",
                                            "createdAt": "0001-01-01T00:00:00Z",
                                            "updatedAt": "0001-01-01T00:00:00Z"
                                          }
                                        ]
                                      }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
			It("returns 200 happy case - no variant params", func() {

				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{}, nil)

				mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{
					PublicID:          "test-product-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Status:            "ACTIVE",
				}, nil)

				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{{
					PublicID:          "test-product-variant-parameter-id-1",
					ProductTemplateID: 1,
					Namespace:         "test-namespace",
					ParameterKey:      "test-product-template-parameter-2",
					ParameterValue:    "test-product-template-parameter-value-2",
					DataType:          "INT",
					OverrideLevel:     "none",
				}}, nil)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
                                        "productVariantParameters": [
                                          {
                                            "id": "test-product-variant-parameter-id-1",
                                            "productVariantID": "test-product-variant-id",
                                            "namespace": "test-namespace",
                                            "parameterKey": "test-product-template-parameter-2",
                                            "parameterValue": "test-product-template-parameter-value-2",
                                            "dataType": "INT",
                                            "overrideLevel": "none",
                                            "createdAt": "0001-01-01T00:00:00Z",
                                            "updatedAt": "0001-01-01T00:00:00Z"
                                          }
                                        ]
                                      }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
			It("returns 200 happy case - no template params", func() {

				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-product-variant-parameter-id-1",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-variant-parameter-1",
					ParameterValue:   "test-product-variant-parameter-value-1",
					DataType:         "INT",
					OverrideLevel:    "none",
				}}, nil)

				mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{
					PublicID:          "test-product-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Status:            "ACTIVE",
				}, nil)

				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{}, nil)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
                                        "productVariantParameters": [
                                          {
                                            "id": "test-product-variant-parameter-id-1",
                                            "productVariantID": "test-product-variant-id",
                                            "namespace": "test-namespace",
                                            "parameterKey": "test-product-variant-parameter-1",
                                            "parameterValue": "test-product-variant-parameter-value-1",
                                            "dataType": "INT",
                                            "overrideLevel": "none",
                                            "createdAt": "0001-01-01T00:00:00Z",
                                            "updatedAt": "0001-01-01T00:00:00Z"
                                          }
                                        ]
                                      }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Product variant code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "", -1), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	// Context("Error - Data not found in database", func() {
	// 	When("Product template parameter not found in database", func() {
	// 		It("return error", func() {
	// 			mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{}, data.ErrNoData)
	//
	// 			resp, err := client.Post()(strings.Replace(ListEffectiveProductVariantParametersURL, ":productTemplateID", "test-product-template-id-1", -1))
	// 			Expect(err).ShouldNot(HaveOccurred())
	// 			Expect(resp.StatusCode).Should(Equal(404))
	//
	// 			expectedResponse := ErrRecordNotFoundResponse
	// 			Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
	// 		})
	// 	})
	// })
	//
	Context("Database errors", func() {
		When("service failure - error while fetching product-variant", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, dbError)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("service failure - error while fetching product-variant - not found", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, errNoData)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("service failure - error while fetching product-variant-parameters", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{}, dbError)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("happy case - error while fetching product-variant-parameters - not found", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{}, errNoData)

				mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{
					PublicID:          "test-product-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Status:            "ACTIVE",
				}, nil)

				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{{
					PublicID:          "test-product-variant-parameter-id-1",
					ProductTemplateID: 1,
					Namespace:         "test-namespace",
					ParameterKey:      "test-product-template-parameter-2",
					ParameterValue:    "test-product-template-parameter-value-2",
					DataType:          "INT",
					OverrideLevel:     "none",
				}}, nil)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
                                        "productVariantParameters": [
                                          {
                                            "id": "test-product-variant-parameter-id-1",
                                            "productVariantID": "test-product-variant-id",
                                            "namespace": "test-namespace",
                                            "parameterKey": "test-product-template-parameter-2",
                                            "parameterValue": "test-product-template-parameter-value-2",
                                            "dataType": "INT",
                                            "overrideLevel": "none",
                                            "createdAt": "0001-01-01T00:00:00Z",
                                            "updatedAt": "0001-01-01T00:00:00Z"
                                          }
                                        ]
                                      }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("service failure - error while fetching product", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-product-variant-parameter-id-1",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-variant-parameter-1",
					ParameterValue:   "test-product-variant-parameter-value-1",
					DataType:         "INT",
					OverrideLevel:    "none",
				}}, nil)

				mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(nil, dbError)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("service failure - error while fetching product-template-parameters", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-product-variant-id",
					ProductID: 1,
					Code:      "test-product-code",
					Version:   "1",
					Name:      "test-product-variant",
					Status:    "ACTIVE",
				}}, nil)

				mockProductVariantParameters.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-product-variant-parameter-id-1",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-product-variant-parameter-1",
					ParameterValue:   "test-product-variant-parameter-value-1",
					DataType:         "INT",
					OverrideLevel:    "none",
				}}, nil)

				mockProduct.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.Product{
					PublicID:          "test-product-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Status:            "ACTIVE",
				}, nil)

				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{}, dbError)

				resp, err := client.Post(strings.Replace(ListEffectiveProductVariantParametersURL, ":productVariantCode", "test-product-variant-code", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateProductVariant", func() {
	var mockProductVariant *storage.MockIProductVariantDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	nowPlus10 := time.Now().UTC().Add(10 * time.Minute)
	nowPlus10String, _ := nowPlus10.MarshalText()
	nowMinus10 := time.Now().UTC().Add(-10 * time.Minute)
	nowMinus10String, _ := nowMinus10.MarshalText()

	BeforeEach(func() {
		mockProductDAO := &storage.MockIProductDAO{}
		mockProductDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Product{{}}, nil)

		mockProductVariant = &storage.MockIProductVariantDAO{}
		service.Store = &storage.DBStore{
			ProductVariantDAO: mockProductVariant,
			ProductDAO:        mockProductDAO,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{
					PublicID:  "test-id",
					ProductID: 1,
					Code:      "test-product-variant-code",
					Name:      "test-product-variant-name",
					Version:   "1.0.0",
					Description: sql.NullString{
						String: "create test product variant",
						Valid:  true,
					},
					ValidFrom: now,
					ValidTo:   nowPlus10,
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockProductVariant.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productVariant": {
										   "id": "test-id",
										   "productID": "test-product",
										   "code": "test-product-variant-code",
										   "version": "1.0.0",
										   "name": "test-product-variant-name",
										   "description": "create test product variant",
										   "status": "ACTIVE",
										   "validFrom":` + `"` + string(nowString) + `",` + `
										   "validTo":` + `"` + string(nowPlus10String) + `",` + `
										   "createdBy": "api-test",
										   "createdAt":` + `"` + string(nowString) + `",` + `
                                           "updatedBy": "api-test",
                                           "updatedAt":` + `"` + string(nowString) + `"` + `
										}
									 }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Product id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Version is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingVersionResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Valid from is less than current date", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowMinus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrValidFromLessThanCurrentResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Valid to is less than current date", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validTo":`+`"`+string(nowMinus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrValidToLessThanCurrentResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("ValidTo is less than ValidFrom", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowPlus10String)+`",`+`
										"validTo":`+`"`+string(nowString)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrValidFromGreaterThanValidToResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockProductVariant.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, errors.New("database load error"))
				mockProductVariant.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductVariantURL,
					hcl.JSON(`{
										"productID": "test-product",
										"code": "test-product-variant-code",
										"name": "test-product-variant-name",
										"version" : "1.0.0",
										"validFrom":`+`"`+string(nowString)+`",`+`
										"validTo":`+`"`+string(nowPlus10String)+`",`+`
										"description": "create test product variant",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

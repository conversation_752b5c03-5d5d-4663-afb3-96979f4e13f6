package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("CreateProductVariantParameterValue", func() {
	var mockProductVariantParameter *storage.MockIProductVariantParameterDAO

	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	nowMinus10String, _ := nowMinus10.MarshalText()
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductVariantDAO := &storage.MockIProductVariantDAO{}
		mockProductVariantDAO.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariant{PublicID: "test-product-variant-id-1"}, nil)

		mockProductVariantParameter = &storage.MockIProductVariantParameterDAO{}
		service.Store = &storage.DBStore{
			ProductVariantParameterDAO: mockProductVariantParameter,
			ProductVariantDAO:          mockProductVariantDAO,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-id",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-parameter-key",
					ParameterValue:   "test-parameter-value",
					DataType:         "STRING",
					OverrideLevel:    "NO_OVERRIDE",
					ExceptionLevel: sql.NullString{
						String: "",
						Valid:  false,
					},
					Description: sql.NullString{
						String: "create test product variant parameter",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()
				mockProductVariantParameter.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockProductVariantParameter.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariantParameter{
					PublicID:         "test-id",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-parameter-key",
					ParameterValue:   "test-parameter-value-2",
					DataType:         "STRING",
					OverrideLevel:    "NO_OVERRIDE",
					ExceptionLevel: sql.NullString{
						String: "",
						Valid:  false,
					},
					Description: sql.NullString{
						String: "create test product variant parameter",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"parameterValue": "test-parameter-value-2",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productVariantParameter": {
											"id": "test-id",
											"productVariantID": "test-product-variant-id-1",
											"namespace": "test-namespace",
											"parameterKey": "test-parameter-key",
								 			"parameterValue": "test-parameter-value-2",
											"dataType": "STRING",
											"overrideLevel": "NO_OVERRIDE",
											"description": "create test product variant parameter",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
										}
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"parameterValue": "test-parameter-value-2",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("ID is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "", -1),
					hcl.JSON(`{
										"parameterValue": "test-parameter-value-2",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Parameter value is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"parameterValue": "",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingParameterValueResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Updated By is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"parameterValue": "test-parameter-value-2",
										"updatedBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingUpdatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product variant parameter not found in database", func() {
			It("return error", func() {
				mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{}, data.ErrNoData)

				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"parameterValue": "test-parameter-value-2",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database update error", func() {
			It("return error", func() {
				mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{{
					PublicID:         "test-id",
					ProductVariantID: 1,
					Namespace:        "test-namespace",
					ParameterKey:     "test-parameter-key",
					ParameterValue:   "test-parameter-value",
					DataType:         "STRING",
					OverrideLevel:    "NO_OVERRIDE",
					ExceptionLevel: sql.NullString{
						String: "",
						Valid:  false,
					},
					Description: sql.NullString{
						String: "create test product variant parameter",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockProductVariantParameter.On("Update", mock.Anything, mock.Anything).Return(errors.New("database update error"))

				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"parameterValue": "test-parameter-value-2",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseUpdateResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProductVariantParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantParameter{}, errors.New("database load error"))

				resp, err := client.Put(strings.Replace(UpdateProductVariantParameterValueURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"parameterValue": "test-parameter-value-2",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

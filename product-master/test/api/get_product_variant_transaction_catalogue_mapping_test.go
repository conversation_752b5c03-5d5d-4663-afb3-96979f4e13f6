package testapi

import (
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetProductVariantTransactionCatalogueMapping", func() {
	var mockProductVariantTransactionCatalogueMapping *storage.MockIProductVariantTransactionCatalogueMappingDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockTransactionCatalogue := &storage.MockITransactionCatalogueDAO{}
		mockTransactionCatalogue.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.TransactionCatalogue{PublicID: "test-transaction-catalogue-id"}, nil)

		mockProductVariant := &storage.MockIProductVariantDAO{}
		mockProductVariant.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariant{PublicID: "test-product-variant-id"}, nil)

		mockProductVariantTransactionCatalogueMapping = &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
		service.Store = &storage.DBStore{
			ProductVariantTransactionCatalogueMappingDAO: mockProductVariantTransactionCatalogueMapping,
			TransactionCatalogueDAO:                      mockTransactionCatalogue,
			ProductVariantDAO:                            mockProductVariant,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariantTransactionCatalogueMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueMapping{{
					PublicID:               "test-id",
					ProductVariantID:       1,
					TransactionCatalogueID: 1,
					Status:                 string(api.EntityStatus_ACTIVE),
					CreatedBy:              "api-test",
					CreatedAt:              now,
					UpdatedBy:              "api-test",
					UpdatedAt:              now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetProductVariantTransactionCatalogueMappingURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productVariantTransactionCatalogueMapping": {
											"id":  "test-id",
											"productVariantID": "test-product-variant-id",
											"transactionCatalogueID": "test-transaction-catalogue-id",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
										}
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product Variant Transaction Catalogue Mapping not found in database", func() {
			It("return error", func() {
				mockProductVariantTransactionCatalogueMapping.On("Find", mock.Anything, mock.Anything).Return(
					[]*storage.ProductVariantTransactionCatalogueMapping{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetProductVariantTransactionCatalogueMappingURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockProductVariantTransactionCatalogueMapping.On("Find", mock.Anything, mock.Anything).Return(
					[]*storage.ProductVariantTransactionCatalogueMapping{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetProductVariantTransactionCatalogueMappingURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateBaseInterest", func() {
	var mockDepositInterest *storage.MockIDepositInterestDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockBaseInterestDao := &storage.MockIBaseInterestDAO{}
		mockBaseInterestDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{{}}, nil)
		mockProductVariantDao := &storage.MockIProductVariantDAO{}
		mockProductVariantDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{}}, nil)

		mockDepositInterest = &storage.MockIDepositInterestDAO{}
		service.Store = &storage.DBStore{
			DepositInterestDAO: mockDepositInterest,
			BaseInterestDAO:    mockBaseInterestDao,
			ProductVariantDAO:  mockProductVariantDao,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct - deposit-interest linked to base interest", func() {
			It("returns 200 happy case", func() {
				mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{{
					PublicID:           "test-id",
					ProductVariantID:   1,
					IsLinkedToBaseRate: true,
					BaseInterestID: sql.NullInt64{
						Int64: 1,
						Valid: true,
					},
					Code: "test-deposit-interest-code",
					Name: "test-deposit-interest-name",
					Description: sql.NullString{
						String: "get test deposit-interest",
						Valid:  true,
					},
					Currency:              currency,
					RoundOffType:          "FLOOR",
					InterestSlabType:      "AMOUNT",
					InterestSlabStructure: "ABSOLUTE",
					CreatedBy:             "api-test",
					CreatedAt:             now,
					UpdatedBy:             "api-test",
					UpdatedAt:             now,
				}}, nil)
				mockDepositInterest.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterest": {
											"id": "test-id",
											"productVariantID": "test-product-variant-id",
									        "isLinkedToBaseRate": true,
											"baseInterestID": "test-base-interest-id",
											"code": "test-deposit-interest-code",
											"name": "test-deposit-interest-name",
											"description": "get test deposit-interest",
											"currency": ` + `"` + currency + `",` + `
											"roundOffType": "FLOOR",
											"interestSlabType": "AMOUNT",
											"interestSlabStructure": "ABSOLUTE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                      }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("body is correct - deposit-interest not linked to base interest", func() {
			It("returns 200 happy case", func() {
				mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{{
					PublicID:           "test-id",
					ProductVariantID:   1,
					IsLinkedToBaseRate: false,
					BaseInterestID:     sql.NullInt64{},
					Code:               "test-deposit-interest-code",
					Name:               "test-deposit-interest-name",
					Description: sql.NullString{
						String: "get test deposit-interest",
						Valid:  true,
					},
					Currency:              currency,
					RoundOffType:          "FLOOR",
					InterestSlabType:      "AMOUNT",
					InterestSlabStructure: "ABSOLUTE",
					CreatedBy:             "api-test",
					CreatedAt:             now,
					UpdatedBy:             "api-test",
					UpdatedAt:             now,
				}}, nil)
				mockDepositInterest.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": false,
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterest": {
											"id": "test-id",
											"productVariantID": "test-product-variant-id",
											"code": "test-deposit-interest-code",
											"name": "test-deposit-interest-name",
											"description": "get test deposit-interest",
											"currency":` + `"` + currency + `",` + `
											"roundOffType": "FLOOR",
											"interestSlabType": "AMOUNT",
											"interestSlabStructure": "ABSOLUTE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                      }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Product Variant ID is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductVariantIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Base interest id is missing when IsLinkedToBaseRate is true", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingLinkedBaseInterestIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Currency is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": "",
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid currency", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": "INVALID_CURRENCY",
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Round off type is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidRoundOffTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid round off type", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "INVALID_TYPE",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidRoundOffTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Interest slab type is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidInterestSlabTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid interest slab type", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "INVALID_TYPE",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidInterestSlabTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Interest slab structure is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidInterestSlabStructureResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid interest slab structure", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "INVALID_STRUCTURE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidInterestSlabStructureResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockDepositInterest.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{}, errors.New("database load error"))
				mockDepositInterest.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateDepositInterestURL,
					hcl.JSON(`{
										"productVariantID": "test-product-variant-id",
										"isLinkedToBaseRate": true,
										"baseInterestID": "test-base-interest-id",
										"code": "test-deposit-interest-code",
										"name": "test-deposit-interest-name",
										"description": "get test deposit-interest",
										"currency": `+`"`+currency+`",`+`
										"roundOffType": "FLOOR",
										"interestSlabType": "AMOUNT",
										"interestSlabStructure": "ABSOLUTE",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

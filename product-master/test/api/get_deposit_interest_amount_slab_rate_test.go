package testapi

import (
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetDepositInterestAmountSlabRate", func() {
	var mockDepositInterestAmountSlabRate *storage.MockIDepositInterestAmountSlabRateDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockDepositInterestVersion := &storage.MockIDepositInterestVersionDAO{}
		mockDepositInterestVersion.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.DepositInterestVersion{PublicID: "test-deposit-interest-version-id"}, nil)

		mockDepositInterestAmountSlabRate = &storage.MockIDepositInterestAmountSlabRateDAO{}
		service.Store = &storage.DBStore{
			DepositInterestAmountSlabRateDAO: mockDepositInterestAmountSlabRate,
			DepositInterestVersionDAO:        mockDepositInterestVersion,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestAmountSlabRate{{
					PublicID:                         "test-id",
					DepositInterestVersionID:         1,
					FromAmount:                       "100",
					ToAmount:                         "200",
					BaseRateInterestSpreadPercentage: "0.1",
					AbsoluteInterestRatePercentage:   "2",
					CreatedBy:                        "api-test",
					CreatedAt:                        now,
					UpdatedBy:                        "api-test",
					UpdatedAt:                        now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetDepositInterestAmountSlabRateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterestAmountSlabRate": {
											"id": "test-id",
											"depositInterestVersionID": "test-deposit-interest-version-id",
											"fromAmount": "100",
										    "toAmount": "200",
										    "baseRateInterestSpreadPercentage": "0.1",
											"absoluteInterestRatePercentage": "2",
										    "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("base interest time slab rate not found in database", func() {
			It("return error", func() {
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything).Return(
					[]*storage.DepositInterestAmountSlabRate{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetDepositInterestAmountSlabRateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything).Return(
					[]*storage.DepositInterestAmountSlabRate{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetDepositInterestAmountSlabRateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

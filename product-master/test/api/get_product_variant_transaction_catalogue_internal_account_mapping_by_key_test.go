package testapi

import (
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
)

var _ = Describe("GetProductVariantTransactionCatalogueInternalAccountMappingByKey", func() {
	var mockMappingDAO *storage.MockIProductVariantTransactionCatalogueInternalAccountMappingDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockMapping1 := &storage.MockIInternalAccountDAO{}
		mockMapping1.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.InternalAccount{PublicID: "test-internal-account-id"}, nil)

		mockMapping2 := &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
		mockMapping2.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariantTransactionCatalogueMapping{PublicID: "test-product-variant-transaction-catalogue-mapping-id"}, nil)

		mockMappingDAO = &storage.MockIProductVariantTransactionCatalogueInternalAccountMappingDAO{}
		service.Store = &storage.DBStore{
			ProductVariantTransactionCatalogueInternalAccountMappingDAO: mockMappingDAO,
			InternalAccountDAO:                           mockMapping1,
			ProductVariantTransactionCatalogueMappingDAO: mockMapping2,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockMappingDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueInternalAccountMapping{
					{
						PublicID:          "test-id",
						InternalAccountID: 1,
						ProductVariantTransactionCatalogueMappingID: 1,
						IdentifierKey: "test-key",
						Status:        "ACTIVE",
						CreatedBy:     "api-test",
						CreatedAt:     now,
						UpdatedBy:     "api-test",
						UpdatedAt:     now,
					},
				}, nil)

				resp, err := client.Get(strings.Replace(GetProductVariantTransactionCatalogueInternalAccountMappingByKeyURL, ":identifierKey", "test-key", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										 "productVariantTransactionCatalogueInternalAccountMapping": {
                                          "id": "test-id",
                                          "internalAccountID": "test-internal-account-id",
                                          "productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                          "identifierKey": "test-key",
                                          "status": "ACTIVE",
                                          "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
										}
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Key is missing", func() {
			It("return error", func() {
				resp, err := client.Get(strings.Replace(GetProductVariantTransactionCatalogueInternalAccountMappingByKeyURL, ":identifierKey", "", -1))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdentifierKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product not found in database", func() {
			It("return error", func() {
				mockMappingDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueInternalAccountMapping{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetProductVariantTransactionCatalogueInternalAccountMappingByKeyURL, ":identifierKey", "test-key", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockMappingDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueInternalAccountMapping{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetProductVariantTransactionCatalogueInternalAccountMappingByKeyURL, ":identifierKey", "test-key", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

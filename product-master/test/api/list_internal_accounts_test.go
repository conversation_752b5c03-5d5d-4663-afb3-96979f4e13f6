package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("ListInternalAccounts", func() {
	var mockInternalAccount *storage.MockIInternalAccountDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
		mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{PublicID: "test-general-ledger-id"}}, nil)
		mockGeneralLedger.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.GeneralLedger{PublicID: "test-general-ledger-id"}, nil)

		mockInternalAccount = &storage.MockIInternalAccountDAO{}
		service.Store = &storage.DBStore{
			InternalAccountDAO: mockInternalAccount,
			GeneralLedgerDAO:   mockGeneralLedger,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("List internal accounts by combination of code and general ledger id", func() {
			It("returns 200 happy case", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InternalAccount{
					{
						PublicID:        "test-id",
						GeneralLedgerID: 1,
						Code:            "test-internal-account-code",
						Name:            "test-internal-account-name",
						Description: sql.NullString{
							String: "create test internal account",
							Valid:  true,
						},
						Currency:  currency,
						Status:    string(api.EntityStatus_ACTIVE),
						CreatedBy: "api-test",
						CreatedAt: now,
						UpdatedBy: "api-test",
						UpdatedAt: now,
					},
				}, nil)

				url := strings.Replace(ListInternalAccountsURL, ":code", "test-internal-account-code", -1)
				url = strings.Replace(url, ":generalLedgerID", "test-general-ledger-id", -1)
				resp, err := client.Get(url)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"internalAccounts": [
											{
												"id": "test-id",
												"generalLedgerID": "test-general-ledger-id",
												"code": "test-internal-account-code",
												"name": "test-internal-account-name",
												"description": "create test internal account",
												"currency":` + `"` + currency + `",` + `
												"status": "ACTIVE",
												"createdBy": "api-test",
												"createdAt":` + `"` + string(nowString) + `",` + `
												"updatedBy": "api-test",
												"updatedAt":` + `"` + string(nowString) + `"` + `
											}
										]
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("List internal accounts by code only", func() {
			It("returns 200 happy case", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InternalAccount{
					{
						PublicID:        "test-id",
						GeneralLedgerID: 1,
						Code:            "test-internal-account-code",
						Name:            "test-internal-account-name",
						Description: sql.NullString{
							String: "create test internal account",
							Valid:  true,
						},
						Currency:  currency,
						Status:    string(api.EntityStatus_ACTIVE),
						CreatedBy: "api-test",
						CreatedAt: now,
						UpdatedBy: "api-test",
						UpdatedAt: now,
					},
				}, nil)

				url := strings.Replace(ListInternalAccountsURL, ":code", "test-internal-account-code", -1)
				url = strings.Replace(url, ":generalLedgerID", "", -1)
				resp, err := client.Get(url)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"internalAccounts": [
											{
												"id": "test-id",
												"generalLedgerID": "test-general-ledger-id",
												"code": "test-internal-account-code",
												"name": "test-internal-account-name",
												"description": "create test internal account",
												 "currency":` + `"` + currency + `",` + `
												"status": "ACTIVE",
												"createdBy": "api-test",
												"createdAt":` + `"` + string(nowString) + `",` + `
												"updatedBy": "api-test",
												"updatedAt":` + `"` + string(nowString) + `"` + `
											}
										]
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("List internal accounts by general ledger id only", func() {
			It("returns 200 happy case", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.InternalAccount{
					{
						PublicID:        "test-id-1",
						GeneralLedgerID: 1,
						Code:            "test-internal-account-code-1",
						Name:            "test-internal-account-name-1",
						Description: sql.NullString{
							String: "create test internal account",
							Valid:  true,
						},
						Currency:  currency,
						Status:    string(api.EntityStatus_ACTIVE),
						CreatedBy: "api-test",
						CreatedAt: now,
						UpdatedBy: "api-test",
						UpdatedAt: now,
					},
					{
						PublicID:        "test-id-2",
						GeneralLedgerID: 1,
						Code:            "test-internal-account-code-2",
						Name:            "test-internal-account-name-2",
						Description: sql.NullString{
							String: "create test internal account",
							Valid:  true,
						},
						Currency:  currency,
						Status:    string(api.EntityStatus_INACTIVE),
						CreatedBy: "api-test",
						CreatedAt: now,
						UpdatedBy: "api-test",
						UpdatedAt: now,
					},
				}, nil)

				url := strings.Replace(ListInternalAccountsURL, ":code", "", -1)
				url = strings.Replace(url, ":generalLedgerID", "test-general-ledger-id", -1)
				resp, err := client.Get(url)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"internalAccounts": [
											{
												"id": "test-id-1",
												"generalLedgerID": "test-general-ledger-id",
												"code": "test-internal-account-code-1",
												"name": "test-internal-account-name-1",
												"description": "create test internal account",
												"currency":` + `"` + currency + `",` + `
												"status": "ACTIVE",
												"createdBy": "api-test",
												"createdAt":` + `"` + string(nowString) + `",` + `
												"updatedBy": "api-test",
												"updatedAt":` + `"` + string(nowString) + `"` + `
											},
											{
												"id": "test-id-2",
												"generalLedgerID": "test-general-ledger-id",
												"code": "test-internal-account-code-2",
												"name": "test-internal-account-name-2",
												"description": "create test internal account",
												"currency":` + `"` + currency + `",` + `
												"status": "INACTIVE",
												"createdBy": "api-test",
												"createdAt":` + `"` + string(nowString) + `",` + `
												"updatedBy": "api-test",
												"updatedAt":` + `"` + string(nowString) + `"` + `
											}
										]
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Code and general ledger id both are missing", func() {
			It("return error", func() {
				url := strings.Replace(ListInternalAccountsURL, ":code", "", -1)
				url = strings.Replace(url, ":generalLedgerID", "", -1)
				resp, err := client.Get(url)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingAtLeastOneFieldResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Code not found in database", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
					[]*storage.InternalAccount{}, data.ErrNoData)

				url := strings.Replace(ListInternalAccountsURL, ":code", "test-internal-account-code", -1)
				url = strings.Replace(url, ":generalLedgerID", "", -1)
				resp, err := client.Get(url)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("General Ledger id not found in database", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
					[]*storage.InternalAccount{}, data.ErrNoData)

				url := strings.Replace(ListInternalAccountsURL, ":code", "", -1)
				url = strings.Replace(url, ":generalLedgerID", "test-general-ledger-id", -1)
				resp, err := client.Get(url)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Combination of code and general ledger id not found in database", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
					[]*storage.InternalAccount{}, data.ErrNoData)

				url := strings.Replace(ListInternalAccountsURL, ":code", "test-internal-account-code", -1)
				url = strings.Replace(url, ":generalLedgerID", "test-general-ledger-id", -1)
				resp, err := client.Get(url)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(
					[]*storage.InternalAccount{}, errors.New("database load error"))

				url := strings.Replace(ListInternalAccountsURL, ":code", "test-internal-account-code", -1)
				url = strings.Replace(url, ":generalLedgerID", "test-general-ledger-id", -1)
				resp, err := client.Get(url)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

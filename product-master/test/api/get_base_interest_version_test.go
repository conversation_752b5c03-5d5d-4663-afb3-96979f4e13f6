package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetBaseInterestVersion", func() {
	var mockBaseInterestVersion *storage.MockIBaseInterestVersionDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockBaseInterest := &storage.MockIBaseInterestDAO{}
		mockBaseInterest.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.BaseInterest{PublicID: "test-base-interest-id"}, nil)

		mockBaseInterestVersion = &storage.MockIBaseInterestVersionDAO{}
		service.Store = &storage.DBStore{
			BaseInterestVersionDAO: mockBaseInterestVersion,
			BaseInterestDAO:        mockBaseInterest,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockBaseInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestVersion{{
					PublicID:       "test-id",
					BaseInterestID: 1,
					Version:        "1",
					EffectiveDate:  now,
					Description: sql.NullString{
						String: "get test base interest version",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetBaseInterestVersionURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"baseInterestVersion": {
											"id": "test-id",
											"baseInterestID": "test-base-interest-id",
											"version": "1",
											"effectiveDate":` + `"` + string(nowString) + `",` + `
											"description": "get test base interest version",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("base interest version not found in database", func() {
			It("return error", func() {
				mockBaseInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestVersion{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetBaseInterestVersionURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockBaseInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestVersion{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetBaseInterestVersionURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

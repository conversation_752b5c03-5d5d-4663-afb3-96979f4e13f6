package testapi

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("CreatePocketTemplateQuestions", func() {
	now := time.Now().UTC()
	var mockPocketTemplate *storage.MockIPocketTemplateDAO
	var mockPocketTemplateQuestion *storage.MockIPocketTemplateQuestionDAO
	var mockPocketTemplateAnswerSuggestions *storage.MockIPocketTemplateAnswerSuggestionDAO

	BeforeEach(func() {
		mockPocketTemplate = &storage.MockIPocketTemplateDAO{}
		mockPocketTemplateQuestion = &storage.MockIPocketTemplateQuestionDAO{}
		mockPocketTemplateAnswerSuggestions = &storage.MockIPocketTemplateAnswerSuggestionDAO{}
		service.Store = &storage.DBStore{
			PocketTemplateDAO:                 mockPocketTemplate,
			PocketTemplateQuestionDAO:         mockPocketTemplateQuestion,
			PocketTemplateAnswerSuggestionDAO: mockPocketTemplateAnswerSuggestions,
		}
	})

	Context("Happy-path", func() {
		When("the request params and data in db are valid", func() {
			It("returns 200 OK and a valid response", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateQuestionsMockDBResponse(now), nil)
				mockPocketTemplateAnswerSuggestions.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateAnswerSuggestionsMockDBResponse(now), nil)

				resp, err := client.Post(CreatePocketTemplateQuestionsURL, hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.CreatePocketTemplateQuestionsResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())

				expectedResponse := responses.CreatePocketTemplateQuestionResponse()
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL, hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, ""))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("PocketTemplateID is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingPocketTemplateID
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("QuestionAnswerPairs is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				expectedResponse := ErrMissingQuestionAnswerPairs
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("QuestionText in QuestionAnswerPairs is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				expectedResponse := ErrMissingQuestionTextOrAnswerSuggestions
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("AnswerSuggestions in QuestionAnswerPairs is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":[]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				expectedResponse := ErrMissingQuestionTextOrAnswerSuggestions
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("QuestionText and AnswerSuggestions in QuestionAnswerPairs is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "",
  					 			"answerSuggestions":[]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				expectedResponse := ErrMissingQuestionTextOrAnswerSuggestions
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Locale is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingLocale
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("CreatedBy is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":""
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Data not found in the DB", func() {
		When("Pocket template not found in DB", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplate{}, data.ErrNoData)

				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template Question not found in DB", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template Answer suggestions not found in DB", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateQuestionsMockDBResponse(now), nil)
				mockPocketTemplateAnswerSuggestions.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				resp, err := client.Post(CreatePocketTemplateQuestionsURL, hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.CreatePocketTemplateQuestionsResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())

				expectedResponse := responses.CreatePocketTemplateQuestionWithoutAnswerResponse()
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})

	Context("Database Error", func() {
		When("Pocket Template DB hits load error", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplate{}, errors.New("database load error"))

				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket Template Question DB hits load error", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("database load error"))

				resp, err := client.Post(CreatePocketTemplateQuestionsURL,
					hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket Template AnswerSuggestion DB hits load error", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateQuestionsMockDBResponse(now), nil)
				mockPocketTemplateAnswerSuggestions.On("SaveBatch", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("database load error"))

				resp, err := client.Post(CreatePocketTemplateQuestionsURL, hcl.JSON(`{
  							"pocketTemplateID":"test-id",
 							"locale":"EN",
							"questionAnswerPairs":[{
   							"questionText": "Where are you heading to?",
  					 			"answerSuggestions":["US", "Paris","UK"]
							}],
 							"createdBy":"unit-test"
				}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

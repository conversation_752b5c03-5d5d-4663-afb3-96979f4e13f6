package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetBaseInterestByCode", func() {
	var mockBaseInterest *storage.MockIBaseInterestDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockBaseInterest = &storage.MockIBaseInterestDAO{}
		service.Store = &storage.DBStore{
			BaseInterestDAO: mockBaseInterest,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockBaseInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{
					{
						PublicID: "test-id",
						Code:     "test-base-interest-code",
						Name:     "test-base-interest-name",
						Description: sql.NullString{
							String: "create test base interest",
							Valid:  true,
						},
						Currency:  currency,
						CreatedBy: "api-test",
						CreatedAt: now,
						UpdatedBy: "api-test",
						UpdatedAt: now,
					},
				}, nil)

				resp, err := client.Get(strings.Replace(GetBaseInterestByCodeURL, ":code", "test-base-interest-code", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"baseInterest": {
											"id": "test-id",
											"code": "test-base-interest-code",
											"name": "test-base-interest-name",
											"description": "create test base interest",
											"currency": ` + `"` + currency + `",` + `
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Get(strings.Replace(GetBaseInterestByCodeURL, ":code", "", -1))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Base Interest not found in database", func() {
			It("return error", func() {
				mockBaseInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetBaseInterestByCodeURL, ":code", "test-base-interest-code", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockBaseInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetBaseInterestByCodeURL, ":code", "test-base-interest-code", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetGeneralLedger", func() {
	var mockGeneralLedger *storage.MockIGeneralLedgerDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockGeneralLedger = &storage.MockIGeneralLedgerDAO{}
		service.Store = &storage.DBStore{
			GeneralLedgerDAO: mockGeneralLedger,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{
					PublicID: "test-id",
					Code:     "test-general-ledger-code",
					Name:     "test-general-ledger-name",
					Description: sql.NullString{
						String: "create test general ledger",
						Valid:  true,
					},
					Status:    "ACTIVE",
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetGeneralLedgerURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"generalLedger": {
											"id": "test-id",
											"code": "test-general-ledger-code",
											"name": "test-general-ledger-name",
											"description": "create test general ledger",
											"status": "ACTIVE",
											"currency":` + `"` + currency + `",` + `
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("general-ledger not found in database", func() {
			It("return error", func() {
				mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetGeneralLedgerURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetGeneralLedgerURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

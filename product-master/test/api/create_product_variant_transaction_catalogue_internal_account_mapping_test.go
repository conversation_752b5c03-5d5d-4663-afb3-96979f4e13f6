package testapi

import (
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateProductVariantTransactionCatalogueInternalAccountMapping", func() {
	var MockProductVariantTransactionCatalogueInternalAccountMapping *storage.MockIProductVariantTransactionCatalogueInternalAccountMappingDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockInternalAccountDAO := &storage.MockIInternalAccountDAO{}
		mockInternalAccountDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{{}}, nil)
		mappingDAO2 := &storage.MockIProductVariantTransactionCatalogueMappingDAO{}
		mappingDAO2.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueMapping{{}}, nil)

		MockProductVariantTransactionCatalogueInternalAccountMapping = &storage.MockIProductVariantTransactionCatalogueInternalAccountMappingDAO{}
		service.Store = &storage.DBStore{
			ProductVariantTransactionCatalogueInternalAccountMappingDAO: MockProductVariantTransactionCatalogueInternalAccountMapping,
			InternalAccountDAO:                           mockInternalAccountDAO,
			ProductVariantTransactionCatalogueMappingDAO: mappingDAO2,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				MockProductVariantTransactionCatalogueInternalAccountMapping.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductVariantTransactionCatalogueInternalAccountMapping{{
					PublicID:          "test-id",
					InternalAccountID: 1,
					ProductVariantTransactionCatalogueMappingID: 1,
					IdentifierKey: "test-key",
					Status:        "ACTIVE",
					CreatedBy:     "api-test",
					CreatedAt:     now,
					UpdatedBy:     "api-test",
					UpdatedAt:     now,
				}}, nil)
				MockProductVariantTransactionCatalogueInternalAccountMapping.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductVariantTransactionCatalogueInternalAccountMappingURL,
					hcl.JSON(`{
										"internalAccountID": "test-internal-account-id",
										"productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                        "IdentifierKey": "test-key",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										 "productVariantTransactionCatalogueInternalAccountMapping": {
                                          "id": "test-id",
                                          "internalAccountID": "test-internal-account-id",
                                          "productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                          "identifierKey": "test-key",
                                          "status": "ACTIVE",
                                          "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
										}
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueInternalAccountMappingURL,
					hcl.JSON(`{
										"internalAccountID": "test-internal-account-id",
										"productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                        "identifierKey": "test-key",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Internal Account id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueInternalAccountMappingURL,
					hcl.JSON(`{
										"internalAccountID": "",
										"productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                        "identifierKey": "test-key",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingInternalAccountIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("ProductVariantTransactionCatalogueMapping id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueInternalAccountMappingURL,
					hcl.JSON(`{
										"internalAccountID": "test-internal-account-id",
										"productVariantTransactionCatalogueMappingID": "",
                                        "identifierKey": "test-key",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductVariantTransactionCatalogueMappingIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created by is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductVariantTransactionCatalogueInternalAccountMappingURL,
					hcl.JSON(`{
										"internalAccountID": "test-internal-account-id",
										"productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                        "identifierKey": "test-key",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				MockProductVariantTransactionCatalogueInternalAccountMapping.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateProductVariantTransactionCatalogueInternalAccountMappingURL,
					hcl.JSON(`{
										"internalAccountID": "test-internal-account-id",
										"productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                        "identifierKey": "test-key",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				MockProductVariantTransactionCatalogueInternalAccountMapping.On("Find", mock.Anything, mock.Anything).Return(
					[]*storage.ProductVariantTransactionCatalogueInternalAccountMapping{}, errors.New("database load error"))
				MockProductVariantTransactionCatalogueInternalAccountMapping.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductVariantTransactionCatalogueInternalAccountMappingURL,
					hcl.JSON(`{
										"internalAccountID": "test-internal-account-id",
										"productVariantTransactionCatalogueMappingID": "test-product-variant-transaction-catalogue-mapping-id",
                                        "identifierKey": "test-key",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

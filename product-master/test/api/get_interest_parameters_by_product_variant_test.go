package testapi

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetInterestParametersByProductVariant", func() {
	now := time.Now().UTC()

	var xfccHeader hcl.RequestModifier
	var mockProductVariant *storage.MockIProductVariantDAO
	var mockDepositInterest *storage.MockIDepositInterestDAO
	var mockDepositInterestVersion *storage.MockIDepositInterestVersionDAO
	var mockDepositInterestAmountSlabRate *storage.MockIDepositInterestAmountSlabRateDAO
	var mockLoanInterest *storage.MockILoanInterestDAO
	var mockLoanInterestVersion *storage.MockILoanInterestVersionDAO
	var mockLoanInterestSlabRate *storage.MockILoanInterestSlabRateDAO

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockProductVariant = &storage.MockIProductVariantDAO{}
		mockDepositInterest = &storage.MockIDepositInterestDAO{}
		mockDepositInterestVersion = &storage.MockIDepositInterestVersionDAO{}
		mockDepositInterestAmountSlabRate = &storage.MockIDepositInterestAmountSlabRateDAO{}
		mockLoanInterest = &storage.MockILoanInterestDAO{}
		mockLoanInterestVersion = &storage.MockILoanInterestVersionDAO{}
		mockLoanInterestSlabRate = &storage.MockILoanInterestSlabRateDAO{}

		service.Store = &storage.DBStore{
			ProductVariantDAO:                mockProductVariant,
			DepositInterestDAO:               mockDepositInterest,
			DepositInterestVersionDAO:        mockDepositInterestVersion,
			DepositInterestAmountSlabRateDAO: mockDepositInterestAmountSlabRate,
			LoanInterestDAO:                  mockLoanInterest,
			LoanInterestVersionDAO:           mockLoanInterestVersion,
			LoanInterestSlabRateDAO:          mockLoanInterestSlabRate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case - with product variant version & interest version", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestVersionDAOResponse(now), nil)
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestSlabRateDAOResponse(now), nil)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.GetInterestParametersByProductVariantResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetInterestParametersByProductVariantResponse()))
			})

			It("returns 200 happy case - with product variant version only", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestVersionDAOResponse(now), nil)
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestSlabRateDAOResponse(now), nil)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.GetInterestParametersByProductVariantResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetInterestParametersByProductVariantResponse()))
			})

			It("returns 200 happy case - with interest version only", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestVersionDAOResponse(now), nil)
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestSlabRateDAOResponse(now), nil)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.GetInterestParametersByProductVariantResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetInterestParametersByProductVariantResponse()))
			})

			It("returns 200 happy case - without product variant version & interest version", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestVersionDAOResponse(now), nil)
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestSlabRateDAOResponse(now), nil)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.GetInterestParametersByProductVariantResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetInterestParametersByProductVariantResponse()))
			})

			It("returns 200 happy case - with biz product variant version", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockLoanInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleLoanInterestDAOResponse(), nil)
				mockLoanInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleLoanInterestVersionDAOResponse(), nil)
				mockLoanInterestSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleBizLoanInterestSlabRateDAOResponse(), nil)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
					  									"interestParamRequest": [
					      									{
					          									"productVariantCode": "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN"
					      									}
					 	 									]
														}`), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.GetInterestParametersByProductVariantResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.GetBizLoanInterestParametersByProductVariantResponse()))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Missing product variant code", func() {
			It("return error", func() {
				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidProductVariantCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid product variant code", func() {
			It("return error", func() {
				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "INVALID_PRODUCT_VARIANT_CODE",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidProductVariantCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Data not found in database errors", func() {
		When("Product variant not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, data.ErrNoData)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposit interest not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposit interest version not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposits interest amount slab rate not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestVersionDAOResponse(now), nil)
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("loan interest not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockLoanInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
					  									"interestParamRequest": [
					      									{
					          									"productVariantCode": "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN"
					      									}
					 	 									]
														}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("loan interest version not found in database", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockLoanInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleLoanInterestDAOResponse(), nil)
				mockLoanInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
					  									"interestParamRequest": [
					      									{
					          									"productVariantCode": "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN"
					      									}
					 	 									]
														}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Product variant database load error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error from product variant"))

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposit interest database load error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error from deposits interest"))

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposit interest version database load error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error from deposits interest version"))

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposits interest amount slab rate database load error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockDepositInterest.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestDAOResponse(), nil)
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleDepositInterestVersionDAOResponse(now), nil)
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("error from deposits interest amount slab rate"))

				resp, err := client.Post(GetInterestParametersByProductVariantURL,
					hcl.JSON(`{
    									"interestParamRequest": [
        									{
            									"productVariantCode": "`+string(api.ProductVariantCode_DEPOSITS_ACCOUNT)+`",
            									"productVariantVersion": "1",
            									"interestVersion": "1"
        									}
   	 									]
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

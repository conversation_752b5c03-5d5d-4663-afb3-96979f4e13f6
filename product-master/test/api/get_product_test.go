package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetProduct", func() {
	var xfccHeader hcl.RequestModifier
	var mockProduct *storage.MockIProductDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockProductTemplate := &storage.MockIProductTemplateDAO{}
		mockProductTemplate.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductTemplate{PublicID: "test-product-template-id-1"}, nil)

		mockProduct = &storage.MockIProductDAO{}
		service.Store = &storage.DBStore{
			ProductDAO:         mockProduct,
			ProductTemplateDAO: mockProductTemplate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProduct.On("Find", mock.Anything, mock.Anything).Return([]*storage.Product{{
					PublicID:          "test-id",
					ProductTemplateID: 1,
					Code:              "test-product-code",
					Name:              "test-product-name",
					Description: sql.NullString{
						String: "create test product",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetProductURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"product": {
											"id": "test-id",
											"productTemplateID": "test-product-template-id-1",
											"code": "test-product-code",
											"name": "test-product-name",
											"description": "create test product",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                       }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product not found in database", func() {
			It("return error", func() {
				mockProduct.On("Find", mock.Anything, mock.Anything).Return([]*storage.Product{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetProductURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockProduct.On("Find", mock.Anything, mock.Anything).Return([]*storage.Product{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetProductURL, ":id", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"encoding/json"
	"errors"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
)

var _ = Describe("ListLoanDocumentTypesByProductVariantCode", func() {
	var xfccHeader hcl.RequestModifier
	var mockProductVariant *storage.MockIProductVariantDAO
	var mockLoanDocumentSubmissionOptionParameters *storage.MockILoanDocumentSubmissionOptionParametersDAO
	var mockLoanDocumentSubmissionOptionVersion *storage.MockILoanDocumentSubmissionOptionVersionDAO
	var mockLoanDocumentSubmissionOption *storage.MockILoanDocumentSubmissionOptionDAO
	dummyErr := errors.New("dummy error")
	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)

		mockProductVariant = &storage.MockIProductVariantDAO{}
		mockLoanDocumentSubmissionOptionParameters = &storage.MockILoanDocumentSubmissionOptionParametersDAO{}
		mockLoanDocumentSubmissionOptionVersion = &storage.MockILoanDocumentSubmissionOptionVersionDAO{}
		mockLoanDocumentSubmissionOption = &storage.MockILoanDocumentSubmissionOptionDAO{}

		service.Store = &storage.DBStore{
			ProductVariantDAO:                         mockProductVariant,
			LoanDocumentSubmissionOptionParametersDAO: mockLoanDocumentSubmissionOptionParameters,
			LoanDocumentSubmissionOptionVersionDAO:    mockLoanDocumentSubmissionOptionVersion,
			LoanDocumentSubmissionOptionDAO:           mockLoanDocumentSubmissionOption,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockLoanDocumentSubmissionOption.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(responses.SampleDocumentSubmissionOptionDAOResponse(), nil)
				mockLoanDocumentSubmissionOptionVersion.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(responses.SampleDocumentSubmissionOptionVersionDAOResponse(), nil)
				mockLoanDocumentSubmissionOptionParameters.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(responses.SampleDocumentSubmissionOptionParametersDAOResponse(), nil)
				resp, err := client.Get(ListLoanDocumentSubmissionOptionsByProductVariantCodeURL,
					hcl.JSON(`{
										"productVariantCode": "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.ListLoanDocumentOptionsByProductVariantResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.ListLoanDocumentTypesSampleResponse()))
			})
		})
	})
	Context("Error scenerios", func() {
		When("db error occurs", func() {
			It("returns 500 when unable to fetch product variant", func() {
				mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(nil, dummyErr)
				resp, err := client.Get(ListLoanDocumentSubmissionOptionsByProductVariantCodeURL,
					hcl.JSON(`{
										"productVariantCode": "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(`{"code":"3244","message":"Database load error"}`))
			})
			It("returns 500 when unable to fetch loan document type options", func() {
				mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockLoanDocumentSubmissionOption.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(nil, dummyErr)
				resp, err := client.Get(ListLoanDocumentSubmissionOptionsByProductVariantCodeURL,
					hcl.JSON(`{
										"productVariantCode": "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(`{"code":"3244","message":"Database load error"}`))
			})
			It("returns 500 when unable to fetch loan document type options version", func() {
				mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockLoanDocumentSubmissionOption.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(responses.SampleDocumentSubmissionOptionDAOResponse(), nil)
				mockLoanDocumentSubmissionOptionVersion.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(nil, dummyErr)
				resp, err := client.Get(ListLoanDocumentSubmissionOptionsByProductVariantCodeURL,
					hcl.JSON(`{
										"productVariantCode": "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(`{"code":"3244","message":"Database load error"}`))
			})
			It("returns 500 when unable to fetch loan document type options parameters", func() {
				mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(responses.SampleProductVariantDAOResponse(), nil)
				mockLoanDocumentSubmissionOption.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(responses.SampleDocumentSubmissionOptionDAOResponse(), nil)
				mockLoanDocumentSubmissionOptionVersion.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(responses.SampleDocumentSubmissionOptionVersionDAOResponse(), nil)
				mockLoanDocumentSubmissionOptionParameters.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(nil, dummyErr)
				resp, err := client.Get(ListLoanDocumentSubmissionOptionsByProductVariantCodeURL,
					hcl.JSON(`{
										"productVariantCode": "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
									}`), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(resp.Body.String).Should(MatchJSON(`{"code":"3244","message":"Database load error"}`))
			})
		})
	})
	Context("validation error scenerios", func() {
		When("validation failure", func() {
			It("returns 400 when product variant code is not correct", func() {
				resp, err := client.Get(ListLoanDocumentSubmissionOptionsByProductVariantCodeURL,
					hcl.JSON(`{
										"productVariantCode": "DEFAULT_FLEXI"
									}`), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				Expect(resp.Body.String).Should(MatchJSON(`{"code":"3268","message":"Invalid productVariantCode field"}`))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateDepositInterestVersion", func() {
	var mockDepositInterestVersion *storage.MockIDepositInterestVersionDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	nowMinus10String, _ := nowMinus10.MarshalText()

	BeforeEach(func() {
		mockDepositInterest := &storage.MockIDepositInterestDAO{}
		mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{{}}, nil)

		mockDepositInterestVersion = &storage.MockIDepositInterestVersionDAO{}
		service.Store = &storage.DBStore{
			DepositInterestVersionDAO: mockDepositInterestVersion,
			DepositInterestDAO:        mockDepositInterest,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestVersion{{
					PublicID:          "test-id",
					DepositInterestID: 1,
					Version:           "1",
					EffectiveDate:     now,
					Description: sql.NullString{
						String: "create base interest version",
						Valid:  true,
					},
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockDepositInterestVersion.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "test-deposit-interest-id",
										"version": "1",
										"effectiveDate":`+`"`+string(nowString)+`",`+`
										"description": "create base interest version",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterestVersion": {
											"id": "test-id",
											"depositInterestID": "test-deposit-interest-id",
											"version": "1",
											"effectiveDate":` + `"` + string(nowString) + `",` + `
											"description": "create base interest version",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "test-deposit-interest-id",
										"version": "1",
										"effectiveDate":`+`"`+string(nowString)+`",`+`
										"description": "create base interest version",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposit Interest Id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "",
										"version": "1",
										"effectiveDate":`+`"`+string(nowString)+`",`+`
										"description": "create deposit interest version",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingDepositInterestIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Version is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "test-deposit-interest-id",
										"version": "",
										"effectiveDate":`+`"`+string(nowString)+`",`+`
										"description": "create deposit interest version",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingVersionResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "test-deposit-interest-id",
										"version": "1",
										"effectiveDate":`+`"`+string(nowString)+`",`+`
										"description": "create deposit interest version",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Effective date is less than current date", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "test-deposit-interest-id",
										"version": "1",
										"effectiveDate":`+`"`+string(nowMinus10String)+`",`+`
										"description": "create base interest version",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrEffectiveDateLessThanCurrentResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockDepositInterestVersion.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "test-deposit-interest-id",
										"version": "1",
										"effectiveDate":`+`"`+string(nowString)+`",`+`
										"description": "create deposit interest version",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockDepositInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestVersion{}, errors.New("database load error"))
				mockDepositInterestVersion.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateDepositInterestVersionURL,
					hcl.JSON(`{
										"depositInterestID": "test-deposit-interest-id",
										"version": "1",
										"effectiveDate":`+`"`+string(nowString)+`",`+`
										"description": "create deposit interest version",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

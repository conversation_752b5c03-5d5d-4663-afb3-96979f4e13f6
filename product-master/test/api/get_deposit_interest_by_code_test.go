package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetDepositInterestByCode", func() {
	var mockDepositInterest *storage.MockIDepositInterestDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockBaseInterest := &storage.MockIBaseInterestDAO{}
		mockBaseInterest.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.BaseInterest{PublicID: "test-base-interest-id"}, nil)

		mockProductVariantDAO := &storage.MockIProductVariantDAO{}
		mockProductVariantDAO.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.ProductVariant{PublicID: "test-product-variant-id"}, nil)

		mockDepositInterest = &storage.MockIDepositInterestDAO{}
		service.Store = &storage.DBStore{
			DepositInterestDAO: mockDepositInterest,
			BaseInterestDAO:    mockBaseInterest,
			ProductVariantDAO:  mockProductVariantDAO,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct - deposit-interest linked to base interest", func() {
			It("returns 200 happy case", func() {
				mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{
					{
						PublicID:           "test-id",
						ProductVariantID:   1,
						IsLinkedToBaseRate: true,
						BaseInterestID: sql.NullInt64{
							Int64: 1,
							Valid: true,
						},
						Code: "test-deposit-interest-code",
						Name: "test-deposit-interest-name",
						Description: sql.NullString{
							String: "get test deposit-interest",
							Valid:  true,
						},
						Currency:              currency,
						RoundOffType:          "FLOOR",
						InterestSlabType:      "AMOUNT",
						InterestSlabStructure: "ABSOLUTE",
						CreatedBy:             "api-test",
						CreatedAt:             now,
						UpdatedBy:             "api-test",
						UpdatedAt:             now,
					},
				}, nil)

				resp, err := client.Get(strings.Replace(GetDepositInterestByCodeURL, ":code", "test-deposit-interest-code", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterest": {
											"id": "test-id",
											"productVariantID": "test-product-variant-id",
									        "isLinkedToBaseRate": true,
											"baseInterestID": "test-base-interest-id",
											"code": "test-deposit-interest-code",
											"name": "test-deposit-interest-name",
											"description": "get test deposit-interest",
											"currency":` + `"` + currency + `",` + `
											"roundOffType": "FLOOR",
											"interestSlabType": "AMOUNT",
											"interestSlabStructure": "ABSOLUTE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                       }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("body is correct - deposit-interest not linked to base interest", func() {
			It("returns 200 happy case", func() {
				mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{
					{
						PublicID:           "test-id",
						ProductVariantID:   1,
						IsLinkedToBaseRate: false,
						BaseInterestID:     sql.NullInt64{},
						Code:               "test-deposit-interest-code",
						Name:               "test-deposit-interest-name",
						Description: sql.NullString{
							String: "get test deposit-interest",
							Valid:  true,
						},
						Currency:              currency,
						RoundOffType:          "FLOOR",
						InterestSlabType:      "AMOUNT",
						InterestSlabStructure: "ABSOLUTE",
						CreatedBy:             "api-test",
						CreatedAt:             now,
						UpdatedBy:             "api-test",
						UpdatedAt:             now,
					},
				}, nil)

				resp, err := client.Get(strings.Replace(GetDepositInterestByCodeURL, ":code", "test-deposit-interest-code", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterest": {
											"id": "test-id",
											"productVariantID": "test-product-variant-id",
											"code": "test-deposit-interest-code",
											"name": "test-deposit-interest-name",
											"description": "get test deposit-interest",
											"currency":` + `"` + currency + `",` + `
											"roundOffType": "FLOOR",
											"interestSlabType": "AMOUNT",
											"interestSlabStructure": "ABSOLUTE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                       }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Get(strings.Replace(GetDepositInterestByCodeURL, ":code", "", -1))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Deposit Interest not found in database", func() {
			It("return error", func() {
				mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetDepositInterestByCodeURL, ":code", "test-deposit-interest-code", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockDepositInterest.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterest{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetDepositInterestByCodeURL, ":code", "test-deposit-interest-code", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

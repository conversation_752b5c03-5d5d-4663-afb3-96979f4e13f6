package testapi

import (
	"encoding/json"
	"errors"
	"time"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
)

var _ = Describe("ListProductVariantQuestions", func() {
	var xfccHeader hcl.RequestModifier
	dummyErr := errors.New("some error")
	var mockProductVariantQuestion *storage.MockIProductVariantQuestionDAO
	var mockProductVariantAnswerSuggestion *storage.MockIProductVariantAnswerSuggestionDAO
	var mockProductVariant *storage.MockIProductVariantDAO
	now := time.Now().UTC()
	validRequestBody := hcl.JSON(`{
                                   "productVariantCode": "product1", "productVariantVersion": "1"
							}`)
	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		mockProductVariant = &storage.MockIProductVariantDAO{}
		mockProductVariantQuestion = &storage.MockIProductVariantQuestionDAO{}
		mockProductVariantAnswerSuggestion = &storage.MockIProductVariantAnswerSuggestionDAO{}

		service.Store = &storage.DBStore{
			ProductVariantQuestionDAO:         mockProductVariantQuestion,
			ProductVariantAnswerSuggestionDAO: mockProductVariantAnswerSuggestion,
			ProductVariantDAO:                 mockProductVariant,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{ID: 10}}, nil)
				mockProductVariantQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantQuestions(now), nil)
				mockProductVariantAnswerSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.SampleProductVariantAnswerSuggestions(now), nil)

				resp, err := client.Get(ListProductVariantQuestionURL, xfccHeader, validRequestBody)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.ListProductVariantQuestionsResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())

				expectedResponse := responses.ListProductVariantQuestionsResponse(now)
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Product variant code is missing", func() {
			It("return error", func() {
				requestBody := hcl.JSON(`{ "productVariantVersion": "1" }`)
				resp, err := client.Get(ListProductVariantQuestionURL, xfccHeader, requestBody)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductVariantCode
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error from database", func() {
		When("Product variant find resulted in error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.ProductVariant{}, dummyErr)
				resp, err := client.Get(ListProductVariantQuestionURL, xfccHeader, validRequestBody)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Product variant question find resulted in error", func() {
			It("return error", func() {
				mockProductVariant.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.ProductVariant{{ID: 10}}, nil)
				mockProductVariantQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.ProductVariantQuestion{}, dummyErr)
				resp, err := client.Get(ListProductVariantQuestionURL, xfccHeader, validRequestBody)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateBaseInterest", func() {
	var mockBaseInterestDAO *storage.MockIBaseInterestDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockBaseInterestDAO = &storage.MockIBaseInterestDAO{}
		service.Store = &storage.DBStore{
			BaseInterestDAO: mockBaseInterestDAO,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockBaseInterestDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{{
					PublicID: "test-id",
					Code:     "test-base-interest-code",
					Name:     "test-base-interest-name",
					Description: sql.NullString{
						String: "create test base interest",
						Valid:  true,
					},
					Currency:  currency,
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockBaseInterestDAO.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"code": "test-base-interest-code",
										"name": "test-base-interest-name",
										"description": "create test base interest",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"baseInterest": {
											"id": "test-id",
											"code": "test-base-interest-code",
											"name": "test-base-interest-name",
											"description": "create test base interest",
											"currency": ` + `"` + currency + `",` + `
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"code": "test-base-interest-code",
										"name": "test-base-interest-name",
										"description": "create test base-interest",
										"createdBy": "api-test",
 										"currency": `+`"`+currency+`"`+`
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid currency", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"currency": "INVALID_X",
										"code": "test-base-interest-code",
										"name": "test-base-interest-name",
										"description": "create test base-interest",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Currency is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"currency": "",
										"code": "test-base-interest-code",
										"name": "test-base-interest-name",
										"description": "create test base-interest",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"code": "",
										"name": "test-base-interest-name",
										"description": "create test base-interest",
										"createdBy": "api-test",
										"currency": `+`"`+currency+`"`+`
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"code": "test-base-interest-code",
										"name": "",
										"description": "create test base-interest",
										"createdBy": "api-test",
										"currency": `+`"`+currency+`"`+`
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"code": "test-base-interest-code",
										"name": "test-base-interest-name",
										"description": "create test base-interest",
										"currency": `+`"`+currency+`",`+`
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockBaseInterestDAO.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"code": "test-base-interest-code",
										"name": "test-base-interest-name",
										"description": "create test base-interest",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockBaseInterestDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterest{}, errors.New("database load error"))
				mockBaseInterestDAO.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateBaseInterestURL,
					hcl.JSON(`{
										"code": "test-base-interest-code",
										"name": "test-base-interest-name",
										"description": "create test base-interest",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

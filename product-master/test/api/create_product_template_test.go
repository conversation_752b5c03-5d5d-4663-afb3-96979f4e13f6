package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateProductTemplate", func() {
	var mockProductTemplateDAO *storage.MockIProductTemplateDAO

	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductTemplateDAO = &storage.MockIProductTemplateDAO{}
		service.Store = &storage.DBStore{
			ProductTemplateDAO: mockProductTemplateDAO,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
				mockProductTemplateDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{
					PublicID: "511",
					Code:     "test-product-template-code",
					Name:     "test-product-template",
					Description: sql.NullString{
						String: "create test product template",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Post(CreateProductTemplateURL,
					hcl.JSON(`{
										"name": "case_3",
										"code": "test-product-template-code",
										"createdBy": "api-test"
                                    }`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productTemplate": {
										  "id": "511",
										  "code": "test-product-template-code",
										  "name": "test-product-template",
										  "description": "create test product template",
										  "status": "ACTIVE",
										  "createdBy": "api-test",
										  "createdAt": ` + `"` + string(nowString) + `"` + `,
										  "updatedBy": "api-test",
										  "updatedAt": ` + `"` + string(nowString) + `"` + `
										}
									  }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})

		})
	})
	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateURL,
					hcl.JSON(`{
									   "name": "case_3",
 										"code": "test-product-template-code",
									   "createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateURL,
					hcl.JSON(`{
										"name": "",
 										"code": "test-product-template-code",
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateURL,
					hcl.JSON(`{
										"name": "case_3",
 										"code": "",
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateProductTemplateURL,
					hcl.JSON(`{
									"name": "case_3",
 									"code": "test-product-template-code",
									 "createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockProductTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateProductTemplateURL,
					hcl.JSON(`{
									  "name": "case_3",
									  "code": "test-product-template-code",
									  "createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockProductTemplateDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{}, errors.New("database load error"))
				mockProductTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateProductTemplateURL,
					hcl.JSON(`{
										"name": "case_3",
 										"code": "test-product-template-code",
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

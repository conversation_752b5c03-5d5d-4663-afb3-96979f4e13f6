package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("UpdateInternalAccountStatus", func() {
	var mockInternalAccount *storage.MockIInternalAccountDAO

	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	nowMinus10String, _ := nowMinus10.MarshalText()
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
		mockGeneralLedger.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.GeneralLedger{PublicID: "test-general-ledger-id"}, nil).Once()

		mockInternalAccount = &storage.MockIInternalAccountDAO{}
		service.Store = &storage.DBStore{
			InternalAccountDAO: mockInternalAccount,
			GeneralLedgerDAO:   mockGeneralLedger,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case - ACTIVE to INACTIVE", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  currency,
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()
				mockInternalAccount.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockInternalAccount.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.InternalAccount{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  currency,
					Status:    string(api.EntityStatus_INACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"internalAccount": {
											"id": "test-id",
											"generalLedgerID": "test-general-ledger-id",
											"code": "test-internal-account-code",
											"name": "test-internal-account-name",
											"description": "create test internal account",
											"currency":` + `"` + currency + `",` + `
											"status": "INACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
			It("returns 200 happy case - INACTIVE TO ACTIVE", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  currency,
					Status:    string(api.EntityStatus_INACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()
				mockInternalAccount.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockInternalAccount.On("LoadByID", mock.Anything, mock.Anything).Return(&storage.InternalAccount{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  currency,
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "ACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"internalAccount": {
											"id": "test-id",
											"generalLedgerID": "test-general-ledger-id",
											"code": "test-internal-account-code",
											"name": "test-internal-account-name",
											"description": "create test internal account",
											"currency":` + `"` + currency + `",` + `
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Id is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Status is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid status", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INVALID_STATUS",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Updated by is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingUpdatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Internal Account not found in database", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{}, data.ErrNoData)

				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database update error", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  currency,
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockInternalAccount.On("Update", mock.Anything, mock.Anything).Return(errors.New("database update error"))

				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseUpdateResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{}, errors.New("database load error"))
				mockInternalAccount.On("Update", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Put(strings.Replace(UpdateInternalAccountStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

// Package testapi ...
package testapi

// Product Config URLs ...
const (
	// Product Template URLs ...
	CreateProductTemplateURL       = "/v1/product-templates"
	GetProductTemplateURL          = "/v1/product-templates/:id"
	UpdateProductTemplateStatusURL = "/v1/product-templates/:id/status"

	// Product Template Parameter URLs ...
	CreateProductTemplateParameterURL      = "/v1/product-template-parameters"
	GetProductTemplateParameterURL         = "/v1/product-template-parameters/:id"
	ListProductTemplateParametersURL       = "/v1/product-template-parameters?productTemplateID=:productTemplateID"
	UpdateProductTemplateParameterValueURL = "/v1/product-template-parameters/:id/value"

	// Product URLs ...
	CreateProductURL       = "/v1/products"
	GetProductURL          = "/v1/products/:id"
	GetProductByCodeURL    = "/v1/products?code=:code"
	UpdateProductStatusURL = "/v1/products/:id/status"

	// Product Variant URLs ...
	CreateProductVariantURL       = "/v1/product-variants"
	GetProductVariantURL          = "/v1/product-variants/:id"
	GetProductVariantByCodeURL    = "/v1/product-variants?code=:code"
	UpdateProductVariantStatusURL = "/v1/product-variants/:id/status"

	// Product Variant Parameter URLs ...
	CreateProductVariantParameterURL         = "/v1/product-variant-parameters"
	GetProductVariantParameterURL            = "/v1/product-variant-parameters/:id"
	ListProductVariantParametersURL          = "/v1/product-variant-parameters?productVariantID=:productVariantID"
	ListEffectiveProductVariantParametersURL = "/v2/effective-product-variant-parameters/get?productVariantCode=:productVariantCode"
	UpdateProductVariantParameterValueURL    = "/v1/product-variant-parameters/:id/value"
	ListProductVariantQuestionURL            = "/v1/product-variant-question"

	// Transaction Catalogue URLs
	CreateTransactionCatalogueURL       = "/v1/transaction-catalogue"
	GetTransactionCatalogueURL          = "/v1/transaction-catalogue/:id"
	UpdateTransactionCatalogueStatusURL = "/v1/transaction-catalogue/:id/status"

	// Product Variant Transaction Catalogue Mapping URLs
	CreateProductVariantTransactionCatalogueMappingURL       = "/v1/product-variant-transaction-catalogue-mapping"
	GetProductVariantTransactionCatalogueMappingURL          = "/v1/product-variant-transaction-catalogue-mapping/:id"
	UpdateProductVariantTransactionCatalogueMappingStatusURL = "/v1/product-variant-transaction-catalogue-mapping/:id/status"

	// General Ledger URLs
	CreateGeneralLedgerURL       = "/v1/general-ledgers"
	GetGeneralLedgerURL          = "/v1/general-ledgers/:id"
	GetGeneralLedgerByCodeURL    = "/v1/general-ledgers?code=:code"
	UpdateGeneralLedgerStatusURL = "/v1/general-ledgers/:id/status"

	// Internal Account URLs
	CreateInternalAccountURL       = "/v1/internal-accounts"
	GetInternalAccountURL          = "/v1/internal-accounts/:id"
	ListInternalAccountsURL        = "/v1/internal-accounts?code=:code&generalLedgerID=:generalLedgerID"
	UpdateInternalAccountStatusURL = "/v1/internal-accounts/:id/status"

	// ProductVariantTransactionCatalogueInternalAccountMapping URLs
	CreateProductVariantTransactionCatalogueInternalAccountMappingURL       = "/v1/product-variant-transaction-catalogue-internal-account-mapping"
	GetProductVariantTransactionCatalogueInternalAccountMappingURL          = "/v1/product-variant-transaction-catalogue-internal-account-mapping/:id"
	GetProductVariantTransactionCatalogueInternalAccountMappingByKeyURL     = "/v1/product-variant-transaction-catalogue-internal-account-mapping?identifierKey=:identifierKey"
	UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusURL = "/v1/product-variant-transaction-catalogue-internal-account-mapping/:id/status"

	// BaseInterest URls
	CreateBaseInterestURL    = "/v1/base-interest"
	GetBaseInterestURL       = "/v1/base-interest/:id"
	GetBaseInterestByCodeURL = "/v1/base-interest?code=:code"

	// Base Interest Version URLs
	CreateBaseInterestVersionURL = "/v1/base-interest-version"
	GetBaseInterestVersionURL    = "/v1/base-interest-version/:id"

	// Base Interest Time Slab Rate URLs
	CreateBaseInterestTimeSlabRateURL = "/v1/base-interest-time-slab-rate"
	GetBaseInterestTimeSlabRateURL    = "/v1/base-interest-time-slab-rate/:id"

	// DepositInterest URls
	CreateDepositInterestURL    = "/v1/deposit-interest"
	GetDepositInterestURL       = "/v1/deposit-interest/:id"
	GetDepositInterestByCodeURL = "/v1/deposit-interest?code=:code"

	// DepositInterestVersion URLs
	CreateDepositInterestVersionURL = "/v1/deposit-interest-version"
	GetDepositInterestVersionURL    = "/v1/deposit-interest-version/:id"

	// DepositInterestAmountSlabRate
	CreateDepositInterestAmountSlabRateURL = "/v1/deposit-interest-amount-slab-rate"
	GetDepositInterestAmountSlabRateURL    = "/v1/deposit-interest-amount-slab-rate/:id"

	// Interest
	GetInterestParametersByProductVariantURL = "/v1/get-interest-parameters"

	// Pocket
	CreatePocketTemplateURL          = "/v1/pocket-templates"
	ListPocketTemplatesURL           = "/v1/pocket-templates?type=:type"
	GetPocketTemplateURL             = "/v1/pocket-templates/:id"
	CreatePocketTemplateQuestionsURL = "/v1/pocket-template-questions"
	ListPocketTemplateQuestionsURL   = "/v1/pocket-template-questions?pocketTemplateID=:pocketTemplateID"
	// ListLoanDocumentSubmissionOptionsByProductVariantCodeURL api to list loan document types by product variant code
	ListLoanDocumentSubmissionOptionsByProductVariantCodeURL = "/v1/loan-document-submission-options"
)

// Error Codes ...
const (
	ErrUnknownResponse = `{
							"code": "3201",
							"message": "Unknown Error"
						  }`
	ErrMissingIdempotencyKeyResponse = `{
											"code": "3202",
											"message": "Missing idempotency key"
										}`
	ErrMissingIDResponse = `{
								"code": "3203",
								"message": "Missing id"
							}`
	ErrMissingProductIDResponse = `{
									   "code": "3204",
									   "message": "Missing product id field"
								    }`
	ErrMissingProductTemplateIDResponse = `{
											   "code": "3205",
											   "message": "Missing product template id field"
										  	}`
	ErrMissingProductVariantIDResponse = `{
											 "code": "3206",
											 "message": "Missing product variant id field"
										  }`
	ErrMissingCodeResponse = `{
								"code": "3207",
								"message": "Missing code field"
							  }`
	ErrMissingNameResponse = `{
								"code": "3208",
								"message": "Missing name"
							  }`
	ErrMissingNamespaceResponse = `{
									  "code": "3209",
									  "message": "Missing namespace field"
								   }`
	ErrMissingVersionResponse = `{
								"code": "3210",
								"message": "Missing version field"
							  }`
	ErrMissingParameterKeyResponse = `{
										"code": "3211",
										"message": "Missing parameter key field"
									  }`
	ErrMissingParameterValueResponse = `{
											"code": "3212",
											"message": "Missing parameter value field"
										}`
	ErrMissingCreatedByResponse = `{
										"code": "3213",
										"message": "Missing created by field"
								   }`
	ErrMissingUpdatedByResponse = `{
										"code": "3214",
										"message": "Missing updated by field"
								   }`
	ErrMissingDomainResponse = `{
									"code": "3215",
									"message": "Missing domain field"
							    }`
	ErrMissingTxnTypeResponse = `{
									"code": "3216",
									"message": "Missing txn type field"
							     }`
	ErrMissingTxnSubTypeResponse = `{
										"code": "3217",
										"message": "Missing txn sub type field"
								    }`
	ErrMissingDisplayNameResponse = `{
										"code": "3218",
										"message": "Missing display name field"
								     }`

	ErrMissingTransactionCatalogueIDResponse = `{
													"code": "3219",
													"message": "Missing transaction catalogue id field"
												}`
	ErrMissingGeneralLedgerIDResponse = `{
												"code": "3220",
												"message":  "Missing general ledger id field"
											}`
	ErrMissingAtLeastOneFieldResponse = `{
											"code": "3221",
											"message":  "At least one parameter is necessary"
										}`
	ErrMissingInternalAccountIDResponse = `{
												"code": "3222",
												"message": "Missing internal account id field"
											}`
	ErrMissingProductVariantTransactionCatalogueMappingIDResponse = `{
																		"code": "3223",
																		"message": "Missing product variant transaction catalogue mapping id field"
																	 }`
	ErrMissingIdentifierKeyResponse = `{
											"code": "3224",
											"message": "Missing identifier key field"
										}`
	ErrMissingBaseInterestIDResponse = `{
											"code": "3225",
											"message":  "Missing base interest id field"
										}`
	ErrMissingBaseInterestVersionIDResponse = `{
													"code": "3226",
													"message":  "Missing base interest version id field"
												}`
	ErrMissingDepositInterestIDResponse = `{
												"code": "3227",
												"message": "Missing deposit interest id field"
											}`
	ErrMissingDepositInterestVersionIDResponse = `{
														"code": "3228",
														"message":  "Missing deposit interest version id field"
                                                  }`
	ErrMissingLinkedBaseInterestIDResponse = `{
												 "code": "3229",
												 "message": "Base Interest ID is mandatory as IsLinkedToBaseRate is true"
											  }`
	ErrInvalidFromAmountResponse = `{
										"code": "3230",
										"message":  "Invalid from amount field"
									}`
	ErrInvalidToAmountResponse = `{
										"code": "3231",
										"message":  "Invalid to amount field"
									}`
	ErrInvalidEntityStatusResponse = `{
											"code": "3232",
											"message": "Invalid status field"
									  }`
	ErrInvalidParameterDatatypeResponse = `{
												"code": "3233",
												"message": "Invalid parameter data type field"
										    }`
	ErrInvalidParameterOverrideLevelResponse = `{
													"code": "3234",
													"message": "Invalid parameter override level field"
												}`
	ErrInvalidTermUnit = `{
								"code": "3235",
								"message":  "Invalid term unit field"
						  }`
	ErrInvalidTermValue = `{
								"code": "3236",
								"message":  "Invalid term value field"
							}`
	ErrInvalidBaseInterestPercentage = `{
											"code": "3237",
											"message":  "Invalid base interest percentage field"
										}`
	ErrInvalidCurrencyResponse = `{
									 "code": "3238",
									 "message": "Invalid currency field"
								  }`
	ErrInvalidRoundOffTypeResponse = `{
										 "code": "3239",
										 "message":  "Invalid round off type field"
									  }`
	ErrInvalidInterestSlabTypeResponse = `{
											 "code": "3240",
											 "message": "Invalid interest slab type field"
										  }`
	ErrInvalidInterestSlabStructureResponse = `{
												 "code": "3241",
												 "message": "Invalid interest slab structure field"
											  }`
	ErrInvalidBaseRateInterestSpreadPercentageResponse = `{
															 "code": "3242",
															 "message": "Invalid base rate interest spread percentage field"
														  }`
	ErrInvalidAbsoluteInterestRatePercentageResponse = `{
															 "code": "3243",
															 "message": "Invalid absolute interest rate percentage field"
														 }`
	ErrDatabaseLoadResponse = `{
									"code": "3244",
									"message": "Database load error"
							   }`
	ErrRecordNotFoundResponse = `{
									"code": "3245",
									"message": "Record not found"
								 }`
	ErrDatabaseSaveResponse = `{
									"code": "3246",
									"message": "Database save error"
							   }`
	ErrDatabaseUpdateResponse = `{
									"code": "3247",
									"message": "Database update error"
								 }`
	ErrFromAmountGreaterThanToAmountResponse = `{
													"code": "3248",
													"message": "FromAmount cannot be greater than ToAmount"
												 }`
	ErrEffectiveDateLessThanCurrentResponse = `{
													"code": "3249",
													"message": "Effective date cannot be less than current date"
												}`
	ErrValidFromLessThanCurrentResponse = `{
												"code": "3250",
												"message": "ValidFrom cannot be less than current date"
											}`
	ErrValidToLessThanCurrentResponse = `{
											"code": "3251",
											"message": "ValidTo cannot be less than current date"
										 }`
	ErrValidFromGreaterThanValidToResponse = `{
												"code": "3252",
												"message": "ValidFrom cannot be greater than ValidTo"
											  }`
	ErrMissingImageURLResponse = `{
									  "code": "3258",
									  "message": "Missing image url"
								  }`
	ErrInvalidPocketTypeResponse = `{
										"code": "3259",
										"message": "Invalid pocket type field"
									}`
	ErrMissingPocketTemplateID = `{
										"code": "3260",
										"message": "Missing Pocket Template ID"
								  }`
	ErrMissingLocale = `{
							"code": "3261",
							"message": "Missing Locale field"
						}`
	ErrMissingQuestionAnswerPairs = `{
										"code": "3262",
										"message": "Missing QuestionAnswerPairs field"
									}`
	ErrMissingQuestionTextOrAnswerSuggestions = `{
													"code": "3263",
													"message": "Missing QuestionText or AnswerSuggestions field"
												}`
	ErrMissingImageIDResponse = `{
								  	"code": "3264",
								  	"message": "Missing image id"
							  	}`
	ErrInvalidProductVariantCodeResponse = `{
												"code": "3268",
												"message": "Invalid productVariantCode field"
											}`

	ErrMissingProductVariantCode = `{ 
		"code":   "3206",
		"message": "Missing productVariantCode field"
	}`

	ErrMissingProductVariantVersion = `{ 
		"code":   "3266",
		"message": "Missing productVariantVersion field"
	}`
)

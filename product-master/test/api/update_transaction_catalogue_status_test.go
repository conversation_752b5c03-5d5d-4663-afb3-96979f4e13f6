package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("UpdateTransactionCatalogueStatus", func() {
	var mockTransactionCatalogue *storage.MockITransactionCatalogueDAO

	nowMinus10 := time.Now().Add(-10 * time.Minute).UTC()
	nowMinus10String, _ := nowMinus10.MarshalText()
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockTransactionCatalogue = &storage.MockITransactionCatalogueDAO{}
		service.Store = &storage.DBStore{
			TransactionCatalogueDAO: mockTransactionCatalogue,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case - ACTIVE to INACTIVE", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{
					PublicID:       "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description: sql.NullString{
						String: "create transaction catalogue",
						Valid:  true,
					},
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()
				mockTransactionCatalogue.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{
					PublicID:       "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description: sql.NullString{
						String: "create transaction catalogue",
						Valid:  true,
					},
					Status:    string(api.EntityStatus_INACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"transactionCatalogue": {
											"id": "test-id",
											"domain": "test-domain",
											"isFinancialTxn": true,
											"txnType": "test-txn-type",
											"txnSubType": "test-txn-sub-type",
											"displayName": "test-display-name",
											"description": "create transaction catalogue",
											"status": "INACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                      }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
			It("returns 200 happy case - INACTIVE to ACTIVE", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{
					PublicID:       "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description: sql.NullString{
						String: "create transaction catalogue",
						Valid:  true,
					},
					Status:    string(api.EntityStatus_INACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()
				mockTransactionCatalogue.On("Update", mock.Anything, mock.Anything).Return(nil)
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{
					PublicID:       "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description: sql.NullString{
						String: "create transaction catalogue",
						Valid:  true,
					},
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil).Once()

				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "ACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"transactionCatalogue": {
											"id": "test-id",
											"domain": "test-domain",
											"isFinancialTxn": true,
											"txnType": "test-txn-type",
											"txnSubType": "test-txn-sub-type",
											"displayName": "test-display-name",
											"description": "create transaction catalogue",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowMinus10String) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                      }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Id is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Status is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid status", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INVALID_STATUS",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidEntityStatusResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Updated by is missing", func() {
			It("return error", func() {
				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingUpdatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Transaction catalogue not found in database", func() {
			It("return error", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{}, data.ErrNoData)

				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database update error", func() {
			It("return error", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{
					PublicID:       "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description: sql.NullString{
						String: "create transaction catalogue",
						Valid:  true,
					},
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "unit-test",
					CreatedAt: nowMinus10,
					UpdatedBy: "unit-test",
					UpdatedAt: now,
				}}, nil)
				mockTransactionCatalogue.On("Update", mock.Anything, mock.Anything).Return(errors.New("database update error"))

				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseUpdateResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{}, errors.New("database load error"))
				mockTransactionCatalogue.On("Update", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Put(strings.Replace(UpdateTransactionCatalogueStatusURL, ":id", "test-id", -1),
					hcl.JSON(`{
										"status": "INACTIVE",
										"updatedBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

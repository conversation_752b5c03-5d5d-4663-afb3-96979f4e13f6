package testapi

import (
	"encoding/json"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

var _ = Describe("CreatePocketTemplate", func() {
	var mockPocketTemplateDAO *storage.MockIPocketTemplateDAO
	var mockPocketTemplateImageSuggestion *storage.MockIPocketTemplateImageSuggestionDAO
	now := time.Now().UTC()

	BeforeEach(func() {
		mockPocketTemplateDAO = &storage.MockIPocketTemplateDAO{}
		mockPocketTemplateImageSuggestion = &storage.MockIPocketTemplateImageSuggestionDAO{}
		mockHermes = &hermesMock.Hermes{}

		service.HermesClient = mockHermes
		service.Store = &storage.DBStore{
			PocketTemplateDAO:                mockPocketTemplateDAO,
			PocketTemplateImageSuggestionDAO: mockPocketTemplateImageSuggestion,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockPocketTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("SaveBatch", mock.Anything, mock.Anything).Return(nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(responses.ListImageDetailsResponseFromHermes(), nil)

				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				responseObj := &api.CreatePocketTemplateResponse{}
				err = json.Unmarshal(resp.Body.Bytes, responseObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(responseObj).Should(Equal(responses.CreatePocketTemplateResponse()))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid Pocket type", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "INVALID_POCKET_TYPE",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidPocketTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket type is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidPocketTypeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Image URL is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": [],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingImageIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Pocket template database save error", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template database load error", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.PocketTemplate{}, errors.New("database load error"))
				mockPocketTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template image suggestion database load error", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateImageSuggestion.On("SaveBatch", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("External service errors", func() {
		When("Hermes error", func() {
			It("return error", func() {
				mockPocketTemplateDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
				mockPocketTemplateDAO.On("Find", mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("SaveBatch", mock.Anything, mock.Anything).Return(nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(&hermes.GetDocumentsResponse{}, errors.New("error from hermes"))

				resp, err := client.Post(CreatePocketTemplateURL,
					hcl.JSON(`{
										"type": "SAVINGS",
										"name": "test-template-name",
										"imageIDs": ["test-image-id"],
									 	"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
			})
		})
	})
})

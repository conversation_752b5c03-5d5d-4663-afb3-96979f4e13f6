package testapi

import (
	"testing"
	"time"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/handlers"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/utils"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/common/testauto/servustest"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
	"gitlab.myteksi.net/gophers/go/staples/statsd/statsdapi"
)

var (
	server     servustest.ServerDescriptor
	client     *hcl.Client
	setupErr   error
	mockHermes *hermesMock.Hermes
	service    *handlers.ProductMasterService
)

func TestSuiteTest(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Test Suite")
}

var _ = BeforeSuite(func() {
	mockHermes = &hermesMock.Hermes{}

	service = &handlers.ProductMasterService{
		AppConfig: &config.AppConfig{
			DefaultAppConfig: servus.DefaultAppConfig{
				Data: &servus.DataConfig{
					MySQL: &data.MysqlConfig{},
				},
			},
			Locale: utils.GetLocale(),
		},
		Statsd: statsdapi.NewNoop(),
	}

	server = servustest.StartServer(service)
	time.Sleep(time.Second)

	client, setupErr = hcl.NewClient(server.URL())
	Expect(setupErr).ShouldNot(HaveOccurred())
})

package testapi

import (
	"encoding/json"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

var _ = Describe("ListPocketTemplateQuestions", func() {
	now := time.Now().UTC()
	var xfccHeader hcl.RequestModifier
	var mockPocketTemplate *storage.MockIPocketTemplateDAO
	var mockPocketTemplateQuestion *storage.MockIPocketTemplateQuestionDAO
	var mockPocketTemplateAnswerSuggestions *storage.MockIPocketTemplateAnswerSuggestionDAO
	var mockPocketTemplateImageSuggestion *storage.MockIPocketTemplateImageSuggestionDAO

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.ProductMaster, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		mockPocketTemplate = &storage.MockIPocketTemplateDAO{}
		mockPocketTemplateQuestion = &storage.MockIPocketTemplateQuestionDAO{}
		mockPocketTemplateAnswerSuggestions = &storage.MockIPocketTemplateAnswerSuggestionDAO{}
		mockPocketTemplateImageSuggestion = &storage.MockIPocketTemplateImageSuggestionDAO{}
		mockHermes = &hermesMock.Hermes{}

		service.HermesClient = mockHermes
		service.Store = &storage.DBStore{
			PocketTemplateDAO:                 mockPocketTemplate,
			PocketTemplateQuestionDAO:         mockPocketTemplateQuestion,
			PocketTemplateAnswerSuggestionDAO: mockPocketTemplateAnswerSuggestions,
			PocketTemplateImageSuggestionDAO:  mockPocketTemplateImageSuggestion,
		}
	})

	Context("Happy-path", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateQuestionsMockDBResponse(now), nil)
				mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateAnswerSuggestionsMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateImageSuggestionMockDBResponse(), nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(responses.ListImageDetailsResponseFromHermes(), nil)

				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "test-id", -1), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				respObj := &api.ListPocketTemplateQuestionsResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.ListPocketTemplateQuestionResponse()))
			})
		})
	})

	Context("Request validation error", func() {
		When("PocketTemplate ID is missing", func() {
			It("return 400 Bad Request", func() {
				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "", -1), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingPocketTemplateID
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Data not found in the DB", func() {
		When("Pocket template not found in DB", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplate{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template Question not found in DB", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Pocket template Answer suggestions not found in DB", func() {
			It("returns 200 happy case", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateQuestionsMockDBResponse(now), nil)
				mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateImageSuggestionMockDBResponse(), nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(responses.ListImageDetailsResponseFromHermes(), nil)

				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				// expectedResponse := ErrRecordNotFoundResponse
				// Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				respObj := &api.ListPocketTemplateQuestionsResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(responses.ListPocketTemplateQuestionWithoutAnswerResponse()))
			})
		})

		When("Pocket template Image suggestions not found in DB", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateQuestionsMockDBResponse(now), nil)
				mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateAnswerSuggestionsMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database Error", func() {
		When("Pocket Template DB hits load error", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.PocketTemplate{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "test-id", -1), xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("External service errors", func() {
		When("Pixie error", func() {
			It("return error", func() {
				mockPocketTemplate.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateMockDBResponse(now), nil)
				mockPocketTemplateQuestion.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateQuestionsMockDBResponse(now), nil)
				mockPocketTemplateAnswerSuggestions.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateAnswerSuggestionsMockDBResponse(now), nil)
				mockPocketTemplateImageSuggestion.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(responses.PocketTemplateImageSuggestionMockDBResponse(), nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(&hermes.GetDocumentsResponse{}, errors.New("error from hermes"))

				resp, err := client.Get(strings.Replace(ListPocketTemplateQuestionsURL, ":pocketTemplateID", "test-id", -1), xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
			})
		})
	})
})

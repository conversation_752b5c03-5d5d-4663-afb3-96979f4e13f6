package testapi

import (
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateBaseInterestTimeSlabRate", func() {
	var mockDepositInterestAmountSlabRate *storage.MockIDepositInterestAmountSlabRateDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockInterestVersion := &storage.MockIDepositInterestVersionDAO{}
		mockInterestVersion.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestVersion{{}}, nil)

		mockDepositInterestAmountSlabRate = &storage.MockIDepositInterestAmountSlabRateDAO{}
		service.Store = &storage.DBStore{
			DepositInterestAmountSlabRateDAO: mockDepositInterestAmountSlabRate,
			DepositInterestVersionDAO:        mockInterestVersion,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything).Return([]*storage.DepositInterestAmountSlabRate{{
					PublicID:                         "test-id",
					DepositInterestVersionID:         1,
					FromAmount:                       "100",
					ToAmount:                         "200",
					BaseRateInterestSpreadPercentage: "0.1",
					AbsoluteInterestRatePercentage:   "2",
					CreatedBy:                        "api-test",
					CreatedAt:                        now,
					UpdatedBy:                        "api-test",
					UpdatedAt:                        now,
				}}, nil)
				mockDepositInterestAmountSlabRate.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"depositInterestAmountSlabRate": {
											"id": "test-id",
											"depositInterestVersionID": "test-deposit-interest-version-id",
											"fromAmount": "100",
										    "toAmount": "200",
										    "baseRateInterestSpreadPercentage": "0.1",
											"absoluteInterestRatePercentage": "2",
										    "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Deposit Interest Version Id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingDepositInterestVersionIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("From Amount is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidFromAmountResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid From Amount", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "xxx",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidFromAmountResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("To Amount is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidToAmountResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid To Amount", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "xxx",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidToAmountResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Base rate interest spread percentage is non numeric", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "xxx",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidBaseRateInterestSpreadPercentageResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Base rate interest spread percentage is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidBaseRateInterestSpreadPercentageResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Absolute interest rate percentage is non numeric", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "xxx",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidAbsoluteInterestRatePercentageResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Absolute interest rate percentage is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidAbsoluteInterestRatePercentageResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("FromAmount is greater than toAmount", func() {
			It("return error", func() {
				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "200",
										"toAmount": "100",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrFromAmountGreaterThanToAmountResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockDepositInterestAmountSlabRate.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockDepositInterestAmountSlabRate.On("Find", mock.Anything, mock.Anything).Return(
					[]*storage.DepositInterestAmountSlabRate{}, errors.New("database load error"))
				mockDepositInterestAmountSlabRate.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateDepositInterestAmountSlabRateURL,
					hcl.JSON(`{
										"depositInterestVersionID": "test-deposit-interest-version-id",
										"fromAmount": "100",
										"toAmount": "200",
										"baseRateInterestSpreadPercentage": "0.1",
										"absoluteInterestRatePercentage": "2",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

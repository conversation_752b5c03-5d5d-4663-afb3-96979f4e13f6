package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetTransactionCatalogue", func() {
	var mockTransactionCatalogue *storage.MockITransactionCatalogueDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockTransactionCatalogue = &storage.MockITransactionCatalogueDAO{}
		service.Store = &storage.DBStore{
			TransactionCatalogueDAO: mockTransactionCatalogue,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{{
					PublicID:       "test-id",
					Domain:         "test-domain",
					IsFinancialTxn: true,
					TxnType:        "test-txn-type",
					TxnSubType:     "test-txn-sub-type",
					DisplayName:    "test-display-name",
					Description: sql.NullString{
						String: "create transaction catalogue",
						Valid:  true,
					},
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetTransactionCatalogueURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"transactionCatalogue": {
											"id": "test-id",
											"domain": "test-domain",
											"isFinancialTxn": true,
											"txnType": "test-txn-type",
											"txnSubType": "test-txn-sub-type",
											"displayName": "test-display-name",
											"description": "create transaction catalogue",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                      }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Transaction Catalogue not found in database", func() {
			It("return error", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetTransactionCatalogueURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockTransactionCatalogue.On("Find", mock.Anything, mock.Anything).Return([]*storage.TransactionCatalogue{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetTransactionCatalogueURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

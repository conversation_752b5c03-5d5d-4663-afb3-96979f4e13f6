package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("ListProductTemplateParameters", func() {
	var mockProductTemplateParameter *storage.MockIProductTemplateParameterDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductTemplate := &storage.MockIProductTemplateDAO{}
		mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{PublicID: "test-product-template-id-1"}}, nil)

		mockProductTemplateParameter = &storage.MockIProductTemplateParameterDAO{}
		service.Store = &storage.DBStore{
			ProductTemplateParameterDAO: mockProductTemplateParameter,
			ProductTemplateDAO:          mockProductTemplate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{
					{
						PublicID:          "test-id",
						ProductTemplateID: 1,
						Namespace:         "test-namespace",
						ParameterKey:      "test-parameter-key",
						ParameterValue:    "test-parameter-value",
						DataType:          "STRING",
						OverrideLevel:     "NO_OVERRIDE",
						ExceptionLevel: sql.NullString{
							String: "",
							Valid:  false,
						},
						Description: sql.NullString{
							String: "create test product template parameter",
							Valid:  true,
						},
						CreatedBy: "api-test",
						CreatedAt: now,
						UpdatedBy: "api-test",
						UpdatedAt: now,
					},
				}, nil)

				resp, err := client.Get(strings.Replace(ListProductTemplateParametersURL, ":productTemplateID", "test-product-template-id-1", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productTemplateParameters": [
											{
												"id": "test-id",
												"productTemplateID": "test-product-template-id-1",
												"namespace": "test-namespace",
												"parameterKey": "test-parameter-key",
												"parameterValue": "test-parameter-value",
												"dataType": "STRING",
												"overrideLevel": "NO_OVERRIDE",
												"description": "create test product template parameter",
												"createdBy": "api-test",
												"createdAt":` + `"` + string(nowString) + `",` + `
												"updatedBy": "api-test",
												"updatedAt":` + `"` + string(nowString) + `"` + `
											}
										]
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Product template id is missing", func() {
			It("return error", func() {
				resp, err := client.Get(strings.Replace(ListProductTemplateParametersURL, ":productTemplateID", "", -1))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingProductTemplateIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product template parameter not found in database", func() {
			It("return error", func() {
				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(ListProductTemplateParametersURL, ":productTemplateID", "test-product-template-id-1", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockProductTemplateParameter.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplateParameter{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(ListProductTemplateParametersURL, ":productTemplateID", "test-product-template-id-1", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

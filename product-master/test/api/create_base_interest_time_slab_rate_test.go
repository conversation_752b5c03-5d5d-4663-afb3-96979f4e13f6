package testapi

import (
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateBaseInterestTimeSlabRate", func() {
	var mockBaseInterestTimeSlabRate *storage.MockIBaseInterestTimeSlabRateDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockBaseVersionDao := &storage.MockIBaseInterestVersionDAO{}
		mockBaseVersionDao.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestVersion{{}}, nil)
		mockBaseInterestTimeSlabRate = &storage.MockIBaseInterestTimeSlabRateDAO{}
		service.Store = &storage.DBStore{
			BaseInterestTimeSlabRateDAO: mockBaseInterestTimeSlabRate,
			BaseInterestVersionDAO:      mockBaseVersionDao,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockBaseInterestTimeSlabRate.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestTimeSlabRate{{
					PublicID:              "test-id",
					BaseInterestVersionID: 1,
					TermUnit:              string(api.TermUnit_MONTH),
					TermValue:             5,
					BaseRatePercentage:    "5.10",
					CreatedBy:             "api-test",
					CreatedAt:             now,
					UpdatedBy:             "api-test",
					UpdatedAt:             now,
				}}, nil)
				mockBaseInterestTimeSlabRate.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "MONTH",
										"termValue": 5,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"baseInterestTimeSlabRate": {
											"id": "test-id",
											"baseInterestVersionID": "test-base-interest-version-id",
											"termUnit": "MONTH",
										    "termValue": 5,
										    "baseInterestPercentage": "5.10",
										    "createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "MONTH",
										"termValue": 5,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Base Interest Version Id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "",
										"termUnit": "MONTH",
										"termValue": 5,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingBaseInterestVersionIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Base interest percentage is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "MONTH",
										"termValue": 5,
										"baseInterestPercentage": "",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidBaseInterestPercentage
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Base interest percentage is invalid", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "MONTH",
										"termValue": 5,
										"baseInterestPercentage": "XXX",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidBaseInterestPercentage
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Term unit is invalid", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "YYY",
										"termValue": 5,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidTermUnit
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Term unit is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "",
										"termValue": 5,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidTermUnit
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Term value is negative", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "DAY",
										"termValue": -12,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidTermValue
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Term value is zero", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "DAY",
										"termValue": 0,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidTermValue
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "DAY",
										"termValue": 12,
										"baseInterestPercentage": "5.10",
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockBaseInterestTimeSlabRate.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "MONTH",
										"termValue": 5,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockBaseInterestTimeSlabRate.On("Find", mock.Anything, mock.Anything).Return([]*storage.BaseInterestTimeSlabRate{}, errors.New("database load error"))
				mockBaseInterestTimeSlabRate.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateBaseInterestTimeSlabRateURL,
					hcl.JSON(`{
										"baseInterestVersionID": "test-base-interest-version-id",
										"termUnit": "MONTH",
										"termValue": 5,
										"baseInterestPercentage": "5.10",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

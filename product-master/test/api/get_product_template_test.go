package testapi

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

var _ = Describe("GetProductTemplate", func() {
	var mockProductTemplate *storage.MockIProductTemplateDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()

	BeforeEach(func() {
		mockProductTemplate = &storage.MockIProductTemplateDAO{}
		service.Store = &storage.DBStore{
			ProductTemplateDAO: mockProductTemplate,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{{
					PublicID: "test-id",
					Name:     "test-product-template-name",
					Description: sql.NullString{
						String: "create test product template",
						Valid:  true,
					},
					Status:    "ACTIVE",
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)

				resp, err := client.Get(strings.Replace(GetProductTemplateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"productTemplate": {
											"id": "test-id",
											"name": "test-product-template-name",
											"description": "create test product template",
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                       }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Error - Data not found in database", func() {
		When("Product template not found in database", func() {
			It("return error", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{}, data.ErrNoData)

				resp, err := client.Get(strings.Replace(GetProductTemplateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))

				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database load error", func() {
			It("return error", func() {
				mockProductTemplate.On("Find", mock.Anything, mock.Anything).Return([]*storage.ProductTemplate{}, errors.New("database load error"))

				resp, err := client.Get(strings.Replace(GetProductTemplateURL, ":id", "test-id", -1))
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

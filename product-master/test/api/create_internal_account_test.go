package testapi

import (
	"database/sql"
	"errors"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("CreateInternalAccount", func() {
	var mockInternalAccount *storage.MockIInternalAccountDAO
	now := time.Now().UTC()
	nowString, _ := now.MarshalText()
	var currency string

	BeforeEach(func() {
		mockGeneralLedger := &storage.MockIGeneralLedgerDAO{}
		mockGeneralLedger.On("Find", mock.Anything, mock.Anything).Return([]*storage.GeneralLedger{{}}, nil)

		mockInternalAccount = &storage.MockIInternalAccountDAO{}
		service.Store = &storage.DBStore{
			InternalAccountDAO: mockInternalAccount,
			GeneralLedgerDAO:   mockGeneralLedger,
		}
		currency = service.AppConfig.Locale.Currency
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{{
					PublicID:        "test-id",
					GeneralLedgerID: 1,
					Code:            "test-internal-account-code",
					Name:            "test-internal-account-name",
					Description: sql.NullString{
						String: "create test internal account",
						Valid:  true,
					},
					Currency:  currency,
					Status:    string(api.EntityStatus_ACTIVE),
					CreatedBy: "api-test",
					CreatedAt: now,
					UpdatedBy: "api-test",
					UpdatedAt: now,
				}}, nil)
				mockInternalAccount.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										 "currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := `{
										"internalAccount": {
											"id": "test-id",
											"generalLedgerID": "test-general-ledger-id",
											"code": "test-internal-account-code",
											"name": "test-internal-account-name",
											"description": "create test internal account",
											"currency": ` + `"` + currency + `",` + `
											"status": "ACTIVE",
											"createdBy": "api-test",
											"createdAt":` + `"` + string(nowString) + `",` + `
											"updatedBy": "api-test",
											"updatedAt":` + `"` + string(nowString) + `"` + `
                                        }
									}`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Service field validation errors", func() {
		When("Idempotency Key is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, ""))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingIdempotencyKeyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("General ledger id is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingGeneralLedgerIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Code is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCodeResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Name is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "",
										"description": "create internal account entry",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingNameResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Currency is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": "",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Invalid currency", func() {
			It("return error", func() {
				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": "INVALID_X",
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrInvalidCurrencyResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Created By is missing", func() {
			It("return error", func() {
				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": `+`"`+currency+`",`+`
										"createdBy": ""
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := ErrMissingCreatedByResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})

	Context("Database errors", func() {
		When("Database save error", func() {
			It("return error", func() {
				mockInternalAccount.On("Save", mock.Anything, mock.Anything).Return(errors.New("database save error"))

				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseSaveResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Database load error", func() {
			It("return error", func() {
				mockInternalAccount.On("Find", mock.Anything, mock.Anything).Return([]*storage.InternalAccount{}, errors.New("database load error"))
				mockInternalAccount.On("Save", mock.Anything, mock.Anything).Return(nil)

				resp, err := client.Post(CreateInternalAccountURL,
					hcl.JSON(`{
										"generalLedgerID": "test-general-ledger-id",
										"code": "test-internal-account-code",
										"name": "test-internal-account-name",
										"description": "create internal account entry",
										"currency": `+`"`+currency+`",`+`
										"createdBy": "api-test"
									}`), hcl.Header(constants.IdempotencyKeyHeader, "test-idempotency-key"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))

				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})

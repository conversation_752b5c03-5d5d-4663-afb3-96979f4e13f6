package responses

import (
	"database/sql"
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

// PocketTemplateImageSuggestionMockDBResponse ...
func PocketTemplateImageSuggestionMockDBResponse() []*storage.PocketTemplateImageSuggestion {
	return []*storage.PocketTemplateImageSuggestion{
		{
			PublicID:         "test-image-public-id",
			PocketTemplateID: 1,
			ImageID:          "image-id",
			Status:           string(api.PocketType_SAVINGS),
		},
	}
}

// PocketTemplateMockDBResponse sample response
func PocketTemplateMockDBResponse(now time.Time) []*storage.PocketTemplate {
	return []*storage.PocketTemplate{
		{
			ID:        1,
			PublicID:  "test-id",
			Type:      string(api.PocketType_SAVINGS),
			Name:      "test-template-name",
			Status:    constants.ACTIVE,
			CreatedBy: "unit-test",
			CreatedAt: now,
			UpdatedBy: "unit-test",
			UpdatedAt: now,
		},
	}
}

// InactivePocketTemplateMockDBResponse sample response
func InactivePocketTemplateMockDBResponse(now time.Time) []*storage.PocketTemplate {
	return []*storage.PocketTemplate{
		{
			ID:        1,
			PublicID:  "test-id",
			Type:      string(api.PocketType_SAVINGS),
			Name:      "test-template-name",
			Status:    constants.INACTIVE,
			CreatedBy: "unit-test",
			CreatedAt: now,
			UpdatedBy: "unit-test",
			UpdatedAt: now,
		},
	}
}

// DBMYPocketTemplateMockDBResponse sample response
func DBMYPocketTemplateMockDBResponse(now time.Time) []*storage.PocketTemplate {
	return []*storage.PocketTemplate{
		{
			ID:        1,
			PublicID:  "test-id",
			Type:      string(api.PocketType_SAVINGS),
			Name:      "Holiday",
			Status:    constants.ACTIVE,
			CreatedBy: "unit-test",
			CreatedAt: now,
			UpdatedBy: "unit-test",
			UpdatedAt: now,
		},
		{
			ID:        1,
			PublicID:  "test-id",
			Type:      string(api.PocketType_SAVINGS),
			Name:      "Custom",
			Status:    constants.ACTIVE,
			CreatedBy: "unit-test",
			CreatedAt: now,
			UpdatedBy: "unit-test",
			UpdatedAt: now,
		},
	}
}

// PocketTemplateQuestionsMockDBResponse sample response
func PocketTemplateQuestionsMockDBResponse(now time.Time) []*storage.PocketTemplateQuestion {
	return []*storage.PocketTemplateQuestion{
		{
			ID:               1234,
			PublicID:         "test-qid",
			PocketTemplateID: 1,
			QuestionText:     "Where are you heading to?",
			Locale:           string(api.Locale_EN),
			Status:           constants.ACTIVE,
			CreatedBy:        "unit-test",
			CreatedAt:        now,
			UpdatedBy:        "unit-test",
			UpdatedAt:        now,
		},
	}
}

// PocketTemplateAnswerSuggestionsMockDBResponse sample response
func PocketTemplateAnswerSuggestionsMockDBResponse(now time.Time) []*storage.PocketTemplateAnswerSuggestion {
	return []*storage.PocketTemplateAnswerSuggestion{
		{
			ID:                       01,
			PublicID:                 "test-aid-01",
			PocketTemplateQuestionID: 1234,
			AnswerSuggestionText:     "US",
			Locale:                   string(api.Locale_EN),
			Status:                   constants.ACTIVE,
			CreatedBy:                "unit-test",
			CreatedAt:                now,
			UpdatedBy:                "unit-test",
			UpdatedAt:                now,
		},
		{
			ID:                       02,
			PublicID:                 "test-aid-02",
			PocketTemplateQuestionID: 1234,
			AnswerSuggestionText:     "Paris",
			Locale:                   string(api.Locale_EN),
			Status:                   constants.ACTIVE,
			CreatedBy:                "unit-test",
			CreatedAt:                now,
			UpdatedBy:                "unit-test",
			UpdatedAt:                now,
		},
		{
			ID:                       02,
			PublicID:                 "test-aid-02",
			PocketTemplateQuestionID: 1234,
			AnswerSuggestionText:     "UK",
			Locale:                   string(api.Locale_EN),
			Status:                   constants.ACTIVE,
			CreatedBy:                "unit-test",
			CreatedAt:                now,
			UpdatedBy:                "unit-test",
			UpdatedAt:                now,
		},
	}
}

// SampleQuestionAnswerPairs response
func SampleQuestionAnswerPairs() []api.QuestionAnswerPairsResponse {
	return []api.QuestionAnswerPairsResponse{
		{
			ID:                "test-qid",
			QuestionText:      "Where are you heading to?",
			AnswerSuggestions: []string{"US", "Paris", "UK"},
		},
	}
}

// SampleQuestionWithoutAnswerPairs response
func SampleQuestionWithoutAnswerPairs() []api.QuestionAnswerPairsResponse {
	return []api.QuestionAnswerPairsResponse{
		{
			ID:                "test-qid",
			QuestionText:      "Where are you heading to?",
			AnswerSuggestions: nil,
		},
	}
}

// SampleProductVariantDAOResponse ...
func SampleProductVariantDAOResponse() []*storage.ProductVariant {
	return []*storage.ProductVariant{{
		ID:        1,
		PublicID:  "test-product-variant-id",
		ProductID: 1,
		Code:      "test-product-code",
		Version:   "1",
		Name:      "test-product-variant",
		Status:    "ACTIVE",
	}}
}

// SampleProductTemplateResponse ...
func SampleProductTemplateResponse() []*storage.ProductTemplate {
	return []*storage.ProductTemplate{{
		ID:   1,
		Code: "BOOST_POCKET",
	}}
}

// ListProductVariantsResponse ...
func ListProductVariantsResponse(now time.Time, version string) *api.ListProductVariantsResponse {
	return &api.ListProductVariantsResponse{
		ProductVariants: []api.ProductVariant{
			{
				Id:        "test-product-variant-id",
				ProductID: "1",
				Code:      "test-product-code",
				Version:   version,
				Name:      "test-product-variant",
				Status:    "ACTIVE",
			}},
	}
}

// SampleProductVariantVersionsDAOResponse ...
func SampleProductVariantVersionsDAOResponse() []*storage.ProductVariant {
	return []*storage.ProductVariant{
		{
			ID:        1,
			PublicID:  "test-product-variant-id",
			ProductID: 1,
			Code:      "test-product-code",
			Version:   "1",
			Name:      "test-product-variant",
			Status:    "ACTIVE",
		},
		{
			ID:        1,
			PublicID:  "test-product-variant-id",
			ProductID: 1,
			Code:      "test-product-code",
			Version:   "2",
			Name:      "test-product-variant",
			Status:    "ACTIVE",
		},
	}
}

// InvalidProductVariantVersions ...
func InvalidProductVariantVersions() []*storage.ProductVariant {
	return []*storage.ProductVariant{
		{
			ID:        1,
			PublicID:  "test-product-variant-id",
			ProductID: 1,
			Code:      "test-product-code",
			Version:   "a",
			Name:      "test-product-variant",
			Status:    "ACTIVE",
		},
		{
			ID:        1,
			PublicID:  "test-product-variant-id",
			ProductID: 1,
			Code:      "test-product-code",
			Version:   "b",
			Name:      "test-product-variant",
			Status:    "ACTIVE",
		},
	}
}

// SampleProductVariantParameterDAOResponse ...
func SampleProductVariantParameterDAOResponse() []*storage.ProductVariantParameter {
	return []*storage.ProductVariantParameter{{
		PublicID:         "test-product-variant-parameter-id-1",
		ProductVariantID: 1,
		Namespace:        "test-namespace",
		ParameterKey:     "test-product-variant-parameter-1",
		ParameterValue:   "test-product-variant-parameter-value-1",
		DataType:         "INT",
		OverrideLevel:    "none",
	}}
}

// SampleProductDAOResponse ...
func SampleProductDAOResponse() *storage.Product {
	return &storage.Product{
		PublicID:          "test-product-id",
		ProductTemplateID: 1,
		Code:              "test-product-code",
		Name:              "test-product-name",
		Status:            "ACTIVE",
	}
}

// SampleDepositInterestDAOResponse ...
func SampleDepositInterestDAOResponse() []*storage.DepositInterest {
	return []*storage.DepositInterest{
		{
			ID:               1,
			ProductVariantID: 1,
		},
	}
}

// SampleDepositInterestVersionDAOResponse ...
func SampleDepositInterestVersionDAOResponse(now time.Time) []*storage.DepositInterestVersion {
	return []*storage.DepositInterestVersion{
		{
			ID:                1,
			DepositInterestID: 1,
			Version:           "1",
			EffectiveDate:     now,
		},
	}
}

// SampleDepositInterestSlabRateDAOResponse ...
func SampleDepositInterestSlabRateDAOResponse(now time.Time) []*storage.DepositInterestAmountSlabRate {
	return []*storage.DepositInterestAmountSlabRate{
		{
			PublicID:                         "test-id-1",
			DepositInterestVersionID:         1,
			FromAmount:                       "0",
			ToAmount:                         "75000",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "0.08",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-1",
			DepositInterestVersionID:         1,
			FromAmount:                       "75000",
			ToAmount:                         "999999999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "0.08",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
	}
}

// SampleProductVariantQuestions ...
func SampleProductVariantQuestions(now time.Time) []*storage.ProductVariantQuestion {
	return []*storage.ProductVariantQuestion{
		{
			ID:           1,
			PublicID:     "test-qid",
			Code:         "test-question-code",
			QuestionText: "How are you?",
			Locale:       string(api.Locale_EN),
			CreatedAt:    now,
			UpdatedAt:    now,
			UpdatedBy:    "test-case",
			CreatedBy:    "test-case",
		},
	}
}

// SampleProductVariantAnswerSuggestions ...
func SampleProductVariantAnswerSuggestions(now time.Time) []*storage.ProductVariantAnswerSuggestion {
	return []*storage.ProductVariantAnswerSuggestion{
		{ID: 1, ProductVariantQuestionID: 1, Code: "i_am_fine", AnswerSuggestionText: "I am fine", Locale: string(api.Locale_EN)},
		{ID: 2, ProductVariantQuestionID: 1, Code: "i_am_very_well", AnswerSuggestionText: "I am very well", Locale: string(api.Locale_EN)},
	}
}

// SampleLoanAllowedAmountTenorSlabDAO ...
func SampleLoanAllowedAmountTenorSlabDAO() []*storage.LoanAllowedAmountTenorSlab {
	return []*storage.LoanAllowedAmountTenorSlab{
		{ID: 1, FromAmount: "100", ToAmount: "500", Currency: "SGD", TenorUnit: "month", MinTenor: "2", MaxTenor: "10"},
	}
}

// SampleProductTemplateParameterDAOResponse ...
func SampleProductTemplateParameterDAOResponse() []*storage.ProductTemplateParameter {
	return []*storage.ProductTemplateParameter{{
		PublicID:          "test-product-variant-parameter-id-1",
		ProductTemplateID: 1,
		Namespace:         "test-namespace",
		ParameterKey:      "test-product-template-parameter-2",
		ParameterValue:    "test-product-template-parameter-value-2",
		DataType:          "INT",
		OverrideLevel:     "none",
	}}
}

// SampleProductVariantTransactionCatalogueMappingDAOResponse ...
func SampleProductVariantTransactionCatalogueMappingDAOResponse(now time.Time) []*storage.ProductVariantTransactionCatalogueMapping {
	return []*storage.ProductVariantTransactionCatalogueMapping{{
		ID:                     1,
		PublicID:               "test-product-variant-transaction-catalogue-id",
		ProductVariantID:       1,
		TransactionCatalogueID: 1,
		Status:                 "ACTIVE",
		CreatedBy:              "unit-test",
		CreatedAt:              now,
		UpdatedBy:              "unit-test",
		UpdatedAt:              now,
	}}
}

// SampleProductVariantTransactionCatalogueInternalAccountMappingDAOResponse ...
func SampleProductVariantTransactionCatalogueInternalAccountMappingDAOResponse(now time.Time) []*storage.ProductVariantTransactionCatalogueInternalAccountMapping {
	return []*storage.ProductVariantTransactionCatalogueInternalAccountMapping{{
		ID:       1,
		PublicID: "test-product-variant-transaction-catalogue-internal-account-id",
		ProductVariantTransactionCatalogueMappingID: 1,
		InternalAccountID: 1,
		IdentifierKey:     "",
		Status:            "ACTIVE",
		CreatedBy:         "unit-test",
		CreatedAt:         now,
		UpdatedBy:         "unit-test",
		UpdatedAt:         now,
	}}
}

// SampleInternalAccountDAOResponse ...
func SampleInternalAccountDAOResponse(now time.Time) []*storage.InternalAccount {
	return []*storage.InternalAccount{{
		ID:              1,
		PublicID:        "test-internal-account-id",
		GeneralLedgerID: 1,
		Code:            "10001",
		Name:            "Sample Clearing Account",
		Description:     sql.NullString{String: "Sample Clearing Account", Valid: true},
		Currency:        "SGD",
		Status:          "ACTIVE",
		CreatedBy:       "unit-test",
		CreatedAt:       now,
		UpdatedBy:       "unit-test",
		UpdatedAt:       now,
	}}
}

// SampleGeneralLedgerDAOResponse ...
func SampleGeneralLedgerDAOResponse(now time.Time) []*storage.GeneralLedger {
	return []*storage.GeneralLedger{{
		ID:          1,
		PublicID:    "test-general-ledger-1",
		Code:        "10001",
		Name:        "test-general-ledger-1",
		Description: sql.NullString{String: "test-general-ledger-1", Valid: true},
		Currency:    "SGD",
		Status:      "ACTIVE",
		CreatedBy:   "unit-test",
		CreatedAt:   now,
		UpdatedBy:   "",
		UpdatedAt:   now,
	}}
}

// SampleLoanPastDueVersionDAOResponse ...
func SampleLoanPastDueVersionDAOResponse(now time.Time) []*storage.LoanPastDueVersion {
	return []*storage.LoanPastDueVersion{
		{
			ID:            1,
			PublicID:      "test-loan-past-due-version-1",
			ProductID:     5,
			Version:       "1",
			EffectiveDate: now,
			CreatedBy:     "MANUAL",
			CreatedAt:     now,
			UpdatedBy:     "MANUAL",
			UpdatedAt:     now,
		},
	}
}

// SampleGetLoanPastDueSlabDAOResponse ...
func SampleGetLoanPastDueSlabDAOResponse(now time.Time) []*storage.LoanPastDueSlab {
	return []*storage.LoanPastDueSlab{
		{
			PublicID:   "test-loan-past-due-slab-1",
			FromUnit:   1,
			ToUnit:     29,
			SlabType:   "DAY",
			BucketName: "Bucket 1",
			CreatedBy:  "MANUAL",
			CreatedAt:  now,
			UpdatedBy:  "MANUAL",
			UpdatedAt:  now,
		},
		{
			PublicID:   "test-loan-past-due-slab-2",
			FromUnit:   30,
			ToUnit:     59,
			SlabType:   "DAY",
			BucketName: "Bucket 2",
			CreatedBy:  "MANUAL",
			CreatedAt:  now,
			UpdatedBy:  "MANUAL",
			UpdatedAt:  now,
		},
	}
}

// SampleProductDBResponse ...
func SampleProductDBResponse() []*storage.Product {
	return []*storage.Product{
		{
			PublicID:          "test-product-id",
			ProductTemplateID: 1,
			Code:              "test-product-code",
			Name:              "test-product-name",
			Status:            "ACTIVE",
		},
	}
}

// SampleLoanInstructionVersionDBResponse ...
func SampleLoanInstructionVersionDBResponse() []*storage.LoanInstructionVersion {
	return []*storage.LoanInstructionVersion{{
		ID:               101,
		PublicID:         "4fb08d2a-f180-4c6b-9974-f60cb17ff06c",
		ProductVariantID: 10,
		Version:          "1",
		InstructionType:  "WRITE_OFF",
		EffectiveDate:    time.Time{},
		Description:      "Default Flexi Loan - Term Loan WriteOff Instruction v1.0",
		CreatedAt:        time.Time{},
		CreatedBy:        "MANUAL",
		UpdatedAt:        time.Time{},
		UpdatedBy:        "MANUAL",
	}}
}

// SampleLoanInstructionDBResponse ...
func SampleLoanInstructionDBResponse() []*storage.LoanInstruction {
	restrictions, _ := json.Marshal(map[string]interface{}{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false})
	return []*storage.LoanInstruction{{
		ID:                       101,
		PublicID:                 "4fb08d2a-f180-4c6b-9974-f60cb17ff06c",
		LoanInstructionVersionID: 10,
		Code:                     "DECEASED",
		Name:                     "Deceased",
		CreatedAt:                time.Time{},
		CreatedBy:                "MANUAL",
		UpdatedAt:                time.Time{},
		UpdatedBy:                "MANUAL",
		Restrictions:             restrictions,
	}}
}

// SampleLoanInterestDAOResponse returns the sample loan interest DAO response
func SampleLoanInterestDAOResponse() []*storage.LoanInterest {
	return []*storage.LoanInterest{
		{
			ID:           1,
			InterestType: "NORMAL",
		},
	}
}

// SampleLoanInterestVersionDAOResponse returns the sample loan interest version DAO response
func SampleLoanInterestVersionDAOResponse() []*storage.LoanInterestVersion {
	return []*storage.LoanInterestVersion{{
		ID:             1,
		LoanInterestID: 1,
		Version:        "1",
		Description:    sql.NullString{String: "Test Interest Version"},
	}}
}

// SampleBizLoanInterestSlabRateDAOResponse returns the sample loan slab rates DAO response
func SampleBizLoanInterestSlabRateDAOResponse() []*storage.LoanInterestSlabRate {
	return []*storage.LoanInterestSlabRate{
		{
			ID:                               1,
			SlabType:                         "DAY",
			FromUnit:                         "1",
			ToUnit:                           "15",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "3",
		},
	}
}

// SampleBoostPocketParameterDAOResponse ...
func SampleBoostPocketParameterDAOResponse() []*storage.ProductVariantParameter {
	return []*storage.ProductVariantParameter{{
		ParameterValue: "3.68",
	}}
}

// SampleBoostPocketBonusSlabRateDAOResponse ...
func SampleBoostPocketBonusSlabRateDAOResponse(now time.Time) []*storage.DepositInterestAmountSlabRate {
	return []*storage.DepositInterestAmountSlabRate{
		{
			PublicID:                         "test-id-1",
			DepositInterestVersionID:         1,
			FromAmount:                       "75000",
			ToAmount:                         "999999999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "10",
			MaxTenor:                         "999",
			MinTenorUnit:                     "DAY",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
	}
}

// SampleBoostPocketMultipleBonusSlabRatesResponse ...
// nolint : funlen
func SampleBoostPocketMultipleBonusSlabRatesResponse(now time.Time) []*storage.DepositInterestAmountSlabRate {
	return []*storage.DepositInterestAmountSlabRate{
		{
			PublicID:                         "test-id-3",
			DepositInterestVersionID:         1,
			FromAmount:                       "1000",
			ToAmount:                         "4999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "10",
			MaxTenor:                         "999",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-1",
			DepositInterestVersionID:         1,
			FromAmount:                       "1000",
			ToAmount:                         "4999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-2",
			DepositInterestVersionID:         1,
			FromAmount:                       "5000",
			ToAmount:                         "10000",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-4",
			DepositInterestVersionID:         1,
			FromAmount:                       "5000",
			ToAmount:                         "10000",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "10",
			MaxTenor:                         "999",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
	}
}

// SampleBoostPocketMaxBonusSlabRatesResponse ...
// nolint : funlen
func SampleBoostPocketMaxBonusSlabRatesResponse(now time.Time) []*storage.DepositInterestAmountSlabRate {
	return []*storage.DepositInterestAmountSlabRate{
		{
			PublicID:                         "test-id-3",
			DepositInterestVersionID:         1,
			FromAmount:                       "1000",
			ToAmount:                         "4999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "10",
			MaxTenor:                         "999999",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-1",
			DepositInterestVersionID:         1,
			FromAmount:                       "1000",
			ToAmount:                         "4999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-2",
			DepositInterestVersionID:         1,
			FromAmount:                       "5000",
			ToAmount:                         "10000",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-4",
			DepositInterestVersionID:         1,
			FromAmount:                       "5000",
			ToAmount:                         "10000",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "10",
			MaxTenor:                         "999999",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
	}
}

// SampleBoostPocketDifferentSlabResponse ...
// nolint : funlen
func SampleBoostPocketDifferentSlabResponse(now time.Time) []*storage.DepositInterestAmountSlabRate {
	return []*storage.DepositInterestAmountSlabRate{
		{
			PublicID:                         "test-id-2",
			DepositInterestVersionID:         1,
			FromAmount:                       "1000",
			ToAmount:                         "4999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-1",
			DepositInterestVersionID:         1,
			FromAmount:                       "1000",
			ToAmount:                         "4999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "DAY",
			MaxTenorUnit:                     "DAY",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-3",
			DepositInterestVersionID:         1,
			FromAmount:                       "5000",
			ToAmount:                         "10000",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-5",
			DepositInterestVersionID:         1,
			FromAmount:                       "1000",
			ToAmount:                         "4999",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "1",
			MaxTenor:                         "9",
			MinTenorUnit:                     "YEAR",
			MaxTenorUnit:                     "YEAR",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
		{
			PublicID:                         "test-id-4",
			DepositInterestVersionID:         1,
			FromAmount:                       "5000",
			ToAmount:                         "10000",
			BaseRateInterestSpreadPercentage: "0",
			AbsoluteInterestRatePercentage:   "2.08",
			MinTenor:                         "10",
			MaxTenor:                         "999999",
			MinTenorUnit:                     "MONTH",
			MaxTenorUnit:                     "MONTH",
			CreatedBy:                        "unit-test",
			CreatedAt:                        now,
			UpdatedBy:                        "unit-test",
			UpdatedAt:                        now,
		},
	}
}

func SampleDocumentSubmissionOptionDAOResponse() []*storage.LoanDocumentSubmissionOption {
	return []*storage.LoanDocumentSubmissionOption{
		{
			ID:               1,
			ProductVariantID: 1,
			SalaryType:       "SA",
			ApplicationType:  "NEW",
			DocumentType:     "epfStatement",
		},
		{
			ID:               2,
			ProductVariantID: 1,
			SalaryType:       "SA",
			ApplicationType:  "NEW",
			DocumentType:     "personalBankStatement",
		},
		{
			ID:               3,
			ProductVariantID: 1,
			SalaryType:       "SE",
			ApplicationType:  "REVIEW_REVIEW_LIMIT",
			DocumentType:     "businessBankStatement",
		},
	}
}

func SampleDocumentSubmissionOptionVersionDAOResponse() []*storage.LoanDocumentSubmissionOptionVersion {
	return []*storage.LoanDocumentSubmissionOptionVersion{
		{
			ID:                             1,
			LoanDocumentSubmissionOptionID: 1,
			Status:                         "ACTIVE",
		},
		{
			ID:                             2,
			LoanDocumentSubmissionOptionID: 2,
			Status:                         "ACTIVE",
		},
		{
			ID:                             3,
			LoanDocumentSubmissionOptionID: 3,
			Status:                         "ACTIVE",
		},
	}
}

func SampleDocumentSubmissionOptionParametersDAOResponse() []*storage.LoanDocumentSubmissionOptionParameters {
	allowedFileExtensionsArr := []string{"pdf", "jpg", "jpeg", "png"}
	allowedFileExtensions, _ := json.Marshal(allowedFileExtensionsArr)
	return []*storage.LoanDocumentSubmissionOptionParameters{
		{
			ID:                                    1,
			LoanDocumentSubmissionOptionVersionID: 1,
			IsEnabled:                             true,
			Priority:                              1,
			RequiredDocumentNumber:                2,
			RequiredDocumentUnit:                  "year",
			MaxFileSizeInBytes:                    3145728,
			MaxUploadLimit:                        12,
			AllowedFileExtensions:                 allowedFileExtensions,
		},
		{
			ID:                                    2,
			LoanDocumentSubmissionOptionVersionID: 2,
			IsEnabled:                             true,
			Priority:                              2,
			RequiredDocumentNumber:                2,
			RequiredDocumentUnit:                  "year",
			MaxFileSizeInBytes:                    3145728,
			MaxUploadLimit:                        12,
			AllowedFileExtensions:                 allowedFileExtensions,
		},
		{
			ID:                                    3,
			LoanDocumentSubmissionOptionVersionID: 3,
			IsEnabled:                             true,
			Priority:                              2,
			RequiredDocumentNumber:                2,
			RequiredDocumentUnit:                  "year",
			MaxFileSizeInBytes:                    3145728,
			MaxUploadLimit:                        12,
			AllowedFileExtensions:                 allowedFileExtensions,
		},
	}
}

package responses

import (
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
)

// CreatePocketTemplateResponse ...
func CreatePocketTemplateResponse() *api.CreatePocketTemplateResponse {
	return &api.CreatePocketTemplateResponse{
		PocketTemplate: &api.PocketTemplate{
			ID:   "test-id",
			Type: api.PocketType_SAVINGS,
			Name: "test-template-name",
			Images: []api.Image{
				{
					ID:  "image-id",
					URL: "test-url",
				},
			},
			DefaultImage: &api.Image{
				ID:  "image-id",
				URL: "test-url",
			},
		},
	}
}

// GetPocketTemplateResponse ...
func GetPocketTemplateResponse() *api.GetPocketTemplateResponse {
	return &api.GetPocketTemplateResponse{
		PocketTemplate: &api.PocketTemplate{
			ID:   "test-id",
			Type: api.PocketType_SAVINGS,
			Name: "test-template-name",
			Images: []api.Image{
				{
					ID:  "image-id",
					URL: "test-url",
				},
			},
			DefaultImage: &api.Image{
				ID:  "image-id",
				URL: "test-url",
			},
		},
	}
}

// GetInactivePocketTemplateResponse ...
func GetInactivePocketTemplateResponse() *api.GetPocketTemplateResponse {
	return &api.GetPocketTemplateResponse{
		PocketTemplate: &api.PocketTemplate{
			ID:           "test-id",
			Type:         api.PocketType_SAVINGS,
			Name:         "test-template-name",
			Images:       nil,
			DefaultImage: &api.Image{},
		},
	}
}

// ListPocketTemplatesResponse ...
func ListPocketTemplatesResponse() *api.ListPocketTemplatesResponse {
	return &api.ListPocketTemplatesResponse{
		PocketTemplates: []api.PocketTemplate{
			{
				ID:   "test-id",
				Type: api.PocketType_SAVINGS,
				Name: "test-template-name",
				Images: []api.Image{
					{
						ID:  "image-id",
						URL: "test-url",
					},
				},
				DefaultImage: &api.Image{
					ID:  "image-id",
					URL: "test-url",
				},
			},
		},
	}
}

// DBMYListPocketTemplatesResponse ...
func DBMYListPocketTemplatesResponse() *api.ListPocketTemplatesResponse {
	return &api.ListPocketTemplatesResponse{
		PocketTemplates: []api.PocketTemplate{
			{
				ID:   "test-id",
				Type: api.PocketType_SAVINGS,
				Name: "Holiday",
				Images: []api.Image{
					{
						ID:  "image-id",
						URL: "test-url",
					},
				},
				DefaultImage: &api.Image{
					ID:  "image-id",
					URL: "test-url",
				},
			},
			{
				ID:   "test-id",
				Type: api.PocketType_SAVINGS,
				Name: "Custom",
				Images: []api.Image{
					{
						ID:  "image-id",
						URL: "test-url",
					},
				},
				DefaultImage: &api.Image{
					ID:  "image-id",
					URL: "test-url",
				},
			},
		},
	}
}

// CreatePocketTemplateQuestionResponse sample response
func CreatePocketTemplateQuestionResponse() *api.CreatePocketTemplateQuestionsResponse {
	return &api.CreatePocketTemplateQuestionsResponse{
		PocketTemplateQuestionsDetail: &api.PocketTemplateQuestionsDetail{
			PocketTemplateID:    "test-id",
			Locale:              api.Locale_EN,
			QuestionAnswerPairs: SampleQuestionAnswerPairs(),
		},
	}
}

// CreatePocketTemplateQuestionWithoutAnswerResponse sample response
func CreatePocketTemplateQuestionWithoutAnswerResponse() *api.CreatePocketTemplateQuestionsResponse {
	return &api.CreatePocketTemplateQuestionsResponse{
		PocketTemplateQuestionsDetail: &api.PocketTemplateQuestionsDetail{
			PocketTemplateID:    "test-id",
			Locale:              api.Locale_EN,
			QuestionAnswerPairs: SampleQuestionWithoutAnswerPairs(),
		},
	}
}

// ListPocketTemplateQuestionResponse sample response
func ListPocketTemplateQuestionResponse() *api.ListPocketTemplateQuestionsResponse {
	return &api.ListPocketTemplateQuestionsResponse{
		PocketTemplateQuestionsDetail: &api.PocketTemplateQuestionsDetail{
			PocketTemplateID: "test-id",
			Locale:           api.Locale_EN,
			QuestionAnswerPairs: []api.QuestionAnswerPairsResponse{
				{
					ID:                "test-qid",
					QuestionText:      "Where are you heading to?",
					AnswerSuggestions: []string{"US", "Paris", "UK"},
				},
			},
			Images: []api.Image{
				{
					ID:  "image-id",
					URL: "test-url",
				},
			},
			DefaultImage: &api.Image{
				ID:  "image-id",
				URL: "test-url",
			},
		},
	}
}

// ListPocketTemplateQuestionWithoutAnswerResponse sample response
func ListPocketTemplateQuestionWithoutAnswerResponse() *api.ListPocketTemplateQuestionsResponse {
	return &api.ListPocketTemplateQuestionsResponse{
		PocketTemplateQuestionsDetail: &api.PocketTemplateQuestionsDetail{
			PocketTemplateID: "test-id",
			Locale:           api.Locale_EN,
			QuestionAnswerPairs: []api.QuestionAnswerPairsResponse{
				{
					ID:                "test-qid",
					QuestionText:      "Where are you heading to?",
					AnswerSuggestions: nil,
				},
			},
			Images: []api.Image{
				{
					ID:  "image-id",
					URL: "test-url",
				},
			},
			DefaultImage: &api.Image{
				ID:  "image-id",
				URL: "test-url",
			},
		},
	}
}

// ListImageDetailsResponseFromHermes ...
func ListImageDetailsResponseFromHermes() *hermes.GetDocumentsResponse {
	return &hermes.GetDocumentsResponse{
		Documents: []hermes.Document{
			{
				Id:           "image-id",
				SafeID:       "test-safe-id",
				PresignedURL: "test-url",
				FileType:     "test-file-type",
				Metadata:     nil,
				CreatedBy:    "test-safe-id",
				CreatedAt:    time.Now().UTC(),
			},
		},
		Pagination: nil,
	}
}

// GetInterestParametersByProductVariantResponse ...
func GetInterestParametersByProductVariantResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		DepositsAccount: &api.DepositsAccountInterestParameters{
			InterestRateType: api.InterestRateType_flat,
			FlatInterest: &api.DepositsAccountFlatInterest{
				Rate:       8,
				Multiplier: 100,
			},
		},
	}
}

// GetBizLoanInterestParametersByProductVariantResponse ...
func GetBizLoanInterestParametersByProductVariantResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		LoanAccount: &api.LoanAccountInterestParameters{
			NormalInterest: []api.LoanAccountFlatInterest{{
				Rate:       300,
				Multiplier: 100,
				SlabType:   "DAY",
				FromUnit:   "1",
				ToUnit:     "15",
			}},
			PenalInterest: []api.LoanAccountFlatInterest{{
				Rate:       300,
				Multiplier: 100,
				SlabType:   "DAY",
				FromUnit:   "1",
				ToUnit:     "15",
			}},
		},
	}
}

// CreateProductVariantQuestionResponse ...
func CreateProductVariantQuestionResponse(now time.Time) *api.CreateProductVariantQuestionResponse {
	return &api.CreateProductVariantQuestionResponse{
		QuestionsDetail: &api.ProductVariantQuestionsDetail{
			ProductVariantCode: "product1", ProductVariantVersion: "1", Locale: api.Locale_EN,
			QuestionAnswerPairs: []api.QuestionAnswerPairsDetailsResponse{{
				ID:           "test-qid",
				Code:         "test-question-code",
				QuestionText: "How are you?",
				AnswerSuggestions: []api.ProductVariantAnswerSuggestion{
					{
						Code: "i_am_fine",
						Text: "I am fine",
					},
					{
						Code: "i_am_very_well",
						Text: "I am very well",
					},
				},
				Locale:    api.Locale_EN,
				CreatedBy: "test-case",
				CreatedAt: now,
				UpdatedBy: "test-case",
				UpdatedAt: now,
			}},
		},
	}
}

// ListProductVariantQuestionsResponse ...
func ListProductVariantQuestionsResponse(now time.Time) *api.ListProductVariantQuestionsResponse {
	return &api.ListProductVariantQuestionsResponse{
		QuestionsDetail: &api.ProductVariantQuestionsDetail{
			ProductVariantCode: "product1", ProductVariantVersion: "1",
			QuestionAnswerPairs: []api.QuestionAnswerPairsDetailsResponse{{
				ID:           "test-qid",
				Code:         "test-question-code",
				QuestionText: "How are you?",
				AnswerSuggestions: []api.ProductVariantAnswerSuggestion{
					{
						Code: "i_am_fine",
						Text: "I am fine",
					},
					{
						Code: "i_am_very_well",
						Text: "I am very well",
					},
				},
				Locale:    api.Locale_EN,
				CreatedBy: "test-case",
				CreatedAt: now,
				UpdatedBy: "test-case",
				UpdatedAt: now,
			}},
		},
	}
}

// ListEffectiveProductVariantParametersResponseWithLoanData ...
func ListEffectiveProductVariantParametersResponseWithLoanData() *api.ListEffectiveProductVariantParametersResponse {
	return &api.ListEffectiveProductVariantParametersResponse{
		ProductVariantParameters: []api.ProductVariantParameter{
			{
				Id:               "test-product-variant-parameter-id-1",
				ProductVariantID: "test-product-variant-id",
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-template-parameter-2",
				ParameterValue:   "test-product-template-parameter-value-2",
				DataType:         "INT",
				OverrideLevel:    "none",
			},
			{
				Id:               "test-product-variant-parameter-id-1",
				ProductVariantID: "test-product-variant-id",
				Namespace:        "test-namespace",
				ParameterKey:     "test-product-variant-parameter-1",
				ParameterValue:   "test-product-variant-parameter-value-1",
				DataType:         "INT",
				OverrideLevel:    "none",
			},
		},
		LoanParameters: &api.LoanParametersDetail{
			LoanAllowedAmountTenorSlab: []api.LoanAllowedAmountTenorSlab{
				{
					FromAmount: "100",
					ToAmount:   "500",
					Currency:   "SGD",
					TenorUnit:  "month",
					MinTenor:   "2",
					MaxTenor:   "10",
				},
			},
		},
	}
}

// GetLoanPastDueParametersByProductCodeResponse ...
func GetLoanPastDueParametersByProductCodeResponse(now time.Time) *api.GetLoanPastDueParametersByProductCodeResponse {
	return &api.GetLoanPastDueParametersByProductCodeResponse{
		ProductCode:        api.ProductCode_FLEXI_LOAN_TERM_LOAN,
		LoanPastDueVersion: "1",
		LoanPastDueSlab: []api.LoanPastDueSlab{
			{
				Id:         "test-loan-past-due-slab-1",
				FromUnit:   1,
				ToUnit:     29,
				SlabType:   "DAY",
				BucketName: "Bucket 1",
				CreatedBy:  "MANUAL",
				CreatedAt:  now,
				UpdatedBy:  "MANUAL",
				UpdatedAt:  now,
			},
			{
				Id:         "test-loan-past-due-slab-2",
				FromUnit:   30,
				ToUnit:     59,
				SlabType:   "DAY",
				BucketName: "Bucket 2",
				CreatedBy:  "MANUAL",
				CreatedAt:  now,
				UpdatedBy:  "MANUAL",
				UpdatedAt:  now,
			},
		},
	}
}

// GetInternalAccountsByProductVariantCodeResponse ...
func GetInternalAccountsByProductVariantCodeResponse(now time.Time) *api.ListInternalAccountsResponse {
	return &api.ListInternalAccountsResponse{
		InternalAccounts: []api.InternalAccount{{
			Id:              "test-internal-account-id",
			GeneralLedgerID: "test-general-ledger-1",
			Code:            "10001",
			Name:            "Sample Clearing Account",
			Description:     "Sample Clearing Account",
			Currency:        "SGD",
			Status:          "ACTIVE",
			CreatedBy:       "unit-test",
			CreatedAt:       now,
			UpdatedBy:       "unit-test",
			UpdatedAt:       now,
		}},
	}
}

// CreateLoanInstructionVersionResponse ...
func CreateLoanInstructionVersionResponse() *api.CreateLoanInstructionVersionResponse {
	return &api.CreateLoanInstructionVersionResponse{
		LoanInstructionVersion: &api.LoanInstructionVersion{
			Id:                 "4fb08d2a-f180-4c6b-9974-f60cb17ff06c",
			ProductVariantCode: "DEFAULT_FLEXI_LOAN_TERM_LOAN",
			Version:            "1",
			InstructionType:    "WRITE_OFF",
			Description:        "Default Flexi Loan - Term Loan WriteOff Instruction v1.0",
			EffectiveDate:      time.Time{},
			CreatedBy:          "MANUAL",
			CreatedAt:          time.Time{},
			UpdatedBy:          "MANUAL",
			UpdatedAt:          time.Time{},
		},
	}
}

// CreateLoanInstructionResponse ...
func CreateLoanInstructionResponse() *api.CreateLoanInstructionResponse {
	return &api.CreateLoanInstructionResponse{
		LoanInstruction: &api.LoanInstruction{
			Id:           "4fb08d2a-f180-4c6b-9974-f60cb17ff06c",
			Code:         "DECEASED",
			Name:         "Deceased",
			CreatedBy:    "MANUAL",
			CreatedAt:    time.Time{},
			UpdatedBy:    "MANUAL",
			UpdatedAt:    time.Time{},
			Restrictions: map[string]interface{}{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false},
		},
	}
}

// GetLoanInstructionResponse ...
func GetLoanInstructionResponse() *api.GetLoanInstructionsByCodeResponse {
	return &api.GetLoanInstructionsByCodeResponse{
		ProductVariantCode: "DEFAULT_FLEXI_LOAN_TERM_LOAN",
		Version:            "1",
		InstructionType:    "WRITE_OFF",
		LoanInstruction: []api.LoanInstruction{{
			Id:           "4fb08d2a-f180-4c6b-9974-f60cb17ff06c",
			Code:         "DECEASED",
			Name:         "Deceased",
			CreatedAt:    time.Time{},
			CreatedBy:    "MANUAL",
			UpdatedAt:    time.Time{},
			UpdatedBy:    "MANUAL",
			Restrictions: map[string]interface{}{"DRAWDOWN_BLOCKED": true, "REPAYMENT_BLOCKED": false},
		}},
	}
}

// GetBoostPocketInterestParametersResponse ...
func GetBoostPocketInterestParametersResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		BoostPocket: &api.BoostPocketInterestParameters{
			MaxInterestRateOffered: "3.68",
			InterestRateType:       "tier",
			TenorType:              api.TenorType_RANGE,
			TierInterest: []api.BoostPocketTierInterest{
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          7500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          99999999900,
					},
					Tenor:        "10",
					TenorUnit:    "DAY",
					MaxTenor:     "999",
					MaxTenorUnit: "MONTH",
				},
			},
		},
	}
}

// GetSavingsAndBoostPocketInterestParametersResponse ...
func GetSavingsAndBoostPocketInterestParametersResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		SavingsPocket: &api.SavingsPocketInterestParameters{
			InterestRateType: "flat",
			FlatInterest: &api.SavingsPocketFlatInterest{
				Rate:       208,
				Multiplier: 100,
			},
		},
		BoostPocket: &api.BoostPocketInterestParameters{
			MaxInterestRateOffered: "3.68",
			InterestRateType:       "tier",
			TenorType:              api.TenorType_RANGE,
			TierInterest: []api.BoostPocketTierInterest{
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          7500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          99999999900,
					},
					Tenor:        "10",
					TenorUnit:    "DAY",
					MaxTenor:     "999",
					MaxTenorUnit: "MONTH",
				},
			},
		},
	}
}

// GetSavingsAndBoostPocketInterestParametersMultipleTenorsResponse ...
// nolint : funlen
func GetSavingsAndBoostPocketInterestParametersMultipleTenorsResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		SavingsPocket: &api.SavingsPocketInterestParameters{
			InterestRateType: "flat",
			FlatInterest: &api.SavingsPocketFlatInterest{
				Rate:       208,
				Multiplier: 100,
			},
		},
		BoostPocket: &api.BoostPocketInterestParameters{
			MaxInterestRateOffered: "3.68",
			InterestRateType:       "tier",
			TenorType:              api.TenorType_RANGE,
			TierInterest: []api.BoostPocketTierInterest{
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:        "1",
					TenorUnit:    "MONTH",
					MaxTenor:     "9",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:        "1",
					TenorUnit:    "MONTH",
					MaxTenor:     "9",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:        "10",
					TenorUnit:    "MONTH",
					MaxTenor:     "999",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:        "10",
					TenorUnit:    "MONTH",
					MaxTenor:     "999",
					MaxTenorUnit: "MONTH",
				},
			},
		},
	}
}

// GetSavingsAndBoostPocketInterestParametersMaxTenorResponse ...
// nolint : funlen
func GetSavingsAndBoostPocketInterestParametersMaxTenorResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		SavingsPocket: &api.SavingsPocketInterestParameters{
			InterestRateType: "flat",
			FlatInterest: &api.SavingsPocketFlatInterest{
				Rate:       208,
				Multiplier: 100,
			},
		},
		BoostPocket: &api.BoostPocketInterestParameters{
			MaxInterestRateOffered: "3.68",
			InterestRateType:       "tier",
			TenorType:              api.TenorType_RANGE,
			TierInterest: []api.BoostPocketTierInterest{
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:        "1",
					TenorUnit:    "MONTH",
					MaxTenor:     "9",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:        "1",
					TenorUnit:    "MONTH",
					MaxTenor:     "9",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:        "10",
					TenorUnit:    "MONTH",
					MaxTenor:     "999999",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:        "10",
					TenorUnit:    "MONTH",
					MaxTenor:     "999999",
					MaxTenorUnit: "MONTH",
				},
			},
		},
	}
}

// GetSavingsAndBoostPocketInterestParametersSpotTenorResponse ...
// nolint : funlen
func GetSavingsAndBoostPocketInterestParametersSpotTenorResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		SavingsPocket: &api.SavingsPocketInterestParameters{
			InterestRateType: "flat",
			FlatInterest: &api.SavingsPocketFlatInterest{
				Rate:       208,
				Multiplier: 100,
			},
		},
		BoostPocket: &api.BoostPocketInterestParameters{
			MaxInterestRateOffered: "3.68",
			InterestRateType:       "tier",
			TenorType:              api.TenorType_SPOT,
			TierInterest: []api.BoostPocketTierInterest{
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:     "1",
					TenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:     "1",
					TenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:     "10",
					TenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:     "10",
					TenorUnit: "MONTH",
				},
			},
		},
	}
}

// GetSavingsAndBoostPocketInterestParametersDiffTenorsResponse ...
// nolint : funlen
func GetSavingsAndBoostPocketInterestParametersDiffTenorsResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		SavingsPocket: &api.SavingsPocketInterestParameters{
			InterestRateType: "flat",
			FlatInterest: &api.SavingsPocketFlatInterest{
				Rate:       208,
				Multiplier: 100,
			},
		},
		BoostPocket: &api.BoostPocketInterestParameters{
			MaxInterestRateOffered: "3.68",
			InterestRateType:       "tier",
			TenorType:              api.TenorType_RANGE,
			TierInterest: []api.BoostPocketTierInterest{
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:        "1",
					TenorUnit:    "DAY",
					MaxTenor:     "9",
					MaxTenorUnit: "DAY",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:        "1",
					TenorUnit:    "MONTH",
					MaxTenor:     "9",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:        "1",
					TenorUnit:    "MONTH",
					MaxTenor:     "9",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:        "10",
					TenorUnit:    "MONTH",
					MaxTenor:     "999999",
					MaxTenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:        "1",
					TenorUnit:    "YEAR",
					MaxTenor:     "9",
					MaxTenorUnit: "YEAR",
				},
			},
		},
	}
}

// GetSavingsAndBoostPocketInterestParametersDiffSpotTenorsResponse ...
// nolint : funlen
func GetSavingsAndBoostPocketInterestParametersDiffSpotTenorsResponse() *api.GetInterestParametersByProductVariantResponse {
	return &api.GetInterestParametersByProductVariantResponse{
		SavingsPocket: &api.SavingsPocketInterestParameters{
			InterestRateType: "flat",
			FlatInterest: &api.SavingsPocketFlatInterest{
				Rate:       208,
				Multiplier: 100,
			},
		},
		BoostPocket: &api.BoostPocketInterestParameters{
			MaxInterestRateOffered: "3.68",
			InterestRateType:       "tier",
			TenorType:              api.TenorType_SPOT,
			TierInterest: []api.BoostPocketTierInterest{
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:     "1",
					TenorUnit: "DAY",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:     "1",
					TenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:     "1",
					TenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          500000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          1000000,
					},
					Tenor:     "10",
					TenorUnit: "MONTH",
				},
				{
					BaseInterestRate:  8,
					BonusInterestRate: 208,
					TotalInterestRate: 216,
					Multiplier:        100,
					FromAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          100000,
					},
					ToAmount: &api.Money{
						CurrencyCode: string(api.Currency_MYR),
						Val:          499900,
					},
					Tenor:     "1",
					TenorUnit: "YEAR",
				},
			},
		},
	}
}

func ListLoanDocumentTypesSampleResponse() *api.ListLoanDocumentOptionsByProductVariantResponse {
	return &api.ListLoanDocumentOptionsByProductVariantResponse{
		LoanDocumentSubmissionOptions: []api.LoanDocumentSubmissionOption{
			{
				DocumentType:      "epfStatement",
				ApplicationType:   "NEW",
				SalaryType:        "SA",
				IsEnable:          true,
				Priority:          1,
				RequiredDocNumber: 2,
				RequiredDocUnit:   "year",
				FileConfig: &api.FileConfig{
					MaxFileSizeInBytes:    3145728,
					MaxUploadLimit:        12,
					AllowedFileExtensions: []string{"pdf", "jpg", "jpeg", "png"},
				},
			},
			{
				DocumentType:      "personalBankStatement",
				ApplicationType:   "NEW",
				SalaryType:        "SA",
				IsEnable:          true,
				Priority:          2,
				RequiredDocNumber: 2,
				RequiredDocUnit:   "year",
				FileConfig: &api.FileConfig{
					MaxFileSizeInBytes:    3145728,
					MaxUploadLimit:        12,
					AllowedFileExtensions: []string{"pdf", "jpg", "jpeg", "png"},
				},
			},
			{
				DocumentType:      "businessBankStatement",
				ApplicationType:   "REVIEW_REVIEW_LIMIT",
				SalaryType:        "SE",
				IsEnable:          true,
				Priority:          2,
				RequiredDocNumber: 2,
				RequiredDocUnit:   "year",
				FileConfig: &api.FileConfig{
					MaxFileSizeInBytes:    3145728,
					MaxUploadLimit:        12,
					AllowedFileExtensions: []string{"pdf", "jpg", "jpeg", "png"},
				},
			},
		}}
}

package interestconfig //nolint:dupl

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateBaseInterestVersion ...
func CreateBaseInterestVersion(ctx context.Context, req *api.CreateBaseInterestVersionRequest, store *storage.DBStore) (*api.CreateBaseInterestVersionResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	if req.EffectiveDate.IsZero() {
		req.EffectiveDate = time.Now().UTC()
	}

	baseInterest, err := store.BaseInterestDAO.Find(ctx, data.EqualTo("PublicID", req.BaseInterestID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest from database after saving for request with id: %s", req.BaseInterestID), err)
	}

	baseInterestVersionID := uuid.New().String()
	if err = store.BaseInterestVersionDAO.Save(ctx, &storage.BaseInterestVersion{
		PublicID:       baseInterestVersionID,
		BaseInterestID: baseInterest[0].ID,
		Version:        req.Version,
		EffectiveDate:  req.EffectiveDate,
		Description:    description,
		CreatedBy:      req.CreatedBy,
		UpdatedBy:      req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save base-interest-version to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	baseInterestVersion, err := store.BaseInterestVersionDAO.Find(ctx, data.EqualTo("PublicID", baseInterestVersionID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-version from database after saving for request with id: %s", baseInterestVersionID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateBaseInterestVersionLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateBaseInterestVersionResponse{
		BaseInterestVersion: mapBaseInterestVersionFromStorage(*baseInterestVersion[0], req.BaseInterestID),
	}, nil
}

// GetBaseInterestVersion ...
func GetBaseInterestVersion(ctx context.Context, req *api.GetBaseInterestVersionRequest, store *storage.DBStore) (*api.GetBaseInterestVersionResponse, error) {
	baseInterestVersion, err := store.BaseInterestVersionDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-version from database for request with id: %s", req.Id), err)
	}

	baseInterest, err := store.BaseInterestDAO.LoadByID(ctx, baseInterestVersion[0].BaseInterestID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest from database after saving for request with id: %d", baseInterestVersion[0].BaseInterestID), err)
	}

	slog.FromContext(ctx).Info(constants.GetBaseInterestVersionLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetBaseInterestVersionResponse{
		BaseInterestVersion: mapBaseInterestVersionFromStorage(*baseInterestVersion[0], baseInterest.PublicID),
	}, nil
}

func getLatestBaseInterestVersion(versions []*storage.BaseInterestVersion) (*storage.BaseInterestVersion, error) {
	if len(versions) == 0 {
		return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrNoBaseInterestVersionForVersionID.Code), 10),
			apiErr.ErrNoBaseInterestVersionForVersionID.Message)
	}
	latest := versions[0]
	for _, version := range versions {
		v1, v1Err := strconv.Atoi(latest.Version)
		if v1Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestVersion.Code), 10),
				apiErr.ErrInvalidBaseInterestVersion.Message)
		}
		v2, v2Err := strconv.Atoi(version.Version)
		if v2Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestVersion.Code), 10),
				apiErr.ErrInvalidBaseInterestVersion.Message)
		}
		if v2 > v1 {
			latest = version
		}
	}
	return latest, nil
}

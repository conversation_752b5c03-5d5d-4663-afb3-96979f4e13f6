package interestconfig

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"strconv"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateBaseInterestTimeSlabRate ...
func CreateBaseInterestTimeSlabRate(ctx context.Context, req *api.CreateBaseInterestTimeSlabRateRequest, store *storage.DBStore) (*api.CreateBaseInterestTimeSlabRateResponse, error) {
	id := uuid.New().String()

	baseInterestVersion, err := store.BaseInterestVersionDAO.Find(ctx, data.EqualTo("PublicID", req.BaseInterestVersionID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-version from database  with id: %s", req.BaseInterestVersionID), err)
	}

	if err = store.BaseInterestTimeSlabRateDAO.Save(ctx, &storage.BaseInterestTimeSlabRate{
		PublicID:              id,
		BaseInterestVersionID: baseInterestVersion[0].ID,
		TermUnit:              string(req.TermUnit),
		TermValue:             req.TermValue,
		BaseRatePercentage:    req.BaseInterestPercentage,
		CreatedBy:             req.CreatedBy,
		UpdatedBy:             req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save base-interest-time-slab-rate to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	baseInterestTimeSlabRate, err := store.BaseInterestTimeSlabRateDAO.Find(ctx, data.EqualTo("PublicID", id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-time-slab-rate from database after saving for request with id: %s", id), err)
	}

	slog.FromContext(ctx).Info(constants.CreateBaseInterestTimeSlabRateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateBaseInterestTimeSlabRateResponse{
		BaseInterestTimeSlabRate: mapBaseInterestTimeSlabRateFromStorage(*baseInterestTimeSlabRate[0], req.BaseInterestVersionID),
	}, nil
}

// GetBaseInterestTimeSlabRate ...
func GetBaseInterestTimeSlabRate(ctx context.Context, req *api.GetBaseInterestTimeSlabRateRequest, store *storage.DBStore) (*api.GetBaseInterestTimeSlabRateResponse, error) {
	baseInterestTimeSlabRate, err := store.BaseInterestTimeSlabRateDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-time-slab-rate from database for request with id: %s", req.Id), err)
	}

	baseInterestVersion, err := store.BaseInterestVersionDAO.LoadByID(ctx, baseInterestTimeSlabRate[0].BaseInterestVersionID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-version from database  with id: %d", baseInterestTimeSlabRate[0].BaseInterestVersionID), err)
	}

	slog.FromContext(ctx).Info(constants.GetBaseInterestTimeSlabRateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetBaseInterestTimeSlabRateResponse{
		BaseInterestTimeSlabRate: mapBaseInterestTimeSlabRateFromStorage(*baseInterestTimeSlabRate[0], baseInterestVersion.PublicID),
	}, nil
}

func getBaseInterestTimeSlabRateFromDB(ctx context.Context, store *storage.DBStore, baseInterestNullable sql.NullInt64) (*storage.BaseInterestTimeSlabRate, error) {
	if !baseInterestNullable.Valid {
		slog.FromContext(ctx).Warn(constants.GetBaseInterestTimeSlabRateLogTag, "Missing base interest id field", apiCommon.GetTraceID(ctx))
		return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrMissingBaseInterestID.Code), 10), apiErr.ErrMissingBaseInterestID.Message)
	}

	baseInterestID := baseInterestNullable.Int64
	baseInterestVersions, err := store.BaseInterestVersionDAO.Find(ctx, data.EqualTo("BaseInterestID", baseInterestID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetBaseInterestTimeSlabRateLogTag, fmt.Sprintf("Error in finding from the BaseInterestVersion DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-versions from database for request with base-interest-id: %d", baseInterestID), err)
	}

	latestBaseInterestVersion, err := getLatestBaseInterestVersion(baseInterestVersions)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLatestBaseInterestVersionLogTag, fmt.Sprintf("Error getting the latestBase Interest version, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, err
	}

	baseInterestTimeSlabRates, err := store.BaseInterestTimeSlabRateDAO.Find(ctx, data.EqualTo("BaseInterestVersionID", latestBaseInterestVersion.ID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetBaseInterestTimeSlabRateLogTag, fmt.Sprintf("Error in finding from the BaseInterestTimeSlabRate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-time-slab-rate from database for request with deposit-interest-version-id: %d", latestBaseInterestVersion.ID), err)
	}

	return baseInterestTimeSlabRates[0], nil
}

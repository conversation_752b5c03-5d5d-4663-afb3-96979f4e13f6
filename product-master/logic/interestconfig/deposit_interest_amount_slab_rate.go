package interestconfig

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/featureflag"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateDepositInterestAmountSlabRate ...
func CreateDepositInterestAmountSlabRate(ctx context.Context, req *api.CreateDepositInterestAmountSlabRateRequest, store *storage.DBStore) (*api.CreateDepositInterestAmountSlabRateResponse, error) {
	id := uuid.New().String()

	depositInterestVersions, err := store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("PublicID", req.DepositInterestVersionID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-version from database with id: %s", req.DepositInterestVersionID), err)
	}

	if err = store.DepositInterestAmountSlabRateDAO.Save(ctx, &storage.DepositInterestAmountSlabRate{
		PublicID:                         id,
		DepositInterestVersionID:         depositInterestVersions[0].ID,
		FromAmount:                       req.FromAmount,
		ToAmount:                         req.ToAmount,
		BaseRateInterestSpreadPercentage: req.BaseRateInterestSpreadPercentage,
		AbsoluteInterestRatePercentage:   req.AbsoluteInterestRatePercentage,
		CreatedBy:                        req.CreatedBy,
		UpdatedBy:                        req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save deposit-interest-amount-slab-rate to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	depositInterestAmountSlabRate, err := store.DepositInterestAmountSlabRateDAO.Find(ctx, data.EqualTo("PublicID", id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-amount-slab-rate from database after saving for request with id: %s", id), err)
	}

	slog.FromContext(ctx).Info(constants.CreateDepositInterestAmountSlabRateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateDepositInterestAmountSlabRateResponse{
		DepositInterestAmountSlabRate: mapDepositInterestAmountSlabRateFromStorage(*depositInterestAmountSlabRate[0], req.DepositInterestVersionID),
	}, nil
}

// GetDepositInterestAmountSlabRate ...
func GetDepositInterestAmountSlabRate(ctx context.Context, req *api.GetDepositInterestAmountSlabRateRequest, store *storage.DBStore) (*api.GetDepositInterestAmountSlabRateResponse, error) {
	depositInterestAmountSlabRate, err := store.DepositInterestAmountSlabRateDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-amouny-slab-rate from database for request with id: %s", req.Id), err)
	}

	depositInterestVersion, err := store.DepositInterestVersionDAO.LoadByID(ctx, depositInterestAmountSlabRate[0].DepositInterestVersionID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-version from database with id: %d", depositInterestAmountSlabRate[0].DepositInterestVersionID), err)
	}

	slog.FromContext(ctx).Info(constants.GetDepositInterestAmountSlabRateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetDepositInterestAmountSlabRateResponse{
		DepositInterestAmountSlabRate: mapDepositInterestAmountSlabRateFromStorage(*depositInterestAmountSlabRate[0], depositInterestVersion.PublicID),
	}, nil
}

// GetDepositsInterestParamsByProductVariant ...
func GetDepositsInterestParamsByProductVariant(ctx context.Context, store *storage.DBStore, productVariantID uint64, interestVersion string, productVariantCode string) (map[string]api.DepositsInterestAmountSlabRate, error) {
	getDepositInterestFilter := []data.Condition{data.EqualTo("ProductVariantID", productVariantID)}

	if productVariantCode != "" {
		getDepositInterestFilter = append(getDepositInterestFilter, data.EqualTo("Code", productVariantCode))
	}

	depositInterests, err := store.DepositInterestDAO.Find(ctx, getDepositInterestFilter...)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the DepositInterest DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest from database for request with product-variant-id: %d", productVariantID), err)
	}

	depositInterestVersion, err := getDepositsInterestVersion(ctx, store, depositInterests[0].ID, interestVersion)
	if err != nil {
		return nil, err
	}

	depositInterestAmountSlabRates, err := store.DepositInterestAmountSlabRateDAO.Find(ctx, data.EqualTo("DepositInterestVersionID", depositInterestVersion.ID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the DepositInterestAmountSlabRate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-amount-slab-rate from database for request with deposit-interest-version-id: %d", depositInterestVersion.ID), err)
	}

	var baseInterestRate string
	if depositInterests[0].IsLinkedToBaseRate {
		baseInterestTimeSlabRate, _ := getBaseInterestTimeSlabRateFromDB(ctx, store, depositInterests[0].BaseInterestID)
		baseInterestRate = baseInterestTimeSlabRate.BaseRatePercentage
	}
	return getDepositsInterestSlab(baseInterestRate, depositInterestAmountSlabRates, depositInterests[0].IsLinkedToBaseRate)
}

func getDepositsInterestVersion(ctx context.Context, store *storage.DBStore, depositInterestID uint64, interestVersion string) (*storage.DepositInterestVersion, error) {
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	isEffectiveDateEnabled := featFlag != nil && featFlag.IsGetDepositInterestByEffectiveDateEnabled()
	var (
		depositInterestVersions []*storage.DepositInterestVersion
		err                     error
	)
	if interestVersion == "" {
		// Just fetch the latest interest version
		if isEffectiveDateEnabled {
			depositInterestVersions, err = store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("DepositInterestID", depositInterestID), data.LessThanOrEqualTo("EffectiveDate", time.Now().UTC()), data.DescendingOrder("EffectiveDate"))
		} else {
			depositInterestVersions, err = store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("DepositInterestID", depositInterestID))
		}
	} else {
		depositInterestVersions, err = store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("DepositInterestID", depositInterestID), data.EqualTo("Version", interestVersion))
	}

	if err != nil {
		if errors.Is(err, data.ErrNoData) {
			slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Deposits interest version %s not found in db.", interestVersion), apiCommon.GetTraceID(ctx))
			return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message)
		}
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the DepositInterestVersion DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-versions from database for request with deposit-interest-id: %d", depositInterestID), err)
	}

	depositInterestVersion := depositInterestVersions[0]
	// if feature flag is not enabled, get the latest deposit interest version from the interest version collection we got from db
	if !isEffectiveDateEnabled && interestVersion == "" {
		latestInterestVersion, err := GetLatestDepositInterestVersion(depositInterestVersions)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Failed to identify latest interest from results set, err : %s", err), apiCommon.GetTraceID(ctx))
			return nil, err
		}
		depositInterestVersion = latestInterestVersion
	}
	return depositInterestVersion, nil
}

func getDepositsInterestSlab(baseInterestRate string, depositInterestAmountSlabRates []*storage.DepositInterestAmountSlabRate, isLinkedToBaseRate bool) (map[string]api.DepositsInterestAmountSlabRate, error) {
	interestSlabMap := make(map[string]api.DepositsInterestAmountSlabRate)

	if isLinkedToBaseRate {
		baseRate, err := strconv.ParseFloat(baseInterestRate, 32)
		if err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestPercentage.Code), 10), apiErr.ErrInvalidBaseInterestPercentage.Message)
		}

		for i, interestSlab := range depositInterestAmountSlabRates {
			spreadRate, parseErr := strconv.ParseFloat(interestSlab.BaseRateInterestSpreadPercentage, 32)
			if parseErr != nil {
				return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Code), 10), apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Message)
			}

			interestSlabMap[fmt.Sprint("tier", i+1)] = api.DepositsInterestAmountSlabRate{
				Min:          interestSlab.FromAmount,
				MinAmount:    interestSlab.FromAmount,
				Max:          interestSlab.ToAmount,
				MaxAmount:    interestSlab.ToAmount,
				Rate:         fmt.Sprintf("%f", baseRate+spreadRate),
				MinTenor:     interestSlab.MinTenor,
				MaxTenor:     interestSlab.MaxTenor,
				MinTenorUnit: interestSlab.MinTenorUnit,
				MaxTenorUnit: interestSlab.MaxTenorUnit,
			}
		}
	} else {
		for i, interestSlab := range depositInterestAmountSlabRates {
			interestSlabMap[fmt.Sprint("tier", i+1)] = api.DepositsInterestAmountSlabRate{
				Min:          interestSlab.FromAmount,
				MinAmount:    interestSlab.FromAmount,
				Max:          interestSlab.ToAmount,
				MaxAmount:    interestSlab.ToAmount,
				Rate:         interestSlab.AbsoluteInterestRatePercentage,
				MinTenor:     interestSlab.MinTenor,
				MaxTenor:     interestSlab.MaxTenor,
				MinTenorUnit: interestSlab.MinTenorUnit,
				MaxTenorUnit: interestSlab.MaxTenorUnit,
			}
		}
	}

	return interestSlabMap, nil
}

package interestconfig //nolint:dupl

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateDepositInterestVersion ...
func CreateDepositInterestVersion(ctx context.Context, req *api.CreateDepositInterestVersionRequest, store *storage.DBStore) (*api.CreateDepositInterestVersionResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	if req.EffectiveDate.IsZero() {
		req.EffectiveDate = time.Now().UTC()
	}

	depositInterests, err := store.DepositInterestDAO.Find(ctx, data.EqualTo("PublicID", req.DepositInterestID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest from database for request with id: %s", req.DepositInterestID), err)
	}

	depositInterestVersionID := uuid.New().String()
	if err = store.DepositInterestVersionDAO.Save(ctx, &storage.DepositInterestVersion{
		PublicID:          depositInterestVersionID,
		DepositInterestID: depositInterests[0].ID,
		Version:           req.Version,
		EffectiveDate:     req.EffectiveDate,
		Description:       description,
		CreatedBy:         req.CreatedBy,
		UpdatedBy:         req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save deposit-interest-version to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	depositInterestVersion, err := store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("PublicID", depositInterestVersionID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-version from database for request with id: %s", depositInterestVersionID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateDepositInterestVersionLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateDepositInterestVersionResponse{
		DepositInterestVersion: mapDepositInterestVersionFromStorage(*depositInterestVersion[0], req.DepositInterestID),
	}, nil
}

// GetDepositInterestVersion ...
func GetDepositInterestVersion(ctx context.Context, req *api.GetDepositInterestVersionRequest, store *storage.DBStore) (*api.GetDepositInterestVersionResponse, error) {
	depositInterestVersion, err := store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-version from database for request with id: %s", req.Id), err)
	}

	depositInterest, err := store.DepositInterestDAO.LoadByID(ctx, depositInterestVersion[0].DepositInterestID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest from database for request with id: %d", depositInterestVersion[0].DepositInterestID), err)
	}

	slog.FromContext(ctx).Info(constants.GetDepositInterestVersionLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetDepositInterestVersionResponse{
		DepositInterestVersion: mapDepositInterestVersionFromStorage(*depositInterestVersion[0], depositInterest.PublicID),
	}, nil
}

// GetLatestDepositInterestVersion ...
func GetLatestDepositInterestVersion(versions []*storage.DepositInterestVersion) (*storage.DepositInterestVersion, error) {
	if len(versions) == 0 {
		return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrNoDepositInterestVersionForVersionID.Code), 10),
			apiErr.ErrNoDepositInterestVersionForVersionID.Message)
	}
	latest := versions[0]
	for _, version := range versions {
		v1, v1Err := strconv.Atoi(latest.Version)
		if v1Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidDepositInterestVersion.Code), 10),
				apiErr.ErrInvalidDepositInterestVersion.Message)
		}
		v2, v2Err := strconv.Atoi(version.Version)
		if v2Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidDepositInterestVersion.Code), 10),
				apiErr.ErrInvalidDepositInterestVersion.Message)
		}
		if v2 > v1 {
			latest = version
		}
	}
	return latest, nil
}

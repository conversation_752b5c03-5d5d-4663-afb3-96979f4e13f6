package interestconfig

import (
	"context"
	"fmt"
	"net/http"
	"sort"
	"strconv"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/server/config"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	commonUtils "gitlab.myteksi.net/dbmy/core-banking/common/utils"

	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetInterestParametersByProductVariant ...
// nolint:gocognit,funlen
func GetInterestParametersByProductVariant(ctx context.Context, req *api.GetInterestParametersByProductVariantRequest, store *storage.DBStore, appCfg *config.AppConfig) (*api.GetInterestParametersByProductVariantResponse, error) {
	var productVariants []*storage.ProductVariant
	var err error
	response := &api.GetInterestParametersByProductVariantResponse{}

	for _, requestParam := range req.InterestParamRequest {
		mappedProductVariantCode := constants.ProductVariantMap[requestParam.ProductVariantCode]
		if requestParam.ProductVariantVersion == "" {
			productVariants, err = store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", mappedProductVariantCode))
		} else {
			productVariants, err = store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", mappedProductVariantCode), data.EqualTo("Version", requestParam.ProductVariantVersion))
		}

		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetInterestParametersByProductVariantLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load product-variant from database for request with code: %s", mappedProductVariantCode), err)
		}

		productVariant := productVariants[0]
		if requestParam.ProductVariantVersion == "" {
			productVariant, err = getLatestProductVariantVersion(productVariants)
			if err != nil {
				return nil, err
			}
		}

		switch requestParam.ProductVariantCode {
		case api.ProductVariantCode_DEFAULT_FLEXI_LOAN_TERM_LOAN:
			loanAccountInterestParams, loanAccountInterestParamErr := GetLoanInterestParamsByProductVariant(ctx, store, productVariant.ID, requestParam.InterestVersion)
			if loanAccountInterestParamErr != nil {
				return nil, loanAccountInterestParamErr
			}
			response.LoanAccount = loanAccountInterestParams
		case api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN:
			bizLoanAccountInterestParams, bizLoanAccountInterestParamErr := GetBizLoanInterestParamsByProductVariant(ctx, store, productVariant.ID, requestParam.InterestVersion)
			if bizLoanAccountInterestParamErr != nil {
				return nil, bizLoanAccountInterestParamErr
			}
			response.LoanAccount = bizLoanAccountInterestParams
		case api.ProductVariantCode_DEPOSITS_ACCOUNT, api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT:
			interestRateParamMap, interestParamErr := GetDepositsInterestParamsByProductVariant(ctx, store, productVariant.ID, requestParam.InterestVersion, "")
			if interestParamErr != nil {
				return nil, interestParamErr
			}
			response.DepositsAccount = createDepositsAccountInterestParametersResponse(interestRateParamMap)
		case api.ProductVariantCode_SAVINGS_POCKET:
			interestRateParamMap, interestParamErr := GetDepositsInterestParamsByProductVariant(ctx, store, productVariant.ID, requestParam.InterestVersion, "")
			if interestParamErr != nil {
				return nil, interestParamErr
			}
			response.SavingsPocket = createSavingsPocketInterestParametersResponse(interestRateParamMap)

		case api.ProductVariantCode_BOOST_POCKET:
			result, boostPocketInterestParamErr := getBoostPocketInterestParametersResponse(ctx, store, productVariant, requestParam, appCfg)
			if boostPocketInterestParamErr != nil {
				return nil, boostPocketInterestParamErr
			}
			response.BoostPocket = result
		}
	}

	slog.FromContext(ctx).Info(constants.GetInterestParametersByProductVariantLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return response, nil
}

func createDepositsAccountInterestParametersResponse(interestRateParamMap map[string]api.DepositsInterestAmountSlabRate) *api.DepositsAccountInterestParameters {
	RateInFloat, _ := strconv.ParseFloat(interestRateParamMap["tier1"].Rate, 64)
	return &api.DepositsAccountInterestParameters{
		InterestRateType: api.InterestRateType_flat,
		FlatInterest: &api.DepositsAccountFlatInterest{
			Rate:       int32(RateInFloat * 100),
			Multiplier: 100,
		},
	}
}

func createSavingsPocketInterestParametersResponse(interestRateParamMap map[string]api.DepositsInterestAmountSlabRate) *api.SavingsPocketInterestParameters {
	RateInFloat, _ := strconv.ParseFloat(interestRateParamMap["tier1"].Rate, 64)
	return &api.SavingsPocketInterestParameters{
		InterestRateType: api.InterestRateType_flat,
		FlatInterest: &api.SavingsPocketFlatInterest{
			Rate:       int32(RateInFloat * 100),
			Multiplier: 100,
		},
	}
}

func createBoostPocketInterestParametersResponse(ctx context.Context, defaultBoostPocketRateParamMaps, bonusBoostPocketRateParamMaps map[string]api.DepositsInterestAmountSlabRate, maxInterestRateOffered string, appCfg *config.AppConfig) (*api.BoostPocketInterestParameters, error) {
	boostPocketTierInterest, err := getBoostPocketTierInterest(defaultBoostPocketRateParamMaps, bonusBoostPocketRateParamMaps, appCfg)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetProductVariantParameterLogTag, fmt.Sprintf("Error while building boostt pocket interest rate tiers, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, err
	}

	tenorType := lo.Ternary(appCfg.FeatureFlags.EnableBoostPocketFESpotRate, api.TenorType_SPOT, api.TenorType_RANGE)

	// sort interests based on the tenor offered in the tier
	// for example, 1 month comes before 3 months.
	sort.Slice(boostPocketTierInterest, func(i, j int) bool {
		// convert both tenor to DAYS so it can be compared
		tenorIth := lo.Must(utils.ConvertTenorToDays(ctx, boostPocketTierInterest[i].Tenor, boostPocketTierInterest[i].TenorUnit))
		tenorJth := lo.Must(utils.ConvertTenorToDays(ctx, boostPocketTierInterest[j].Tenor, boostPocketTierInterest[j].TenorUnit))
		if tenorIth != tenorJth {
			return tenorIth < tenorJth
		}
		// if both tenor are equal, now sort based on the amount slab.
		// lower amount comes before higher amount.
		return boostPocketTierInterest[i].FromAmount.Val < boostPocketTierInterest[j].FromAmount.Val
	})

	return &api.BoostPocketInterestParameters{
		MaxInterestRateOffered: maxInterestRateOffered,
		InterestRateType:       api.InterestRateType_tier,
		TenorType:              tenorType,
		TierInterest:           boostPocketTierInterest,
	}, nil
}

func getLatestProductVariantVersion(versions []*storage.ProductVariant) (*storage.ProductVariant, error) {
	if len(versions) == 0 {
		return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrNoDepositInterestVersionForVersionID.Code), 10),
			apiErr.ErrNoDepositInterestVersionForVersionID.Message)
	}
	latest := versions[0]
	for _, version := range versions {
		v1, v1Err := strconv.Atoi(latest.Version)
		if v1Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidDepositInterestVersion.Code), 10),
				apiErr.ErrInvalidDepositInterestVersion.Message)
		}
		v2, v2Err := strconv.Atoi(version.Version)
		if v2Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidDepositInterestVersion.Code), 10),
				apiErr.ErrInvalidDepositInterestVersion.Message)
		}
		if v2 > v1 {
			latest = version
		}
	}
	return latest, nil
}

func getBoostPocketInterestParametersResponse(ctx context.Context, store *storage.DBStore, productVariant *storage.ProductVariant, requestParam api.InterestParametersByProductVariantRequest, appCfg *config.AppConfig) (*api.BoostPocketInterestParameters, error) {
	interestRateParamMap, interestParamErr := GetDepositsInterestParamsByProductVariant(ctx, store, productVariant.ID, requestParam.InterestVersion, constants.DefaultBoostPocketInterestCode)
	if interestParamErr != nil {
		return nil, interestParamErr
	}

	slog.FromContext(ctx).Info(constants.GetInterestParametersByProductVariantLogTag, "Fetched default boost pocket interest params", apiCommon.GetTraceID(ctx))
	boostPocketInterestRateParamMap, boostPocketInterestParamErr := GetDepositsInterestParamsByProductVariant(ctx, store, productVariant.ID, requestParam.InterestVersion, constants.BoostPocketBonusInterestCode)
	if boostPocketInterestParamErr != nil {
		return nil, boostPocketInterestParamErr
	}
	slog.FromContext(ctx).Info(constants.GetInterestParametersByProductVariantLogTag, "Fetched bonus boost pocket interest params", apiCommon.GetTraceID(ctx))

	maxInterestRateOffered, maxInterestRateOfferedErr := getMaxInterestOfferedForBoostPocket(ctx, store, productVariant.ID)
	if maxInterestRateOfferedErr != nil {
		slog.FromContext(ctx).Error(constants.GetProductVariantParameterLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", maxInterestRateOfferedErr), apiCommon.GetTraceID(ctx))
		return nil, maxInterestRateOfferedErr
	}

	return createBoostPocketInterestParametersResponse(ctx, interestRateParamMap, boostPocketInterestRateParamMap, maxInterestRateOffered, appCfg)
}

func getBoostPocketTierInterest(defaultBoostPocketRateParamMaps, bonusBoostPocketRateParamMaps map[string]api.DepositsInterestAmountSlabRate, appCfg *config.AppConfig) ([]api.BoostPocketTierInterest, error) {
	decimal100 := decimal.NewFromInt(100)
	baseRateInDecimal, parseErr := decimal.NewFromString(defaultBoostPocketRateParamMaps["tier1"].Rate)
	if parseErr != nil {
		return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestPercentage.Code), 10), apiErr.ErrInvalidBaseInterestPercentage.Message)
	}

	var boostPocketTierInterest []api.BoostPocketTierInterest
	for _, boostPocketInterestRateParam := range bonusBoostPocketRateParamMaps {
		bonusRateInDecimal, parseErr := decimal.NewFromString(boostPocketInterestRateParam.Rate)
		if parseErr != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Code), 10), apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Message)
		}
		tenor := boostPocketInterestRateParam.MinTenor
		baseInterest := (baseRateInDecimal.Mul(constants.DepositInterestMultiplierInDecimal).Mul(decimal100)).Round(0).Div(decimal100)
		bonusInterest := (bonusRateInDecimal.Mul(constants.DepositInterestMultiplierInDecimal).Mul(decimal100)).Round(0).Div(decimal100)

		fromAmount, err := commonUtils.ConvertAmountToCents(boostPocketInterestRateParam.MinAmount)
		if err != nil {
			return nil, err
		}

		toAmount, err := commonUtils.ConvertAmountToCents(boostPocketInterestRateParam.MaxAmount)
		if err != nil {
			return nil, err
		}

		bpTierInterest := api.BoostPocketTierInterest{
			BaseInterestRate:  commonUtils.MustConvertToInt32(baseInterest),
			BonusInterestRate: commonUtils.MustConvertToInt32(bonusInterest),
			TotalInterestRate: commonUtils.MustConvertToInt32(baseInterest) + commonUtils.MustConvertToInt32(bonusInterest),
			Multiplier:        constants.DepositInterestMultiplier,
			FromAmount: &api.Money{
				CurrencyCode: appCfg.Locale.Currency,
				Val:          fromAmount,
			},
			ToAmount: &api.Money{
				CurrencyCode: appCfg.Locale.Currency,
				Val:          toAmount,
			},
			Tenor:     tenor,
			TenorUnit: boostPocketInterestRateParam.MinTenorUnit,
		}

		if !appCfg.FeatureFlags.EnableBoostPocketFESpotRate {
			bpTierInterest.MaxTenor = boostPocketInterestRateParam.MaxTenor
			bpTierInterest.MaxTenorUnit = boostPocketInterestRateParam.MaxTenorUnit
		}
		boostPocketTierInterest = append(boostPocketTierInterest, bpTierInterest)
	}
	return boostPocketTierInterest, nil
}

func getMaxInterestOfferedForBoostPocket(ctx context.Context, store *storage.DBStore, productVariantID uint64) (string, error) {
	maxInterestRateOfferedFilter := []data.Condition{
		data.EqualTo("ProductVariantID", productVariantID),
		data.EqualTo("ParameterKey", constants.MaxInterestRateOfferedParameter),
	}
	productVariantParameter, err := store.ProductVariantParameterDAO.Find(ctx, maxInterestRateOfferedFilter...)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetProductVariantParameterLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return "", apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load productVariantParameter from db for request with code: %d", productVariantID), err)
	}
	return productVariantParameter[0].ParameterValue, nil
}

package interestconfig

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"strconv"

	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateLoanInterest ...
func CreateLoanInterest(ctx context.Context, req *api.CreateLoanInterestRequest, store *storage.DBStore) (*api.CreateLoanInterestResponse, error) {
	var description, baseInterestID sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	baseInterestID.Valid = !(req.BaseInterestID == "")
	baseInterestID.String = req.BaseInterestID

	var baseInterestInternalID sql.NullInt64
	if baseInterestID.Valid {
		baseInterests, err := store.BaseInterestDAO.Find(ctx, data.EqualTo("PublicID", req.BaseInterestID))
		if err != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load base-interest from database for request with code: %s", req.Code), err)
		}
		baseInterestInternalID = sql.NullInt64{
			Int64: utils.MustConvertToInt64(baseInterests[0].ID),
			Valid: true,
		}
	}

	productVariants, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.ProductVariantID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.ProductVariantID), err)
	}

	loanInterestID := uuid.New().String()
	if err = store.LoanInterestDAO.Save(ctx, &storage.LoanInterest{
		PublicID:              loanInterestID,
		ProductVariantID:      productVariants[0].ID,
		IsLinkedToBaseRate:    req.IsLinkedToBaseRate,
		BaseInterestID:        baseInterestInternalID,
		Code:                  req.Code,
		Name:                  req.Name,
		Description:           description,
		Currency:              string(req.Currency),
		RoundOffType:          string(req.RoundOffType),
		InterestType:          string(req.InterestType),
		InterestSlabUnitType:  string(req.InterestSlabUnitType),
		InterestSlabStructure: string(req.InterestSlabStructure),
		CreatedBy:             req.CreatedBy,
		UpdatedBy:             req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save loan-interest to database for request: %s", utils.ToJSON(req)), err)
	}

	loanInterest, err := store.LoanInterestDAO.Find(ctx, data.EqualTo("PublicID", loanInterestID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-interest from database after saving for request with id: %s", loanInterestID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateLoanInterestLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateLoanInterestResponse{
		LoanInterest: mapLoanInterestFromStorage(*loanInterest[0], req.ProductVariantID, req.BaseInterestID),
	}, nil
}

// GetLoanInterestByID : method to get loan interest in id from db and return the mapped response
func GetLoanInterestByID(ctx context.Context, req *api.GetLoanInterestByIDRequest, store *storage.DBStore) (*api.GetLoanInterestByIDResponse, error) {
	loanInterest, err := store.LoanInterestDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-interest from database for request with id: %s", req.Id), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, loanInterest[0].ProductVariantID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database with id: %d", loanInterest[0].ProductVariantID), err)
	}

	var baseInterestPublicID string
	if loanInterest[0].IsLinkedToBaseRate {
		baseInterest, baseIntErr := store.BaseInterestDAO.LoadByID(ctx, utils.MustConvertToUint64(loanInterest[0].BaseInterestID.Int64))
		if baseIntErr != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load base-interest from database with id: %d", utils.MustConvertToUint64(loanInterest[0].BaseInterestID.Int64)), baseIntErr)
		}
		baseInterestPublicID = baseInterest.PublicID
	}

	slog.FromContext(ctx).Info(constants.GetLoanInterestByIDLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetLoanInterestByIDResponse{
		LoanInterest: mapLoanInterestFromStorage(*loanInterest[0], productVariant.PublicID, baseInterestPublicID),
	}, nil
}

// GetLoanInterestByCode ...
func GetLoanInterestByCode(ctx context.Context, req *api.GetLoanInterestByCodeRequest, store *storage.DBStore) (*api.GetLoanInterestByCodeResponse, error) {
	loanInterest, err := store.LoanInterestDAO.Find(ctx, data.EqualTo("Code", req.Code))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-interest from database for request with code: %s", req.Code), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, loanInterest[0].ProductVariantID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database with id: %d", loanInterest[0].ProductVariantID), err)
	}

	var baseInterestPublicID string
	if loanInterest[0].IsLinkedToBaseRate {
		baseInterest, baseIntErr := store.BaseInterestDAO.LoadByID(ctx, utils.MustConvertToUint64(loanInterest[0].BaseInterestID.Int64))
		if baseIntErr != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load base-interest from database with id: %d", utils.MustConvertToUint64(loanInterest[0].BaseInterestID.Int64)), baseIntErr)
		}
		baseInterestPublicID = baseInterest.PublicID
	}

	slog.FromContext(ctx).Info(constants.GetLoanInterestByCodeLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetLoanInterestByCodeResponse{
		LoanInterest: mapLoanInterestFromStorage(*loanInterest[0], productVariant.PublicID, baseInterestPublicID),
	}, nil
}

// GetLoanInterestParamsByProductVariant ...
func GetLoanInterestParamsByProductVariant(ctx context.Context, store *storage.DBStore, productVariantID uint64, interestVersion string) (*api.LoanAccountInterestParameters, error) {
	normalInterestParams, err := getLoanInterestParams(ctx, store, interestVersion, productVariantID, constants.NormalInterest)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestParametersByProductVariantLogTag, fmt.Sprintf("Error in fetching normal Interest params from DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, err
	}
	penalInterestParams, err := getLoanInterestParams(ctx, store, interestVersion, productVariantID, constants.PenalInterest)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestParametersByProductVariantLogTag, fmt.Sprintf("Error in fetching penal Interest params from DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, err
	}
	discoveryInterestParams, err := getLoanInterestParams(ctx, store, interestVersion, productVariantID, constants.DiscoveryInterest)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestParametersByProductVariantLogTag, fmt.Sprintf("Error in fetching discovery Interest params from DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, err
	}
	return getLoanInterestSlab(normalInterestParams, penalInterestParams, discoveryInterestParams)
}

// GetBizLoanInterestParamsByProductVariant ...
func GetBizLoanInterestParamsByProductVariant(ctx context.Context, store *storage.DBStore, productVariantID uint64, interestVersion string) (*api.LoanAccountInterestParameters, error) {
	bizNormalInterestParams, err := getLoanInterestParams(ctx, store, interestVersion, productVariantID, constants.BizNormalInterest)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestParametersByProductVariantLogTag, fmt.Sprintf("Error in fetching biz normal Interest params from DB, err : %s", err.Error()), apiCommon.GetTraceID(ctx))
		return nil, err
	}
	bizPenalInterestParams, err := getLoanInterestParams(ctx, store, interestVersion, productVariantID, constants.BizPenalInterest)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestParametersByProductVariantLogTag, fmt.Sprintf("Error in fetching biz penal Interest params from DB, err : %s", err.Error()), apiCommon.GetTraceID(ctx))
		return nil, err
	}
	return getBizLoanInterestSlab(bizNormalInterestParams, bizPenalInterestParams)
}

// GetLatestLoanInterestVersion ...
func GetLatestLoanInterestVersion(versions []*storage.LoanInterestVersion) (*storage.LoanInterestVersion, error) {
	if len(versions) == 0 {
		return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, utils.SafeIntToString(apiErr.ErrNoLoanInterestVersionForVersionID.Code),
			apiErr.ErrNoLoanInterestVersionForVersionID.Message)
	}
	latest := versions[0]
	for _, version := range versions {
		v1, v1Err := strconv.Atoi(latest.Version)
		if v1Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, utils.SafeIntToString(apiErr.ErrInvalidLoanInterestVersion.Code),
				apiErr.ErrInvalidLoanInterestVersion.Message)
		}
		v2, v2Err := strconv.Atoi(version.Version)
		if v2Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, utils.SafeIntToString(apiErr.ErrInvalidLoanInterestVersion.Code),
				apiErr.ErrInvalidLoanInterestVersion.Message)
		}
		if v2 > v1 {
			latest = version
		}
	}
	return latest, nil
}

func getLoanInterestSlab(normalInterest []api.LoanAccountFlatInterest, penalInterest []api.LoanAccountFlatInterest, discoveryInterest []api.LoanAccountFlatInterest) (*api.LoanAccountInterestParameters, error) {
	interestSlabMap := &api.LoanAccountInterestParameters{
		NormalInterest:    normalInterest,
		PenalInterest:     penalInterest,
		DiscoveryInterest: discoveryInterest,
	}
	return interestSlabMap, nil
}

func getBizLoanInterestSlab(normalInterest []api.LoanAccountFlatInterest, penalInterest []api.LoanAccountFlatInterest) (*api.LoanAccountInterestParameters, error) {
	interestSlabMap := &api.LoanAccountInterestParameters{
		NormalInterest: normalInterest,
		PenalInterest:  penalInterest,
	}
	return interestSlabMap, nil
}

func getLoanInterestParams(ctx context.Context, store *storage.DBStore, interestVersion string, productVariantID uint64, code string) ([]api.LoanAccountFlatInterest, error) {
	loanInterests, err := store.LoanInterestDAO.Find(ctx, data.EqualTo("ProductVariantID", productVariantID), data.EqualTo("Code", code))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the LoanInterest DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-interest from database for request with product-variant-id: %d", productVariantID), err)
	}

	var loanInterestVersions []*storage.LoanInterestVersion
	var interestVersionErr error
	if interestVersion == "" {
		loanInterestVersions, interestVersionErr = store.LoanInterestVersionDAO.Find(ctx, data.EqualTo("LoanInterestID", loanInterests[0].ID))
	} else {
		loanInterestVersions, interestVersionErr = store.LoanInterestVersionDAO.Find(ctx, data.EqualTo("LoanInterestID", loanInterests[0].ID), data.EqualTo("Version", interestVersion))
	}
	if interestVersionErr != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the LoanInterestVersion DB, err : %s", interestVersionErr), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-versions from database for request with loan-interest-id: %d", loanInterests[0].ID), interestVersionErr)
	}

	loanInterestVersion := loanInterestVersions[0]
	if interestVersion == "" {
		loanInterestVersion, err = GetLatestLoanInterestVersion(loanInterestVersions)
		if err != nil {
			return nil, err
		}
	}
	loanInterestSlabRates, err := store.LoanInterestSlabRateDAO.Find(ctx, data.EqualTo("LoanInterestVersionID", loanInterestVersion.ID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the LoanInterestSlabRate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-interest-slab-rate from database for request with loan-interest-version-id: %d", loanInterestVersion.ID), err)
	}

	var interestParams []api.LoanAccountFlatInterest
	for i := 0; i < len(loanInterestSlabRates); i++ {
		rateInFloat, err := strconv.ParseFloat(loanInterestSlabRates[i].AbsoluteInterestRatePercentage, 64)
		if err != nil {
			return nil, err
		}
		interestParams = append(interestParams, api.LoanAccountFlatInterest{
			Rate:       int32(rateInFloat * 100),
			Multiplier: 100,
			SlabType:   loanInterestSlabRates[i].SlabType,
			FromUnit:   loanInterestSlabRates[i].FromUnit,
			ToUnit:     loanInterestSlabRates[i].ToUnit,
		})
	}

	return interestParams, nil
}

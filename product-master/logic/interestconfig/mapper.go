package interestconfig

import (
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

func mapBaseInterestFromStorage(baseInterest storage.BaseInterest) *api.BaseInterest {
	return &api.BaseInterest{
		Id:          baseInterest.PublicID,
		Code:        baseInterest.Code,
		Name:        baseInterest.Name,
		Description: baseInterest.Description.String,
		Currency:    api.Currency(baseInterest.Currency),
		CreatedBy:   baseInterest.CreatedBy,
		CreatedAt:   baseInterest.CreatedAt,
		UpdatedBy:   baseInterest.UpdatedBy,
		UpdatedAt:   baseInterest.UpdatedAt,
	}
}

func mapBaseInterestVersionFromStorage(baseInterestVersion storage.BaseInterestVersion, baseInterestPublicID string) *api.BaseInterestVersion {
	return &api.BaseInterestVersion{
		Id:             baseInterestVersion.PublicID,
		BaseInterestID: baseInterestPublicID,
		Version:        baseInterestVersion.Version,
		EffectiveDate:  baseInterestVersion.EffectiveDate,
		Description:    baseInterestVersion.Description.String,
		CreatedBy:      baseInterestVersion.CreatedBy,
		CreatedAt:      baseInterestVersion.CreatedAt,
		UpdatedBy:      baseInterestVersion.UpdatedBy,
		UpdatedAt:      baseInterestVersion.UpdatedAt,
	}
}

func mapBaseInterestTimeSlabRateFromStorage(baseInterestTimeSlabRate storage.BaseInterestTimeSlabRate, baseInterestVersionPublicID string) *api.BaseInterestTimeSlabRate {
	return &api.BaseInterestTimeSlabRate{
		Id:                     baseInterestTimeSlabRate.PublicID,
		BaseInterestVersionID:  baseInterestVersionPublicID,
		TermUnit:               api.TermUnit(baseInterestTimeSlabRate.TermUnit),
		TermValue:              baseInterestTimeSlabRate.TermValue,
		BaseInterestPercentage: baseInterestTimeSlabRate.BaseRatePercentage,
		CreatedBy:              baseInterestTimeSlabRate.CreatedBy,
		CreatedAt:              baseInterestTimeSlabRate.CreatedAt,
		UpdatedBy:              baseInterestTimeSlabRate.UpdatedBy,
		UpdatedAt:              baseInterestTimeSlabRate.UpdatedAt,
	}
}

func mapDepositInterestFromStorage(depositInterest storage.DepositInterest, productVariantPublicID string, baseInterestPublicID string) *api.DepositInterest {
	return &api.DepositInterest{
		Id:                    depositInterest.PublicID,
		ProductVariantID:      productVariantPublicID,
		IsLinkedToBaseRate:    depositInterest.IsLinkedToBaseRate,
		BaseInterestID:        baseInterestPublicID,
		Code:                  depositInterest.Code,
		Name:                  depositInterest.Name,
		Description:           depositInterest.Description.String,
		Currency:              api.Currency(depositInterest.Currency),
		RoundOffType:          api.RoundOffType(depositInterest.RoundOffType),
		InterestSlabType:      api.InterestSlabType(depositInterest.InterestSlabType),
		InterestSlabStructure: api.InterestSlabStructure(depositInterest.InterestSlabStructure),
		CreatedBy:             depositInterest.CreatedBy,
		CreatedAt:             depositInterest.CreatedAt,
		UpdatedBy:             depositInterest.UpdatedBy,
		UpdatedAt:             depositInterest.UpdatedAt,
	}
}

func mapDepositInterestAmountSlabRateFromStorage(depositInterestAmountSlabRate storage.DepositInterestAmountSlabRate, depositInterestVersionPublicID string) *api.DepositInterestAmountSlabRate {
	return &api.DepositInterestAmountSlabRate{
		Id:                               depositInterestAmountSlabRate.PublicID,
		DepositInterestVersionID:         depositInterestVersionPublicID,
		FromAmount:                       depositInterestAmountSlabRate.FromAmount,
		ToAmount:                         depositInterestAmountSlabRate.ToAmount,
		BaseRateInterestSpreadPercentage: depositInterestAmountSlabRate.BaseRateInterestSpreadPercentage,
		AbsoluteInterestRatePercentage:   depositInterestAmountSlabRate.AbsoluteInterestRatePercentage,
		CreatedBy:                        depositInterestAmountSlabRate.CreatedBy,
		CreatedAt:                        depositInterestAmountSlabRate.CreatedAt,
		UpdatedBy:                        depositInterestAmountSlabRate.UpdatedBy,
		UpdatedAt:                        depositInterestAmountSlabRate.UpdatedAt,
	}
}

func mapDepositInterestVersionFromStorage(depositInterestVersion storage.DepositInterestVersion, depositInterestPublicID string) *api.DepositInterestVersion {
	return &api.DepositInterestVersion{
		Id:                depositInterestVersion.PublicID,
		DepositInterestID: depositInterestPublicID,
		Version:           depositInterestVersion.Version,
		EffectiveDate:     depositInterestVersion.EffectiveDate,
		Description:       depositInterestVersion.Description.String,
		CreatedBy:         depositInterestVersion.CreatedBy,
		CreatedAt:         depositInterestVersion.CreatedAt,
		UpdatedBy:         depositInterestVersion.UpdatedBy,
		UpdatedAt:         depositInterestVersion.UpdatedAt,
	}
}

func mapLoanInterestFromStorage(loanInterest storage.LoanInterest, productVariantPublicID string, baseInterestPublicID string) *api.LoanInterest {
	return &api.LoanInterest{
		Id:                    loanInterest.PublicID,
		ProductVariantID:      productVariantPublicID,
		IsLinkedToBaseRate:    loanInterest.IsLinkedToBaseRate,
		BaseInterestID:        baseInterestPublicID,
		Code:                  loanInterest.Code,
		Name:                  loanInterest.Name,
		Description:           loanInterest.Description.String,
		Currency:              api.Currency(loanInterest.Currency),
		RoundOffType:          api.RoundOffType(loanInterest.RoundOffType),
		InterestType:          api.InterestType(loanInterest.InterestType),
		InterestSlabUnitType:  api.InterestSlabType(loanInterest.InterestSlabUnitType),
		InterestSlabStructure: api.InterestSlabStructure(loanInterest.InterestSlabStructure),
		CreatedBy:             loanInterest.CreatedBy,
		CreatedAt:             loanInterest.CreatedAt,
		UpdatedBy:             loanInterest.UpdatedBy,
		UpdatedAt:             loanInterest.UpdatedAt,
	}
}

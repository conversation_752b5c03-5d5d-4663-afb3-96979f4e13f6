package interestconfig

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateBaseInterest ...
func CreateBaseInterest(ctx context.Context, req *api.CreateBaseInterestRequest, store *storage.DBStore) (*api.CreateBaseInterestResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	baseInterestID := uuid.New().String()
	if err := store.BaseInterestDAO.Save(ctx, &storage.BaseInterest{
		PublicID:    baseInterestID,
		Code:        req.Code,
		Name:        req.Name,
		Description: description,
		Currency:    string(req.Currency),
		CreatedBy:   req.CreatedBy,
		UpdatedBy:   req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save base-interest to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	baseInterest, err := store.BaseInterestDAO.Find(ctx, data.EqualTo("PublicID", baseInterestID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest from database after saving for request with id: %s", baseInterestID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateBaseInterestLogTag, "Successfully completed request", apiCommon.GetTraceID(ctx))
	return &api.CreateBaseInterestResponse{
		BaseInterest: mapBaseInterestFromStorage(*baseInterest[0]),
	}, nil
}

// GetBaseInterest ...
func GetBaseInterest(ctx context.Context, req *api.GetBaseInterestRequest, store *storage.DBStore) (*api.GetBaseInterestResponse, error) {
	baseInterest, err := store.BaseInterestDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest from database for request with id: %s", req.Id), err)
	}

	slog.FromContext(ctx).Info(constants.GetBaseInterestLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetBaseInterestResponse{
		BaseInterest: mapBaseInterestFromStorage(*baseInterest[0]),
	}, nil
}

// GetBaseInterestByCode ...
func GetBaseInterestByCode(ctx context.Context, req *api.GetBaseInterestByCodeRequest, store *storage.DBStore) (*api.GetBaseInterestByCodeResponse, error) {
	baseInterests, err := store.BaseInterestDAO.Find(ctx, data.EqualTo("Code", req.Code))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest from database for request with code: %s", req.Code), err)
	}

	slog.FromContext(ctx).Info(constants.GetBaseInterestByCodeLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetBaseInterestByCodeResponse{
		BaseInterest: mapBaseInterestFromStorage(*baseInterests[0]),
	}, nil
}

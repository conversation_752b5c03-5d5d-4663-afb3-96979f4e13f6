package interestconfig

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateDepositInterest ...
func CreateDepositInterest(ctx context.Context, req *api.CreateDepositInterestRequest, store *storage.DBStore) (*api.CreateDepositInterestResponse, error) {
	var description, baseInterestID sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	baseInterestID.Valid = !(req.BaseInterestID == "")
	baseInterestID.String = req.BaseInterestID

	var baseInterestInternalID sql.NullInt64
	if baseInterestID.Valid {
		baseInterests, err := store.BaseInterestDAO.Find(ctx, data.EqualTo("PublicID", req.BaseInterestID))
		if err != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load base-interest from database for request with code: %s", req.Code), err)
		}
		baseInterestInternalID = sql.NullInt64{
			Int64: utils.MustConvertToInt64(baseInterests[0].ID),
			Valid: true,
		}
	}

	productVariants, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.ProductVariantID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.ProductVariantID), err)
	}

	depositInterestID := uuid.New().String()
	if err = store.DepositInterestDAO.Save(ctx, &storage.DepositInterest{
		PublicID:              depositInterestID,
		ProductVariantID:      productVariants[0].ID,
		IsLinkedToBaseRate:    req.IsLinkedToBaseRate,
		BaseInterestID:        baseInterestInternalID,
		Code:                  req.Code,
		Name:                  req.Name,
		Description:           description,
		Currency:              string(req.Currency),
		RoundOffType:          string(req.RoundOffType),
		InterestSlabType:      string(req.InterestSlabType),
		InterestSlabStructure: string(req.InterestSlabStructure),
		CreatedBy:             req.CreatedBy,
		UpdatedBy:             req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save deposit-interest to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	depositInterest, err := store.DepositInterestDAO.Find(ctx, data.EqualTo("PublicID", depositInterestID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest from database after saving for request with id: %s", depositInterestID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateDepositInterestLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateDepositInterestResponse{
		DepositInterest: mapDepositInterestFromStorage(*depositInterest[0], req.ProductVariantID, req.BaseInterestID),
	}, nil
}

// GetDepositInterest ...
func GetDepositInterest(ctx context.Context, req *api.GetDepositInterestRequest, store *storage.DBStore) (*api.GetDepositInterestResponse, error) {
	depositInterest, err := store.DepositInterestDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest from database for request with id: %s", req.Id), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, depositInterest[0].ProductVariantID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database with id: %d", depositInterest[0].ProductVariantID), err)
	}

	var baseInterestPublicID string
	if depositInterest[0].IsLinkedToBaseRate {
		baseInterest, baseIntErr := store.BaseInterestDAO.LoadByID(ctx, utils.MustConvertToUint64(depositInterest[0].BaseInterestID.Int64))
		if baseIntErr != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load base-interest from database with id: %d", utils.MustConvertToUint64(depositInterest[0].BaseInterestID.Int64)), baseIntErr)
		}
		baseInterestPublicID = baseInterest.PublicID
	}

	slog.FromContext(ctx).Info(constants.GetDepositInterestLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetDepositInterestResponse{
		DepositInterest: mapDepositInterestFromStorage(*depositInterest[0], productVariant.PublicID, baseInterestPublicID),
	}, nil
}

// GetDepositInterestByCode ...
func GetDepositInterestByCode(ctx context.Context, req *api.GetDepositInterestByCodeRequest, store *storage.DBStore) (*api.GetDepositInterestByCodeResponse, error) {
	depositInterest, err := store.DepositInterestDAO.Find(ctx, data.EqualTo("Code", req.Code))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest from database for request with code: %s", req.Code), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, depositInterest[0].ProductVariantID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database with id: %d", depositInterest[0].ProductVariantID), err)
	}

	var baseInterestPublicID string
	if depositInterest[0].IsLinkedToBaseRate {
		baseInterest, baseIntErr := store.BaseInterestDAO.LoadByID(ctx, utils.MustConvertToUint64(depositInterest[0].BaseInterestID.Int64))
		if baseIntErr != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load base-interest from database with id: %d", utils.MustConvertToUint64(depositInterest[0].BaseInterestID.Int64)), baseIntErr)
		}
		baseInterestPublicID = baseInterest.PublicID
	}

	slog.FromContext(ctx).Info(constants.GetDepositInterestByCodeLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetDepositInterestByCodeResponse{
		DepositInterest: mapDepositInterestFromStorage(*depositInterest[0], productVariant.PublicID, baseInterestPublicID),
	}, nil
}

package financialconfig

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateGeneralLedger ...
func CreateGeneralLedger(ctx context.Context, req *api.CreateGeneralLedgerRequest, store *storage.DBStore) (*api.CreateGeneralLedgerResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	generalLedgerID := uuid.New().String()
	if err := store.GeneralLedgerDAO.Save(ctx, &storage.GeneralLedger{
		PublicID:    generalLedgerID,
		Code:        req.Code,
		Name:        req.Name,
		Description: description,
		Currency:    string(req.Currency),
		Status:      string(api.EntityStatus_ACTIVE),
		CreatedBy:   req.CreatedBy,
		UpdatedBy:   req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save general-ledger to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	generalLedger, err := store.GeneralLedgerDAO.Find(ctx, data.EqualTo("PublicID", generalLedgerID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load general-ledger from database after saving for request with id: %s", generalLedgerID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateGeneralLedgerLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateGeneralLedgerResponse{
		GeneralLedger: mapGeneralLedgerFromStorage(*generalLedger[0]),
	}, nil
}

// GetGeneralLedger ...
func GetGeneralLedger(ctx context.Context, req *api.GetGeneralLedgerRequest, store *storage.DBStore) (*api.GetGeneralLedgerResponse, error) {
	generalLedger, err := store.GeneralLedgerDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database for request with id: %s", req.Id), err)
	}

	slog.FromContext(ctx).Info(constants.GetGeneralLedgerLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetGeneralLedgerResponse{
		GeneralLedger: mapGeneralLedgerFromStorage(*generalLedger[0]),
	}, nil
}

// GetGeneralLedgerByCode ...
func GetGeneralLedgerByCode(ctx context.Context, req *api.GetGeneralLedgerByCodeRequest, store *storage.DBStore) (*api.GetGeneralLedgerByCodeResponse, error) {
	generalLedgers, err := store.GeneralLedgerDAO.Find(ctx, data.EqualTo("Code", req.Code))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database for request with code: %s", req.Code), err)
	}

	slog.FromContext(ctx).Info(constants.GetGeneralLedgerByCodeLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetGeneralLedgerByCodeResponse{
		GeneralLedger: mapGeneralLedgerFromStorage(*generalLedgers[0]),
	}, nil
}

// UpdateGeneralLedgerStatus ...
func UpdateGeneralLedgerStatus(ctx context.Context, req *api.UpdateGeneralLedgerStatusRequest, store *storage.DBStore) (*api.UpdateGeneralLedgerStatusResponse, error) {
	generalLedger, err := store.GeneralLedgerDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.GeneralLedgerDAO.Update(ctx, &storage.GeneralLedger{
		ID:          generalLedger[0].ID,
		PublicID:    generalLedger[0].PublicID,
		Code:        generalLedger[0].Code,
		Name:        generalLedger[0].Name,
		Description: generalLedger[0].Description,
		Currency:    generalLedger[0].Currency,
		Status:      string(req.Status),
		CreatedBy:   generalLedger[0].CreatedBy,
		CreatedAt:   generalLedger[0].CreatedAt,
		UpdatedBy:   req.UpdatedBy,
	}); updateErr != nil {
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update generalLedger in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	generalLedger, err = store.GeneralLedgerDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database after update for request with id: %s", req.Id), err)
	}

	slog.FromContext(ctx).Info(constants.UpdateGeneralLedgerStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateGeneralLedgerStatusResponse{
		GeneralLedger: mapGeneralLedgerFromStorage(*generalLedger[0]),
	}, nil
}

package financialconfig

import (
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

func mapGeneralLedgerFromStorage(generalLedger storage.GeneralLedger) *api.GeneralLedger {
	return &api.GeneralLedger{
		Id:          generalLedger.PublicID,
		Code:        generalLedger.Code,
		Name:        generalLedger.Name,
		Description: generalLedger.Description.String,
		Currency:    api.Currency(generalLedger.Currency),
		Status:      api.EntityStatus(generalLedger.Status),
		CreatedBy:   generalLedger.CreatedBy,
		CreatedAt:   generalLedger.CreatedAt,
		UpdatedBy:   generalLedger.UpdatedBy,
		UpdatedAt:   generalLedger.UpdatedAt,
	}
}

func mapInternalAccountFromStorage(internalAccount storage.InternalAccount, generalLedgerPublicID string) *api.InternalAccount {
	return &api.InternalAccount{
		Id:              internalAccount.PublicID,
		GeneralLedgerID: generalLedgerPublicID,
		Code:            internalAccount.Code,
		Name:            internalAccount.Name,
		Description:     internalAccount.Description.String,
		Currency:        api.Currency(internalAccount.Currency),
		Status:          api.EntityStatus(internalAccount.Status),
		CreatedBy:       internalAccount.CreatedBy,
		CreatedAt:       internalAccount.CreatedAt,
		UpdatedBy:       internalAccount.UpdatedBy,
		UpdatedAt:       internalAccount.UpdatedAt,
	}
}

func mapInternalAccountsFromStorage(internalAccounts []*storage.InternalAccount, generalLedgerPublicID string) []api.InternalAccount {
	var mappedAccounts []api.InternalAccount
	for _, internalAccount := range internalAccounts {
		mappedAccounts = append(mappedAccounts, *mapInternalAccountFromStorage(*internalAccount, generalLedgerPublicID))
	}
	return mappedAccounts
}

func mapProductVariantTransactionCatalogueInternalAccountMappingFromStorage(productVariantTransactionCatalogueInternalAccountMapping storage.ProductVariantTransactionCatalogueInternalAccountMapping, internalAccountPublicID string, productVariantTransactionCatalogueInternalAccountMappingPublicID string) *api.ProductVariantTransactionCatalogueInternalAccountMapping {
	return &api.ProductVariantTransactionCatalogueInternalAccountMapping{
		Id:                productVariantTransactionCatalogueInternalAccountMapping.PublicID,
		InternalAccountID: internalAccountPublicID,
		ProductVariantTransactionCatalogueMappingID: productVariantTransactionCatalogueInternalAccountMappingPublicID,
		IdentifierKey: productVariantTransactionCatalogueInternalAccountMapping.IdentifierKey,
		Status:        api.EntityStatus(productVariantTransactionCatalogueInternalAccountMapping.Status),
		CreatedBy:     productVariantTransactionCatalogueInternalAccountMapping.CreatedBy,
		CreatedAt:     productVariantTransactionCatalogueInternalAccountMapping.CreatedAt,
		UpdatedBy:     productVariantTransactionCatalogueInternalAccountMapping.UpdatedBy,
		UpdatedAt:     productVariantTransactionCatalogueInternalAccountMapping.UpdatedAt,
	}
}

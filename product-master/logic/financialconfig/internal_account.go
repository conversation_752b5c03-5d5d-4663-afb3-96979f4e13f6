package financialconfig

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateInternalAccount ...
func CreateInternalAccount(ctx context.Context, req *api.CreateInternalAccountRequest, store *storage.DBStore) (*api.CreateInternalAccountResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	generalLedger, err := store.GeneralLedgerDAO.Find(ctx, data.EqualTo("PublicID", req.GeneralLedgerID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database for request with id: %s", req.GeneralLedgerID), err)
	}

	internalAccountID := uuid.New().String()
	if err = store.InternalAccountDAO.Save(ctx, &storage.InternalAccount{
		PublicID:        internalAccountID,
		GeneralLedgerID: generalLedger[0].ID,
		Code:            req.Code,
		Name:            req.Name,
		Description:     description,
		Currency:        string(req.Currency),
		Status:          string(api.EntityStatus_ACTIVE),
		CreatedBy:       req.CreatedBy,
		UpdatedBy:       req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save internal-account to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	internalAccount, err := store.InternalAccountDAO.Find(ctx, data.EqualTo("PublicID", internalAccountID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database after saving for request with id: %s", internalAccountID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateInternalAccountLogTag, fmt.Sprintf("Successfuly completed the request: %+v", req), apiCommon.GetTraceID(ctx))
	return &api.CreateInternalAccountResponse{
		InternalAccount: mapInternalAccountFromStorage(*internalAccount[0], req.GeneralLedgerID),
	}, nil
}

// GetInternalAccount ...
func GetInternalAccount(ctx context.Context, req *api.GetInternalAccountRequest, store *storage.DBStore) (*api.GetInternalAccountResponse, error) {
	internalAccount, err := store.InternalAccountDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database for request with id: %s", req.Id), err)
	}

	generalLedger, err := store.GeneralLedgerDAO.LoadByID(ctx, internalAccount[0].GeneralLedgerID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database for request with id: %d", internalAccount[0].GeneralLedgerID), err)
	}

	slog.FromContext(ctx).Info(constants.GetInternalAccountLogTag, fmt.Sprintf("Successfuly completed the request: %+v", req), apiCommon.GetTraceID(ctx))
	return &api.GetInternalAccountResponse{
		InternalAccount: mapInternalAccountFromStorage(*internalAccount[0], generalLedger.PublicID),
	}, nil
}

// ListInternalAccounts ...
func ListInternalAccounts(ctx context.Context, req *api.ListInternalAccountsRequest, store *storage.DBStore) (*api.ListInternalAccountsResponse, error) {
	var generalLedger []*storage.GeneralLedger
	var err error
	if req.GeneralLedgerID != "" {
		generalLedger, err = store.GeneralLedgerDAO.Find(ctx, data.EqualTo("PublicID", req.GeneralLedgerID))
		if err != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load generalLedger from database for request with id: %s", req.GeneralLedgerID), err)
		}
	}

	var internalAccounts []*storage.InternalAccount
	if req.Code != "" && req.GeneralLedgerID == "" {
		internalAccounts, err = store.InternalAccountDAO.Find(ctx, data.EqualTo("Code", req.Code))
	} else if req.Code == "" && req.GeneralLedgerID != "" {
		internalAccounts, err = store.InternalAccountDAO.Find(ctx, data.EqualTo("GeneralLedgerID", generalLedger[0].ID))
	} else {
		internalAccounts, err = store.InternalAccountDAO.Find(ctx, data.EqualTo("Code", req.Code), data.EqualTo("GeneralLedgerID", generalLedger[0].ID))
	}

	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database for request with code = %s, generalLedgerID = %s", req.Code, req.GeneralLedgerID), err)
	}

	ledger, err := store.GeneralLedgerDAO.LoadByID(ctx, internalAccounts[0].GeneralLedgerID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database for request with id: %d", internalAccounts[0].GeneralLedgerID), err)
	}

	slog.FromContext(ctx).Info(constants.ListInternalAccountsLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.ListInternalAccountsResponse{
		InternalAccounts: mapInternalAccountsFromStorage(internalAccounts, ledger.PublicID),
	}, nil
}

// UpdateInternalAccountStatus ...
func UpdateInternalAccountStatus(ctx context.Context, req *api.UpdateInternalAccountStatusRequest, store *storage.DBStore) (*api.UpdateInternalAccountStatusResponse, error) {
	internalAccounts, err := store.InternalAccountDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.InternalAccountDAO.Update(ctx, &storage.InternalAccount{
		ID:              internalAccounts[0].ID,
		PublicID:        internalAccounts[0].PublicID,
		GeneralLedgerID: internalAccounts[0].GeneralLedgerID,
		Code:            internalAccounts[0].Code,
		Name:            internalAccounts[0].Name,
		Description:     internalAccounts[0].Description,
		Currency:        internalAccounts[0].Currency,
		Status:          string(req.Status),
		CreatedBy:       internalAccounts[0].CreatedBy,
		CreatedAt:       internalAccounts[0].CreatedAt,
		UpdatedBy:       req.UpdatedBy,
	}); updateErr != nil {
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update internal-account in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	internalAccount, err := store.InternalAccountDAO.LoadByID(ctx, internalAccounts[0].ID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database after update for request with id: %s", req.Id), err)
	}

	generalLedger, err := store.GeneralLedgerDAO.LoadByID(ctx, internalAccounts[0].GeneralLedgerID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load generalLedger from database for request with id: %d", internalAccounts[0].GeneralLedgerID), err)
	}

	slog.FromContext(ctx).Info(constants.UpdateInternalAccountStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateInternalAccountStatusResponse{
		InternalAccount: mapInternalAccountFromStorage(*internalAccount, generalLedger.PublicID),
	}, nil
}

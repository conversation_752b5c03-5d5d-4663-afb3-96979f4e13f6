package financialconfig

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateProductVariantTransactionCatalogueInternalAccountMapping ...
func CreateProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueInternalAccountMappingRequest, store *storage.DBStore) (*api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	id := uuid.New().String()

	internalAccount, err := store.InternalAccountDAO.Find(ctx, data.EqualTo("PublicID", req.InternalAccountID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database after saving for request with id: %s", req.InternalAccountID), err)
	}

	productVariantTransactionCatalogueMapping, err := store.ProductVariantTransactionCatalogueMappingDAO.Find(ctx, data.EqualTo("PublicID", req.ProductVariantTransactionCatalogueMappingID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database for request with id: %s", req.ProductVariantTransactionCatalogueMappingID), err)
	}

	if err = store.ProductVariantTransactionCatalogueInternalAccountMappingDAO.Save(ctx, &storage.ProductVariantTransactionCatalogueInternalAccountMapping{
		PublicID: id,
		ProductVariantTransactionCatalogueMappingID: productVariantTransactionCatalogueMapping[0].ID,
		InternalAccountID: internalAccount[0].ID,
		IdentifierKey:     req.IdentifierKey,
		Status:            string(api.EntityStatus_ACTIVE),
		CreatedBy:         req.CreatedBy,
		UpdatedBy:         req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product-variant-transaction-catalogue-internal-account-mapping to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	productVariantTransactionCatalogueInternalAccountMapping, err := store.ProductVariantTransactionCatalogueInternalAccountMappingDAO.Find(ctx, data.EqualTo("PublicID", id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load productVariantTransactionCatalogueInternalAccountMapping from database after saving for request with id: %s", id), err)
	}

	slog.FromContext(ctx).Info(constants.CreateProductVariantTransactionCatalogueInternalAccountMappingLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateProductVariantTransactionCatalogueInternalAccountMappingResponse{
		ProductVariantTransactionCatalogueInternalAccountMapping: mapProductVariantTransactionCatalogueInternalAccountMappingFromStorage(*productVariantTransactionCatalogueInternalAccountMapping[0], req.InternalAccountID, req.ProductVariantTransactionCatalogueMappingID),
	}, nil
}

// GetProductVariantTransactionCatalogueInternalAccountMapping ...
func GetProductVariantTransactionCatalogueInternalAccountMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingRequest, store *storage.DBStore) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse, error) {
	productVariantTransactionCatalogueInternalAccountMapping, err := store.ProductVariantTransactionCatalogueInternalAccountMappingDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load productVariantTransactionCatalogueInternalAccountMapping from database for request with id: %s", req.Id), err)
	}

	internalAccount, err := store.InternalAccountDAO.LoadByID(ctx, productVariantTransactionCatalogueInternalAccountMapping[0].InternalAccountID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database after saving for request with id: %d", productVariantTransactionCatalogueInternalAccountMapping[0].InternalAccountID), err)
	}

	productVariantTransactionCatalogueMapping, err := store.ProductVariantTransactionCatalogueMappingDAO.LoadByID(ctx, productVariantTransactionCatalogueInternalAccountMapping[0].ProductVariantTransactionCatalogueMappingID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database for request with id: %d", productVariantTransactionCatalogueInternalAccountMapping[0].ProductVariantTransactionCatalogueMappingID), err)
	}

	slog.FromContext(ctx).Info(constants.GetProductVariantTransactionCatalogueInternalAccountMappingLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductVariantTransactionCatalogueInternalAccountMappingResponse{
		ProductVariantTransactionCatalogueInternalAccountMapping: mapProductVariantTransactionCatalogueInternalAccountMappingFromStorage(*productVariantTransactionCatalogueInternalAccountMapping[0], internalAccount.PublicID, productVariantTransactionCatalogueMapping.PublicID),
	}, nil
}

// GetProductVariantTransactionCatalogueInternalAccountMappingByKey ...
func GetProductVariantTransactionCatalogueInternalAccountMappingByKey(ctx context.Context, req *api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyRequest, store *storage.DBStore) (*api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse, error) {
	productVariantTransactionCatalogueInternalAccountMapping, err := store.ProductVariantTransactionCatalogueInternalAccountMappingDAO.Find(ctx, data.EqualTo("IdentifierKey", req.IdentifierKey))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load productVariantTransactionCatalogueInternalAccountMapping from database for request with IdentifierKey: %s", req.IdentifierKey), err)
	}

	internalAccount, err := store.InternalAccountDAO.LoadByID(ctx, productVariantTransactionCatalogueInternalAccountMapping[0].InternalAccountID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database after saving for request with id: %d", productVariantTransactionCatalogueInternalAccountMapping[0].InternalAccountID), err)
	}

	productVariantTransactionCatalogueMapping, err := store.ProductVariantTransactionCatalogueMappingDAO.LoadByID(ctx, productVariantTransactionCatalogueInternalAccountMapping[0].ProductVariantTransactionCatalogueMappingID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database for request with id: %d", productVariantTransactionCatalogueInternalAccountMapping[0].ProductVariantTransactionCatalogueMappingID), err)
	}

	slog.FromContext(ctx).Info(constants.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductVariantTransactionCatalogueInternalAccountMappingByKeyResponse{
		ProductVariantTransactionCatalogueInternalAccountMapping: mapProductVariantTransactionCatalogueInternalAccountMappingFromStorage(*productVariantTransactionCatalogueInternalAccountMapping[0], internalAccount.PublicID, productVariantTransactionCatalogueMapping.PublicID),
	}, nil
}

// UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus ...
func UpdateProductVariantTransactionCatalogueInternalAccountMappingStatus(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusRequest, store *storage.DBStore) (*api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse, error) {
	productVariantTransactionCatalogueInternalAccountMapping, err := store.ProductVariantTransactionCatalogueInternalAccountMappingDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant-transaction-catalogue-internal-account-mapping from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.ProductVariantTransactionCatalogueInternalAccountMappingDAO.Update(ctx, &storage.ProductVariantTransactionCatalogueInternalAccountMapping{
		ID:       productVariantTransactionCatalogueInternalAccountMapping[0].ID,
		PublicID: productVariantTransactionCatalogueInternalAccountMapping[0].PublicID,
		ProductVariantTransactionCatalogueMappingID: productVariantTransactionCatalogueInternalAccountMapping[0].ProductVariantTransactionCatalogueMappingID,
		InternalAccountID: productVariantTransactionCatalogueInternalAccountMapping[0].InternalAccountID,
		IdentifierKey:     productVariantTransactionCatalogueInternalAccountMapping[0].IdentifierKey,
		Status:            string(req.Status),
		CreatedBy:         productVariantTransactionCatalogueInternalAccountMapping[0].CreatedBy,
		CreatedAt:         productVariantTransactionCatalogueInternalAccountMapping[0].CreatedAt,
		UpdatedBy:         req.UpdatedBy,
	}); updateErr != nil {
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update product-variant-transaction-catalogue-internal-account-mapping in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	productVariantTransactionCatalogueInternalAccountMapping, err = store.ProductVariantTransactionCatalogueInternalAccountMappingDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database after update for request with id: %s", req.Id), err)
	}

	internalAccount, err := store.InternalAccountDAO.LoadByID(ctx, productVariantTransactionCatalogueInternalAccountMapping[0].InternalAccountID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load internal-account from database after saving for request with id: %d", productVariantTransactionCatalogueInternalAccountMapping[0].InternalAccountID), err)
	}

	productVariantTransactionCatalogueMapping, err := store.ProductVariantTransactionCatalogueMappingDAO.LoadByID(ctx, productVariantTransactionCatalogueInternalAccountMapping[0].ProductVariantTransactionCatalogueMappingID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database for request with id: %d", productVariantTransactionCatalogueInternalAccountMapping[0].ProductVariantTransactionCatalogueMappingID), err)
	}

	slog.FromContext(ctx).Info(constants.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateProductVariantTransactionCatalogueInternalAccountMappingStatusResponse{
		ProductVariantTransactionCatalogueInternalAccountMapping: mapProductVariantTransactionCatalogueInternalAccountMappingFromStorage(*productVariantTransactionCatalogueInternalAccountMapping[0], internalAccount.PublicID, productVariantTransactionCatalogueMapping.PublicID),
	}, nil
}

package loaninstruction

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateLoanInstruction ...
func CreateLoanInstruction(ctx context.Context, req *api.CreateLoanInstructionRequest, store *storage.DBStore) (*api.CreateLoanInstructionResponse, error) {
	version, err := store.LoanInstructionVersionDAO.Find(ctx, data.EqualTo("PublicID", req.LoanInstructionVersionID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load loan instruction version from database with request: %s", utils.ToJSON(req)), err)
	}

	LoanInstructionID := uuid.New().String()
	if err = store.LoanInstructionDAO.Save(ctx, &storage.LoanInstruction{
		PublicID:                 LoanInstructionID,
		LoanInstructionVersionID: version[0].ID,
		Code:                     req.Code,
		Name:                     req.Name,
		CreatedBy:                req.CreatedBy,
		UpdatedBy:                req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save loan-instruction to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	LoanInstruction, err := store.LoanInstructionDAO.Find(ctx, data.EqualTo("PublicID", LoanInstructionID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-instruction from database after saving for request with id: %s", LoanInstructionID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateLoanInstructionLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateLoanInstructionResponse{
		LoanInstruction: mapLoanInstructionFromStorage(LoanInstruction[0]),
	}, nil
}

// GetLoanInstructionByCode ...
func GetLoanInstructionByCode(ctx context.Context, req *api.GetLoanInstructionsByCodeRequest, store *storage.DBStore) (*api.GetLoanInstructionsByCodeResponse, error) {
	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", req.ProductVariantCode), data.DescendingOrder("Version"))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load productVariant from database with request: %s", utils.ToJSON(req)), err)
	}
	var instructionFilters = []data.Condition{
		data.EqualTo("ProductVariantID", productVariant[0].ID),
		data.EqualTo("InstructionType", req.InstructionType),
		data.DescendingOrder("Version"),
	}
	if req.Version != "" {
		instructionFilters = append(instructionFilters, data.EqualTo("Version", req.Version))
	}
	loanInstructionVersion, err := store.LoanInstructionVersionDAO.Find(ctx, instructionFilters...)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load loan instruction version from database with request: %s", utils.ToJSON(req)), err)
	}

	storageInstructions, err := store.LoanInstructionDAO.Find(ctx, data.EqualTo("LoanInstructionVersionID", loanInstructionVersion[0].ID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load loan instructions from database with request: %s", utils.ToJSON(req)), err)
	}

	var loanInstructions []api.LoanInstruction
	for _, l := range storageInstructions {
		loanInstructions = append(loanInstructions, *mapLoanInstructionFromStorage(l))
	}

	return &api.GetLoanInstructionsByCodeResponse{
		ProductVariantCode: req.ProductVariantCode,
		Version:            loanInstructionVersion[0].Version,
		InstructionType:    req.InstructionType,
		LoanInstruction:    loanInstructions,
	}, nil
}

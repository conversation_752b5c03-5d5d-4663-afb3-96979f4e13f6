package loaninstruction

import (
	"encoding/json"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

func mapLoanInstructionVersionFromStorage(l *storage.LoanInstructionVersion, productVariantCode string) *api.LoanInstructionVersion {
	return &api.LoanInstructionVersion{
		Id:                 l.PublicID,
		ProductVariantCode: productVariantCode,
		Version:            l.Version,
		InstructionType:    l.InstructionType,
		Description:        l.Description,
		EffectiveDate:      l.EffectiveDate,
		CreatedBy:          l.CreatedBy,
		CreatedAt:          l.<PERSON>t,
		UpdatedBy:          l.UpdatedBy,
		UpdatedAt:          l.Updated<PERSON>t,
	}
}

func mapLoanInstructionFromStorage(l *storage.LoanInstruction) *api.LoanInstruction {
	var restrictions map[string]interface{}
	if err := json.Unmarshal(l.Restrictions, &restrictions); err != nil {
		return nil
	}
	return &api.LoanInstruction{
		Id:           l.PublicID,
		Code:         l.Code,
		Name:         l.Name,
		CreatedBy:    l.<PERSON>,
		CreatedAt:    l.<PERSON>,
		UpdatedBy:    l.<PERSON>y,
		UpdatedAt:    l.UpdatedAt,
		Restrictions: restrictions,
	}
}

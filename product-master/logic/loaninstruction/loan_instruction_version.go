// Package loaninstruction ...
package loaninstruction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateLoanInstructionVersion ...
func CreateLoanInstructionVersion(ctx context.Context, req *api.CreateLoanInstructionVersionRequest, store *storage.DBStore) (*api.CreateLoanInstructionVersionResponse, error) {
	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", req.ProductVariantCode), data.EqualTo("Version", req.ProductVariantVersion))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load productVariant from database with request: %s", utils.ToJSON(req)), err)
	}

	if req.EffectiveDate.IsZero() {
		req.EffectiveDate = time.Now().UTC()
	}
	LoanInstructionVersionID := uuid.New().String()
	if err = store.LoanInstructionVersionDAO.Save(ctx, &storage.LoanInstructionVersion{
		PublicID:         LoanInstructionVersionID,
		ProductVariantID: productVariant[0].ID,
		InstructionType:  req.InstructionType,
		Version:          req.Version,
		EffectiveDate:    req.EffectiveDate,
		Description:      req.Description,
		CreatedBy:        req.CreatedBy,
		UpdatedBy:        req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save loan-instruction-version to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	LoanInstructionVersion, err := store.LoanInstructionVersionDAO.Find(ctx, data.EqualTo("PublicID", LoanInstructionVersionID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-instruction-version from database after saving for request with id: %s", LoanInstructionVersionID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateLoanInstructionVersionLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateLoanInstructionVersionResponse{
		LoanInstructionVersion: mapLoanInstructionVersionFromStorage(LoanInstructionVersion[0], req.ProductVariantCode),
	}, nil
}

package loanpastdueconfig

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetLoanPastDueParametersByProductCode fetches a loan past due parameters by product code
func GetLoanPastDueParametersByProductCode(ctx context.Context, req *api.GetLoanPastDueParametersByProductCodeRequest, store *storage.DBStore) (*api.GetLoanPastDueParametersByProductCodeResponse, error) {
	productCode := req.ProductCode
	products, err := store.ProductDAO.Find(ctx, data.EqualTo("Code", productCode))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLoanPastDueParametersByProductCodeLogTag, fmt.Sprintf("Error in finding from the Product DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with code: %s", productCode), err)
	}

	product := products[0]
	loanPastDueVersion, err := getLoanPastDueVersion(ctx, store, req.LoanPastDueVersion, product.ID)
	if err != nil {
		return nil, err
	}

	loanPastDueSlabs, err := getLoanPastDueSlabByVersionID(ctx, store, loanPastDueVersion.ID)
	if err != nil {
		return nil, err
	}

	response := &api.GetLoanPastDueParametersByProductCodeResponse{
		ProductCode:        productCode,
		LoanPastDueVersion: loanPastDueVersion.Version,
		LoanPastDueSlab:    loanPastDueSlabs,
	}

	return response, nil
}

func getLoanPastDueVersion(ctx context.Context, store *storage.DBStore, version string, productID uint64) (*storage.LoanPastDueVersion, error) {
	var loanPastDueVersions []*storage.LoanPastDueVersion
	var loanPastDueVersionErr, latestVersionErr error
	if version == "" {
		loanPastDueVersions, loanPastDueVersionErr = store.LoanPastDueVersionDAO.Find(ctx, data.EqualTo("ProductID", productID))
	} else {
		loanPastDueVersions, loanPastDueVersionErr = store.LoanPastDueVersionDAO.Find(ctx, data.EqualTo("ProductID", productID), data.EqualTo("Version", version))
	}
	if loanPastDueVersionErr != nil {
		slog.FromContext(ctx).Warn(constants.GetLoanPastDueParametersByProductCodeLogTag, fmt.Sprintf("Error in finding from the LoanPastDueVersion DB, err : %s", loanPastDueVersionErr), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-past-due-versions from database for request with product-id: %d", productID), loanPastDueVersionErr)
	}

	loanPastDueVersion := loanPastDueVersions[0]
	if version == "" {
		loanPastDueVersion, latestVersionErr = GetLatestLoanPastDueVersion(loanPastDueVersions)
		if latestVersionErr != nil {
			return nil, latestVersionErr
		}
	}

	return loanPastDueVersion, nil
}

func getLoanPastDueSlabByVersionID(ctx context.Context, store *storage.DBStore, loanPastDueVersionID uint64) ([]api.LoanPastDueSlab, error) {
	loanPastDueSlabRecords, err := store.LoanPastDueSlabDAO.Find(ctx, data.EqualTo("LoanPastDueVersionID", loanPastDueVersionID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLoanPastDueParametersByProductCodeLogTag, fmt.Sprintf("Error in finding from the LoanPastDueSlab DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load loan-past-due-slab from database for request with loan-past-due-version-id: %d", loanPastDueVersionID), err)
	}

	var loanPastDueSlabs []api.LoanPastDueSlab
	for _, value := range loanPastDueSlabRecords {
		loanPastDueSlab := api.LoanPastDueSlab{
			Id:         value.PublicID,
			FromUnit:   value.FromUnit,
			ToUnit:     value.ToUnit,
			SlabType:   value.SlabType,
			BucketName: value.BucketName,
			CreatedBy:  value.CreatedBy,
			CreatedAt:  value.CreatedAt,
			UpdatedBy:  value.UpdatedBy,
			UpdatedAt:  value.UpdatedAt,
		}
		loanPastDueSlabs = append(loanPastDueSlabs, loanPastDueSlab)
	}

	return loanPastDueSlabs, nil
}

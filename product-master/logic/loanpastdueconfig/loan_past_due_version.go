package loanpastdueconfig

import (
	"net/http"
	"strconv"

	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

// GetLatestLoanPastDueVersion ...
func GetLatestLoanPastDueVersion(versions []*storage.LoanPastDueVersion) (*storage.LoanPastDueVersion, error) {
	if len(versions) == 0 {
		return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrNoLoanPastDueVersionForVersionID.Code), 10),
			apiErr.ErrNoLoanPastDueVersionForVersionID.Message)
	}
	latest := versions[0]
	for _, version := range versions {
		v1, v1Err := strconv.Atoi(latest.Version)
		if v1Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidLoanPastDueVersion.Code), 10),
				apiErr.ErrInvalidDepositInterestVersion.Message)
		}
		v2, v2Err := strconv.Atoi(version.Version)
		if v2Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidLoanPastDueVersion.Code), 10),
				apiErr.ErrInvalidDepositInterestVersion.Message)
		}
		if v2 > v1 {
			latest = version
		}
	}
	return latest, nil
}

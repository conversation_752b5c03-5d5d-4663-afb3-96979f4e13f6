package error

// CustomError ...
type CustomError struct {
	Code    int
	Message string
}

var (

	// ErrUnknown ...
	ErrUnknown = CustomError{
		Code:    3201,
		Message: "Unknown Error",
	}

	// ErrMissingIdempotencyKey ...
	ErrMissingIdempotencyKey = CustomError{
		Code:    3202,
		Message: "Missing idempotency key",
	}

	// ErrMissingID ...
	ErrMissingID = CustomError{
		Code:    3203,
		Message: "Missing id",
	}

	// ErrMissingProductID ...
	ErrMissingProductID = CustomError{
		Code:    3204,
		Message: "Missing product id field",
	}

	// ErrMissingProductTemplateID ...
	ErrMissingProductTemplateID = CustomError{
		Code:    3205,
		Message: "Missing product template id field",
	}

	// ErrMissingProductVariantID ...
	ErrMissingProductVariantID = CustomError{
		Code:    3206,
		Message: "Missing product variant id field",
	}

	// ErrMissingCode ...
	ErrMissingCode = CustomError{
		Code:    3207,
		Message: "Missing code field",
	}

	// ErrMissingName ...
	ErrMissingName = CustomError{
		Code:    3208,
		Message: "Missing name",
	}

	// ErrMissingNamespace ...
	ErrMissingNamespace = CustomError{
		Code:    3209,
		Message: "Missing namespace field",
	}

	// ErrMissingVersion ...
	ErrMissingVersion = CustomError{
		Code:    3210,
		Message: "Missing version field",
	}

	// ErrMissingParameterKey ...
	ErrMissingParameterKey = CustomError{
		Code:    3211,
		Message: "Missing parameter key field",
	}

	// ErrMissingParameterValue ...
	ErrMissingParameterValue = CustomError{
		Code:    3212,
		Message: "Missing parameter value field",
	}

	// ErrMissingCreatedBy ...
	ErrMissingCreatedBy = CustomError{
		Code:    3213,
		Message: "Missing created by field",
	}

	// ErrMissingUpdatedBy ...
	ErrMissingUpdatedBy = CustomError{
		Code:    3214,
		Message: "Missing updated by field",
	}

	// ErrMissingDomain ...
	ErrMissingDomain = CustomError{
		Code:    3215,
		Message: "Missing domain field",
	}

	// ErrMissingTxnType ...
	ErrMissingTxnType = CustomError{
		Code:    3216,
		Message: "Missing txn type field",
	}

	// ErrMissingTxnSubType ...
	ErrMissingTxnSubType = CustomError{
		Code:    3217,
		Message: "Missing txn sub type field",
	}

	// ErrMissingDisplayName ...
	ErrMissingDisplayName = CustomError{
		Code:    3218,
		Message: "Missing display name field",
	}

	// ErrMissingTransactionCatalogueID ...
	ErrMissingTransactionCatalogueID = CustomError{
		Code:    3219,
		Message: "Missing transaction catalogue id field",
	}

	// ErrMissingGeneralLedgerID ...
	ErrMissingGeneralLedgerID = CustomError{
		Code:    3220,
		Message: "Missing general ledger id field",
	}

	// ErrMissingAtLeastOneField ...
	ErrMissingAtLeastOneField = CustomError{
		Code:    3221,
		Message: "At least one parameter is necessary",
	}

	// ErrMissingInternalAccountID ...
	ErrMissingInternalAccountID = CustomError{
		Code:    3222,
		Message: "Missing internal account id field",
	}

	// ErrMissingProductVariantTransactionCatalogueMappingID ...
	ErrMissingProductVariantTransactionCatalogueMappingID = CustomError{
		Code:    3223,
		Message: "Missing product variant transaction catalogue mapping id field",
	}

	// ErrMissingIdentifierKey ...
	ErrMissingIdentifierKey = CustomError{
		Code:    3224,
		Message: "Missing identifier key field",
	}

	// ErrMissingBaseInterestID ...
	ErrMissingBaseInterestID = CustomError{
		Code:    3225,
		Message: "Missing base interest id field",
	}

	// ErrMissingBaseInterestVersionID ...
	ErrMissingBaseInterestVersionID = CustomError{
		Code:    3226,
		Message: "Missing base interest version id field",
	}

	// ErrMissingDepositInterestID ...
	ErrMissingDepositInterestID = CustomError{
		Code:    3227,
		Message: "Missing deposit interest id field",
	}

	// ErrMissingDepositInterestVersionID ...
	ErrMissingDepositInterestVersionID = CustomError{
		Code:    3228,
		Message: "Missing deposit interest version id field",
	}

	// ErrMissingLinkedBaseInterestID ...
	ErrMissingLinkedBaseInterestID = CustomError{
		Code:    3229,
		Message: "Base Interest ID is mandatory as IsLinkedToBaseRate is true",
	}

	// ErrInvalidFromAmount ...
	ErrInvalidFromAmount = CustomError{
		Code:    3230,
		Message: "Invalid from amount field",
	}

	// ErrInvalidToAmount ...
	ErrInvalidToAmount = CustomError{
		Code:    3231,
		Message: "Invalid to amount field",
	}

	// ErrInvalidEntityStatus ...
	ErrInvalidEntityStatus = CustomError{
		Code:    3232,
		Message: "Invalid status field",
	}

	// ErrInvalidParameterDatatype ...
	ErrInvalidParameterDatatype = CustomError{
		Code:    3233,
		Message: "Invalid parameter data type field",
	}

	// ErrInvalidParameterOverrideLevel ...
	ErrInvalidParameterOverrideLevel = CustomError{
		Code:    3234,
		Message: "Invalid parameter override level field",
	}

	// ErrInvalidTermUnit ...
	ErrInvalidTermUnit = CustomError{
		Code:    3235,
		Message: "Invalid term unit field",
	}

	// ErrInvalidTermValue ...
	ErrInvalidTermValue = CustomError{
		Code:    3236,
		Message: "Invalid term value field",
	}

	// ErrInvalidBaseInterestPercentage ...
	ErrInvalidBaseInterestPercentage = CustomError{
		Code:    3237,
		Message: "Invalid base interest percentage field",
	}

	// ErrInvalidCurrency ...
	ErrInvalidCurrency = CustomError{
		Code:    3238,
		Message: "Invalid currency field",
	}

	// ErrInvalidRoundOffType ...
	ErrInvalidRoundOffType = CustomError{
		Code:    3239,
		Message: "Invalid round off type field",
	}

	// ErrInvalidInterestSlabType ...
	ErrInvalidInterestSlabType = CustomError{
		Code:    3240,
		Message: "Invalid interest slab type field",
	}

	// ErrInvalidInterestSlabStructure ...
	ErrInvalidInterestSlabStructure = CustomError{
		Code:    3241,
		Message: "Invalid interest slab structure field",
	}

	// ErrInvalidBaseRateInterestSpreadPercentage ...
	ErrInvalidBaseRateInterestSpreadPercentage = CustomError{
		Code:    3242,
		Message: "Invalid base rate interest spread percentage field",
	}

	// ErrInvalidAbsoluteInterestRatePercentage ...
	ErrInvalidAbsoluteInterestRatePercentage = CustomError{
		Code:    3243,
		Message: "Invalid absolute interest rate percentage field",
	}

	// ErrDatabaseLoad ...
	ErrDatabaseLoad = CustomError{
		Code:    3244,
		Message: "Database load error",
	}

	// ErrRecordNotFound ...
	ErrRecordNotFound = CustomError{
		Code:    3245,
		Message: "Record not found",
	}

	// ErrDatabaseSave ...
	ErrDatabaseSave = CustomError{
		Code:    3246,
		Message: "Database save error",
	}

	// ErrDatabaseUpdate ...
	ErrDatabaseUpdate = CustomError{
		Code:    3247,
		Message: "Database update error",
	}

	// ErrFromAmountGreaterThanToAmount ...
	ErrFromAmountGreaterThanToAmount = CustomError{
		Code:    3248,
		Message: "FromAmount cannot be greater than ToAmount",
	}

	// ErrEffectiveDateLessThanCurrent ...
	ErrEffectiveDateLessThanCurrent = CustomError{
		Code:    3249,
		Message: "Effective date cannot be less than current date",
	}

	// ErrValidFromLessThanCurrent ...
	ErrValidFromLessThanCurrent = CustomError{
		Code:    3250,
		Message: "ValidFrom cannot be less than current date",
	}

	// ErrValidToLessThanCurrent ...
	ErrValidToLessThanCurrent = CustomError{
		Code:    3251,
		Message: "ValidTo cannot be less than current date",
	}

	// ErrValidFromGreaterThanValidTo ...
	ErrValidFromGreaterThanValidTo = CustomError{
		Code:    3252,
		Message: "ValidFrom cannot be greater than ValidTo",
	}

	// ErrAddUpdateForeignKeyConstraint ...
	ErrAddUpdateForeignKeyConstraint = CustomError{
		Code:    3253,
		Message: "Cannot add or update a child row: a foreign key constraint fails",
	}

	// ErrNoDepositInterestVersionForVersionID ...
	ErrNoDepositInterestVersionForVersionID = CustomError{
		Code:    3254,
		Message: "No deposit interest version for version id",
	}

	// ErrInvalidDepositInterestVersion ...
	ErrInvalidDepositInterestVersion = CustomError{
		Code:    3255,
		Message: "Invalid deposit interest version",
	}

	// ErrNoBaseInterestVersionForVersionID ...
	ErrNoBaseInterestVersionForVersionID = CustomError{
		Code:    3256,
		Message: "No base interest version for version id",
	}

	// ErrInvalidBaseInterestVersion ...
	ErrInvalidBaseInterestVersion = CustomError{
		Code:    3257,
		Message: "Invalid base interest version",
	}

	// ErrMissingImageURL ...
	ErrMissingImageURL = CustomError{
		Code:    3258,
		Message: "Missing image url",
	}

	// ErrInvalidPocketType ...
	ErrInvalidPocketType = CustomError{
		Code:    3259,
		Message: "Invalid pocket type field",
	}

	// ErrInvalidRequestParameter ...
	ErrInvalidRequestParameter = CustomError{
		Code:    3260,
		Message: "Invalid request parameters",
	}

	// ErrMissingIsNotifiedKey ...
	ErrMissingIsNotifiedKey = CustomError{
		Code:    3259,
		Message: "Missing IsNotified key field",
	}

	// ErrSmartContractVersionID ...
	ErrSmartContractVersionID = CustomError{
		Code:    3260,
		Message: "Missing smartContractVersionID key field",
	}

	// ErrMissingParameterVersionKey ...
	ErrMissingParameterVersionKey = CustomError{
		Code:    3261,
		Message: "Missing parameterVersion key field",
	}

	// ErrMissingIsScheduledKey ...
	ErrMissingIsScheduledKey = CustomError{
		Code:    3259,
		Message: "Missing IsScheduled key field",
	}

	// ErrMissingPocketTemplateID ...
	ErrMissingPocketTemplateID = CustomError{
		Code:    3260,
		Message: "Missing Pocket Template ID",
	}

	// ErrMissingLocale ...
	ErrMissingLocale = CustomError{
		Code:    3261,
		Message: "Missing Locale field",
	}

	// ErrMissingQuestionAnswerPairs ...
	ErrMissingQuestionAnswerPairs = CustomError{
		Code:    3262,
		Message: "Missing QuestionAnswerPairs field",
	}

	// ErrMissingQuestionTextOrAnswerSuggestions ...
	ErrMissingQuestionTextOrAnswerSuggestions = CustomError{
		Code:    3263,
		Message: "Missing QuestionText or AnswerSuggestions field",
	}

	// ErrMissingImageID ...
	ErrMissingImageID = CustomError{
		Code:    3264,
		Message: "Missing image id",
	}

	// ErrMissingProductVariantCode ...
	ErrMissingProductVariantCode = CustomError{
		Code:    3206,
		Message: "Missing productVariantCode field",
	}

	// ErrInvalidProductVariantCode ...
	ErrInvalidProductVariantCode = CustomError{
		Code:    3268,
		Message: "Invalid productVariantCode field",
	}

	// ErrMissingProductVariantVersion ...
	ErrMissingProductVariantVersion = CustomError{
		Code:    3266,
		Message: "Missing productVariantVersion field",
	}

	// ErrInvalidLocaleField ...
	ErrInvalidLocaleField = CustomError{
		Code:    3267,
		Message: "Invalid Locale field",
	}

	// ErrMissingQuestionCode ...
	ErrMissingQuestionCode = CustomError{
		Code:    3268,
		Message: "Missing question code field",
	}

	// ErrInvalidInterestType ...
	ErrInvalidInterestType = CustomError{
		Code:    3270,
		Message: "Invalid interest type field",
	}

	// ErrNoLoanInterestVersionForVersionID ...
	ErrNoLoanInterestVersionForVersionID = CustomError{
		Code:    3271,
		Message: "No loan interest version for version id",
	}

	// ErrInvalidLoanInterestVersion ...
	ErrInvalidLoanInterestVersion = CustomError{
		Code:    3272,
		Message: "Invalid loan interest version",
	}

	// ErrMissingProductCode ...
	ErrMissingProductCode = CustomError{
		Code:    3273,
		Message: "Missing productCode field",
	}

	// ErrInvalidProductCode ...
	ErrInvalidProductCode = CustomError{
		Code:    3274,
		Message: "Invalid productCode field",
	}

	// ErrNoLoanPastDueVersionForVersionID ...
	ErrNoLoanPastDueVersionForVersionID = CustomError{
		Code:    3275,
		Message: "No loan past due version for version id",
	}

	// ErrInvalidLoanPastDueVersion ...
	ErrInvalidLoanPastDueVersion = CustomError{
		Code:    3276,
		Message: "Invalid loan past due version",
	}

	// ErrInvalidInstructionVersionRequest ...
	ErrInvalidInstructionVersionRequest = CustomError{
		Code:    3277,
		Message: "Invalid loan instruction version request",
	}

	// ErrMissingInstructionType ...
	ErrMissingInstructionType = CustomError{
		Code:    3278,
		Message: "Missing loan instruction type field",
	}

	// ErrInvalidInstructionRequest ...
	ErrInvalidInstructionRequest = CustomError{
		Code:    3279,
		Message: "Invalid loan instruction request",
	}

	// ErrMissingLoanInstructionVersionID ...
	ErrMissingLoanInstructionVersionID = CustomError{
		Code:    3280,
		Message: "Missing loan instruction version id field",
	}

	// ErrInvalidInstructionType ...
	ErrInvalidInstructionType = CustomError{
		Code:    3281,
		Message: "Invalid loan instruction type field",
	}

	// ErrInvalidProductVariantCodeInstructionTypeMap ...
	ErrInvalidProductVariantCodeInstructionTypeMap = CustomError{
		Code:    3282,
		Message: "Invalid productVariantCode and instruction type mapping",
	}

	// ErrNoProductVariantVersionForVersionID ...
	ErrNoProductVariantVersionForVersionID = CustomError{
		Code:    3283,
		Message: "No product variant version for version id",
	}
)

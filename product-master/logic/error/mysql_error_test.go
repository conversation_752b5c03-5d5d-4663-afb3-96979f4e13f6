package error

import (
	"errors"
	"testing"

	"github.com/go-sql-driver/mysql"
)

func TestMySQLErrorCode(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected uint16
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: 0,
		},
		{
			name:     "non-MySQL error",
			err:      errors.New("some error"),
			expected: 0,
		},
		{
			name:     "MySQL error",
			err:      &mysql.MySQLError{Number: 1062, Message: "Duplicate entry"},
			expected: 1062,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MySQLErrorCode(tt.err); got != tt.expected {
				t.Errorf("MySQLErrorCode() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestCanRetry(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "ErrCannotConnect",
			err:      ErrCannotConnect,
			expected: true,
		},
		{
			name:     "ErrConnLost",
			err:      ErrConnLost,
			expected: true,
		},
		{
			name:     "ErrReadOnly",
			err:      ErrReadOnly,
			expected: true,
		},
		{
			name:     "ErrQueryKilled",
			err:      ErrQueryKilled,
			expected: true,
		},
		{
			name:     "ErrDupeKey",
			err:      ErrDupeKey,
			expected: false,
		},
		{
			name:     "non-package error",
			err:      errors.New("some error"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CanRetry(tt.err); got != tt.expected {
				t.Errorf("CanRetry() = %v, want %v", got, tt.expected)
			}
		})
	}
}

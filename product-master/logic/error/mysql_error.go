package error

import (
	"database/sql/driver"
	"errors"
	"net"

	"github.com/go-sql-driver/mysql"
)

var (
	// ErrCannotConnect is returned when a connection cannot be made because MySQL is unreachable for any reason.
	ErrCannotConnect = errors.New("cannot connect")

	// ErrConnLost is returned when the connection is lost, closed, or killed for any reason.
	ErrConnLost = errors.New("connection lost")

	// ErrQueryKilled is returned when the KILL QUERY command is used. This only kills the currently active query
	ErrQueryKilled = errors.New("query killed")

	// ErrReadOnly is returned when MySQL read-only is enabled.
	ErrReadOnly = errors.New("server is read-only")

	// ErrDupeKey is returned when a unique index prevents a value from being inserted or updated.
	ErrDupeKey = errors.New("duplicate key value")
)

// Error returns an error in this package if possible. The boolean return
// value is true if the given error is any MySQL error.
func Error(err error) (bool, error) {
	if err == nil {
		return false, nil
	}

	if Down(err) {
		return true, ErrCannotConnect
	}

	if Lost(err) {
		return true, ErrConnLost
	}

	errCode := MySQLErrorCode(err)
	if errCode == 0 {
		return false, err // not a MySQL error
	}
	switch errCode {
	case 1317: // ER_QUERY_INTERRUPTED
		return true, ErrQueryKilled
	case 1290, 1836: // ER_OPTION_PREVENTS_STATEMENT, ER_READ_ONLY_MODE
		return true, ErrReadOnly
	case 1062: // ER_DUP_ENTRY
		return true, ErrDupeKey
	}

	// A MySQL error, but not one we handle explicitly
	return true, err
}

// Down returns true if the error indicates MySQL cannot be reached for any reason.
func Down(err error) bool {
	// Being unable to reach MySQL is a network issue, so we get a net.OpError.
	// If MySQL is reachable, then we'd get a mysql.* or driver.* error instead.
	_, ok := err.(*net.OpError)
	return ok
}

// Lost returns true if the error indicates the MySQL connection was lost.
func Lost(err error) bool {
	if err == mysql.ErrInvalidConn || err == driver.ErrBadConn {
		return true
	}

	// Server shutdown in progress is a special case: the conn will be lost soon.
	if errCode := MySQLErrorCode(err); errCode == 1053 { // ER_SERVER_SHUTDOWN
		return true
	}

	return false
}

// MySQLErrorCode returns the MySQL server error code for the error, or zero if the error is not a MySQL error.
func MySQLErrorCode(err error) uint16 {
	if val, ok := err.(*mysql.MySQLError); ok {
		return val.Number
	}
	return 0 // not a mysql error
}

// CanRetry returns true for every error in this package except ErrDupeKey. It returns false for all other errors, including nil.
func CanRetry(err error) bool {
	switch err {
	case ErrCannotConnect, ErrConnLost, ErrReadOnly, ErrQueryKilled:
		return true
	}
	return false
}

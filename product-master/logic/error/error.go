// Package error ..
package error

import (
	"gitlab.myteksi.net/dakota/servus/v2"
)

// BuildErrorResponse create common error message
func BuildErrorResponse(httpStatus int, code string, msg string) servus.ServiceError {
	return servus.ServiceError{
		HTTPCode: httpStatus,
		Code:     code,
		Message:  msg,
	}
}

// BuildErrorResponseWithErrorDetail create error response with erro detail
func BuildErrorResponseWithErrorDetail(httpStatus int, code string, msg string, errs []servus.ErrorDetail) servus.ServiceError {
	return servus.ServiceError{
		HTTPCode: httpStatus,
		Code:     code,
		Message:  msg,
		Errors:   errs,
	}
}

package pocket

import (
	"context"
	"fmt"
	"net/http"
	"sort"
	"strconv"

	"gitlab.myteksi.net/dbmy/common/tenants"

	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/external"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/google/uuid"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
)

// CreatePocketTemplate ...
func CreatePocketTemplate(ctx context.Context, hermesClient hermes.Hermes, req *api.CreatePocketTemplateRequest, store *storage.DBStore) (*api.CreatePocketTemplateResponse, error) {
	pocketTemplateID := uuid.New().String()
	if pocketTempSaveErr := store.PocketTemplateDAO.Save(ctx, &storage.PocketTemplate{
		PublicID:  pocketTemplateID,
		Type:      string(req.Type),
		Name:      req.Name,
		Status:    string(api.EntityStatus_ACTIVE),
		CreatedBy: req.CreatedBy,
		UpdatedBy: req.CreatedBy,
	}); pocketTempSaveErr != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save pocket-template to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), pocketTempSaveErr)
	}

	pocketTemplate, err := store.PocketTemplateDAO.Find(ctx, data.EqualTo("PublicID", pocketTemplateID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load pocket-template from database with id: %s", pocketTemplateID), err)
	}

	var pocketTemplateImageSuggestionDBObj []*storage.PocketTemplateImageSuggestion
	for _, imageID := range req.ImageIDs {
		pocketTemplateImageSuggestionDBObj = append(pocketTemplateImageSuggestionDBObj, &storage.PocketTemplateImageSuggestion{
			PublicID:         uuid.New().String(),
			PocketTemplateID: pocketTemplate[0].ID,
			ImageID:          imageID,
			Status:           string(api.EntityStatus_ACTIVE),
			CreatedBy:        req.CreatedBy,
			UpdatedBy:        req.CreatedBy,
		})
	}
	if imageSuggSaveErr := store.PocketTemplateImageSuggestionDAO.SaveBatch(ctx, pocketTemplateImageSuggestionDBObj); imageSuggSaveErr != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save pocket-template-image-suggestions to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), imageSuggSaveErr)
	}

	imageList, err := external.ListImageDetailsFromHermes(ctx, constants.CreatePocketTemplateLogTag, hermesClient, req.ImageIDs)
	if err != nil {
		return nil, err
	}

	slog.FromContext(ctx).Info(constants.CreatePocketTemplateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreatePocketTemplateResponse{
		PocketTemplate: mapPocketTemplateFromStorage(*pocketTemplate[0], imageList, imageList[0]),
	}, nil
}

// ListPocketTemplates ...
func ListPocketTemplates(ctx context.Context, hermesClient hermes.Hermes, req *api.ListPocketTemplatesRequest, store *storage.DBStore) (*api.ListPocketTemplatesResponse, error) {
	pocketTemplates, err := store.PocketTemplateDAO.Find(ctx, data.EqualTo("Type", req.Type), data.EqualTo("Status", string(api.EntityStatus_ACTIVE)))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load pocket-templates from database for request with type: %s", req.Type), err)
	}

	if tenants.FromContext(ctx) == tenants.TenantMY {
		sort.Slice(pocketTemplates, func(i, j int) bool {
			return constants.PocketTemplateSortingConfig[pocketTemplates[i].Name] < constants.PocketTemplateSortingConfig[pocketTemplates[j].Name]
		})
	}

	var pocketTemplateIDs []uint64
	for _, pocketTemplate := range pocketTemplates {
		pocketTemplateIDs = append(pocketTemplateIDs, pocketTemplate.ID)
	}

	imageSuggestions, err := store.PocketTemplateImageSuggestionDAO.Find(
		ctx, data.ContainedIn("PocketTemplateID", apiCommon.ConvertInt64ToInterface(pocketTemplateIDs)...),
		data.EqualTo("Status", string(api.EntityStatus_ACTIVE)),
	)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx, "unable to load pocket-template-image-suggestions from database", err)
	}

	var imageIDs []string
	pocketTemplateToImageMap := make(map[uint64]string)
	for _, imageSuggestion := range imageSuggestions {
		if _, ok := pocketTemplateToImageMap[imageSuggestion.PocketTemplateID]; !ok {
			pocketTemplateToImageMap[imageSuggestion.PocketTemplateID] = imageSuggestion.ImageID
			imageIDs = append(imageIDs, imageSuggestion.ImageID)
		}
	}
	resp, err := hermesClient.GetDocuments(ctx, &hermes.GetDocumentsRequest{
		DocumentIDs: imageIDs,
	})
	if err != nil {
		return nil, err
	}
	if len(resp.Documents) == 0 {
		slog.FromContext(ctx).Warn(constants.ListPocketTemplatesLogTag, fmt.Sprintf("no record available in hermes with imageIDs: %s", utils.ToJSON(imageIDs)), utils.GetTraceID(ctx))
		return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message)
	}

	imageMapping := make(map[string]api.Image)
	for _, r := range resp.Documents {
		imageMapping[r.Id] = api.Image{
			ID:  r.Id,
			URL: r.PresignedURL,
		}
	}

	slog.FromContext(ctx).Info(constants.ListPocketTemplatesLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.ListPocketTemplatesResponse{
		PocketTemplates: mapPocketTemplatesFromStorage(pocketTemplates, pocketTemplateToImageMap, imageMapping),
	}, nil
}

// GetProductTemplate ...
func GetProductTemplate(ctx context.Context, hermesClient hermes.Hermes, req *api.GetPocketTemplateRequest, store *storage.DBStore) (*api.GetPocketTemplateResponse, error) {
	var (
		imageSuggestions []*storage.PocketTemplateImageSuggestion
		imageList        []api.Image
		defaultImg       api.Image
		imageIDs         []string
	)

	pocketTemplates, err := store.PocketTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load pocket-template from database for request with id: %s", req.Id), err)
	}

	// for dbmy, only display image suggestion if the pocket template is ACTIVE
	if (tenants.FromContext(ctx) == tenants.TenantMY && pocketTemplates[0].Status == constants.ACTIVE) || tenants.FromContext(ctx) != tenants.TenantMY {
		imageSuggestions, err = store.PocketTemplateImageSuggestionDAO.Find(ctx,
			data.EqualTo("PocketTemplateID", pocketTemplates[0].ID),
			data.EqualTo("Status", constants.ACTIVE))
		if err != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load pocket-template-image-suggestions from database for request with id: %s", req.Id), err)
		}
	}

	for _, imageSuggestion := range imageSuggestions {
		imageIDs = append(imageIDs, imageSuggestion.ImageID)
	}

	if len(imageIDs) != 0 {
		imageList, err = external.ListImageDetailsFromHermes(ctx, constants.GetPocketTemplateLogTag, hermesClient, imageIDs)
		if err != nil {
			return nil, err
		}
	}
	// set first img as default img if imgList > 0
	if len(imageList) != 0 {
		defaultImg = imageList[0]
	}

	slog.FromContext(ctx).Info(constants.GetPocketTemplateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetPocketTemplateResponse{
		PocketTemplate: mapPocketTemplateFromStorage(*pocketTemplates[0], imageList, defaultImg),
	}, nil
}

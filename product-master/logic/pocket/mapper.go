package pocket

import (
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

func mapPocketTemplateFromStorage(pocketTemplate storage.PocketTemplate, imageList []api.Image, defaultImage api.Image) *api.PocketTemplate {
	return &api.PocketTemplate{
		ID:           pocketTemplate.PublicID,
		Type:         api.PocketType(pocketTemplate.Type),
		Name:         pocketTemplate.Name,
		Images:       imageList,
		DefaultImage: &defaultImage,
	}
}

func mapPocketTemplatesFromStorage(pocketTemplates []*storage.PocketTemplate, pocketTemplateToImageMap map[uint64]string, imageMapping map[string]api.Image) []api.PocketTemplate {
	var mappedPocketTemplates []api.PocketTemplate
	for _, pocketTemplate := range pocketTemplates {
		mappedPocketTemplates = append(mappedPocketTemplates, *mapPocketTemplateFromStorage(*pocketTemplate, []api.Image{imageMapping[pocketTemplateToImageMap[pocketTemplate.ID]]},
			imageMapping[pocketTemplateToImageMap[pocketTemplate.ID]]))
	}
	return mappedPocketTemplates
}

func mapPocketTemplateQuestionFromStorage(pocketTemplateID string, pocketTemplateQuestions []*storage.PocketTemplateQuestion,
	questionAnswerPair []api.QuestionAnswerPairsResponse, imageList []api.Image, defaultImage *api.Image) *api.PocketTemplateQuestionsDetail {
	return &api.PocketTemplateQuestionsDetail{
		PocketTemplateID:    pocketTemplateID,
		QuestionAnswerPairs: questionAnswerPair,
		Locale:              api.Locale(pocketTemplateQuestions[0].Locale),
		Images:              imageList,
		DefaultImage:        defaultImage,
	}
}

package pocket

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/localisation"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/external"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/samber/lo"
	"gitlab.myteksi.net/dbmy/common/tenants"

	"github.com/google/uuid"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
)

// CreatePocketTemplateQuestion ...
// nolint: funlen
func CreatePocketTemplateQuestion(ctx context.Context, req *api.CreatePocketTemplateQuestionsRequest, store *storage.DBStore) (*api.CreatePocketTemplateQuestionsResponse, error) {
	pocketTemplate, err := store.PocketTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.PocketTemplateID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load pocketTemplate from database with id: %s", req.PocketTemplateID), err)
	}
	questionIDAnswersMap := make(map[string][]string)
	var questionList, loadedQuestions []*storage.PocketTemplateQuestion
	for _, questionAnswerPair := range req.QuestionAnswerPairs {
		pocketTemplateQuestionID := uuid.New().String()
		questionIDAnswersMap[pocketTemplateQuestionID] = questionAnswerPair.AnswerSuggestions
		questionList = append(questionList, &storage.PocketTemplateQuestion{
			PublicID:         pocketTemplateQuestionID,
			PocketTemplateID: pocketTemplate[0].ID,
			QuestionText:     questionAnswerPair.QuestionText,
			Locale:           string(req.Locale),
			Status:           constants.ACTIVE,
			CreatedBy:        req.CreatedBy,
			UpdatedBy:        req.CreatedBy,
		})
	}
	if pocketTempQuesSaveErr := store.PocketTemplateQuestionDAO.SaveBatch(ctx, questionList); pocketTempQuesSaveErr != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save pocket-template-question to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	questionIDMap := make(map[string]uint64)
	for _, question := range questionList {
		pocketTemplateQuestions, loadErr := store.PocketTemplateQuestionDAO.Find(ctx, data.EqualTo("PublicID", question.PublicID))
		if loadErr != nil {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load the PocketTemplateQuestions of the pocket with id %v from the database", req.PocketTemplateID), loadErr)
		}
		questionIDMap[pocketTemplateQuestions[0].PublicID] = pocketTemplateQuestions[0].ID
		loadedQuestions = append(loadedQuestions, pocketTemplateQuestions[0])
	}

	var answerSuggestionList []*storage.PocketTemplateAnswerSuggestion
	for quesID, answers := range questionIDAnswersMap {
		answerSuggestionID := uuid.New().String()
		for _, answer := range answers {
			answerSuggestionList = append(answerSuggestionList, &storage.PocketTemplateAnswerSuggestion{
				PublicID:                 answerSuggestionID,
				PocketTemplateQuestionID: questionIDMap[quesID],
				AnswerSuggestionText:     answer,
				Locale:                   string(req.Locale),
				Status:                   constants.ACTIVE,
				CreatedBy:                req.CreatedBy,
				UpdatedBy:                req.CreatedBy,
			})
		}
	}

	if pocketTempAnsSuggestionSaveErr := store.PocketTemplateAnswerSuggestionDAO.SaveBatch(ctx, answerSuggestionList); pocketTempAnsSuggestionSaveErr != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save pocket-template-answer-suggestion to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	questionAnswerPairs, quesAnsPairErr := buildQuestionAnswerPairs(ctx, loadedQuestions, store)
	// the suggested answer might be left null by intended
	if quesAnsPairErr != nil && quesAnsPairErr != data.ErrNoData {
		return nil, quesAnsPairErr
	}

	slog.FromContext(ctx).Info(constants.CreatePocketTemplateQuestionLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreatePocketTemplateQuestionsResponse{
		PocketTemplateQuestionsDetail: mapPocketTemplateQuestionFromStorage(req.PocketTemplateID, loadedQuestions, questionAnswerPairs, nil, nil),
	}, nil
}

// ListPocketTemplateQuestions ...
func ListPocketTemplateQuestions(ctx context.Context, hermesClient hermes.Hermes, req *api.ListPocketTemplateQuestionsRequest, store *storage.DBStore) (*api.ListPocketTemplateQuestionsResponse, error) {
	pocketTemplates, err := store.PocketTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.PocketTemplateID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load pocket-template from database with id: %s", req.PocketTemplateID), err)
	}
	locale := localisation.GetLocaleFromCtx(ctx).String()
	defaultLocale := localisation.DefaultLocale().String()
	// We fetch both default and requested locale copy in 1 RTT. In case no result for requested locale, we fallback to default locale
	pocketTemplateQuestions, err := store.PocketTemplateQuestionDAO.Find(ctx,
		data.EqualTo("PocketTemplateID", pocketTemplates[0].ID), data.EqualTo("Status", string(api.EntityStatus_ACTIVE)), data.ContainedIn("Locale", locale, defaultLocale))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load pocket-template-questions from database with pocket template id: %s ", req.PocketTemplateID), err)
	}

	groupedQuestions := lo.GroupBy(pocketTemplateQuestions, func(item *storage.PocketTemplateQuestion) string {
		return item.Locale
	})
	pocketTemplateQuestions = lo.Ternary(lo.HasKey(groupedQuestions, locale), groupedQuestions[locale], groupedQuestions[defaultLocale])
	slices.SortStableFunc(pocketTemplateQuestions, func(prev, next *storage.PocketTemplateQuestion) int {
		if prev.ID == next.ID {
			return 0
		}
		return lo.Ternary(prev.ID < next.ID, -1, 1)
	})

	questionAnswerPairs, err := buildQuestionAnswerPairs(ctx, pocketTemplateQuestions, store)
	if err != nil {
		return nil, err
	}

	imageSuggestions, err := store.PocketTemplateImageSuggestionDAO.Find(ctx,
		data.EqualTo("PocketTemplateID", pocketTemplates[0].ID), data.EqualTo("Status", string(api.EntityStatus_ACTIVE)))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			"unable to load pocket-template-image-suggestions from database for request", err)
	}

	imageIDs := lo.Map(imageSuggestions, func(s *storage.PocketTemplateImageSuggestion, _ int) string {
		return s.ImageID
	})

	imageList, err := external.ListImageDetailsFromHermes(ctx, constants.ListPocketTemplateQuestionsLogTag, hermesClient, imageIDs)
	if err != nil {
		return nil, err
	}
	if tenants.FromContext(ctx) == tenants.TenantMY {
		slices.SortStableFunc(imageList, func(prev, next api.Image) int {
			return strings.Compare(prev.URL, next.URL)
		})
	}

	slog.FromContext(ctx).Info(constants.ListPocketTemplateQuestionsLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.ListPocketTemplateQuestionsResponse{
		PocketTemplateQuestionsDetail: mapPocketTemplateQuestionFromStorage(req.PocketTemplateID, pocketTemplateQuestions, questionAnswerPairs, imageList, &imageList[0]),
	}, nil
}

func buildQuestionAnswerPairs(ctx context.Context, questionList []*storage.PocketTemplateQuestion, store *storage.DBStore) ([]api.QuestionAnswerPairsResponse, error) {
	questionAnswerPairs := make([]api.QuestionAnswerPairsResponse, len(questionList))
	for i, question := range questionList {
		answerSuggestions, err := store.PocketTemplateAnswerSuggestionDAO.Find(ctx, data.EqualTo("PocketTemplateQuestionID", question.ID))

		// the suggested answer might be left null by intended
		if err != nil && !errors.Is(err, data.ErrNoData) {
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load the answersuggestions of the PocketTemplateQuestions with id %v from the database", question.PublicID), err)
		}
		answerSet := lo.Map(answerSuggestions, func(s *storage.PocketTemplateAnswerSuggestion, _ int) string {
			return s.AnswerSuggestionText
		})
		questionAnswerPairs[i] = api.QuestionAnswerPairsResponse{
			ID:                question.PublicID,
			QuestionText:      question.QuestionText,
			AnswerSuggestions: answerSet,
		}
	}
	return questionAnswerPairs, nil
}

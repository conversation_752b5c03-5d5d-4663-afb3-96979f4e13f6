// Package validations ..
package validations

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// ValidateFieldNotEmpty ...
func ValidateFieldNotEmpty(ctx context.Context, fieldValue string, fieldName string, returnError apiErr.CustomError) error {
	if fieldValue == "" {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, fmt.Sprintf("%s field is empty", fieldName), common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(returnError.Code), 10), returnError.Message)
	}
	return nil
}

// ValidateListNotEmpty  ...
func ValidateListNotEmpty(ctx context.Context, fieldValue []string, fieldName string, returnError apiErr.CustomError) error {
	if len(fieldValue) == 0 {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, fmt.Sprintf("Missing list of %s", fieldName), common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(returnError.Code), 10), returnError.Message)
	}
	for _, value := range fieldValue {
		if err := ValidateFieldNotEmpty(ctx, value, fieldName, returnError); err != nil {
			return err
		}
	}
	return nil
}

// ValidateFieldNonNegative ...
func ValidateFieldNonNegative(ctx context.Context, fieldValue int, fieldName string, returnError apiErr.CustomError) error {
	if fieldValue <= 0 {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, fmt.Sprintf("%s field is zero or negative", fieldName), common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(returnError.Code), 10), returnError.Message)
	}
	return nil
}

// ValidatePercentage ...
func ValidatePercentage(ctx context.Context, fieldValue string, fieldName string, returnError apiErr.CustomError) error {
	floatVal, err := strconv.ParseFloat(fieldValue, 32)
	if err != nil || floatVal < 0 || floatVal > 100 {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, fmt.Sprintf("%s field is not a valid percentage", fieldName), common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(returnError.Code), 10), returnError.Message)
	}
	return nil
}

// ValidateAmount ...
func ValidateAmount(ctx context.Context, fieldValue string, fieldName string, returnError apiErr.CustomError) error {
	_, err := strconv.ParseFloat(fieldValue, 32)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, fmt.Sprintf("%s field is not a valid amount", fieldName), common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(returnError.Code), 10), returnError.Message)
	}
	return nil
}

// ValidateDateFieldWithCurrentDate ...
func ValidateDateFieldWithCurrentDate(ctx context.Context, fieldValue time.Time, fieldName string, returnError apiErr.CustomError) error {
	if !fieldValue.IsZero() && fieldValue.Add(constants.DeltaTimeInSec*time.Second).Before(time.Now().UTC()) {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, fmt.Sprintf("%s field cannot be less than current date", fieldName), common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(returnError.Code), 10), returnError.Message)
	}
	return nil
}

// ValidateEntityStatus ...
func ValidateEntityStatus(ctx context.Context, status string) error {
	switch status {
	case string(api.EntityStatus_ACTIVE):
	case string(api.EntityStatus_INACTIVE):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid entity status", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidEntityStatus.Code), 10), apiErr.ErrInvalidEntityStatus.Message)
	}
	return nil
}

// ValidateParameterDataType ...
func ValidateParameterDataType(ctx context.Context, dataType string) error {
	switch dataType {
	case string(api.ParameterDataType_INT):
	case string(api.ParameterDataType_BOOL):
	case string(api.ParameterDataType_FLOAT):
	case string(api.ParameterDataType_ARRAY):
	case string(api.ParameterDataType_JSON):
	case string(api.ParameterDataType_STRING):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid parameter data type", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidParameterDatatype.Code), 10), apiErr.ErrInvalidParameterDatatype.Message)
	}
	return nil
}

// ValidateParameterOverrideLevel ...
func ValidateParameterOverrideLevel(ctx context.Context, overrideLevel string) error {
	switch overrideLevel {
	case string(api.ParameterOverrideLevel_NO_OVERRIDE):
	case string(api.ParameterOverrideLevel_AT_PRODUCT_VARIANT):
	case string(api.ParameterOverrideLevel_AT_ACCOUNT):
	case string(api.ParameterOverrideLevel_AT_CUSTOMER):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid parameter override level", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidParameterOverrideLevel.Code), 10), apiErr.ErrInvalidParameterOverrideLevel.Message)
	}
	return nil
}

// ValidateCurrency ...
func ValidateCurrency(ctx context.Context, currency string) error {
	switch currency {
	case string(api.Currency_SGD), string(api.Currency_IDR):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid currency", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidCurrency.Code), 10), apiErr.ErrInvalidCurrency.Message)
	}
}

// ValidateRoundOffType ...
func ValidateRoundOffType(ctx context.Context, roundOffType string) error {
	switch roundOffType {
	case string(api.RoundOffType_FLOOR):
	case string(api.RoundOffType_CEIL):
	case string(api.RoundOffType_ROUND):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid round off type", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidRoundOffType.Code), 10),
			apiErr.ErrInvalidRoundOffType.Message)
	}
	return nil
}

// ValidateInterestSlabType ...
func ValidateInterestSlabType(ctx context.Context, interestSlabType string) error {
	switch interestSlabType {
	case string(api.InterestSlabType_AMOUNT):
	case string(api.InterestSlabType_TIME):
	case string(api.InterestSlabType_BOTH):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid interest slab type", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabType.Code), 10),
			apiErr.ErrInvalidInterestSlabType.Message)
	}
	return nil
}

// ValidateInterestSlabStructure ...
func ValidateInterestSlabStructure(ctx context.Context, interestSlabStructure string) error {
	switch interestSlabStructure {
	case string(api.InterestSlabStructure_ABSOLUTE):
	case string(api.InterestSlabStructure_INCREMENTAL):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid interest slab structure", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidInterestSlabStructure.Code), 10),
			apiErr.ErrInvalidInterestSlabStructure.Message)
	}
	return nil
}

// ValidateTermUnit ...
func ValidateTermUnit(ctx context.Context, termUnit string) error {
	switch termUnit {
	case string(api.TermUnit_DAY):
	case string(api.TermUnit_WEEK):
	case string(api.TermUnit_MONTH):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid term-unit", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidTermUnit.Code), 10), apiErr.ErrInvalidTermUnit.Message)
	}
	return nil
}

// ValidateIntegerVersion ...
func ValidateIntegerVersion(ctx context.Context, version string, returnError apiErr.CustomError) error {
	_, err := strconv.Atoi(version)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid integer version", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(returnError.Code), 10),
			returnError.Message)
	}
	return nil
}

// ValidateGetInterestParameters ...
func ValidateGetInterestParameters(ctx context.Context, isScheduled, isNotified api.Status) error {
	if isScheduled == "" && isNotified == "" {
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag, "At least one of isScheduled, IsNotified should be non empty", utils.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest,
			strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10),
			apiErr.ErrInvalidRequestParameter.Message)
	}
	if err := ValidateIsNotifiedValue(ctx, isNotified); err != nil {
		return err
	}
	if err := ValidateIsScheduledValue(ctx, isScheduled); err != nil {
		return err
	}

	return nil
}

// ValidateIsNotifiedValue ...
func ValidateIsNotifiedValue(ctx context.Context, isNotified api.Status) error {
	if isNotified != "" && !(isNotified == api.Status_N || isNotified == api.Status_Y) {
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag, "Invalid isNotified Value", utils.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest,
			strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10),
			apiErr.ErrInvalidRequestParameter.Message)
	}
	return nil
}

// ValidateIsScheduledValue ...
func ValidateIsScheduledValue(ctx context.Context, isScheduled api.Status) error {
	if isScheduled != "" && !(isScheduled == api.Status_N || isScheduled == api.Status_Y) {
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag, "Invalid isScheduled Value", utils.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest,
			strconv.FormatInt(int64(apiErr.ErrInvalidRequestParameter.Code), 10),
			apiErr.ErrInvalidRequestParameter.Message)
	}
	return nil
}

// ValidatePocketType ...
func ValidatePocketType(ctx context.Context, pocketType string) error {
	switch pocketType {
	case string(api.PocketType_SAVINGS),
		string(api.PocketType_SAVINGS_POCKET),
		string(api.PocketType_BOOST_POCKET):
		return nil
	default:
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag, "Invalid pocket type", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidPocketType.Code), 10), apiErr.ErrInvalidPocketType.Message)
	}
}

// ValidateProductVariantCode ...
func ValidateProductVariantCode(ctx context.Context, productVariantCode string) error {
	switch productVariantCode {
	case string(api.ProductVariantCode_DEPOSITS_ACCOUNT):
	case string(api.ProductVariantCode_SAVINGS_POCKET):
	case string(api.ProductVariantCode_BOOST_POCKET):
	case string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_TERM_LOAN):
	case string(api.ProductVariantCode_BIZ_DEPOSIT_ACCOUNT):
	case string(api.ProductVariantCode_DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid product variant code", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidProductVariantCode.Code), 10), apiErr.ErrInvalidProductVariantCode.Message)
	}
	return nil
}

// ValidateInterestType ...
func ValidateInterestType(ctx context.Context, interestType string) error {
	switch interestType {
	case string(api.InterestType_NORMAL):
	case string(api.InterestType_PENAL):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid interest type", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidInterestType.Code), 10),
			apiErr.ErrInvalidInterestType.Message)
	}
	return nil
}

// ValidateProductCode ...
func ValidateProductCode(ctx context.Context, productCode string) error {
	switch productCode {
	case "":
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Missing product code", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingProductCode.Code), 10),
			apiErr.ErrMissingProductCode.Message)
	case string(api.ProductCode_FLEXI_LOAN_TERM_LOAN), string(api.ProductCode_BIZ_FLEXI_CREDIT_TERM_LOAN):
		return nil
	}

	slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid product code", common.GetTraceID(ctx))
	return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidProductCode.Code), 10),
		apiErr.ErrInvalidProductCode.Message)
}

// ValidateLocale : validates the locale parameter and it's possible values
func ValidateLocale(ctx context.Context, locale string) error {
	switch locale {
	case "":
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag, "missing locale field", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingLocale.Code), 10), apiErr.ErrMissingLocale.Message)
	case string(api.Locale_EN), string(api.Locale_MS):
		return nil
	default:
		slog.FromContext(ctx).Error(constants.RequestValidationFailureTag, "Invalid locale field", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidLocaleField.Code), 10), apiErr.ErrInvalidLocaleField.Message)
	}
}

// ValidateGetDocumentTypesRequest validates get document types request api
// validates product variant code to be DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT
func ValidateGetDocumentTypesRequest(ctx context.Context, req *api.ListLoanDocumentOptionsByProductVariantRequest) error {
	switch req.ProductVariantCode {
	case string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT):
		return nil
	default:
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Invalid product variant code", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, utils.SafeIntToString(apiErr.ErrInvalidProductVariantCode.Code), apiErr.ErrInvalidProductVariantCode.Message)
	}
}

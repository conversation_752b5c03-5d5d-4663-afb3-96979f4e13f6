package external

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
)

// ListImageDetailsFromHermes function to fetch image details from hermes service
func ListImageDetailsFromHermes(ctx context.Context, tag string, hermesClient hermes.Hermes, imageIDs []string) ([]api.Image, error) {
	resp, err := hermesClient.GetDocuments(ctx, &hermes.GetDocumentsRequest{
		DocumentIDs: imageIDs,
	})
	if err != nil {
		return nil, err
	}
	if len(resp.Documents) == 0 {
		slog.FromContext(ctx).Warn(tag, fmt.Sprintf("no record available in hermes with imageIDs: %s", utils.ToJSON(imageIDs)), utils.GetTraceID(ctx))
		return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message)
	}

	var imageList []api.Image
	for _, r := range resp.Documents {
		imageList = append(imageList, api.Image{
			ID:  r.Id,
			URL: r.PresignedURL,
		})
	}
	return imageList, nil
}

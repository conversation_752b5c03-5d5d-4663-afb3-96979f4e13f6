package productconfig

import (
	"context"
	"database/sql"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"

	"github.com/google/uuid"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateProductTemplate ...
func CreateProductTemplate(ctx context.Context, req *api.CreateProductTemplateRequest, store *storage.DBStore) (*api.CreateProductTemplateResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	templateID := uuid.New().String()
	if err := store.ProductTemplateDAO.Save(ctx, &storage.ProductTemplate{
		PublicID:    templateID,
		Code:        req.Code,
		Name:        req.Name,
		Description: description,
		Status:      string(api.EntityStatus_ACTIVE),
		CreatedBy:   req.CreatedBy,
		UpdatedBy:   req.CreatedBy,
	}); err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductTemplateLogTag, fmt.Sprintf("Error in saving to the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product-template to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	productTemplate, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("PublicID", templateID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductTemplateLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database with id: %s", templateID), err)
	}
	slog.FromContext(ctx).Info(constants.CreateProductTemplateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateProductTemplateResponse{
		ProductTemplate: mapProductTemplateFromStorage(*productTemplate[0]),
	}, nil
}

// GetProductTemplate ...
func GetProductTemplate(ctx context.Context, req *api.GetProductTemplateRequest, store *storage.DBStore) (*api.GetProductTemplateResponse, error) {
	productTemplate, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductTemplateLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %s", req.Id), err)
	}
	slog.FromContext(ctx).Info(constants.GetProductTemplateLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductTemplateResponse{
		ProductTemplate: mapProductTemplateFromStorage(*productTemplate[0]),
	}, nil
}

// UpdateProductTemplateStatus ...
func UpdateProductTemplateStatus(ctx context.Context, req *api.UpdateProductTemplateStatusRequest, store *storage.DBStore) (*api.UpdateProductTemplateStatusResponse, error) {
	productTemplate, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductTemplateStatusLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.ProductTemplateDAO.Update(ctx, &storage.ProductTemplate{
		ID:          productTemplate[0].ID,
		PublicID:    productTemplate[0].PublicID,
		Name:        productTemplate[0].Name,
		Description: productTemplate[0].Description,
		Status:      string(req.Status),
		CreatedBy:   productTemplate[0].CreatedBy,
		CreatedAt:   productTemplate[0].CreatedAt,
		UpdatedBy:   req.UpdatedBy,
	}); updateErr != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductTemplateStatusLogTag, fmt.Sprintf("Error in updating the ProductTemplate DB, err: %s", updateErr), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update product-template in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	updatedProductTemplate, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductTemplateStatusLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %s", req.Id), err)
	}
	slog.FromContext(ctx).Info(constants.UpdateProductTemplateStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateProductTemplateStatusResponse{
		ProductTemplate: mapProductTemplateFromStorage(*updatedProductTemplate[0]),
	}, nil
}

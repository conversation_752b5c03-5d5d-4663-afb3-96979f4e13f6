package productconfig

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateProductVariant ...
func CreateProductVariant(ctx context.Context, req *api.CreateProductVariantRequest, store *storage.DBStore) (*api.CreateProductVariantResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	if req.ValidFrom.IsZero() {
		req.ValidFrom = time.Now().UTC()
	}
	if req.ValidTo.IsZero() {
		req.ValidTo = time.Now().UTC().Add(constants.HundredYears)
	}

	product, err := store.ProductDAO.Find(ctx, data.EqualTo("PublicID", req.ProductID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductVariantLogTag, fmt.Sprintf("Error in finding from the Product DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with id: %s", req.ProductID), err)
	}

	variantID := uuid.New().String()
	if err = store.ProductVariantDAO.Save(ctx, &storage.ProductVariant{
		PublicID:    variantID,
		Name:        req.Name,
		Code:        req.Code,
		Version:     req.Version,
		ProductID:   product[0].ID,
		Description: description,
		Status:      string(api.EntityStatus_ACTIVE),
		ValidFrom:   req.ValidFrom,
		ValidTo:     req.ValidTo,
		CreatedBy:   req.CreatedBy,
		UpdatedBy:   req.CreatedBy,
	}); err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductVariantLogTag, fmt.Sprintf("Error in saving to the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product-variant to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", variantID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductVariantLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database after saving for request with id: %s", variantID), err)
	}
	slog.FromContext(ctx).Info(constants.CreateProductVariantLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateProductVariantResponse{
		ProductVariant: mapProductVariantFromStorage(*productVariant[0], req.ProductID),
	}, nil
}

// GetProductVariant ...
func GetProductVariant(ctx context.Context, req *api.GetProductVariantRequest, store *storage.DBStore) (*api.GetProductVariantResponse, error) {
	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductVariantLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.Id), err)
	}

	product, err := store.ProductDAO.LoadByID(ctx, productVariant[0].ID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductVariantLogTag, fmt.Sprintf("Error in loading from the Product DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with id: %s", req.Id), err)
	}
	slog.FromContext(ctx).Info(constants.GetProductVariantLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductVariantResponse{
		ProductVariant: mapProductVariantFromStorage(*productVariant[0], product.PublicID),
	}, nil
}

// GetProductVariantByCode ...
func GetProductVariantByCode(ctx context.Context, req *api.GetProductVariantByCodeRequest, store *storage.DBStore) (*api.GetProductVariantByCodeResponse, error) {
	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", req.Code))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductVariantByCodeLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with code: %s", req.Code), err)
	}

	product, err := store.ProductDAO.LoadByID(ctx, productVariant[0].ProductID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductVariantByCodeLogTag, fmt.Sprintf("Error in loading from the Product DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with id: %d", productVariant[0].ProductID), err)
	}
	slog.FromContext(ctx).Info(constants.GetProductVariantByCodeLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductVariantByCodeResponse{
		ProductVariant: mapProductVariantFromStorage(*productVariant[0], product.PublicID),
	}, nil
}

// UpdateProductVariantStatus ...
func UpdateProductVariantStatus(ctx context.Context, req *api.UpdateProductVariantStatusRequest, store *storage.DBStore) (*api.UpdateProductVariantStatusResponse, error) {
	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantStatusLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.ProductVariantDAO.Update(ctx, &storage.ProductVariant{
		ID:          productVariant[0].ID,
		PublicID:    productVariant[0].PublicID,
		ProductID:   productVariant[0].ProductID,
		Code:        productVariant[0].Code,
		Version:     productVariant[0].Version,
		Name:        productVariant[0].Name,
		Description: productVariant[0].Description,
		Status:      string(req.Status),
		ValidFrom:   productVariant[0].ValidFrom,
		ValidTo:     productVariant[0].ValidTo,
		CreatedBy:   productVariant[0].CreatedBy,
		CreatedAt:   productVariant[0].CreatedAt,
		UpdatedBy:   req.UpdatedBy,
	}); updateErr != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantStatusLogTag, fmt.Sprintf("Error in updating the ProductVariant DB, err : %s", updateErr), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update product-variant in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	updatedProductVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantStatusLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.Id), err)
	}

	product, err := store.ProductDAO.LoadByID(ctx, productVariant[0].ProductID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantStatusLogTag, fmt.Sprintf("Error in loading from the Product DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with id: %d", productVariant[0].ProductID), err)
	}
	slog.FromContext(ctx).Info(constants.UpdateProductVariantStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateProductVariantStatusResponse{
		ProductVariant: mapProductVariantFromStorage(*updatedProductVariant[0], product.PublicID),
	}, nil
}

// ListProductVariants ...
// nolint: funlen, gocognit
func ListProductVariants(ctx context.Context, req *api.ListProductVariantsRequest, store *storage.DBStore) (*api.ListProductVariantsResponse, error) {
	var getProductFilters []data.Condition
	// Get product_templates
	if req.ProductTemplateCode != "" {
		productTemplates, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("Code", req.ProductTemplateCode))
		if err != nil {
			if errors.Is(err, data.ErrNoData) {
				slog.FromContext(ctx).Info(constants.ListProductVariantsLogTag, fmt.Sprintf("Product template with code %s not found", req.ProductTemplateCode), apiCommon.GetTraceID(ctx))
				return nil, apiErr.BuildErrorResponse(http.StatusNotFound, utils.SafeIntToString(apiErr.ErrRecordNotFound.Code), apiErr.ErrRecordNotFound.Message)
			}
			slog.FromContext(ctx).Error(constants.ListProductVariantsLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err : %s", err), apiCommon.GetTraceID(ctx))
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load product-templates from database for request with productTemplateCode: %s", req.ProductTemplateCode), err)
		}
		getProductFilters = append(getProductFilters, data.EqualTo("ProductTemplateID", productTemplates[0].ID))
	}
	if req.ProductCode != "" {
		getProductFilters = append(getProductFilters, data.EqualTo("Code", req.ProductCode))
	}

	// Get products from product table
	var productIDs []uint64
	if len(getProductFilters) > 0 {
		products, err := store.ProductDAO.Find(ctx, getProductFilters...)
		if err != nil {
			if errors.Is(err, data.ErrNoData) {
				slog.FromContext(ctx).Info(constants.ListProductVariantsLogTag, fmt.Sprintf("Product with code %s not found", req.ProductCode), apiCommon.GetTraceID(ctx))
				return nil, apiErr.BuildErrorResponse(http.StatusNotFound, utils.SafeIntToString(apiErr.ErrRecordNotFound.Code), apiErr.ErrRecordNotFound.Message)
			}
			slog.FromContext(ctx).Error(constants.ListProductVariantsLogTag, fmt.Sprintf("Error in finding from the Product DB, err : %s", err), apiCommon.GetTraceID(ctx))
			return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load product from database for request with productCode: %s", req.ProductCode), err)
		}
		productIDs = lo.Map(products, func(product *storage.Product, _ int) uint64 {
			return product.ID
		})
	}
	// Fetch productVariants based on the filters from productVariant table
	getProductVariantFilters := []data.Condition{data.AscendingOrder("CreatedAt")}
	if len(productIDs) > 0 {
		getProductVariantFilters = append(getProductVariantFilters, data.ContainedIn("ProductID", utils.ConvertToInterfaceSlice(productIDs)...))
	}
	if req.Code != "" {
		getProductVariantFilters = append(getProductVariantFilters, data.EqualTo("Code", req.Code))
	}
	productVariants, err := store.ProductVariantDAO.Find(ctx, getProductVariantFilters...)
	if err != nil {
		if errors.Is(err, data.ErrNoData) {
			slog.FromContext(ctx).Info(constants.ListProductVariantsLogTag, fmt.Sprintf("Product variant not found for productIDs: %+v", utils.ToJSON(productIDs)), apiCommon.GetTraceID(ctx))
			return nil, apiErr.BuildErrorResponse(http.StatusNotFound, utils.SafeIntToString(apiErr.ErrRecordNotFound.Code), apiErr.ErrRecordNotFound.Message)
		}
		slog.FromContext(ctx).Error(constants.ListProductVariantsLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with productIDs: %+v", utils.ToJSON(productIDs)), err)
	}
	// Get the latest version of each product variant
	productVariantCodeList := lo.Uniq(
		lo.Map(productVariants, func(productVariant *storage.ProductVariant, _ int) string { return productVariant.Code }),
	)
	productVariantsDict, err := getLatestProductVariantVersions(ctx, productVariants)
	if err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(constants.ListProductVariantsLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.ListProductVariantsResponse{
		ProductVariants: mapProductVariantsFromStorage(productVariantCodeList, productVariantsDict),
	}, nil
}

func getLatestProductVariantVersions(ctx context.Context, productVariants []*storage.ProductVariant) (map[string]*storage.ProductVariant, error) {
	productVariantsDict := make(map[string]*storage.ProductVariant)
	for _, productVariant := range productVariants {
		seenProductVariant, seen := productVariantsDict[productVariant.Code]
		if !seen {
			productVariantsDict[productVariant.Code] = productVariant
			continue
		}
		latestProductVariant, compareErr := getLatestProductVariantVersion(seenProductVariant, productVariant)
		if compareErr != nil {
			slog.FromContext(ctx).Error("getLatestProductVariantVersions", fmt.Sprintf("Error in finding latest version for product variant code %s, err : %s", productVariant.Code, compareErr), apiCommon.GetTraceID(ctx))
			return nil, compareErr
		}
		productVariantsDict[productVariant.Code] = latestProductVariant
	}
	return productVariantsDict, nil
}

// getLatestProductVariantVersion ...
func getLatestProductVariantVersion(version1, version2 *storage.ProductVariant) (*storage.ProductVariant, error) {
	if version1 == nil || version2 == nil {
		return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, utils.SafeIntToString(apiErr.ErrNoProductVariantVersionForVersionID.Code),
			apiErr.ErrNoProductVariantVersionForVersionID.Message)
	}

	v1, v1Err := strconv.Atoi(version1.Version)
	v2, v2Err := strconv.Atoi(version2.Version)
	if v1Err != nil || v2Err != nil {
		return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, utils.SafeIntToString(apiErr.ErrNoProductVariantVersionForVersionID.Code),
			apiErr.ErrNoProductVariantVersionForVersionID.Message)
	}
	if v2 > v1 {
		return version2, nil
	}
	return version1, nil
}

func mapProductVariantsFromStorage(productVariantCodeList []string, productVariants map[string]*storage.ProductVariant) []api.ProductVariant {
	return lo.Map(productVariantCodeList, func(code string, _ int) api.ProductVariant {
		return *mapProductVariantFromStorage(*productVariants[code], strconv.FormatUint(productVariants[code].ProductID, 10))
	})
}

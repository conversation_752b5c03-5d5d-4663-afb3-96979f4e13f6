package productconfig

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/localisation"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/validations"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// QuestionsAnswersImpl ...
type QuestionsAnswersImpl struct {
	Store *storage.DBStore
}

// ValidateCreateProductVariantQuestionRequest : validates the request parameter
func ValidateCreateProductVariantQuestionRequest(ctx context.Context, req *api.CreateProductVariantQuestionRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantCode, "product-variant-code", apiErr.ErrMissingProductVariantCode); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantVersion, "product-variant-version", apiErr.ErrMissingProductVariantVersion); err != nil {
		return err
	}
	if err := validations.ValidateLocale(ctx, string(req.Locale)); err != nil {
		return err
	}
	if err := validations.ValidateFieldNotEmpty(ctx, req.CreatedBy, "created-by", apiErr.ErrMissingCreatedBy); err != nil {
		return err
	}

	if len(req.QuestionAnswerPairs) == 0 {
		slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "QuestionAnswerPairs field is missing", common.GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingQuestionAnswerPairs.Code), 10), apiErr.ErrMissingQuestionAnswerPairs.Message)
	}
	for _, questionAnswerPair := range req.QuestionAnswerPairs {
		if questionAnswerPair.Code == "" {
			slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "Question code is missing", common.GetTraceID(ctx))
			return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingQuestionCode.Code), 10), apiErr.ErrMissingQuestionCode.Message)
		}

		if questionAnswerPair.QuestionText == "" || len(questionAnswerPair.AnswerSuggestions) == 0 {
			slog.FromContext(ctx).Warn(constants.RequestValidationFailureTag, "QuestionText or AnswerSuggestions field is missing", common.GetTraceID(ctx))
			return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Code), 10), apiErr.ErrMissingQuestionTextOrAnswerSuggestions.Message)
		}
	}
	return nil
}

// CreateProductVariantQuestions : method to create question answer pairs
// nolint:funlen
func (l *QuestionsAnswersImpl) CreateProductVariantQuestions(ctx context.Context, req *api.CreateProductVariantQuestionRequest) (*api.CreateProductVariantQuestionResponse, error) {
	// find productVariant
	productVariant, err := l.Store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", req.ProductVariantCode), data.EqualTo("Version", req.ProductVariantVersion))
	if err != nil {
		return nil, common.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load productVariant from database with request: %s", utils.ToJSON(req)), err)
	}

	questionIDAnswersMap := make(map[string][]api.ProductVariantAnswerSuggestion)
	var questionList, loadedQuestions []*storage.ProductVariantQuestion
	// iterate over all question and create DB object
	for _, questionAnswerPair := range req.QuestionAnswerPairs {
		productVariantQuestionID := uuid.New().String()
		questionIDAnswersMap[productVariantQuestionID] = questionAnswerPair.AnswerSuggestions
		questionList = append(questionList, &storage.ProductVariantQuestion{
			PublicID:         productVariantQuestionID,
			ProductVariantID: productVariant[0].ID,
			Code:             questionAnswerPair.Code,
			QuestionText:     questionAnswerPair.QuestionText,
			Locale:           string(req.Locale),
			Status:           constants.ACTIVE,
			CreatedBy:        req.CreatedBy,
			UpdatedBy:        req.CreatedBy,
		})
	}

	// save questions in batch
	if productVariantQuesSaveErr := l.Store.ProductVariantQuestionDAO.SaveBatch(ctx, questionList); productVariantQuesSaveErr != nil {
		return nil, common.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product-vairant-question to database for request: %s", utils.ToJSON(req)), err)
	}

	// fetch the created questions
	questionIDMap := make(map[string]uint64)
	for _, question := range questionList {
		productVariantQuestions, loadErr := l.Store.ProductVariantQuestionDAO.Find(ctx, data.EqualTo("PublicID", question.PublicID))
		if loadErr != nil {
			return nil, common.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load the ProductVariantQuestion from the database for request: %s", utils.ToJSON(req)), loadErr)
		}
		questionIDMap[productVariantQuestions[0].PublicID] = productVariantQuestions[0].ID
		loadedQuestions = append(loadedQuestions, productVariantQuestions[0])
	}

	// save answer suggestion wrt above created question
	if productVariantAnsSuggestionSaveErr := l.insertAnswerSuggestionInDatabase(ctx, req, questionIDMap, questionIDAnswersMap); productVariantAnsSuggestionSaveErr != nil {
		return nil, productVariantAnsSuggestionSaveErr
	}

	// create pairs of question and answer
	questionAnswerPairs, quesAnsPairErr := l.createQuestionAnswerPairs(ctx, loadedQuestions)
	if quesAnsPairErr != nil {
		return nil, quesAnsPairErr
	}

	return &api.CreateProductVariantQuestionResponse{
		QuestionsDetail: &api.ProductVariantQuestionsDetail{
			ProductVariantCode:    req.ProductVariantCode,
			ProductVariantVersion: req.ProductVariantVersion,
			Locale:                req.Locale,
			QuestionAnswerPairs:   questionAnswerPairs,
		},
	}, nil
}

// insertAnswerSuggestionInDatabase : insert answer questions wrt questionID
func (l *QuestionsAnswersImpl) insertAnswerSuggestionInDatabase(ctx context.Context, req *api.CreateProductVariantQuestionRequest, questionIDMap map[string]uint64, questionIDAnswersMap map[string][]api.ProductVariantAnswerSuggestion) error {
	var answerSuggestionList []*storage.ProductVariantAnswerSuggestion
	for quesID, answers := range questionIDAnswersMap {
		for _, answer := range answers {
			answerSuggestionID := uuid.New().String()
			answerSuggestionList = append(answerSuggestionList, &storage.ProductVariantAnswerSuggestion{
				PublicID:                 answerSuggestionID,
				ProductVariantQuestionID: questionIDMap[quesID],
				Code:                     answer.Code,
				AnswerSuggestionText:     answer.Text,
				Locale:                   string(req.Locale),
				Status:                   constants.ACTIVE,
				CreatedBy:                req.CreatedBy,
				UpdatedBy:                req.CreatedBy,
			})
		}
	}

	// save answer suggestion in batches
	if productVariantAnsSuggestionSaveErr := l.Store.ProductVariantAnswerSuggestionDAO.SaveBatch(ctx, answerSuggestionList); productVariantAnsSuggestionSaveErr != nil {
		return common.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product-variant-answer-suggestion to database for request: %s", utils.ToJSON(req)), productVariantAnsSuggestionSaveErr)
	}
	return nil
}

func (l *QuestionsAnswersImpl) createQuestionAnswerPairs(ctx context.Context, questionList []*storage.ProductVariantQuestion) ([]api.QuestionAnswerPairsDetailsResponse, error) {
	questionAnswerPairs := make([]api.QuestionAnswerPairsDetailsResponse, len(questionList))
	for i, question := range questionList {
		answerSuggestions, err := l.Store.ProductVariantAnswerSuggestionDAO.Find(ctx,
			data.EqualTo("ProductVariantQuestionID", question.ID),
			data.EqualTo("Locale", question.Locale),
		)
		if err != nil {
			return nil, common.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("unable to load the answersuggestions of the productVariantQuestion with id %v from the database", question.PublicID), err)
		}
		answerSet := make([]api.ProductVariantAnswerSuggestion, len(answerSuggestions))
		for itr, answerSuggestion := range answerSuggestions {
			answerSet[itr] = api.ProductVariantAnswerSuggestion{
				Code: answerSuggestion.Code,
				Text: answerSuggestion.AnswerSuggestionText,
			}
		}
		questionAnswerPairs[i] = api.QuestionAnswerPairsDetailsResponse{
			ID:                question.PublicID,
			Code:              question.Code,
			QuestionText:      question.QuestionText,
			AnswerSuggestions: answerSet,
			Locale:            api.Locale(question.Locale),
			CreatedBy:         question.CreatedBy,
			CreatedAt:         question.CreatedAt,
			UpdatedBy:         question.UpdatedBy,
			UpdatedAt:         question.UpdatedAt,
		}
	}
	return questionAnswerPairs, nil
}

// ValidateListProductVariantQuestionsRequest : validate the request parameter
func ValidateListProductVariantQuestionsRequest(ctx context.Context, req *api.ListProductVariantQuestionsRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.ProductVariantCode, "product-variant-code", apiErr.ErrMissingProductVariantCode); err != nil {
		return err
	}
	return nil
}

// ListProductVariantQuestions : method to fetch question answers pairs
func (l *QuestionsAnswersImpl) ListProductVariantQuestions(ctx context.Context, req *api.ListProductVariantQuestionsRequest) (*api.ListProductVariantQuestionsResponse, error) {
	// Find productVariant, by default ORDER BY version DESC
	productVariantFilter := []data.Condition{
		data.EqualTo("Code", req.ProductVariantCode),
		data.DescendingOrder("Version"),
	}

	if req.ProductVariantVersion != "" {
		productVariantFilter = append(productVariantFilter, data.EqualTo("Version", req.ProductVariantVersion))
	}

	productVariant, err := l.Store.ProductVariantDAO.Find(ctx, productVariantFilter...)
	if err != nil {
		return nil, common.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load product-variant from database with request: %s", utils.ToJSON(req)), err)
	}

	locale := localisation.GetLocaleFromCtx(ctx)
	productVariantQuestionFilter := []data.Condition{
		data.EqualTo("ProductVariantID", productVariant[0].ID),
		data.EqualTo("Locale", string(locale)),
		data.EqualTo("Status", string(api.EntityStatus_ACTIVE)),
	}

	// Add option to filter by code, since a particular product variant may have more than one product-variant-question
	if req.Code != "" {
		productVariantQuestionFilter = append(productVariantQuestionFilter, data.EqualTo("Code", req.Code))
	}

	productVariantQuestions, err := l.Store.ProductVariantQuestionDAO.Find(ctx, productVariantQuestionFilter...)
	if err != nil {
		return nil, common.BuildLoadDataFromDBErrResponse(ctx, fmt.Sprintf("unable to load product-variant-questions from database for request: %s ", utils.ToJSON(req)), err)
	}

	questionAnswerPairs, quesAnsPairErr := l.createQuestionAnswerPairs(ctx, productVariantQuestions)
	if quesAnsPairErr != nil {
		return nil, err
	}
	return &api.ListProductVariantQuestionsResponse{
		QuestionsDetail: &api.ProductVariantQuestionsDetail{
			ProductVariantCode:    req.ProductVariantCode,
			ProductVariantVersion: req.ProductVariantVersion,
			QuestionAnswerPairs:   questionAnswerPairs,
		},
	}, nil
}

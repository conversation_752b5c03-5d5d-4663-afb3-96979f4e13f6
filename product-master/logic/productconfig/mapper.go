package productconfig

import (
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

func mapProductTemplateFromStorage(productTemplate storage.ProductTemplate) *api.ProductTemplate {
	return &api.ProductTemplate{
		Id:          productTemplate.PublicID,
		Code:        productTemplate.Code,
		Name:        productTemplate.Name,
		Description: productTemplate.Description.String,
		Status:      api.EntityStatus(productTemplate.Status),
		CreatedBy:   productTemplate.CreatedBy,
		CreatedAt:   productTemplate.CreatedAt,
		UpdatedBy:   productTemplate.UpdatedBy,
		UpdatedAt:   productTemplate.UpdatedAt,
	}
}

func mapProductTemplateParameterFromStorage(productTemplateParameter storage.ProductTemplateParameter, productTemplatePublicID string) *api.ProductTemplateParameter {
	return &api.ProductTemplateParameter{
		Id:                productTemplateParameter.PublicID,
		ProductTemplateID: productTemplatePublicID,
		Namespace:         productTemplateParameter.Namespace,
		ParameterKey:      productTemplateParameter.ParameterKey,
		ParameterValue:    productTemplateParameter.ParameterValue,
		DataType:          api.ParameterDataType(productTemplateParameter.DataType),
		OverrideLevel:     api.ParameterOverrideLevel(productTemplateParameter.OverrideLevel),
		ExceptionLevel:    productTemplateParameter.ExceptionLevel.String,
		Description:       productTemplateParameter.Description.String,
		CreatedBy:         productTemplateParameter.CreatedBy,
		CreatedAt:         productTemplateParameter.CreatedAt,
		UpdatedBy:         productTemplateParameter.UpdatedBy,
		UpdatedAt:         productTemplateParameter.UpdatedAt,
	}
}

func mapProductTemplateParametersFromStorage(productTemplateParameters []*storage.ProductTemplateParameter, productTemplatePublicID string) []api.ProductTemplateParameter {
	var mappedParameters []api.ProductTemplateParameter
	for _, parameter := range productTemplateParameters {
		mappedParameters = append(mappedParameters, *mapProductTemplateParameterFromStorage(*parameter, productTemplatePublicID))
	}
	return mappedParameters
}

func mapProductFromStorage(product storage.Product, productTemplatePublicID string) *api.Product {
	return &api.Product{
		Id:                product.PublicID,
		ProductTemplateID: productTemplatePublicID,
		Code:              product.Code,
		Name:              product.Name,
		Description:       product.Description.String,
		Status:            api.EntityStatus(product.Status),
		CreatedBy:         product.CreatedBy,
		CreatedAt:         product.CreatedAt,
		UpdatedBy:         product.UpdatedBy,
		UpdatedAt:         product.UpdatedAt,
	}
}

func mapProductVariantFromStorage(productVariant storage.ProductVariant, productPublicID string) *api.ProductVariant {
	return &api.ProductVariant{
		Id:          productVariant.PublicID,
		Name:        productVariant.Name,
		ProductID:   productPublicID,
		Code:        productVariant.Code,
		Version:     productVariant.Version,
		Description: productVariant.Description.String,
		Status:      api.EntityStatus(productVariant.Status),
		ValidFrom:   productVariant.ValidFrom,
		ValidTo:     productVariant.ValidTo,
		CreatedBy:   productVariant.CreatedBy,
		CreatedAt:   productVariant.CreatedAt,
		UpdatedBy:   productVariant.UpdatedBy,
		UpdatedAt:   productVariant.UpdatedAt,
	}
}

func mapProductVariantParameterFromStorage(productVariantParameter storage.ProductVariantParameter, productVariantPublicID string) *api.ProductVariantParameter {
	return &api.ProductVariantParameter{
		Id:               productVariantParameter.PublicID,
		ProductVariantID: productVariantPublicID,
		Namespace:        productVariantParameter.Namespace,
		ParameterKey:     productVariantParameter.ParameterKey,
		ParameterValue:   productVariantParameter.ParameterValue,
		DataType:         api.ParameterDataType(productVariantParameter.DataType),
		OverrideLevel:    api.ParameterOverrideLevel(productVariantParameter.OverrideLevel),
		ExceptionLevel:   productVariantParameter.ExceptionLevel.String,
		Description:      productVariantParameter.Description.String,
		CreatedBy:        productVariantParameter.CreatedBy,
		CreatedAt:        productVariantParameter.CreatedAt,
		UpdatedBy:        productVariantParameter.UpdatedBy,
		UpdatedAt:        productVariantParameter.UpdatedAt,
	}
}

func mapProductVariantParametersFromStorage(productVariantParameters []*storage.ProductVariantParameter, productVariantPublicID string) []api.ProductVariantParameter {
	var mappedParameters []api.ProductVariantParameter
	for _, parameter := range productVariantParameters {
		mappedParameters = append(mappedParameters, *mapProductVariantParameterFromStorage(*parameter, productVariantPublicID))
	}
	return mappedParameters
}

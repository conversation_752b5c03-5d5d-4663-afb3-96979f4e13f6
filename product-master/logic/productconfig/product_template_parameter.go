package productconfig

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateProductTemplateParameter ...
func CreateProductTemplateParameter(ctx context.Context, req *api.CreateProductTemplateParameterRequest, store *storage.DBStore) (*api.CreateProductTemplateParameterResponse, error) {
	var description, exceptionLevel sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	exceptionLevel.Valid = !(req.ExceptionLevel == "")
	exceptionLevel.String = req.ExceptionLevel

	productTemplate, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.ProductTemplateID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductTemplateParameterLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database with id: %s", req.ProductTemplateID), err)
	}

	templateParameterID := uuid.New().String()
	if err = store.ProductTemplateParameterDAO.Save(ctx, &storage.ProductTemplateParameter{
		PublicID:          templateParameterID,
		ProductTemplateID: productTemplate[0].ID,
		Namespace:         req.Namespace,
		ParameterKey:      req.ParameterKey,
		ParameterValue:    req.ParameterValue,
		DataType:          string(req.DataType),
		OverrideLevel:     string(req.OverrideLevel),
		ExceptionLevel:    exceptionLevel,
		Description:       description,
		CreatedBy:         req.CreatedBy,
		UpdatedBy:         req.CreatedBy,
	}); err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductTemplateParameterLogTag, fmt.Sprintf("Error in saving to the ProductTemplateParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product-template-parameter to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	productTemplateParameter, err := store.ProductTemplateParameterDAO.Find(ctx, data.EqualTo("PublicID", templateParameterID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductTemplateParameterLogTag, fmt.Sprintf("Error in finding from the ProductTemplateParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template-parameter from database after saving for request with id: %s", templateParameterID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateProductTemplateParameterLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateProductTemplateParameterResponse{
		ProductTemplateParameter: mapProductTemplateParameterFromStorage(*productTemplateParameter[0], req.ProductTemplateID),
	}, nil
}

// GetProductTemplateParameter ...
func GetProductTemplateParameter(ctx context.Context, req *api.GetProductTemplateParameterRequest, store *storage.DBStore) (*api.GetProductTemplateParameterResponse, error) {
	productTemplateParameter, err := store.ProductTemplateParameterDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductTemplateParameterLogTag, fmt.Sprintf("Error in finding from the ProductTemplateParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load pproduct-template-parameter from database for request with id: %s", req.Id), err)
	}

	productTemplate, err := store.ProductTemplateDAO.LoadByID(ctx, productTemplateParameter[0].ProductTemplateID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductTemplateParameterLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database with id: %d", productTemplateParameter[0].ProductTemplateID), err)
	}
	slog.FromContext(ctx).Info(constants.GetProductTemplateParameterLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductTemplateParameterResponse{
		ProductTemplateParameter: mapProductTemplateParameterFromStorage(*productTemplateParameter[0], productTemplate.PublicID),
	}, nil
}

// ListProductTemplateParameter ...
func ListProductTemplateParameter(ctx context.Context, req *api.ListProductTemplateParametersRequest, store *storage.DBStore) (*api.ListProductTemplateParametersResponse, error) {
	productTemplate, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.ProductTemplateID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ListProductTemplateParametersLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %s", req.ProductTemplateID), err)
	}

	productTemplateParameters, err := store.ProductTemplateParameterDAO.Find(ctx, data.EqualTo("ProductTemplateID", productTemplate[0].ID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ListProductTemplateParametersLogTag, fmt.Sprintf("Error in finding from the ProductTemplateParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template-parameters from database for request with product-template-id: %s", req.ProductTemplateID), err)
	}
	slog.FromContext(ctx).Info(constants.ListProductTemplateParametersLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.ListProductTemplateParametersResponse{
		ProductTemplateParameters: mapProductTemplateParametersFromStorage(productTemplateParameters, req.ProductTemplateID),
	}, nil
}

// UpdateProductTemplateParameterValue ...
func UpdateProductTemplateParameterValue(ctx context.Context, req *api.UpdateProductTemplateParameterValueRequest, store *storage.DBStore) (*api.UpdateProductTemplateParameterValueResponse, error) {
	productTemplateParameter, err := store.ProductTemplateParameterDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductTemplateParameterValueLogTag, fmt.Sprintf("Error in finding from the ProductTemplateParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template-parameter from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.ProductTemplateParameterDAO.Update(ctx, &storage.ProductTemplateParameter{
		ID:                productTemplateParameter[0].ID,
		PublicID:          productTemplateParameter[0].PublicID,
		ProductTemplateID: productTemplateParameter[0].ProductTemplateID,
		Namespace:         productTemplateParameter[0].Namespace,
		ParameterKey:      productTemplateParameter[0].ParameterKey,
		ParameterValue:    req.ParameterValue,
		DataType:          productTemplateParameter[0].DataType,
		OverrideLevel:     productTemplateParameter[0].OverrideLevel,
		ExceptionLevel:    productTemplateParameter[0].ExceptionLevel,
		Description:       productTemplateParameter[0].Description,
		CreatedBy:         productTemplateParameter[0].CreatedBy,
		CreatedAt:         productTemplateParameter[0].CreatedAt,
		UpdatedBy:         req.UpdatedBy,
	}); updateErr != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductTemplateParameterValueLogTag, fmt.Sprintf("Error in updating the ProductTemplateParameter DB, err : %s", updateErr), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update product-template-parameter in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	productTemplateParameter, err = store.ProductTemplateParameterDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductTemplateParameterValueLogTag, fmt.Sprintf("Error in finding from the ProductTemplateParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template-parameter from database after update for request with id: %s", req.Id), err)
	}

	productTemplate, err := store.ProductTemplateDAO.LoadByID(ctx, productTemplateParameter[0].ProductTemplateID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductTemplateParameterValueLogTag, fmt.Sprintf("Error in loading from the ProductTemplate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %d", productTemplateParameter[0].ProductTemplateID), err)
	}
	slog.FromContext(ctx).Info(constants.UpdateProductTemplateParameterValueLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateProductTemplateParameterValueResponse{
		ProductTemplateParameter: mapProductTemplateParameterFromStorage(*productTemplateParameter[0], productTemplate.PublicID),
	}, nil
}

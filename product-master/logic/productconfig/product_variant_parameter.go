package productconfig

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"time"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/internal/featureflag"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/interestconfig"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/dto"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/loanadditionalparameters"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateProductVariantParameter ...
func CreateProductVariantParameter(ctx context.Context, req *api.CreateProductVariantParameterRequest, store *storage.DBStore) (*api.CreateProductVariantParameterResponse, error) {
	var description, exceptionLevel sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	exceptionLevel.Valid = !(req.ExceptionLevel == "")
	exceptionLevel.String = req.ExceptionLevel

	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.ProductVariantID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductVariantParameterLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.ProductVariantID), err)
	}

	parameterID := uuid.New().String()
	if err = store.ProductVariantParameterDAO.Save(ctx, &storage.ProductVariantParameter{
		PublicID:         parameterID,
		ProductVariantID: productVariant[0].ID,
		Namespace:        req.Namespace,
		ParameterKey:     req.ParameterKey,
		ParameterValue:   req.ParameterValue,
		DataType:         string(req.DataType),
		OverrideLevel:    string(req.OverrideLevel),
		ExceptionLevel:   exceptionLevel,
		Description:      description,
		CreatedBy:        req.CreatedBy,
		UpdatedBy:        req.CreatedBy,
	}); err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductVariantParameterLogTag, fmt.Sprintf("Error in saving to the ProductVariantParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product-variant-parameter to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	productVariantParameter, err := store.ProductVariantParameterDAO.Find(ctx, data.EqualTo("PublicID", parameterID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductVariantParameterLogTag, fmt.Sprintf("Error in finding from the ProductVariantParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant-parameter from database for request with id: %s", parameterID), err)
	}
	slog.FromContext(ctx).Info(constants.CreateProductVariantParameterLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateProductVariantParameterResponse{
		ProductVariantParameter: mapProductVariantParameterFromStorage(*productVariantParameter[0], req.ProductVariantID),
	}, nil
}

// GetProductVariantParameter ...
func GetProductVariantParameter(ctx context.Context, req *api.GetProductVariantParameterRequest, store *storage.DBStore) (*api.GetProductVariantParameterResponse, error) {
	productVariantParameter, err := store.ProductVariantParameterDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductVariantParameterLogTag, fmt.Sprintf("Error in finding from the ProductVariantParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant-parameter from database for request with id: %s", req.Id), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, productVariantParameter[0].ProductVariantID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductVariantParameterLogTag, fmt.Sprintf("Error in loading from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %d", productVariantParameter[0].ProductVariantID), err)
	}
	slog.FromContext(ctx).Info(constants.GetProductVariantParameterLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductVariantParameterResponse{
		ProductVariantParameter: mapProductVariantParameterFromStorage(*productVariantParameter[0], productVariant.PublicID),
	}, nil
}

// ListProductVariantParameters ...
func ListProductVariantParameters(ctx context.Context, req *api.ListProductVariantParametersRequest, store *storage.DBStore) (*api.ListProductVariantParametersResponse, error) {
	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.ProductVariantID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ListProductTemplateParametersLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.ProductVariantID), err)
	}

	productVariantParameters, err := store.ProductVariantParameterDAO.Find(ctx, data.EqualTo("ProductVariantID", productVariant[0].ID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ListProductTemplateParametersLogTag, fmt.Sprintf("Error in finding from the ProductVariantParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant-parameters from database for request with product-variant-id: %s", req.ProductVariantID), err)
	}
	slog.FromContext(ctx).Info(constants.ListProductVariantParametersLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.ListProductVariantParametersResponse{
		ProductVariantParameters: mapProductVariantParametersFromStorage(productVariantParameters, req.ProductVariantID),
	}, nil
}

// ListEffectiveProductVariantParameters ...
//
//nolint:funlen
func ListEffectiveProductVariantParameters(ctx context.Context, req *api.ListEffectiveProductVariantParametersRequest, store *storage.DBStore) (*api.ListEffectiveProductVariantParametersResponse, error) {
	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", req.ProductVariantCode))

	if err != nil {
		slog.FromContext(ctx).Warn(constants.ListEffectiveProductVariantParametersLogTag, fmt.Sprintf("Error in finding from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with code: %s", req.ProductVariantCode), err)
	}

	storedParameters, err := store.ProductVariantParameterDAO.Find(ctx, data.EqualTo("ProductVariantID", productVariant[0].ID))
	if err != nil && !reflect.DeepEqual(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.ListEffectiveProductVariantParametersLogTag, fmt.Sprintf("Error in finding from the ProductVariantParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant-parameters from database for request with product-variant-id: %d", productVariant[0].ID), err)
	}

	variantParameters := mapProductVariantParametersFromStorage(storedParameters, productVariant[0].PublicID)

	product, err := store.ProductDAO.LoadByID(ctx, productVariant[0].ProductID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ListEffectiveProductVariantParametersLogTag, fmt.Sprintf("Error in loading from the Product DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with id: %d", productVariant[0].ProductID), err)
	}

	storedTemplateParameters, err := store.ProductTemplateParameterDAO.Find(ctx, data.EqualTo("ProductTemplateID", product.ProductTemplateID))
	if err != nil && !reflect.DeepEqual(err, data.ErrNoData) {
		slog.FromContext(ctx).Warn(constants.ListEffectiveProductVariantParametersLogTag, fmt.Sprintf("Error in finding from the ProductTemplateParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template-parameters from database for request with product-template-id: %d", product.ProductTemplateID), err)
	}

	productTemplate, err := store.ProductTemplateDAO.LoadByID(ctx, product.ProductTemplateID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.ListEffectiveProductVariantParametersLogTag, fmt.Sprintf("Error in loading from the ProductTemplate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %d", product.ProductTemplateID), err)
	}

	templateParameters := mapProductTemplateParametersFromStorage(storedTemplateParameters, productTemplate.PublicID)
	mergedParameters := mergeTemplateAndVariantParameters(variantParameters, templateParameters, productVariant[0].PublicID)

	var interestRateParams []api.InterestRateParameter

	if req.IncludeInterestRateParameters {
		interestRateParams, err = getInterestRateParamsForProductVariant(ctx, store, productVariant[0].ID)
		if err != nil {
			return nil, err
		}
	}

	var additionalLoanParameters *api.LoanParametersDetail
	if req.IncludeAdditionalLoanParameters {
		additionalLoanParameters, err = loanadditionalparameters.GetAdditionalLoanParameters(ctx, store, productVariant[0].ID)
		if err != nil {
			return nil, err
		}
	}

	slog.FromContext(ctx).Info(constants.ListEffectiveProductVariantParametersLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.ListEffectiveProductVariantParametersResponse{
		ProductVariantParameters: mergedParameters,
		InterestRateParameters:   interestRateParams,
		LoanParameters:           additionalLoanParameters,
	}, nil
}

// This function has been refactored from GetDepositsInterestParamsByProductVariant
// TODO: check with BERSAMA code in future on how to merge this changes with GxS
func getInterestRateParamsForProductVariant(ctx context.Context, store *storage.DBStore, productVariantID uint64) ([]api.InterestRateParameter, error) {
	depositInterests, err := store.DepositInterestDAO.Find(ctx, data.EqualTo("ProductVariantID", productVariantID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the DepositInterest DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest from database for request with product-variant-id: %d", productVariantID), err)
	}

	depositInterest := depositInterests[0]
	featFlag := featureflag.FeatureFlagsFromContext(ctx)
	var depositInterestVersions []*storage.DepositInterestVersion
	if featFlag != nil && featFlag.IsGetDepositInterestByEffectiveDateEnabled() {
		depositInterestVersions, err = store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("DepositInterestID", depositInterest.ID), data.LessThanOrEqualTo("EffectiveDate", time.Now().UTC()), data.DescendingOrder("EffectiveDate"))
	} else {
		depositInterestVersions, err = store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("DepositInterestID", depositInterest.ID))
	}
	if err != nil {
		if errors.Is(err, data.ErrNoData) {
			slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Deposits interest version %d not found", depositInterest.ID), apiCommon.GetTraceID(ctx))
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10),
				apiErr.ErrRecordNotFound.Message)
		}
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the DepositInterestVersion DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-versions from database for request with deposit-interest-id: %d", depositInterest.ID), err)
	}

	latestDepositInterestVersion, err := interestconfig.GetLatestDepositInterestVersion(depositInterestVersions)
	if err != nil {
		return nil, err
	}

	depositInterestAmountSlabRates, err := store.DepositInterestAmountSlabRateDAO.Find(ctx, data.EqualTo("DepositInterestVersionID", latestDepositInterestVersion.ID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestRateParamsForProductVariantLogTag, fmt.Sprintf("Error in finding from the DepositInterestAmountSlabRate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-amount-slab-rate from database for request with deposit-interest-version-id: %d", latestDepositInterestVersion.ID), err)
	}

	var baseInterestRate string
	if depositInterest.IsLinkedToBaseRate {
		baseInterestTimeSlabRate, _ := getBaseInterestTimeSlabRate(ctx, store, depositInterest.BaseInterestID)
		baseInterestRate = baseInterestTimeSlabRate.BaseRatePercentage
	}
	interestSlabString, err := getInterestSlab(baseInterestRate, depositInterestAmountSlabRates, depositInterest.IsLinkedToBaseRate)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetInterestSlabLogTag, fmt.Sprintf("Error in getting the Interest slab, err : %s", err), apiCommon.GetTraceID(ctx))
	}
	return interestParameterList(string(interestSlabString)), nil
}

// This function has been refactored from getDepositsInterestSlab
func getInterestSlab(baseInterestRate string, depositInterestAmountSlabRates []*storage.DepositInterestAmountSlabRate, isLinkedToBaseRate bool) ([]byte, error) {
	interestSlabMap := make(map[string]interface{})

	if isLinkedToBaseRate {
		baseRate, err := strconv.ParseFloat(baseInterestRate, 32)
		if err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestPercentage.Code), 10), apiErr.ErrInvalidBaseInterestPercentage.Message)
		}

		for i, interestSlab := range depositInterestAmountSlabRates {
			spreadRate, parseErr := strconv.ParseFloat(interestSlab.BaseRateInterestSpreadPercentage, 32)
			if parseErr != nil {
				return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Code), 10), apiErr.ErrInvalidBaseRateInterestSpreadPercentage.Message)
			}

			interestSlabMap[fmt.Sprint("tier", i+1)] = dto.InterestSlab{
				Min:  interestSlab.FromAmount,
				Max:  interestSlab.ToAmount,
				Rate: fmt.Sprintf("%f", baseRate+spreadRate),
			}
		}
	} else {
		for i, interestSlab := range depositInterestAmountSlabRates {
			interestSlabMap[fmt.Sprint("tier", i+1)] = dto.InterestSlab{
				Min:  interestSlab.FromAmount,
				Max:  interestSlab.ToAmount,
				Rate: interestSlab.AbsoluteInterestRatePercentage,
			}
		}
	}

	return json.Marshal(interestSlabMap)
}

// This function has been refactored from getBaseInterestTimeSlabRateFromDB
func getBaseInterestTimeSlabRate(ctx context.Context, store *storage.DBStore, baseInterestNullable sql.NullInt64) (*storage.BaseInterestTimeSlabRate, error) {
	if !baseInterestNullable.Valid {
		slog.FromContext(ctx).Warn(constants.GetBaseInterestTimeSlabRateLogTag, "Missing base interest id field", apiCommon.GetTraceID(ctx))
		return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrMissingBaseInterestID.Code), 10), apiErr.ErrMissingBaseInterestID.Message)
	}

	baseInterestID := baseInterestNullable.Int64
	baseInterestVersions, err := store.BaseInterestVersionDAO.Find(ctx, data.EqualTo("BaseInterestID", baseInterestID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetBaseInterestTimeSlabRateLogTag, fmt.Sprintf("Error in finding from the BaseInterestVersion DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-versions from database for request with base-interest-id: %d", baseInterestID), err)
	}

	latestBaseInterestVersion, err := getLatestBaseInterestVersion(baseInterestVersions)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetLatestBaseInterestVersionLogTag, fmt.Sprintf("Error getting the latestBase Interest version, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, err
	}

	baseInterestTimeSlabRates, err := store.BaseInterestTimeSlabRateDAO.Find(ctx, data.EqualTo("BaseInterestVersionID", latestBaseInterestVersion.ID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetBaseInterestTimeSlabRateLogTag, fmt.Sprintf("Error in finding from the BaseInterestTimeSlabRate DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load base-interest-time-slab-rate from database for request with deposit-interest-version-id: %d", latestBaseInterestVersion.ID), err)
	}

	return baseInterestTimeSlabRates[0], nil
}

// Deprecated: This function is deprecated together with getInterestRateParamsForProductVariant
func getLatestBaseInterestVersion(versions []*storage.BaseInterestVersion) (*storage.BaseInterestVersion, error) {
	if len(versions) == 0 {
		return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrNoBaseInterestVersionForVersionID.Code), 10),
			apiErr.ErrNoBaseInterestVersionForVersionID.Message)
	}
	latest := versions[0]
	for _, version := range versions {
		v1, v1Err := strconv.Atoi(latest.Version)
		if v1Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestVersion.Code), 10),
				apiErr.ErrInvalidBaseInterestVersion.Message)
		}
		v2, v2Err := strconv.Atoi(version.Version)
		if v2Err != nil {
			return nil, apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidBaseInterestVersion.Code), 10),
				apiErr.ErrInvalidBaseInterestVersion.Message)
		}
		if v2 > v1 {
			latest = version
		}
	}
	return latest, nil
}

// Deprecated: This function is deprecated together with getInterestRateParamsForProductVariant
func interestParameterList(interestSlabsString string) []api.InterestRateParameter {
	interestParams := apiCommon.GetInterestParamConstants(interestSlabsString)
	return []api.InterestRateParameter{
		interestParams.FlatInterestRateParam,
		interestParams.InterestRateTypeParam,
		interestParams.OverallBalanceFeatureInterestApplicationAccountParam,
		interestParams.InterestRateTiersParam,
	}
}

func mergeTemplateAndVariantParameters(variantParams []api.ProductVariantParameter, templateParams []api.ProductTemplateParameter, productVariantID string) []api.ProductVariantParameter {
	variantParamsMap := make(map[string]api.ProductVariantParameter)
	for _, param := range templateParams {
		variantParamsMap[param.ParameterKey] = templateParamToVariantParam(param, productVariantID)
	}

	for _, param := range variantParams {
		variantParamsMap[param.ParameterKey] = param
	}

	paramsList := make([]api.ProductVariantParameter, 0, len(variantParamsMap))
	for _, mergedParam := range variantParamsMap {
		paramsList = append(paramsList, mergedParam)
	}

	return paramsList
}

func templateParamToVariantParam(templateParam api.ProductTemplateParameter, productVariantID string) api.ProductVariantParameter {
	return api.ProductVariantParameter{
		Id:               templateParam.Id,
		ProductVariantID: productVariantID,
		Namespace:        templateParam.Namespace,
		ParameterKey:     templateParam.ParameterKey,
		ParameterValue:   templateParam.ParameterValue,
		DataType:         templateParam.DataType,
		OverrideLevel:    templateParam.OverrideLevel,
		ExceptionLevel:   templateParam.ExceptionLevel,
		Description:      templateParam.Description,
		CreatedBy:        templateParam.CreatedBy,
		CreatedAt:        templateParam.CreatedAt,
		UpdatedBy:        templateParam.UpdatedBy,
		UpdatedAt:        templateParam.UpdatedAt,
	}
}

// UpdateProductVariantParameterValue ...
func UpdateProductVariantParameterValue(ctx context.Context, req *api.UpdateProductVariantParameterValueRequest, store *storage.DBStore) (*api.UpdateProductVariantParameterValueResponse, error) {
	productVariantParameters, err := store.ProductVariantParameterDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantParameterValueLogTag, fmt.Sprintf("Error in finding from the ProductVariantParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant-parameter from database for request with id: %s", req.Id), err)
	}

	nowString := time.Now().String()
	now, _ := time.Parse(time.RFC3339, nowString)
	variantParameter := &storage.ProductVariantParameter{
		ID:               productVariantParameters[0].ID,
		PublicID:         productVariantParameters[0].PublicID,
		ProductVariantID: productVariantParameters[0].ProductVariantID,
		Namespace:        productVariantParameters[0].Namespace,
		ParameterKey:     productVariantParameters[0].ParameterKey,
		ParameterValue:   req.ParameterValue,
		DataType:         productVariantParameters[0].DataType,
		OverrideLevel:    productVariantParameters[0].OverrideLevel,
		ExceptionLevel:   productVariantParameters[0].ExceptionLevel,
		Description:      productVariantParameters[0].Description,
		CreatedBy:        productVariantParameters[0].CreatedBy,
		CreatedAt:        productVariantParameters[0].CreatedAt,
		UpdatedBy:        req.UpdatedBy,
		UpdatedAt:        now,
	}
	if updateErr := store.ProductVariantParameterDAO.Update(ctx, variantParameter); updateErr != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantParameterValueLogTag, fmt.Sprintf("Error in updating the ProductVariantParameter DB, err : %s", updateErr), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update product-variant-parameter in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	productVariantParameter, err := store.ProductVariantParameterDAO.LoadByID(ctx, productVariantParameters[0].ID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantParameterValueLogTag, fmt.Sprintf("Error in loading from the ProductVariantParameter DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant-parameter from database after update for request with id: %s", req.Id), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, productVariantParameters[0].ProductVariantID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductVariantParameterValueLogTag, fmt.Sprintf("Error in loading from the ProductVariant DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %d", productVariantParameters[0].ProductVariantID), err)
	}
	slog.FromContext(ctx).Info(constants.UpdateProductVariantParameterValueLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateProductVariantParameterValueResponse{
		ProductVariantParameter: mapProductVariantParameterFromStorage(*productVariantParameter, productVariant.PublicID),
	}, nil
}

// GetInterestRateParams ...
func GetInterestRateParams(ctx context.Context, store *storage.DBStore) (*dto.InterestRateVersionDetail, error) {
	depositInterests, err := store.DepositInterestDAO.Find(ctx)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			"unable to load deposit-interest from database for request with product-variant-id", err)
	}

	depositInterest := depositInterests[0]
	depositInterestVersions, err := store.DepositInterestVersionDAO.Find(ctx, data.EqualTo("DepositInterestID", depositInterest.ID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-versions from database for request with deposit-interest-id: %d", depositInterest.ID), err)
	}

	latestDepositInterestVersion, err := interestconfig.GetLatestDepositInterestVersion(depositInterestVersions)
	if err != nil {
		return nil, err
	}

	depositInterestAmountSlabRates, err := store.DepositInterestAmountSlabRateDAO.Find(ctx, data.EqualTo("DepositInterestVersionID", latestDepositInterestVersion.ID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load deposit-interest-amount-slab-rate from database for request with deposit-interest-version-id: %d", latestDepositInterestVersion.ID), err)
	}

	interestSlabRates, err := getInterestSlabRates(depositInterestAmountSlabRates)
	if err != nil {
		return nil, err
	}

	return &dto.InterestRateVersionDetail{
		LatestDepositInterestVersion: latestDepositInterestVersion.Version,
		ParameterValue:               string(interestSlabRates),
		EffectiveDate:                depositInterestVersions[0].EffectiveDate,
	}, nil
}

func getInterestSlabRates(depositInterestAmountSlabRates []*storage.DepositInterestAmountSlabRate) ([]byte, error) {
	interestSlabMap := make(map[string]interface{})

	for i, interestSlab := range depositInterestAmountSlabRates {
		interestSlabMap[fmt.Sprint("tier", i+1)] = api.DepositsInterestAmountSlabRate{
			Min:       interestSlab.FromAmount,
			MinAmount: interestSlab.FromAmount,
			Max:       interestSlab.ToAmount,
			MaxAmount: interestSlab.ToAmount,
			Rate:      interestSlab.AbsoluteInterestRatePercentage,
		}
	}

	return json.Marshal(interestSlabMap)
}

// GetLatestParameterDetail ...
func GetLatestParameterDetail(ctx context.Context, latestVersion *dto.InterestRateVersionDetail, store *storage.DBStore, conditions []data.Condition) (*api.GetInterestParametersResponse, error) {
	parameterScheduleDetails, err := store.ParameterChangeScheduleDAO.Find(ctx, conditions...)

	if reflect.DeepEqual(err, data.ErrNoData) {
		return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message)
	}

	if err != nil {
		return nil, err
	}

	parameterScheduleDetail := parameterScheduleDetails[0]
	parameterToSchedule := &api.GetInterestParametersResponse{
		ParameterKey:              parameterScheduleDetail.ParameterKey,
		ParameterValue:            latestVersion.ParameterValue,
		ParameterVersion:          parameterScheduleDetail.ParameterVersion,
		SmartContractVersionID:    parameterScheduleDetail.SmartContractVersionID,
		EffectiveScheduleDate:     latestVersion.EffectiveDate,
		EffectiveNotificationDate: parameterScheduleDetail.NotificationDate,
	}

	return parameterToSchedule, nil
}

// UpdateParameterScheduleStatus ...
func UpdateParameterScheduleStatus(ctx context.Context, store *storage.DBStore, req *api.UpdateInterestParameterScheduleStatusRequest,
	isScheduled bool) (*api.UpdateInterestParameterScheduleStatusResponse, error) {
	parameterDetail, err := getParameterChangeScheduleDetail(ctx, store, req.ParameterKey, req.ParameterVersion, req.SmartContractVersionID)
	if err != nil {
		return nil, err
	}
	parameterDetail.UpdatedBy = req.UpdatedBy
	parameterDetail.IsScheduled = isScheduled

	if err := updateParameterChangeScheduleStatus(ctx, store, parameterDetail); err != nil {
		return nil, err
	}
	return &api.UpdateInterestParameterScheduleStatusResponse{
		IsScheduled: req.IsScheduled,
	}, nil
}

// UpdateNotificationParameterStatus ...
func UpdateNotificationParameterStatus(ctx context.Context, store *storage.DBStore, req *api.UpdateInterestParameterNotificationStatusRequest,
	isNotified bool) (*api.UpdateInterestParameterNotificationStatusResponse, error) {
	parameterDetail, err := getParameterChangeScheduleDetail(ctx, store, req.ParameterKey, req.ParameterVersion, req.SmartContractVersionID)
	if err != nil {
		return nil, err
	}
	parameterDetail.UpdatedBy = req.UpdatedBy
	parameterDetail.IsNotificationSent = isNotified

	if err := updateParameterChangeScheduleStatus(ctx, store, parameterDetail); err != nil {
		return nil, err
	}
	return &api.UpdateInterestParameterNotificationStatusResponse{
		IsNotified: req.IsNotified,
	}, nil
}

func getParameterChangeScheduleDetail(ctx context.Context, store *storage.DBStore, parameterKey, parameterVersion, smartContractVersionID string) (*storage.ParameterChangeSchedule, error) {
	parameterDetails, err := store.ParameterChangeScheduleDAO.Find(ctx, data.EqualTo("ParameterVersion", parameterVersion),
		data.EqualTo("ParameterKey", parameterKey),
		data.EqualTo("SmartContractVersionID", smartContractVersionID),
	)

	if reflect.DeepEqual(err, data.ErrNoData) {
		return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message)
	}
	if err != nil {
		return nil, err
	}
	parameterDetail := parameterDetails[0]
	return parameterDetail, nil
}
func updateParameterChangeScheduleStatus(ctx context.Context, store *storage.DBStore, parameterDetail *storage.ParameterChangeSchedule) error {
	err := store.ParameterChangeScheduleDAO.Update(ctx, parameterDetail)
	if err != nil {
		return err
	}
	return nil
}

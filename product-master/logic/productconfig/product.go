package productconfig

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateProduct ...
func CreateProduct(ctx context.Context, req *api.CreateProductRequest, store *storage.DBStore) (*api.CreateProductResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	productTemplate, err := store.ProductTemplateDAO.Find(ctx, data.EqualTo("PublicID", req.ProductTemplateID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %s", req.ProductTemplateID), err)
	}

	productID := uuid.New().String()
	if err = store.ProductDAO.Save(ctx, &storage.Product{
		PublicID:          productID,
		ProductTemplateID: productTemplate[0].ID,
		Code:              req.Code,
		Name:              req.Name,
		Description:       description,
		Status:            string(api.EntityStatus_ACTIVE),
		CreatedBy:         req.CreatedBy,
		UpdatedBy:         req.CreatedBy,
	}); err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductLogTag, fmt.Sprintf("Error in saving to the Product DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	product, err := store.ProductDAO.Find(ctx, data.EqualTo("PublicID", productID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateProductLogTag, fmt.Sprintf("Error in finding from the Product DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database after saving for request with id: %s", productID), err)
	}
	slog.FromContext(ctx).Info(constants.CreateProductLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))

	return &api.CreateProductResponse{
		Product: mapProductFromStorage(*product[0], req.ProductTemplateID),
	}, nil
}

// GetProduct ...
func GetProduct(ctx context.Context, req *api.GetProductRequest, store *storage.DBStore) (*api.GetProductResponse, error) {
	product, err := store.ProductDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductLogTag, fmt.Sprintf("Error in finding from the Product DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with id: %s", req.Id), err)
	}

	productTemplate, err := store.ProductTemplateDAO.LoadByID(ctx, product[0].ProductTemplateID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductLogTag, fmt.Sprintf("Error in loading from the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %d", product[0].ProductTemplateID), err)
	}
	slog.FromContext(ctx).Info(constants.GetProductLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductResponse{
		Product: mapProductFromStorage(*product[0], productTemplate.PublicID),
	}, nil
}

// GetProductByCode ...
func GetProductByCode(ctx context.Context, req *api.GetProductByCodeRequest, store *storage.DBStore) (*api.GetProductByCodeResponse, error) {
	product, err := store.ProductDAO.Find(ctx, data.EqualTo("Code", req.Code))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductByCodeLogTag, fmt.Sprintf("Error in finding from the Product DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with code: %s", req.Code), err)
	}

	productTemplate, err := store.ProductTemplateDAO.LoadByID(ctx, product[0].ProductTemplateID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetProductByCodeLogTag, fmt.Sprintf("Error in finding from the ProductTemplate DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %d", product[0].ProductTemplateID), err)
	}
	slog.FromContext(ctx).Info(constants.GetProductByCodeLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetProductByCodeResponse{
		Product: mapProductFromStorage(*product[0], productTemplate.PublicID),
	}, nil
}

// UpdateProductStatus ...
func UpdateProductStatus(ctx context.Context, req *api.UpdateProductStatusRequest, store *storage.DBStore) (*api.UpdateProductStatusResponse, error) {
	product, err := store.ProductDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductStatusLogTag, fmt.Sprintf("Error in finding from the Product DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.ProductDAO.Update(ctx, &storage.Product{
		ID:                product[0].ID,
		PublicID:          product[0].PublicID,
		ProductTemplateID: product[0].ProductTemplateID,
		Code:              product[0].Code,
		Name:              product[0].Name,
		Description:       product[0].Description,
		Status:            string(req.Status),
		CreatedBy:         product[0].CreatedBy,
		CreatedAt:         product[0].CreatedAt,
		UpdatedBy:         req.UpdatedBy,
	}); updateErr != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductStatusLogTag, fmt.Sprintf("Error in updating the Product DB, err: %s", updateErr), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update product in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	product, err = store.ProductDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductStatusLogTag, fmt.Sprintf("Error in finding from the Product DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product from database after update for request with id: %s", req.Id), err)
	}

	productTemplate, err := store.ProductTemplateDAO.LoadByID(ctx, product[0].ProductTemplateID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.UpdateProductStatusLogTag, fmt.Sprintf("Error in loading from the Product DB, err: %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-template from database for request with id: %d", product[0].ProductTemplateID), err)
	}
	slog.FromContext(ctx).Info(constants.UpdateProductStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateProductStatusResponse{
		Product: mapProductFromStorage(*product[0], productTemplate.PublicID),
	}, nil
}

// Package loandocumentsubmissionoptions loan document submission options
package loandocumentsubmissionoptions

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/samber/lo"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/flow"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// ListLoanDocumentSubmissionOptionsByProductVariantCode defines a contract for fetching document types based on a product variant code.
// The single method FetchAndBuildLoanDocumentSubmissionOptionsByProductVariantCodeResponse returns a response or an error.
// It is used to handle logic tied to document type retrieval for specific product variants.
type ListLoanDocumentSubmissionOptionsByProductVariantCode interface {
	FetchAndBuildLoanDocumentSubmissionOptionsByProductVariantCodeResponse(ctx context.Context) (*api.ListLoanDocumentOptionsByProductVariantResponse, error)
}

// FetchLoanDocumentSubmissionOptionsParamsDTO represents the parameters required to fetch loan document types by product variant.
type FetchLoanDocumentSubmissionOptionsParamsDTO struct {
	LoanDocumentOptionsRequest *api.ListLoanDocumentOptionsByProductVariantRequest
	LogTag                     string
	Store                      *storage.DBStore
}

// GenericLoanDocumentSubmissionOptionsImpl provides an implementation for retrieving loan document types.
// It includes methods and dependencies for interacting with various models and storage constructs.
type GenericLoanDocumentSubmissionOptionsImpl struct {
	FetchLoanSubmissionOptionsParams       FetchLoanDocumentSubmissionOptionsParamsDTO
	productVariant                         *storage.ProductVariant
	loanDocumentSubmissionOption           []*storage.LoanDocumentSubmissionOption
	loanDocumentSubmissionOptionVersion    []*storage.LoanDocumentSubmissionOptionVersion
	loanDocumentSubmissionOptionParameters []*storage.LoanDocumentSubmissionOptionParameters
}

// NewGenericLoanDocumentTypesImpl initializes a new instance of GenericLoanDocumentSubmissionOptionsImpl with given parameters.
func NewGenericLoanDocumentTypesImpl(fetchLoanDocumentTypesParams FetchLoanDocumentSubmissionOptionsParamsDTO) *GenericLoanDocumentSubmissionOptionsImpl {
	return &GenericLoanDocumentSubmissionOptionsImpl{
		FetchLoanSubmissionOptionsParams: fetchLoanDocumentTypesParams,
	}
}

// GetLoanDocumentTypesByProductVariantCode returns an implementation to fetch loan document types based on the given product variant code.
func GetLoanDocumentTypesByProductVariantCode(fetchLoanDocumentSubmissionOptionsParams FetchLoanDocumentSubmissionOptionsParamsDTO) ListLoanDocumentSubmissionOptionsByProductVariantCode {
	switch fetchLoanDocumentSubmissionOptionsParams.LoanDocumentOptionsRequest.ProductVariantCode {
	default:
		return NewGenericLoanDocumentTypesImpl(fetchLoanDocumentSubmissionOptionsParams)
	}
}

// FetchAndBuildLoanDocumentSubmissionOptionsByProductVariantCodeResponse retrieves and assembles document types for a product variant code.
func (l *GenericLoanDocumentSubmissionOptionsImpl) FetchAndBuildLoanDocumentSubmissionOptionsByProductVariantCodeResponse(ctx context.Context) (*api.ListLoanDocumentOptionsByProductVariantResponse, error) {
	steps := flow.Seq(
		l.getProductVariantByCode(),
		l.getLoanDocumentSubmissionOptionsByProductVariant(),
		l.getLoanDocumentSubmissionOptionVersion(),
		l.getLoanDocumentSubmissionOptionParameters(),
	)
	if seqErr := flow.Exec(ctx, steps); seqErr != nil {
		slog.FromContext(ctx).Error(l.FetchLoanSubmissionOptionsParams.LogTag,
			fmt.Sprintf("error getLoanDocumentSubmissionOption in seq steps: %s", seqErr.Error()), apiCommon.GetTraceID(ctx))
		return nil, seqErr
	}
	return l.buildLoanDocumentSubmissionOptions(), nil
}

// getProductVariantByCode retrieves the product variant details based on the provided product variant code from the database.
func (l *GenericLoanDocumentSubmissionOptionsImpl) getProductVariantByCode() flow.StepFn {
	return func(ctx context.Context) error {
		productCode := l.FetchLoanSubmissionOptionsParams.LoanDocumentOptionsRequest.ProductVariantCode
		productVariant, err := l.FetchLoanSubmissionOptionsParams.Store.ProductVariantDAO.Find(ctx, data.EqualTo("Code", productCode))
		if err != nil {
			slog.FromContext(ctx).Warn(l.FetchLoanSubmissionOptionsParams.LogTag, fmt.Sprintf("Error in finding from the Product Variant DB, err : %s", err), apiCommon.GetTraceID(ctx))
			return apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("Unable to load product variant from database for request with code: %s", productCode), err)
		}
		l.productVariant = productVariant[0]
		return nil
	}
}

// getLoanDocumentSubmissionOptionsByProductVariant retrieves loan document submission options by product variant ID.
// It queries the database for document submission options matching the product variant and updates the local state.
func (l *GenericLoanDocumentSubmissionOptionsImpl) getLoanDocumentSubmissionOptionsByProductVariant() flow.StepFn {
	return func(ctx context.Context) error {
		documentSubmissionOptions, fetchDocumentSubmissionOptionsErr := l.FetchLoanSubmissionOptionsParams.Store.LoanDocumentSubmissionOptionDAO.Find(ctx, data.EqualTo("ProductVariantID", l.productVariant.ID))
		if fetchDocumentSubmissionOptionsErr != nil {
			slog.FromContext(ctx).Warn(l.FetchLoanSubmissionOptionsParams.LogTag, fmt.Sprintf("Error in finding from the document submission options DB, err : %s", fetchDocumentSubmissionOptionsErr), apiCommon.GetTraceID(ctx))
			return apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("Unable to load document submission options from database for request with code: %s", l.productVariant.Code), fetchDocumentSubmissionOptionsErr)
		}
		l.loanDocumentSubmissionOption = documentSubmissionOptions
		return nil
	}
}

// getLoanDocumentSubmissionOptionVersion retrieves the active versions of loan document submission options.
// It filters and loads the corresponding options from the database and updates the local state in the implementation.
func (l *GenericLoanDocumentSubmissionOptionsImpl) getLoanDocumentSubmissionOptionVersion() flow.StepFn {
	return func(ctx context.Context) error {
		filters := []data.Condition{
			data.EqualTo("Status", constants.LoanDocumentSubmissionOptionVersionStatusActive),
		}
		documentSubmissionOptionIDs := []uint64{}
		for _, documentSubmissionOption := range l.loanDocumentSubmissionOption {
			documentSubmissionOptionIDs = append(documentSubmissionOptionIDs, documentSubmissionOption.ID)
		}
		filters = append(filters, data.ContainedIn("LoanDocumentSubmissionOptionID", apiCommon.ConvertInt64ToInterface(documentSubmissionOptionIDs)...))
		documentSubmissionOptionVersion, fetchDocumentSubmissionVersionErr := l.FetchLoanSubmissionOptionsParams.Store.LoanDocumentSubmissionOptionVersionDAO.Find(ctx, filters...)
		if fetchDocumentSubmissionVersionErr != nil {
			slog.FromContext(ctx).Warn(l.FetchLoanSubmissionOptionsParams.LogTag, fmt.Sprintf("Error in finding from the document submission options version DB, err : %s", fetchDocumentSubmissionVersionErr), apiCommon.GetTraceID(ctx))
			return apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("Unable to load document submission options version from database for request with code: %s", l.FetchLoanSubmissionOptionsParams.LoanDocumentOptionsRequest.ProductVariantCode), fetchDocumentSubmissionVersionErr)
		}
		l.loanDocumentSubmissionOptionVersion = documentSubmissionOptionVersion
		return nil
	}
}

// getLoanDocumentSubmissionOptionParameters retrieves parameters for loan document submission options.
// It filters data based on active status and links to specified submission option versions, and updates the local state.
func (l *GenericLoanDocumentSubmissionOptionsImpl) getLoanDocumentSubmissionOptionParameters() flow.StepFn {
	return func(ctx context.Context) error {
		documentSubmissionOptionVersionIDs := []uint64{}
		for _, documentSubmissionOptionVersion := range l.loanDocumentSubmissionOptionVersion {
			documentSubmissionOptionVersionIDs = append(documentSubmissionOptionVersionIDs, documentSubmissionOptionVersion.ID)
		}
		filters := []data.Condition{
			data.ContainedIn("LoanDocumentSubmissionOptionVersionID", apiCommon.ConvertInt64ToInterface(documentSubmissionOptionVersionIDs)...),
			data.EqualTo("IsEnabled", true),
		}
		documentSubmissionOptionParameters, fetchDocumentSubmissionOptionParametersErr := l.FetchLoanSubmissionOptionsParams.Store.LoanDocumentSubmissionOptionParametersDAO.Find(ctx, filters...)
		if fetchDocumentSubmissionOptionParametersErr != nil {
			slog.FromContext(ctx).Warn(l.FetchLoanSubmissionOptionsParams.LogTag, fmt.Sprintf("Error in finding from the document submission options parameters DB, err : %s", fetchDocumentSubmissionOptionParametersErr), apiCommon.GetTraceID(ctx))
			return apiCommon.BuildLoadDataFromDBErrResponse(ctx,
				fmt.Sprintf("Unable to load document submission options parameters from database for request with code: %s", l.FetchLoanSubmissionOptionsParams.LoanDocumentOptionsRequest.ProductVariantCode), fetchDocumentSubmissionOptionParametersErr)
		}
		l.loanDocumentSubmissionOptionParameters = documentSubmissionOptionParameters
		return nil
	}
}

// buildLoanDocumentSubmissionOptions assembles loan document submission options based on the current object state.
// It aggregates options, versions, parameters, and file configurations to form a structured response.
func (l *GenericLoanDocumentSubmissionOptionsImpl) buildLoanDocumentSubmissionOptions() *api.ListLoanDocumentOptionsByProductVariantResponse {
	// creates a map which is based on foreign keys
	// Create loanDocumentSubmissionVersionMap using lo.GroupBy
	loanDocumentSubmissionVersionMap := lo.Associate(l.loanDocumentSubmissionOptionVersion, func(version *storage.LoanDocumentSubmissionOptionVersion) (uint64, *storage.LoanDocumentSubmissionOptionVersion) {
		return version.LoanDocumentSubmissionOptionID, version
	})

	// Create loanDocumentSubmissionOptionParametersMap using lo.GroupBy
	loanDocumentSubmissionOptionParametersMap := lo.Associate(l.loanDocumentSubmissionOptionParameters, func(params *storage.LoanDocumentSubmissionOptionParameters) (uint64, *storage.LoanDocumentSubmissionOptionParameters) {
		return params.LoanDocumentSubmissionOptionVersionID, params
	})

	// Build the loanDocumentSubmissionOptions using lo.Map
	loanDocumentSubmissionOptions := lo.Map(l.loanDocumentSubmissionOption, func(option *storage.LoanDocumentSubmissionOption, _ int) api.LoanDocumentSubmissionOption {
		loanDocumentSubmissionOptionVersion := loanDocumentSubmissionVersionMap[option.ID]
		loanDocumentSubmissionOptionParameters := loanDocumentSubmissionOptionParametersMap[loanDocumentSubmissionOptionVersion.ID]

		allowedFileExtensions := []string{}
		_ = json.Unmarshal(loanDocumentSubmissionOptionParameters.AllowedFileExtensions, &allowedFileExtensions)

		return api.LoanDocumentSubmissionOption{
			DocumentType:      option.DocumentType,
			ApplicationType:   option.ApplicationType,
			SalaryType:        option.SalaryType,
			IsEnable:          loanDocumentSubmissionOptionParameters.IsEnabled,
			Priority:          int64(loanDocumentSubmissionOptionParameters.Priority),
			RequiredDocNumber: int64(loanDocumentSubmissionOptionParameters.RequiredDocumentNumber),
			RequiredDocUnit:   loanDocumentSubmissionOptionParameters.RequiredDocumentUnit,
			ExcludedMonths:    int64(loanDocumentSubmissionOptionParameters.ExcludedMonths),
			FileConfig: &api.FileConfig{
				MaxFileSizeInBytes:    int64(loanDocumentSubmissionOptionParameters.MaxFileSizeInBytes), //nolint:gosec // disable G115
				MaxUploadLimit:        int64(loanDocumentSubmissionOptionParameters.MaxUploadLimit),
				AllowedFileExtensions: allowedFileExtensions,
			},
		}
	})
	return &api.ListLoanDocumentOptionsByProductVariantResponse{LoanDocumentSubmissionOptions: loanDocumentSubmissionOptions}
}

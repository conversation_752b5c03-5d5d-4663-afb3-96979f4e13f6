package loandocumentsubmissionoptions

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/test/responses"
	"gitlab.myteksi.net/dakota/flow"
	"gitlab.myteksi.net/dakota/servus/v2"
)

var mockDBStore = &storage.DBStore{}

func Test_getProductVariantByCode(t *testing.T) {
	scenerios := []struct {
		description string
		expected    *GenericLoanDocumentSubmissionOptionsImpl
		expectedErr error
		dbResponse  []*storage.ProductVariant
	}{
		{
			description: "happy-path success",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           nil,
				loanDocumentSubmissionOptionVersion:    nil,
				loanDocumentSubmissionOptionParameters: nil,
			},
			dbResponse: responses.SampleProductVariantDAOResponse(),
		},
		{
			description: "error while fetching product variant",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				loanDocumentSubmissionOption:           nil,
				loanDocumentSubmissionOptionVersion:    nil,
				loanDocumentSubmissionOptionParameters: nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
	}
	for _, scenerio := range scenerios {
		t.Run(scenerio.description, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(scenerio.dbResponse, scenerio.expectedErr)
			mockDBStore.ProductVariantDAO = mockProductVariant

			steps := flow.Seq(
				scenerio.expected.getProductVariantByCode(),
			)
			actualErr := flow.Exec(context.Background(), steps)
			assert.Equal(t, scenerio.expectedErr, actualErr)
			assert.Equal(t, scenerio.expected.productVariant, scenerio.expected.productVariant)
		})
	}
}

func Test_getDocumentSubmissionOptionsByProductVariant(t *testing.T) {
	scenerios := []struct {
		description string
		expected    *GenericLoanDocumentSubmissionOptionsImpl
		expectedErr error
		dbResponse  []*storage.LoanDocumentSubmissionOption
	}{
		{
			description: "happy-path success",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           responses.SampleDocumentSubmissionOptionDAOResponse(),
				loanDocumentSubmissionOptionVersion:    nil,
				loanDocumentSubmissionOptionParameters: nil,
			},
			dbResponse: responses.SampleDocumentSubmissionOptionDAOResponse(),
		},
		{
			description: "error while fetching LoanDocumentSubmissionOption",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           nil,
				loanDocumentSubmissionOptionVersion:    nil,
				loanDocumentSubmissionOptionParameters: nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
	}
	for _, scenerio := range scenerios {
		t.Run(scenerio.description, func(t *testing.T) {
			mockLoanDocumentSubmissionOption := &storage.MockILoanDocumentSubmissionOptionDAO{}
			mockLoanDocumentSubmissionOption.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(scenerio.dbResponse, scenerio.expectedErr)
			mockDBStore.LoanDocumentSubmissionOptionDAO = mockLoanDocumentSubmissionOption

			steps := flow.Seq(
				scenerio.expected.getLoanDocumentSubmissionOptionsByProductVariant(),
			)
			actualErr := flow.Exec(context.Background(), steps)
			assert.Equal(t, scenerio.expectedErr, actualErr)
			assert.Equal(t, scenerio.expected.productVariant, scenerio.expected.productVariant)
		})
	}
}

func Test_getDocumentSubmissionOptionVersion(t *testing.T) {
	scenerios := []struct {
		description string
		expected    *GenericLoanDocumentSubmissionOptionsImpl
		expectedErr error
		dbResponse  []*storage.LoanDocumentSubmissionOptionVersion
	}{
		{
			description: "happy-path success",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           responses.SampleDocumentSubmissionOptionDAOResponse(),
				loanDocumentSubmissionOptionVersion:    responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
				loanDocumentSubmissionOptionParameters: nil,
			},
			dbResponse: responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
		},
		{
			description: "error while fetching LoanDocumentSubmissionOptionVersion",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           responses.SampleDocumentSubmissionOptionDAOResponse(),
				loanDocumentSubmissionOptionVersion:    responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
				loanDocumentSubmissionOptionParameters: nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
	}
	for _, scenerio := range scenerios {
		t.Run(scenerio.description, func(t *testing.T) {
			mockLoanDocumentSubmissionOptionVersion := &storage.MockILoanDocumentSubmissionOptionVersionDAO{}
			mockLoanDocumentSubmissionOptionVersion.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(scenerio.dbResponse, scenerio.expectedErr)
			mockDBStore.LoanDocumentSubmissionOptionVersionDAO = mockLoanDocumentSubmissionOptionVersion

			steps := flow.Seq(
				scenerio.expected.getLoanDocumentSubmissionOptionVersion(),
			)
			actualErr := flow.Exec(context.Background(), steps)
			assert.Equal(t, scenerio.expectedErr, actualErr)
			assert.Equal(t, scenerio.expected.productVariant, scenerio.expected.productVariant)
		})
	}
}

func Test_getDocumentSubmissionOptionParameters(t *testing.T) {
	scenerios := []struct {
		description string
		expected    *GenericLoanDocumentSubmissionOptionsImpl
		expectedErr error
		dbResponse  []*storage.LoanDocumentSubmissionOptionParameters
	}{
		{
			description: "happy-path success",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           responses.SampleDocumentSubmissionOptionDAOResponse(),
				loanDocumentSubmissionOptionVersion:    responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
				loanDocumentSubmissionOptionParameters: responses.SampleDocumentSubmissionOptionParametersDAOResponse(),
			},
			dbResponse: responses.SampleDocumentSubmissionOptionParametersDAOResponse(),
		},
		{
			description: "error while fetching DocumentSubmissionOptionParameters",
			expected: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           responses.SampleDocumentSubmissionOptionDAOResponse(),
				loanDocumentSubmissionOptionVersion:    responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
				loanDocumentSubmissionOptionParameters: nil,
			},
			expectedErr: servus.ServiceError{
				HTTPCode: 500,
				Code:     "3244",
				Message:  "Database load error",
				Errors:   nil,
			},
		},
	}
	for _, scenerio := range scenerios {
		t.Run(scenerio.description, func(t *testing.T) {
			mockLoanDocumentSubmissionOptionParameters := &storage.MockILoanDocumentSubmissionOptionParametersDAO{}
			mockLoanDocumentSubmissionOptionParameters.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(scenerio.dbResponse, scenerio.expectedErr)
			mockDBStore.LoanDocumentSubmissionOptionParametersDAO = mockLoanDocumentSubmissionOptionParameters

			steps := flow.Seq(
				scenerio.expected.getLoanDocumentSubmissionOptionParameters(),
			)
			actualErr := flow.Exec(context.Background(), steps)
			assert.Equal(t, scenerio.expectedErr, actualErr)
			assert.Equal(t, scenerio.expected.productVariant, scenerio.expected.productVariant)
		})
	}
}

func Test_buildLoanDocumentSubmissionOptions(t *testing.T) {
	scenerios := []struct {
		description string
		expected    *api.ListLoanDocumentOptionsByProductVariantResponse
		input       *GenericLoanDocumentSubmissionOptionsImpl
	}{
		{
			description: "happy-path success",
			input: &GenericLoanDocumentSubmissionOptionsImpl{
				FetchLoanSubmissionOptionsParams: FetchLoanDocumentSubmissionOptionsParamsDTO{
					LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
					LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
					Store:                      mockDBStore,
				},
				productVariant:                         responses.SampleProductVariantDAOResponse()[0],
				loanDocumentSubmissionOption:           responses.SampleDocumentSubmissionOptionDAOResponse(),
				loanDocumentSubmissionOptionVersion:    responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
				loanDocumentSubmissionOptionParameters: responses.SampleDocumentSubmissionOptionParametersDAOResponse(),
			},
			expected: responses.ListLoanDocumentTypesSampleResponse(),
		},
	}
	for _, scenerio := range scenerios {
		t.Run(scenerio.description, func(t *testing.T) {
			actualResponse := scenerio.input.buildLoanDocumentSubmissionOptions()
			assert.Equal(t, actualResponse, scenerio.expected)
		})
	}
}

func Test_FetchAndBuildDocumentTypesByProductVariantCodeResponse(t *testing.T) {
	scenerios := []struct {
		description                                      string
		expected                                         *api.ListLoanDocumentOptionsByProductVariantResponse
		expectedErr                                      error
		productVariantDBResponse                         []*storage.ProductVariant
		loanDocumentSubmissionOptionDBResponse           []*storage.LoanDocumentSubmissionOption
		loanDocumentSubmissionOptionVersionDBResponse    []*storage.LoanDocumentSubmissionOptionVersion
		loanDocumentSubmissionOptionParametersDBResponse []*storage.LoanDocumentSubmissionOptionParameters
	}{
		{
			description:                                      "happy-path success",
			productVariantDBResponse:                         responses.SampleProductVariantDAOResponse(),
			loanDocumentSubmissionOptionDBResponse:           responses.SampleDocumentSubmissionOptionDAOResponse(),
			loanDocumentSubmissionOptionVersionDBResponse:    responses.SampleDocumentSubmissionOptionVersionDAOResponse(),
			loanDocumentSubmissionOptionParametersDBResponse: responses.SampleDocumentSubmissionOptionParametersDAOResponse(),
			expected: responses.ListLoanDocumentTypesSampleResponse(),
		},
	}
	for _, scenerio := range scenerios {
		t.Run(scenerio.description, func(t *testing.T) {
			mockProductVariant := &storage.MockIProductVariantDAO{}
			mockProductVariant.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(scenerio.productVariantDBResponse, nil)
			mockDBStore.ProductVariantDAO = mockProductVariant
			mockLoanDocumentSubmissionOption := &storage.MockILoanDocumentSubmissionOptionDAO{}
			mockLoanDocumentSubmissionOption.On("Find", mock.Anything, mock.AnythingOfType("data.Condition")).Return(scenerio.loanDocumentSubmissionOptionDBResponse, nil)
			mockDBStore.LoanDocumentSubmissionOptionDAO = mockLoanDocumentSubmissionOption
			mockLoanDocumentSubmissionOptionVersion := &storage.MockILoanDocumentSubmissionOptionVersionDAO{}
			mockLoanDocumentSubmissionOptionVersion.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(scenerio.loanDocumentSubmissionOptionVersionDBResponse, nil)
			mockDBStore.LoanDocumentSubmissionOptionVersionDAO = mockLoanDocumentSubmissionOptionVersion
			mockLoanDocumentSubmissionOptionParameters := &storage.MockILoanDocumentSubmissionOptionParametersDAO{}
			mockLoanDocumentSubmissionOptionParameters.On("Find", mock.Anything, mock.AnythingOfType("data.Condition"), mock.AnythingOfType("data.Condition")).Return(scenerio.loanDocumentSubmissionOptionParametersDBResponse, nil)
			mockDBStore.LoanDocumentSubmissionOptionParametersDAO = mockLoanDocumentSubmissionOptionParameters
			documentTypesParamsDTO := FetchLoanDocumentSubmissionOptionsParamsDTO{
				LoanDocumentOptionsRequest: &api.ListLoanDocumentOptionsByProductVariantRequest{ProductVariantCode: string(api.ProductVariantCode_DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT)},
				LogTag:                     constants.ListLoanDocumentTypesByProductVariantLogTag,
				Store:                      mockDBStore,
			}
			actualResponse, err := GetLoanDocumentTypesByProductVariantCode(documentTypesParamsDTO).FetchAndBuildLoanDocumentSubmissionOptionsByProductVariantCodeResponse(context.Background())
			assert.Equal(t, scenerio.expected, actualResponse)
			assert.Nil(t, err)
		})
	}
}

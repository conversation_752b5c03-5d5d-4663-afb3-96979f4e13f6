// Package loanadditionalparameters ...
package loanadditionalparameters

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetAdditionalLoanParameters fetches the loan amount tenor slabs from DB
func GetAdditionalLoanParameters(ctx context.Context, store *storage.DBStore, productVariantID uint64) (*api.LoanParametersDetail, error) {
	dbResponse, err := store.LoanAllowedAmountTenorSlabDAO.Find(ctx, data.EqualTo(constants.ProductVariantID, productVariantID))
	if err != nil && err != data.ErrNoData {
		slog.FromContext(ctx).Warn(constants.GetAdditionalLoanParametersLogTag, fmt.Sprintf("Error in finding from the loanAllowedAmountTenorSlab DB, err : %s", err), apiCommon.GetTraceID(ctx))
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load laon additional parameters from database for request with product-variant-id: %d", productVariantID), err)
	}

	if err == data.ErrNoData {
		slog.FromContext(ctx).Info(constants.GetAdditionalLoanParametersLogTag, "no data found for loanAllowedAmountTenorSlab", apiCommon.GetTraceID(ctx))
		return nil, nil
	}
	var tenorAmountSlabs []api.LoanAllowedAmountTenorSlab
	for _, slabData := range dbResponse {
		tenorAmountSlabs = append(tenorAmountSlabs, api.LoanAllowedAmountTenorSlab{
			FromAmount: slabData.FromAmount,
			ToAmount:   slabData.ToAmount,
			Currency:   slabData.Currency,
			TenorUnit:  slabData.TenorUnit,
			MaxTenor:   slabData.MaxTenor,
			MinTenor:   slabData.MinTenor,
		})
	}

	return &api.LoanParametersDetail{LoanAllowedAmountTenorSlab: tenorAmountSlabs}, nil
}

// Package common ...
package common

import (
	"context"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/dto"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/utils"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetInterestParamConstants ...
func GetInterestParamConstants(interestSlabsString string) *dto.InterestParamConstants {
	return &dto.InterestParamConstants{
		InterestRateTypeParam: api.InterestRateParameter{
			Namespace:      "pricing",
			ParameterKey:   "interest_rate_type",
			ParameterValue: "TIER",
			DataType:       "STRING",
			Description:    "The interest rate can either be a flat OR tiered",
		},
		FlatInterestRateParam: api.InterestRateParameter{
			Namespace:      "pricing",
			ParameterKey:   "flat_interest_rate",
			ParameterValue: "0.0",
			DataType:       "FLOAT",
			Description:    "The flat interest rate (p.a) used to calculate interest on the customer’s account balance and accrued daily. It is percentage (0.0000-X.XXXX%) with 4 decimal places.",
		},
		OverallBalanceFeatureInterestApplicationAccountParam: api.InterestRateParameter{
			Namespace:      "pricing",
			ParameterKey:   "overall_balance_features_interest_application_account",
			ParameterValue: "apportioned",
			DataType:       "STRING",
			Description:    "The flat interest rate (p.a) used to calculate interest on the customer’s account balance and accrued daily. It is percentage (0.0000-X.XXXX%) with 4 decimal places.",
		},
		InterestRateTiersParam: api.InterestRateParameter{
			Namespace:      "pricing",
			ParameterKey:   "interest_rate_tiers",
			ParameterValue: interestSlabsString,
			DataType:       "STRING",
			Description:    "Tiered interest rates applicable to the main denomination as determined by the tier ranges.",
		},
	}
}

// UnorderedEqual ...
func UnorderedEqual(first, second []api.ProductVariantParameter) bool {
	if len(first) != len(second) {
		return false
	}
	exists := make(map[api.ProductVariantParameter]bool)
	for _, value := range first {
		exists[value] = true
	}
	for _, value := range second {
		if !exists[value] {
			return false
		}
	}
	return true
}

// GetTraceID returns the TraceID from the context to include in logs for tracing
func GetTraceID(ctx context.Context) slog.Tag {
	span := commonCtx.GetSpan(ctx)
	return slog.TraceID(utils.SafeIntToString(span.Context().TraceID()))
}

// ConvertInt64ToInterface converts a slice of int64 to slice of interface{}
func ConvertInt64ToInterface(arr []uint64) []interface{} {
	var interfaceArr []interface{}
	for _, elem := range arr {
		interfaceArr = append(interfaceArr, elem)
	}
	return interfaceArr
}

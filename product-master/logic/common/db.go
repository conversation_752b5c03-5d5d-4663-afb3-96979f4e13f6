package common

import (
	"context"
	"fmt"
	"net/http"
	"reflect"
	"strconv"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiErr "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/error"

	mysqlCodes "github.com/VividCortex/mysqlerr"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// BuildLoadDataFromDBErrResponse ...
func BuildLoadDataFromDBErrResponse(ctx context.Context, msg string, err error) error {
	slog.FromContext(ctx).Warn(constants.DatabaseErrorTag, fmt.Sprintf("%s: %s", msg, err.Error()), GetTraceID(ctx))
	if reflect.DeepEqual(err, data.ErrNoData) {
		return apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message)
	}
	return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseLoad.Code), 10), apiErr.ErrDatabaseLoad.Message)
}

// BuildDataSaveInDBErrResponse ...
func BuildDataSaveInDBErrResponse(ctx context.Context, msg string, err error) error {
	slog.FromContext(ctx).Warn(constants.DatabaseErrorTag, fmt.Sprintf("%s: %s", msg, err.Error()), GetTraceID(ctx))
	code, message := getMappedMysqlError(err, apiErr.ErrDatabaseSave)
	return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(code), 10), message)
}

// BuildDataUpdateInDBErrResponse ...
func BuildDataUpdateInDBErrResponse(ctx context.Context, msg string, err error) error {
	slog.FromContext(ctx).Warn(constants.DatabaseErrorTag, fmt.Sprintf("%s: %s", msg, err.Error()), GetTraceID(ctx))
	code, message := getMappedMysqlError(err, apiErr.ErrDatabaseUpdate)
	return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(code), 10), message)
}

func getMappedMysqlError(err error, defaultError apiErr.CustomError) (int, string) {
	var mysqlErrorCode uint16
	var mysqlErr error
	var ok bool
	if ok, mysqlErr = apiErr.Error(err); !ok { // MySQL error
		return defaultError.Code, defaultError.Message
	}

	mysqlErrorCode = apiErr.MySQLErrorCode(mysqlErr)
	switch mysqlErrorCode {
	case mysqlCodes.ER_NO_REFERENCED_ROW_2:
		return apiErr.ErrAddUpdateForeignKeyConstraint.Code, apiErr.ErrAddUpdateForeignKeyConstraint.Message

	default:
		return defaultError.Code, defaultError.Message
	}
}

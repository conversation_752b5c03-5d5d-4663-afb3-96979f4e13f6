package common

import (
	"context"

	commonCtx "gitlab.myteksi.net/dakota/common/context"
)

// GetIdempotencyKeyFromHeader ...
func GetIdempotencyKeyFromHeader(ctx context.Context) string {
	return commonCtx.GetIdempotencyKey(ctx)
}

// AddIdempotencyKeyToHeader ...
func AddIdempotencyKeyToHeader(ctx context.Context, idempotencyKey string) context.Context {
	return commonCtx.WithIdempotencyKey(ctx, idempotencyKey)
}

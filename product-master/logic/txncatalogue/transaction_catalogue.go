package txncatalogue

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CreateTransactionCatalogue ...
func CreateTransactionCatalogue(ctx context.Context, req *api.CreateTransactionCatalogueRequest, store *storage.DBStore) (*api.CreateTransactionCatalogueResponse, error) {
	var description sql.NullString
	description.Valid = !(req.Description == "")
	description.String = req.Description

	transactionCatalogueID := uuid.New().String()
	if err := store.TransactionCatalogueDAO.Save(ctx, &storage.TransactionCatalogue{
		PublicID:       transactionCatalogueID,
		Domain:         req.Domain,
		IsFinancialTxn: req.IsFinancialTxn,
		TxnType:        req.TxnType,
		TxnSubType:     req.TxnSubType,
		DisplayName:    req.DisplayName,
		Description:    description,
		Status:         string(api.EntityStatus_ACTIVE),
		CreatedBy:      req.CreatedBy,
		UpdatedBy:      req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save transaction catalogue to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	transactionCatalogue, err := store.TransactionCatalogueDAO.Find(ctx, data.EqualTo("PublicID", transactionCatalogueID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load transaction catalogue from database after saving for request with id: %s", transactionCatalogueID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateTransactionCatalogueLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.CreateTransactionCatalogueResponse{
		TransactionCatalogue: mapTransactionCatalogueFromStorage(*transactionCatalogue[0]),
	}, nil
}

// GetTransactionCatalogue ...
func GetTransactionCatalogue(ctx context.Context, req *api.GetTransactionCatalogueRequest, store *storage.DBStore) (*api.GetTransactionCatalogueResponse, error) {
	transactionCatalogue, err := store.TransactionCatalogueDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load transaction catalogue from database for request with id: %s", req.Id), err)
	}

	slog.FromContext(ctx).Info(constants.GetTransactionCatalogueLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.GetTransactionCatalogueResponse{
		TransactionCatalogue: mapTransactionCatalogueFromStorage(*transactionCatalogue[0]),
	}, nil
}

// UpdateTransactionCatalogueStatus ...
func UpdateTransactionCatalogueStatus(ctx context.Context, req *api.UpdateTransactionCatalogueStatusRequest, store *storage.DBStore) (*api.UpdateTransactionCatalogueStatusResponse, error) {
	transactionCatalogue, err := store.TransactionCatalogueDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load transaction catalogue from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.TransactionCatalogueDAO.Update(ctx, &storage.TransactionCatalogue{
		ID:             transactionCatalogue[0].ID,
		PublicID:       transactionCatalogue[0].PublicID,
		Domain:         transactionCatalogue[0].Domain,
		IsFinancialTxn: transactionCatalogue[0].IsFinancialTxn,
		TxnType:        transactionCatalogue[0].TxnType,
		TxnSubType:     transactionCatalogue[0].TxnSubType,
		DisplayName:    transactionCatalogue[0].DisplayName,
		Description:    transactionCatalogue[0].Description,
		Status:         string(req.Status),
		CreatedBy:      transactionCatalogue[0].CreatedBy,
		CreatedAt:      transactionCatalogue[0].CreatedAt,
		UpdatedBy:      req.UpdatedBy,
	}); updateErr != nil {
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update transaction catalogue in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	transactionCatalogue, err = store.TransactionCatalogueDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load transaction catalogue from database after update for request with id: %s", req.Id), err)
	}

	slog.FromContext(ctx).Info(constants.UpdateTransactionCatalogueStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateTransactionCatalogueStatusResponse{
		TransactionCatalogue: mapTransactionCatalogueFromStorage(*transactionCatalogue[0]),
	}, nil
}

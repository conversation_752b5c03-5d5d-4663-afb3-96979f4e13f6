package txncatalogue

import (
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
)

func mapTransactionCatalogueFromStorage(transactionCatalogue storage.TransactionCatalogue) *api.TransactionCatalogue {
	return &api.TransactionCatalogue{
		Id:             transactionCatalogue.PublicID,
		Domain:         transactionCatalogue.Domain,
		IsFinancialTxn: transactionCatalogue.IsFinancialTxn,
		TxnType:        transactionCatalogue.TxnType,
		TxnSubType:     transactionCatalogue.TxnSubType,
		DisplayName:    transactionCatalogue.DisplayName,
		Description:    transactionCatalogue.Description.String,
		Status:         api.EntityStatus(transactionCatalogue.Status),
		CreatedBy:      transactionCatalogue.CreatedBy,
		CreatedAt:      transactionCatalogue.CreatedAt,
		UpdatedBy:      transactionCatalogue.UpdatedBy,
		UpdatedAt:      transactionCatalogue.UpdatedAt,
	}
}

func mapProductVariantTransactionCatalogueMappingFromStorage(mapping storage.ProductVariantTransactionCatalogueMapping, productVariantPublicID string, transactionCataloguePublicID string) *api.ProductVariantTransactionCatalogueMapping {
	return &api.ProductVariantTransactionCatalogueMapping{
		Id:                     mapping.PublicID,
		ProductVariantID:       productVariantPublicID,
		TransactionCatalogueID: transactionCataloguePublicID,
		Status:                 api.EntityStatus(mapping.Status),
		CreatedBy:              mapping.CreatedBy,
		CreatedAt:              mapping.CreatedAt,
		UpdatedBy:              mapping.UpdatedBy,
		UpdatedAt:              mapping.UpdatedAt,
	}
}

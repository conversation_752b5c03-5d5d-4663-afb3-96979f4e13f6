package txncatalogue

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/core-banking/product-master/api"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/constants"
	apiCommon "gitlab.com/gx-regional/dbmy/core-banking/product-master/logic/common"
	"gitlab.com/gx-regional/dbmy/core-banking/product-master/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"github.com/google/uuid"
)

// CreateProductVariantTransactionCatalogueMapping ...
func CreateProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.CreateProductVariantTransactionCatalogueMappingRequest, store *storage.DBStore) (*api.CreateProductVariantTransactionCatalogueMappingResponse, error) {
	mappingID := uuid.New().String()

	productVariant, err := store.ProductVariantDAO.Find(ctx, data.EqualTo("PublicID", req.ProductVariantID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %s", req.ProductVariantID), err)
	}

	transactionCatalogue, err := store.TransactionCatalogueDAO.Find(ctx, data.EqualTo("PublicID", req.TransactionCatalogueID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load transaction catalogue from database for request with id: %s", req.TransactionCatalogueID), err)
	}

	if err = store.ProductVariantTransactionCatalogueMappingDAO.Save(ctx, &storage.ProductVariantTransactionCatalogueMapping{
		PublicID:               mappingID,
		ProductVariantID:       productVariant[0].ID,
		TransactionCatalogueID: transactionCatalogue[0].ID,
		Status:                 string(api.EntityStatus_ACTIVE),
		CreatedBy:              req.CreatedBy,
		UpdatedBy:              req.CreatedBy,
	}); err != nil {
		return nil, apiCommon.BuildDataSaveInDBErrResponse(ctx,
			fmt.Sprintf("unable to save product variant transaction catalogue mapping to database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), err)
	}

	productVariantTransactionCatalogueMapping, err := store.ProductVariantTransactionCatalogueMappingDAO.Find(ctx, data.EqualTo("PublicID", mappingID))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database after saving for request with id: %s", mappingID), err)
	}

	slog.FromContext(ctx).Info(constants.CreateProductVariantTransactionCatalogueMappingLogTag, fmt.Sprintf("Succesfully completed the request: %+v", req), apiCommon.GetTraceID(ctx))
	return &api.CreateProductVariantTransactionCatalogueMappingResponse{
		ProductVariantTransactionCatalogueMapping: mapProductVariantTransactionCatalogueMappingFromStorage(*productVariantTransactionCatalogueMapping[0], req.ProductVariantID, req.TransactionCatalogueID),
	}, nil
}

// GetProductVariantTransactionCatalogueMapping ...
func GetProductVariantTransactionCatalogueMapping(ctx context.Context, req *api.GetProductVariantTransactionCatalogueMappingRequest, store *storage.DBStore) (*api.GetProductVariantTransactionCatalogueMappingResponse, error) {
	productVariantTransactionCatalogueMapping, err := store.ProductVariantTransactionCatalogueMappingDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database for request with id: %s", req.Id), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, productVariantTransactionCatalogueMapping[0].ProductVariantID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %d", productVariantTransactionCatalogueMapping[0].ProductVariantID), err)
	}

	transactionCatalogue, err := store.TransactionCatalogueDAO.LoadByID(ctx, productVariantTransactionCatalogueMapping[0].TransactionCatalogueID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load transaction catalogue from database for request with id: %d", productVariantTransactionCatalogueMapping[0].TransactionCatalogueID), err)
	}

	slog.FromContext(ctx).Info(constants.GetProductVariantTransactionCatalogueMappingLogTag, fmt.Sprintf("Succesfully completed the request: %+v", req), apiCommon.GetTraceID(ctx))
	return &api.GetProductVariantTransactionCatalogueMappingResponse{
		ProductVariantTransactionCatalogueMapping: mapProductVariantTransactionCatalogueMappingFromStorage(*productVariantTransactionCatalogueMapping[0], productVariant.PublicID, transactionCatalogue.PublicID),
	}, nil
}

// UpdateProductVariantTransactionCatalogueMappingStatus ...
func UpdateProductVariantTransactionCatalogueMappingStatus(ctx context.Context, req *api.UpdateProductVariantTransactionCatalogueMappingStatusRequest, store *storage.DBStore) (*api.UpdateProductVariantTransactionCatalogueMappingStatusResponse, error) {
	productVariantTransactionCatalogueMapping, err := store.ProductVariantTransactionCatalogueMappingDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database for request with id: %s", req.Id), err)
	}

	if updateErr := store.ProductVariantTransactionCatalogueMappingDAO.Update(ctx, &storage.ProductVariantTransactionCatalogueMapping{
		ID:                     productVariantTransactionCatalogueMapping[0].ID,
		PublicID:               productVariantTransactionCatalogueMapping[0].PublicID,
		ProductVariantID:       productVariantTransactionCatalogueMapping[0].ProductVariantID,
		TransactionCatalogueID: productVariantTransactionCatalogueMapping[0].TransactionCatalogueID,
		Status:                 string(req.Status),
		CreatedBy:              productVariantTransactionCatalogueMapping[0].CreatedBy,
		CreatedAt:              productVariantTransactionCatalogueMapping[0].CreatedAt,
		UpdatedBy:              req.UpdatedBy,
	}); updateErr != nil {
		return nil, apiCommon.BuildDataUpdateInDBErrResponse(ctx,
			fmt.Sprintf("unable to update product variant transaction catalogue mapping in database for request with idempotency-key: %s", apiCommon.GetIdempotencyKeyFromHeader(ctx)), updateErr)
	}

	productVariantTransactionCatalogueMapping, err = store.ProductVariantTransactionCatalogueMappingDAO.Find(ctx, data.EqualTo("PublicID", req.Id))
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product variant transaction catalogue mapping from database after update for request with id: %s", req.Id), err)
	}

	productVariant, err := store.ProductVariantDAO.LoadByID(ctx, productVariantTransactionCatalogueMapping[0].ProductVariantID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load product-variant from database for request with id: %d", productVariantTransactionCatalogueMapping[0].ProductVariantID), err)
	}

	transactionCatalogue, err := store.TransactionCatalogueDAO.LoadByID(ctx, productVariantTransactionCatalogueMapping[0].TransactionCatalogueID)
	if err != nil {
		return nil, apiCommon.BuildLoadDataFromDBErrResponse(ctx,
			fmt.Sprintf("unable to load transaction catalogue from database for request with id: %d", productVariantTransactionCatalogueMapping[0].TransactionCatalogueID), err)
	}

	slog.FromContext(ctx).Info(constants.UpdateProductVariantTransactionCatalogueMappingStatusLogTag, "Successfully completed the request", apiCommon.GetTraceID(ctx))
	return &api.UpdateProductVariantTransactionCatalogueMappingStatusResponse{
		ProductVariantTransactionCatalogueMapping: mapProductVariantTransactionCatalogueMappingFromStorage(*productVariantTransactionCatalogueMapping[0], productVariant.PublicID, transactionCatalogue.PublicID),
	}, nil
}
